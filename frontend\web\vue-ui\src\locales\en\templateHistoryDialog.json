{"templateHistoryDialog": {"title": "Template History", "allHistory": "All History", "versions": "Versions", "operationTime": "Operation Time", "operator": "Operator", "operation": "Operation", "details": "Details", "version": "Version", "versionLabel": "Version", "effectiveDate": "Effective Date", "reason": "Reason", "transferFrom": "Transfer From", "transferTo": "Transfer To", "lastEditTime": "Last Edit Time", "editVersion": "Edit Version", "editEffectiveDate": "Edit Effective Date", "cancelPublish": "Cancel Publish", "editVersionTitle": "Edit Version", "editEffectiveDateTitle": "Edit Effective Date", "cancelPublishTitle": "Cancel Publish", "cancelPublishConfirm": "Are you sure you want to cancel the publication of version \"{version}\"?", "versionInputPlaceholder": "Enter new version", "datePickerPlaceholder": "Select effective date", "dateHint": "Effective date must be tomorrow or later", "versionEmpty": "Version cannot be empty", "dateEmpty": "Please select an effective date", "editSuccess": "Edit successful", "editFailed": "Edit failed", "cancelSuccess": "Version has been cancelled", "cancelFailed": "Failed to cancel publish", "effective": "Effective", "originallyScheduled": "Originally scheduled for", "scheduledFor": "Scheduled for", "notEffective": "Not effective", "notYetEffective": "Not yet effective", "noVersions": "No versions available", "cancel": "Cancel", "confirm": "Confirm", "actions": {"create": "Create Template", "save": "Manual Save", "publish": "Publish Template", "remove": "Move to Recycle Bin", "restore": "Restore from Recycle Bin", "transfer": "Transfer Template", "submit_audit": "Submit for Audit", "cancel_audit": "Cancel Audit", "audit_agree": "Audit Approved", "audit_refuse": "<PERSON><PERSON> Rejected", "submit_publish_audit": "Submit for Publish Audit", "publish_audit_agree": "Publish Audit Approved", "publish_audit_refuse": "Publish Audit Rejected", "cancel_publish": "Cancel Publish", "re_edit": "Re-edit", "edit_version": "Edit Version Number", "edit_effective_date": "Edit Effective Date"}}}