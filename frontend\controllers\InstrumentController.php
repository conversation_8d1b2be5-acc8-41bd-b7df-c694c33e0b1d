<?php

namespace frontend\controllers;

use common\components\Encryption;
use frontend\core\CommonServer;
use frontend\interfaces\CenterInterface;
use frontend\models\ApprovalModel;
use frontend\models\ApprovalNodeModel;
use frontend\models\InstrumentBatchUploadInfo;
use frontend\models\InstrumentBindingModel;
use frontend\models\InstrumentCheckRecordExtendFieldModel;
use frontend\models\InstrumentDataFileModel;
use frontend\models\InstrumentDataNumericalModel;
use frontend\models\InstrumentRunningRecordModel;
use frontend\models\InstrumentRunningRecordExtendFieldModel;
use frontend\models\InstrumentRepairRecordModel;
use frontend\models\InstrumentRepairRecordExtendFieldModel;
use frontend\models\InstrumentCheckRecordModel;
use frontend\models\InstrumentDefineFields;
use frontend\models\InstrumentsBatchEditFile;
use frontend\models\InstrumentsBookModel;
use frontend\models\InstrumentsModel;
use frontend\services\CollaborationServer;
use frontend\services\CommentServer;
use frontend\services\CompanyAuthServer;
use frontend\services\ExperimentServer;
use frontend\services\InstrumentServer;
use PHPExcel;
use yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

/*
 * 仪器库的设置
 * <AUTHOR>
 * @copyright 2019-10-31
 */

class InstrumentController extends MyController
{

    /**
     * Notes:仪器库管理页面,我的仪器库页面
     * Author: hkk
     * Date: 2019/10/31 13:29
     * @return \common\controllers\json
     */
    public function actionManage()
    {

        $postData = \Yii::$app->getRequest()->post();


        // 缓存读取所有可见鹰群
        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $visibleGroups = yii\helpers\ArrayHelper::index($visibleGroups, 'id');
        $data['visibleGroups'] = $visibleGroups;

        // 缓存读取所有可见部门
        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $visibleDepartments = yii\helpers\ArrayHelper::index($visibleDepartments, 'id');
        $data['visibleDepartments'] = $visibleDepartments;

        // 缓存读取所有可见用户
        $visibleUsers = (new CenterInterface())->getVisibleUsers($this->userinfo->id);
        $data['all_user'] = $visibleUsers;

        //所有鹰群和部门
        $allGroups = (new CenterInterface())->getGroupsListByCompanyId();
        $allDepartments = (new CenterInterface())->getDepartmentListByCompanyId(1)['list'];
        $data['allGroups'] = $allGroups;
        $data['allDepartments'] = $allDepartments;

        // 用于查询我加入的鹰群或部门
        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;
        $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器

        // 权限判断
        $data['isCanWrite'] = false;
        // bug 36389 plug/京新：系统管理员在【仪器库管理】和【我的仪器库】中看到的仪器数目不一致,将查看权限拉出来
        // 获取查看权限
        // $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        }

        if ($postData['type'] == 'instruments_manage') {
            if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
                return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
            }
            // 获取是否有编辑权限
            $data['isCanWrite'] = \Yii::$app->view->params['instruments_manage_write'];
        }
//        else { // add by hkk 2020/4/23  我的仪器库只显示可见鹰群或可见部门的仪器
//            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
//        }


        // 获取查询条件
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['name'] = \Yii::$app->request->post('instrument_name', '');
        $where['status'] = \Yii::$app->request->post('instrument_status', '');
        $where['check_situation'] = \Yii::$app->request->post('instrument_check', '');
        $where['check_status'] = \Yii::$app->request->post('instrument_check', '');
        $where['create_by'] = \Yii::$app->request->post('instrument_create_user', '');
        $where['responsible_person'] = \Yii::$app->request->post('instrument_responsible_user', '');
        $where['in_charge_person'] = \Yii::$app->request->post('instrument_in_charge_user', '');
        $where['maintenance_person'] = \Yii::$app->request->post('instrument_maintenance_user', '');
        $where['position'] = \Yii::$app->request->post('instrument_position', '');
        $where['supplier'] = \Yii::$app->request->post('instrument_supplier', '');
        $where['manufacturer'] = \Yii::$app->request->post('instrument_manufacturer', ''); // add by hkk 2022/8/11
        $where['remark'] = \Yii::$app->request->post('instrument_remark', ''); // add by hkk 2022/8/11
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['belong_group'] = \Yii::$app->request->post('instrument_belong_group', ''); // add by hkk 2022/6/27  所属鹰群筛选
        $where['belong_department'] = \Yii::$app->request->post('instrument_belong_department', ''); // add by hkk 2022/6/27  所属部门筛选
        $where['instrument_type'] = \Yii::$app->request->post('instrument_type', ''); // add by hkk 2021/4/28  类别筛选
        $where['order_type'] = \Yii::$app->request->post('order_type', 'default'); // add by hkk 2022/5/26 排序
        $where['order_field'] = \Yii::$app->request->post('order_field', ''); // add by hkk 2021/4/28  排序字段
        $where['order_field_type'] = \Yii::$app->request->post('order_field_type', ''); // add by hkk 2021/4/28  排序字段
        $where['data_type'] = \Yii::$app->request->post('instrument_data_type', ''); // add by hkk 2021/4/28  排序字段


        // 获取查询的数据
        $instrumentsData = (new InstrumentServer())->listInstrumentsView($where, $limit, $page);
        $data['instrumentsList'] = $instrumentsData['instruments_list'];
        $data['totalCount'] = $instrumentsData['totalCount'];
        $data['defineFields'] = $instrumentsData['defineFields']; // add by hkk 2021/4/27
        $data['typeList'] = $instrumentsData['type_list']; // add by hkk 2021/4/27
        $data['limit'] = $limit;
        $data['page'] = $page;
        $data['checkedIds'] = $instrumentsData['checkedIds'];
        $data['unCheckedIds'] = $instrumentsData['unCheckedIds'];


        // 补充排序字段
        $data['type'] = $postData['type']; // add by hkk 2019/11/1 判断是仪器库管理还是我的仪器库页面
        $data['orderType'] = $where['order_type']; // add by hkk 2022/5/26  传递排序参数
        $data['orderField'] = $where['order_field']; // add by hkk 2022/5/26
        $data['orderFieldType'] = $where['order_field_type']; // add by hkk 2022/5/26

        // 查询字段配置 add by hkk 2022/8/10
        $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();

        $data['fieldConfigShowFields'] = explode(',', $fieldConfig['data']['showFields']);

        // 第一次渲染，更新整个页面
        if (!empty($postData['needUpdateAllPage'])) {
            $data['filter'] = [
                'name' => @getVar($where['name'])
            ];
            $file = $this->renderAjax('/setting/instruments_manage.php', $data);
            return $this->success(['contentHtml' => $file]); // modified by hkk 2020/6/28
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/setting/instruments_manage_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes:获取仪器新增，编辑，查看，报修，历史，预约，编辑预约页面
     * Author: hkk
     * Date: 2019/10/31 13:29
     * @return \common\controllers\json
     */
    public function actionGetPage()
    {


        $postData = \Yii::$app->getRequest()->post();
        $type = $postData['type'];
        $data['type'] = $type;

        if ($type !== 'history' && $type !== 'book' && $type !== 'editBook') { //查询该企业下所有用户,用于用户搜索筛选,历史和预约界面不用
            $company_id = \Yii::$app->view->params['curr_company_id'];
            if (!$company_id) {
                return $this->fail(\Yii::t('base', 'select_user'));
            }

            $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id); // 用户所属鹰群

            // 获取人员信息
            $visibleUsers = (new CenterInterface())->getVisibleUsers($this->userinfo->id);
            $data['all_user'] = $visibleUsers;
        }

        switch ($type) {
            case "add": //新增仪器
                $data['defaultResponsibleUserIds'] = [];
                $data['defaultInChargeUserIds'] = [];
                $data['defaultMaintenanceUserIds'] = [];
                $data['defaultResponsibleUserNames'] = [];
                $data['defaultInChargeUserNames'] = [];
                $data['defaultMaintenanceUserNames'] = [];
                // $data['group_list'] = $groupList;
                // $data['default_group_ids'] = array_column($groupList, 'id'); // 默认全选鹰群
                // $data['default_group_names'] = array_column($groupList, 'group_name'); // 默认全选鹰群
                //$data['default_departments'] = []; // add by hkk 2022/6/27 // 默认不选部门
                $data['default_group_ids'] = [];
                $data['default_group_names'] = [];
                $data['default_departments_id'] = [];

                // add by hkk 2021/4/28  获取仪器的分类列表
                $data['typeList'] = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);

                // add by hkk 2021/4/28 去仪器自定义字段表查询自定义字段
                $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
                $data['defineFields'] = $defineRecord ? $defineRecord : [];

                // add by hkk 2022/8/10  查询字段配置
                $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
                $data['fieldConfigShowFields'] = explode(',', $fieldConfig['data']['showFields']);
                $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
                $elnPaidItems = $companyData['data']['elnPaidItems'];
                $data['inscadaNumAll'] = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);

                $instrumentNum = InstrumentsModel::find()->where(['data_type' => 1])->asArray()->all();
                $instrumentFile = InstrumentsModel::find()->where(['data_type' => 2])->asArray()->all();
                $data['inscadaNum'] = count(array_merge($instrumentNum, $instrumentFile));
                break;
            case "batchAdd": //批量添加仪器
                $html = $this->renderAjax('/setting/instrument_batchAdd.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "edit": //查看仪器
            case "view": //编辑仪器
                $instrumentId = $postData['instrumentId'];
                $instrumentDetails = InstrumentsModel::findOne(['id' => $instrumentId])->toArray();

                if ($instrumentDetails['groupIds'] == 'all') { // add by hkk 2022/8/8  兼容不限仪器
                    $instrumentDetails['groupIds'] = '';
                }

                $defaultResponsibleUserIds = array_filter(explode(',', $instrumentDetails['responsible_person']));
                $defaultInChargeUserIds = array_filter(explode(',', $instrumentDetails['in_charge_person']));
                $defaultMaintenanceUserIds = array_filter(explode(',', $instrumentDetails['maintenance_person']));
                $defaultGroupIds = array_filter(explode(',', $instrumentDetails['groupIds'])); // add by hkk 2020/4/23

                $defaultDepartmentIds = array_filter(explode(',', $instrumentDetails['departmentIds'])); // add by hkk 2020/4/23


                //合并去重数组
                $defaultAllUserIds = array_keys(array_flip($defaultResponsibleUserIds) + array_flip($defaultInChargeUserIds) + array_flip($defaultMaintenanceUserIds));

                //获取用户名等详情
                $defaultAllUser = (new CenterInterface())->userDetailsByUserIds($defaultAllUserIds);
                $defaultAllUser = yii\helpers\ArrayHelper::index($defaultAllUser, 'user_id');


                //分别生成责任人，维护人，维修人的用户名字符串
                $defaultResponsibleUserNames = [];
                $defaultInChargeUserNames = [];
                $defaultMaintenanceUserNames = [];
                $defaultGroupNames = []; // add by hkk 2020/4/23
                $selectedDepartments = []; // add by hkk 2022/6/27
                if (!empty($defaultResponsibleUserIds)) {
                    foreach ($defaultResponsibleUserIds as $user) {
                        $currentUser = $defaultAllUser[$user];
                        $defaultResponsibleUserNames[] = $currentUser ? CommentServer::displayUserName($currentUser) : '';
                    }
                }
                if (!empty($defaultInChargeUserIds)) {
                    foreach ($defaultInChargeUserIds as $user) {
                        $currentUser = $defaultAllUser[$user];
                        $defaultInChargeUserNames[] = $currentUser ? CommentServer::displayUserName($currentUser) : '';
                    }
                }
                if (!empty($defaultMaintenanceUserIds)) {
                    foreach ($defaultMaintenanceUserIds as $user) {
                        $currentUser = $defaultAllUser[$user];
                        $defaultMaintenanceUserNames[] = $currentUser ? CommentServer::displayUserName($currentUser) : '';
                    }
                }

                if (!empty($defaultGroupIds)) {
                    $groupsInfo = array_column($groupList, NULL, 'id');
                    foreach ($defaultGroupIds as $groupId) {
                        $defaultGroupNames[] = !empty($groupsInfo[$groupId]) ? $groupsInfo[$groupId]['group_name'] : '';
                    }
                }

                $data['instrumentDetails'] = $instrumentDetails;
                $data['defaultResponsibleUserIds'] = $defaultResponsibleUserIds;
                $data['defaultInChargeUserIds'] = $defaultInChargeUserIds;
                $data['defaultMaintenanceUserIds'] = $defaultMaintenanceUserIds;
                $data['defaultResponsibleUserNames'] = $defaultResponsibleUserNames;
                $data['defaultInChargeUserNames'] = $defaultInChargeUserNames;
                $data['defaultMaintenanceUserNames'] = $defaultMaintenanceUserNames;
                $data['group_list'] = $groupList;
                $data['default_group_ids'] = $defaultGroupIds;
                $data['default_group_names'] = $defaultGroupNames; // 默认全选鹰群
                $data['default_departments_id'] = $defaultDepartmentIds;
                //                $data['default_departments'] = $selectedDepartments; // add by hkk 2022/6/27 部门


                // add by hkk 2021/4/28 去仪器自定义字段表查询自定义字段
                $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
                $data['defineFields'] = $defineRecord ? $defineRecord : [];


                // add by hkk 2022/6/28  增加顶部状态提示
                $data['statusTip'] = '';

                //  1查询仪器维修记录状态
                $repairRecord = InstrumentRepairRecordModel::find()
                    ->where([
                        'instrument_id' => $instrumentId,
                        'if_void' => 0
                    ]) // 维修记录
                    ->andWhere(['>=', 'id', 0])
                    ->select(['id as did', 'repair_start_time', 'repair_end_time', 'review_conclusion'])->asArray()->all();
                $currentTime = strtotime(date("Y-m-d H:i"));
                $repairInfo = array_filter($repairRecord, function ($item) use ($currentTime) {
                    return $currentTime >= strtotime($item['repair_start_time']) && $currentTime <= strtotime($item['repair_end_time']);
                });
                $repairReview = array_filter(array_column($repairRecord, 'review_conclusion'), function ($item) {
                    return $item == '3';
                });
                if (count($repairInfo) > 0 || ($instrumentDetails['status'] == '3')) { // 维修中
                    $data['statusTip'] .= ' ' . \Yii::t('base', 'repairing') . ' ';
                }
                if (count($repairReview) > 0) { // 维修复核中
                    $data['statusTip'] .= ' ' . \Yii::t('base', 'repairing_review') . ' ';
                }


                //  2查询仪器校验记录状态,根据最新一条校验记录判断
                $checkRecord = InstrumentCheckRecordModel::find()
                    ->where([
                        'instrument_id' => $instrumentId,
                        'if_void' => 0
                    ]) // 校验记录
                    ->andWhere(['>=', 'id', 0])
                    ->select(['id as did', 'start_check_time', 'end_check_time', 'review_conclusion'])->asArray()->all();
                $checkInfo = array_filter($checkRecord, function ($item) use ($currentTime) {
                    return $currentTime >= strtotime($item['start_check_time']) && $currentTime <= strtotime($item['end_check_time']);
                });
                $checkReview = array_filter(array_column($checkRecord, 'review_conclusion'), function ($item) {
                    return $item == '3';
                });
                if ((count($checkInfo) > 0)) { // 校验中
                    $data['statusTip'] .= ' ' . \Yii::t('base', 'checking') . ' ';
                }
                if (count($checkReview) > 0) { // 校验复核中
                    $data['statusTip'] .= ' ' . \Yii::t('base', 'checking_review') . ' ';
                }

                //  3 查询仪器运行记录状态
                $operateRecord = InstrumentRunningRecordModel::find()
                    ->where([
                        'instrument_id' => $instrumentId,
                        'if_void' => 0
                    ]) // 3 运行记录
                    ->andWhere(['>=', 'id', 0]) // 3 运行记录
                    ->select(['id as did', 'if_end', 'review_conclusion'])->asArray()->all();
                $runningInfo = array_filter(array_column($operateRecord, 'if_end'), function ($item) {
                    return $item != '1';
                });
                $runningReview = array_filter(array_column($operateRecord, 'review_conclusion'), function ($item) {
                    return $item == '3';
                });

                if (count($runningInfo) > 0) { // 运行中
                    $data['statusTip'] .= ' ' . \Yii::t('base', 'running') . ' ';
                }
                if (count($runningReview) > 0) { // 运行复核中
                    $data['statusTip'] .= ' ' . \Yii::t('base', 'running_review') . ' ';
                }
                if (!empty($data['statusTip'])) {
                    $data['statusTip'] = '(' . $data['statusTip'] . ')';
                }


                // add by hkk 2021/4/28  获取仪器的分类列表
                $data['typeList'] = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);

                // add by hkk 2022/8/10  查询字段配置
                $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
                $data['fieldConfigShowFields'] = explode(',', $fieldConfig['data']['showFields']);

                $data['recordSetting'] = json_decode($instrumentDetails['record_setting'], true);
                $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
                $elnPaidItems = $companyData['data']['elnPaidItems'];
                $data['inscadaNumAll'] = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);

                $instrumentNum = InstrumentsModel::find()->where(['data_type' => 1])->asArray()->all();
                $instrumentFile = InstrumentsModel::find()->where(['data_type' => 2])->asArray()->all();
                $data['inscadaNum'] = count(array_merge($instrumentNum, $instrumentFile));
                break;
            case "repair": // 报修仪器
                $data['id'] = $postData['instrumentId'];
                $data['defaultAllUserIds'] = [];
                $data['defaultAllUserNames'] = [];
                $instrumentDetails = InstrumentsModel::findOne(['id' => $postData['instrumentId']])->toArray();
                $data['name'] = $instrumentDetails['name'];
                $data['batchNumber'] = $instrumentDetails['batch_number'];
                $data['instrumentStatus'] = '';
                $statusApproval = (new InstrumentServer())->instrumentChangeStatusApproval($postData['instrumentId'], 3);
                if ($statusApproval['instrument_status'] != 1) {
                    $userInfoArr = (new CenterInterface())->userDetailsByUserIds($statusApproval['approval_user_ids']);
                    $approvalUser = '';
                    foreach ($userInfoArr as $key => $user) {
                        if ($key != 0) $approvalUser .= ',';
                        $approvalUser .= CommonServer::displayUserName($user);
                    }
                    $data['instrumentStatus'] = $statusApproval['instrument_status'];
                    $data['approvalUser'] = $approvalUser;
                }
                $html = $this->renderAjax('/setting/instrument_repair.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "record_setting": // 运行维修校验记录的设置
                $data['id'] = $postData['instrumentId'];

                // 运行记录复核人员
                $data['defaultOperateRecordCheckUserIds'] = [];
                $data['defaultOperateRecordCheckUserNames'] = [];
                $data['needOperateRecordCheck'] = false;

                // 维修记录复核人员
                $data['defaultRepairRecordCheckUserIds'] = [];
                $data['defaultRepairRecordCheckUserNames'] = [];
                $data['hasRepairRecordCheck'] = false;

                // 校验记录复核人员
                $data['defaultCheckRecordCheckUserIds'] = [];
                $data['defaultCheckRecordCheckUserNames'] = [];
                $data['hasCheckRecordCheck'] = false;

                // 报废需要审核复核人员
                $data['defaultScrapMustCheckUserIds'] = [];
                $data['defaultScrapMustCheckUserNames'] = [];
                $data['needScrapMustCheck'] = false;

                // 停用需要审核复核人员
                $data['defaultSuspendUseMustCheckUserIds'] = [];
                $data['defaultSuspendUseMustCheckUserNames'] = [];
                $data['needSuspendUseMustCheck'] = false;

                // 删除需要审核复核人员
                $data['defaultDeleteMustCheckUserIds'] = [];
                $data['defaultDeleteMustCheckUserNames'] = [];
                $data['needDeleteMustCheck'] = false;

                // 报修需要审核复核人员
                $data['defaultApplyRepairMustCheckUserIds'] = [];
                $data['defaultApplyRepairMustCheckUserNames'] = [];
                $data['needApplyRepairMustCheck'] = false;

                // 记录是否允许时间冲突
                $data['OperateRecordTimeAllowRepeat'] = false;
                $data['RepairRecordTimeAllowRepeat'] = false;
                $data['CheckRecordTimeAllowRepeat'] = false;
                $data['TimeOnlyAfterOperate'] = false; // add by hkk 2022/5/27


                // 获取当前仪器记录的设置，
                $recordSetting = InstrumentsModel::find()->select('record_setting,batch_number,name')->where([
                    'id' => $data['id'],
                ])->asArray()->one();
                $data['name'] = $recordSetting['name']; // add by hkk 2022/6/23
                $data['batchNumber'] = $recordSetting['batch_number']; // add by hkk 2022/6/23

                if (!empty($recordSetting['record_setting'])) {
                    $recordSetting = json_decode($recordSetting['record_setting'], true);
                    if (!empty($recordSetting['operate_check_users'])) {
                        $data['needOperateRecordCheck'] = true;
                        $data['defaultOperateRecordCheckUserIds'] = $recordSetting['operate_check_users'];
                    }
                    if (!empty($recordSetting['repair_check_users'])) {
                        $data['hasRepairRecordCheck'] = true;
                        $data['defaultRepairRecordCheckUserIds'] = $recordSetting['repair_check_users'];
                    }
                    if (!empty($recordSetting['check_check_users'])) {
                        $data['hasCheckRecordCheck'] = true;
                        $data['defaultCheckRecordCheckUserIds'] = $recordSetting['check_check_users'];
                    }
                    if (!empty($recordSetting['scrap_must_check_users'])) {
                        $data['needScrapMustCheck'] = true;
                        $data['defaultScrapMustCheckUserIds'] = $recordSetting['scrap_must_check_users'];
                    }
                    if (!empty($recordSetting['suspend_use_must_check_users'])) {
                        $data['needSuspendUseMustCheck'] = true;
                        $data['defaultSuspendUseMustCheckUserIds'] = $recordSetting['suspend_use_must_check_users'];
                    }
                    if (!empty($recordSetting['delete_must_check_users'])) {
                        $data['needDeleteMustCheck'] = true;
                        $data['defaultDeleteMustCheckUserIds'] = $recordSetting['delete_must_check_users'];
                    }
                    if (!empty($recordSetting['apply_repair_must_check_users'])) {
                        $data['needApplyRepairMustCheck'] = true;
                        $data['defaultApplyRepairMustCheckUserIds'] = $recordSetting['apply_repair_must_check_users'];
                    }

                    if (!empty($recordSetting['operate_repeat']) && $recordSetting['operate_repeat'] == 'true') {
                        $data['OperateRecordTimeAllowRepeat'] = true;
                    }
                    if (!empty($recordSetting['repair_repeat']) && $recordSetting['repair_repeat'] == 'true') {
                        $data['RepairRecordTimeAllowRepeat'] = true;
                    }
                    if (!empty($recordSetting['check_repeat']) && $recordSetting['check_repeat'] == 'true') {
                        $data['CheckRecordTimeAllowRepeat'] = true;
                    }
                    if (!empty($recordSetting['time_operate']) && $recordSetting['time_operate'] == 'true') { // add by hkk 2022/5/27
                        $data['TimeOnlyAfterOperate'] = true;
                    }


                    //合并去重数组,获取用户名等详情
                    $defaultAllUserIds = array_keys(
                        array_flip($data['defaultOperateRecordCheckUserIds'])
                        + array_flip($data['defaultRepairRecordCheckUserIds'])
                        + array_flip($data['defaultCheckRecordCheckUserIds'])
                        + array_flip($data['defaultScrapMustCheckUserIds'])
                        + array_flip($data['defaultSuspendUseMustCheckUserIds'])
                        + array_flip($data['defaultDeleteMustCheckUserIds'])
                        + array_flip($data['defaultApplyRepairMustCheckUserIds']));
                    $defaultAllUser = (new CenterInterface())->userDetailsByUserIds($defaultAllUserIds);
                    $defaultAllUser = yii\helpers\ArrayHelper::index($defaultAllUser, 'user_id');
                    if (!empty($recordSetting['operate_check_users'])) {
                        foreach ($recordSetting['operate_check_users'] as $user) {
                            $data['defaultOperateRecordCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                    if (!empty($recordSetting['repair_check_users'])) {
                        foreach ($recordSetting['repair_check_users'] as $user) {
                            $data['defaultRepairRecordCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                    if (!empty($recordSetting['check_check_users'])) {
                        foreach ($recordSetting['check_check_users'] as $user) {
                            $data['defaultCheckRecordCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                    if (!empty($recordSetting['scrap_must_check_users'])) {
                        foreach ($recordSetting['scrap_must_check_users'] as $user) {
                            $data['defaultScrapMustCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                    if (!empty($recordSetting['suspend_use_must_check_users'])) {
                        foreach ($recordSetting['suspend_use_must_check_users'] as $user) {
                            $data['defaultSuspendUseMustCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                    if (!empty($recordSetting['delete_must_check_users'])) {
                        foreach ($recordSetting['delete_must_check_users'] as $user) {
                            $data['defaultDeleteMustCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                    if (!empty($recordSetting['apply_repair_must_check_users'])) {
                        foreach ($recordSetting['apply_repair_must_check_users'] as $user) {
                            $data['defaultApplyRepairMustCheckUserNames'][] = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        }
                    }
                }


                $html = $this->renderAjax('/setting/instrument_record_setting.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "record_setting_batch": // 批量运行维修校验记录的设置 // add by hkk 2022/5/27

                $data['id'] = '';

                // 运行记录复核人员
                $data['defaultOperateRecordCheckUserIds'] = [];
                $data['defaultOperateRecordCheckUserNames'] = [];
                $data['needOperateRecordCheck'] = false;

                // 维修记录复核人员
                $data['defaultRepairRecordCheckUserIds'] = [];
                $data['defaultRepairRecordCheckUserNames'] = [];
                $data['hasRepairRecordCheck'] = false;

                // 校验记录复核人员
                $data['defaultCheckRecordCheckUserIds'] = [];
                $data['defaultCheckRecordCheckUserNames'] = [];
                $data['hasCheckRecordCheck'] = false;

                // 报废需要审核复核人员
                $data['defaultScrapMustCheckUserIds'] = [];
                $data['defaultScrapMustCheckUserNames'] = [];
                $data['needScrapMustCheck'] = false;

                // 停用需要审核复核人员
                $data['defaultSuspendUseMustCheckUserIds'] = [];
                $data['defaultSuspendUseMustCheckUserNames'] = [];
                $data['needSuspendUseMustCheck'] = false;

                // 删除需要审核复核人员
                $data['defaultDeleteMustCheckUserIds'] = [];
                $data['defaultDeleteMustCheckUserNames'] = [];
                $data['needDeleteMustCheck'] = false;

                // 报修需要审核复核人员
                $data['defaultApplyRepairMustCheckUserIds'] = [];
                $data['defaultApplyRepairMustCheckUserNames'] = [];
                $data['needApplyRepairMustCheck'] = false;

                // 记录是否允许时间冲突
                $data['OperateRecordTimeAllowRepeat'] = false;
                $data['RepairRecordTimeAllowRepeat'] = false;
                $data['CheckRecordTimeAllowRepeat'] = false;
                $data['TimeOnlyAfterOperate'] = false;

                $html = $this->renderAjax('/setting/instrument_record_setting.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "history": // 查看历史
                //获取历史痕迹表格数据
                $instrumentsHistoryData = (new InstrumentServer())->listInstrumentHistory($postData['instrumentId']);
                $data['id'] = $postData['instrumentId'];
                $data['history_list'] = $instrumentsHistoryData;
                $html = $this->renderAjax('/setting/instrument_history.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "book": // 预约页面
                // 获取预约历史数据
                $instrumentsBookData = (new InstrumentServer())->listInstrumentBook($postData['instrumentId']);
                $data['instrumentId'] = $postData['instrumentId'];
                $data['book_list'] = $instrumentsBookData;
                $html = $this->renderAjax('/setting/instrument_book.php', $data);
                return $this->success([
                    'file' => $html
                ]);

                break;
            case "popBook": // 弹框预约页面，材料与仪器编辑器模块
                // 获取预约历史数据
                $instrumentsBookData = (new InstrumentServer())->listInstrumentBook($postData['instrumentId']);
                $data['instrumentId'] = $postData['instrumentId'];
                $data['book_list'] = $instrumentsBookData;
                $html = $this->renderAjax('/setting/instrument_book_pop.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "editBook": // 编辑预约页面
                // 获取当前预约记录的数据
                $currentBookData = InstrumentsBookModel::findOne(['id' => $postData['bookId']])->toArray();
                // 获取仪器预约历史数据
                $instrumentsBookData = (new InstrumentServer())->listInstrumentBook($currentBookData['instrument_id']);
                $data['bookId'] = $postData['bookId'];
                $data['instrumentId'] = $currentBookData['instrument_id'];
                $data['bookData'] = $currentBookData;
                $data['book_list'] = $instrumentsBookData;

                $html = $this->renderAjax('/setting/instrument_book.php', $data);
                return $this->success([
                    'file' => $html
                ]);
                break;
            case "reminder": // 编辑提醒界面

                // 获取该仪器提醒数据
                $settings = [];
                if (!empty($postData['instrumentId'])) {
                    $instrumentReminderData = (new InstrumentServer())->getReminderSetting($postData['instrumentId']);
                    $settingRecord = $instrumentReminderData['data'];
                    if (!empty($settingRecord['setting'])) {
                        $settings = json_decode($settingRecord['setting'], true);
                    }
                }

                $instrumentDetails = InstrumentsModel::findOne(['id' => $postData['instrumentId']])->toArray();
                $data['name'] = $instrumentDetails['name'];
                $data['batchNumber'] = $instrumentDetails['batch_number'];

                // 获取公司所有人员
                $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
                $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');
                $data['user_list'] = $userList;
                $data['settings'] = $settings;
                $data['id'] = $postData['instrumentId'];
                $html = $this->renderAjax('/setting/instrument_reminder_setting.php', $data);

                return $this->success([
                    'file' => $html
                ]);

                break;
            case "check": // 校验弹框界面

                // 获取该仪器校验数据
                $instrumentId = $postData['instrumentId'];
                $instrumentDetails = InstrumentsModel::findOne(['id' => $instrumentId])->toArray();
                $data['id'] = $instrumentId;
                $data['instrumentDetails'] = $instrumentDetails;

                $instrumentCheckList = (new InstrumentServer())->listInstrumentCheck($instrumentId);
                $data['instrumentCheckList'] = $instrumentCheckList;


                $html = $this->renderAjax('/setting/instrument_check.php', $data);
                return $this->success([
                    'file' => $html
                ]);

                break;
            case "addCheck": // 校验弹框里的新增校验页面

                // 获取该仪器校验数据
                $setting = [];
                $instrumentId = $postData['instrumentId'];
                $instrumentDetails = InstrumentsModel::findOne(['id' => $instrumentId])->toArray();

                // 获取公司所有人员
                // $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
                //  $userList = yii\helpers\ArrayHelper::index($data['all_user'], 'id');
                $data['user_list'] = yii\helpers\ArrayHelper::index($data['all_user'], 'id');
                $data['id'] = $postData['instrumentId'];
                $data['instrumentDetails'] = $instrumentDetails;
                $data['setting'] = $setting;

                $html = $this->renderAjax('/setting/instrument_check_add.php', $data);
                return $this->success([
                    'file' => $html
                ]);

                break;
            default:
                break;
        }


        $html = $this->renderAjax('/setting/instrument_page.php', $data);
        return $this->success([
            'file' => $html
        ]);
    }

    public function actionGetInfoById()
    {
        $instrumentId = \Yii::$app->request->post('instrument_id', '');
        if (empty($instrumentId)) {
            return $this->fail('');
        }

        $where = ['instrument_id' => $instrumentId];
        $instrumentsData = (new InstrumentServer())->listInstrumentsView($where, 1, 1);
        $instrumentsList = @getVar($instrumentsData['instruments_list'], []);
        $instrumentsList = array_column($instrumentsList, null, 'id');
        $instrument = @getVar($instrumentsList[$instrumentId], []);
        if (empty($instrument)) {
            return $this->fail('');
        }

        $defineFields = @getVar($instrumentsData['defineFields'], []);

        //所有鹰群和部门
        $allGroups = (new CenterInterface())->getGroupsListByCompanyId();
        $allDepartments = (new CenterInterface())->getDepartmentListByCompanyId(1)['list'];

        // 显示字段
        $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
        $fieldConfigShowFields = explode(',', $fieldConfig['data']['showFields']);
        $instrumentInfo = [];
        foreach ($fieldConfigShowFields as $fieldKey) {
            $field = '';
            $val = '';
            switch ($fieldKey) {
                case 'picture':
                    $field = Yii::t('base', 'picture');
                    $val = '';
                    break;
                case 'name':
                    $field = Yii::t('base', 'name');
                    $val = $instrument['name'];
                    break;
                case 'batch_number':
                    $field = Yii::t('base', 'batch_number');
                    $val = $instrument['batch_number'];
                    break;
                case 'specification':
                    $field = Yii::t('base', 'specification');
                    $val = $instrument['specification'];
                    break;
                case 'instrument_type':
                    $field = Yii::t('base', 'instrument_type');
                    $val = !empty($instrument['instrument_type']) ? $instrument['instrument_type'] : '';
                    break;
                case 'model':
                    $field = Yii::t('base', 'model');
                    $val = $instrument['model'];
                    break;
                case 'manufacturer':
                    $field = Yii::t('base', 'manufacturer');
                    $val = $instrument['manufacturer'];
                    break;
                case 'supplier':
                    $field = Yii::t('base', 'supplier');
                    $val = $instrument['supplier'];
                    break;
                case 'position':
                    $field = Yii::t('base', 'position');
                    $val = $instrument['position'];
                    break;
                case 'status':
                    $status = '';
                    if ($instrument['status'] == "1") {
                        $status = Yii::t('base', 'normal');
                    } else if ($instrument['status'] == "0") {
                        $status = Yii::t('base', 'already_deleted');
                    } else if ($instrument['status'] == "2") {
                        $status = Yii::t('base', 'suspend_use');
                    } else if ($instrument['status'] == "3") {
                        $status = Yii::t('base', 'repairing');
                    } else if ($instrument['status'] == "4") {
                        $status = Yii::t('base', 'scrap');
                    }
                    $field = Yii::t('base', 'status');
                    $val = $status;
                    break;
                case 'check_situation':
                    $situation = '';
                    if ($instrument['check_situation'] == "0") {
                        $situation = Yii::t('base', 'noNeedCheck');
                    } else if ($instrument['check_situation'] == "1") { // 加上有效期判断 expiry_date
                        $startTime = strtotime($instrument['start_expiry_time']);
                        $currentTime = strtotime(date("Y-m-d H:i"));
                        $endTime = strtotime($instrument['end_expiry_time']);
                        if ($startTime <= $currentTime && $endTime >= $currentTime) {
                            $situation = Yii::t('base', 'checked');
                        } else {
                            $situation = Yii::t('base', 'unchecked');
                        }
                    } else {
                        $situation = Yii::t('base', 'unchecked');
                    }
                    $field = Yii::t('base', 'check_situation');
                    $val = $situation;
                    break;
                case 'data_type':
                    $dataType = '';
                    if (isset($instrument['data_type'])) {
                        if ($instrument['data_type'] == "0") {
                            $dataType = Yii::t('base', 'none');
                        } else if ($instrument['data_type'] == "1") {
                            $dataType = Yii::t('base', 'value');
                        } else if ($instrument['data_type'] == "2") {
                            $dataType = Yii::t('base', 'file');
                        }
                    } else {
                        $instrument['data_type'] = 0;
                        $dataType = Yii::t('base', 'none');
                    }

                    $field = Yii::t('base', 'data_type');
                    $val = $dataType;
                    break;
                case 'create_by':
                    $field = Yii::t('base', 'create_person');
                    $val = $instrument['create_name'];
                    break;
                case 'create_time':
                    $field = Yii::t('base', 'create_time');
                    $val = $instrument['create_time'];
                    break;
                case 'responsible_person':
                    $field = Yii::t('base', 'response_person_1');
                    $val = $instrument['responsible_person_string'];
                    break;
                case 'in_charge_person':
                    $field = Yii::t('base', 'person_in_charge');
                    $val = $instrument['in_charge_person_string'];
                    break;
                case 'maintenance_person':
                    $field = Yii::t('base', 'maintainer');
                    $val = $instrument['maintenance_person_string'];
                    break;
                case 'remark':
                    $field = Yii::t('base', 'remark');
                    $val = $instrument['remark'];
                    break;
                case 'groupIds':
                    $groupStr = '';
                    if ($instrument['groupIds'] != 'all') {
                        $groupIds = explode(',', $instrument['groupIds']);
                        $groupNames = array_map(function ($groupId) use ($allGroups) {
                            return @getVar($allGroups[$groupId]['group_name']);
                        }, $groupIds);
                        $groupStr = join(',', $groupNames);
                    }

                    $field = Yii::t('base', 'group_search');
                    $val = $groupStr;
                    break;
                case 'departmentIds':
                    $departmentIds = explode(',', $instrument['departmentIds']);
                    $departmentNames = array_map(function ($departmentId) use ($allDepartments) {
                        return @getVar($allDepartments[$departmentId]['department_name']);
                    }, $departmentIds);
                    $departmentStr = join(',', $departmentNames);

                    $field = Yii::t('base', 'belong_department');
                    $val = $departmentStr;
                    break;

            }

            $instrumentInfo[] = [
                'field_key' => $fieldKey,
                'field_name' => $field,
                'val' => $val,
            ];
        }

        // 自定义字段
        for ($i = 1; $i <= 20; $i++) {
            $fieldKey = 'field' . $i;
            $valKey = 'data' . $i;
            $field = !@emptyExclude0($defineFields[$fieldKey]) ? $defineFields[$fieldKey] : '';
            $val = !@emptyExclude0($instrument[$valKey]) ? $instrument[$valKey] : '';
            if (empty($field) || empty($val)) {
                continue;
            }

            if (in_array($fieldKey, $fieldConfigShowFields)) {
                $instrumentInfo[] = [
                    'field_key' => $fieldKey,
                    'field_name' => $field,
                    'val' => $val,
                ];
            }
        }

        // 管理字段
        $manageFields = [
            'start_check_time' => Yii::t('base', 'check_start_time'),
            'end_check_time' => Yii::t('base', 'check_end_time'),
            'start_expiry_time' => Yii::t('base', 'expiry_data_start_time'),
            'end_expiry_time' => Yii::t('base', 'expiry_data_end_time'),
            'start_running_time' => Yii::t('base', 'start_running_time'),
            'end_running_time' => Yii::t('base', 'end_running_time'),
            'repair_start_time' => Yii::t('base', 'repair_start_time'),
            'repair_end_time' => Yii::t('base', 'repair_end_time'),
        ];
        foreach ($manageFields as $fieldKey => $field) {
            $val = @getVar($instrument[$fieldKey]);
            $instrumentInfo[] = [
                'field_key' => $fieldKey,
                'field_name' => $field,
                'val' => $val,
            ];
        }

        // 名称 -> 值
        $filed2val = array_column($instrumentInfo, 'val', 'field_name');

        return $this->success(['filed2val' => $filed2val]);
    }

    /**
     * @Notes: 根据条件精确查找仪器
     * @return \common\controllers\json
     * @author: tianyang
     * @Time: 2023/6/8 19:37
     */
    public function actionGetInstrumentId()
    {
        $batch_number = Yii::$app->getRequest()->post('batch_number', '');
        $findOne = null;
        if (!empty($batch_number)) {
            $findOne = (new InstrumentServer())->getExactInstrument(['batch_number' => $batch_number]);
        }
        if (empty($findOne)) {
            return $this->fail('');
        }
        return $this->success(['instrument_id' => $findOne['id']]);
    }

    /**
     * Notes: 获取仪器库的弹窗页面，用于材料与仪器模块调用我的仪器库
     * Author: hkk
     * Date: 2019/11/6 10:02
     * @return \common\controllers\json
     */
    public function actionGetPopInstrumentsPage()
    {

        $postData = \Yii::$app->getRequest()->post();

        // 获取查询条件
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', 5);
        $where['name'] = \Yii::$app->request->post('instrument_name', '');
        $where['status'] = \Yii::$app->request->post('instrument_status', '1');
        $where['check_situation'] = \Yii::$app->request->post('instrument_check') ?: ['0', '1'];


        // 可见范围筛选，同我的仪器库，只显示所属鹰群或部门的仪器
        $where['viewAuth'] = '2';
//        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
//        $where['groups'] = array_column($visibleGroups, 'id');

//        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
//        $where['departments'] = array_column($visibleDepartments, 'id');

        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;
        //bug 6242 插入仪器按照权限来
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        }

        // 获取查询的数据
        $instrumentsData = (new InstrumentServer())->listInstrumentsView($where, $limit, $page);
        $data['instrumentsList'] = $instrumentsData['instruments_list'];
        $data['totalCount'] = $instrumentsData['totalCount'];
        $data['limit'] = $limit;

        //所有鹰群和部门
        $allGroups = (new CenterInterface())->getGroupsListByCompanyId();
        $allDepartments = (new CenterInterface())->getDepartmentListByCompanyId(1)['list'];
        $data['allGroups'] = $allGroups;
        $data['allDepartments'] = $allDepartments;


        $data['type'] = $postData['type']; // add by hkk 2019/11/1 判断是仪器库管理还是我的仪器库页面
        $data['editorKey'] = \Yii::$app->request->post('editorKey', '');; // add by hkk 2019/11/7 从editor过来会保存key
        $data['search_name'] = $where['name'] ?: '';
        $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
        $data['fieldConfigShowFields'] = explode(',', $fieldConfig['data']['showFields']);
        //! 获取自定义字段名称及其数据库字段的映射
        $data['fieldConfig_fieldMap'] = $fieldConfig['data']['fieldMap'];

        if (!empty($postData['needUpdateAllPage'])) {
            $file = $this->renderAjax('/setting/instruments_pop.php', $data);
            return $this->success(['file' => $file]);
        }
        $file = $this->renderAjax('/setting/instruments_pop_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes:提交仪器详情页
     * Author: hkk
     * Date: 2019/10/31 14:54
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionSubmitPage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $type = $postData['type'];
        $instrumentData = $postData['instrumentData'];
        switch ($type) {
            case "add": //新增仪器
                $instrumentData['create_by'] = $this->userinfo->id;
                $instrumentsManageRead = \Yii::$app->view->params['instruments_manage_read'];
                $instrumentsManageReadMy = \Yii::$app->view->params['instruments_manage_read_my'];
                if ($instrumentData['ispopup'] == 0 && !$instrumentsManageRead) {
                    $departmentArr = array_column($this->userinfo->department, 'id');
                    $groupsArr = array_column($this->userinfo->groups, 'id');
                    $instrumentGroupIds = $instrumentData['groupIds'] == '' ? [] : explode(',', $instrumentData['groupIds']);
                    $instrumentDepartmentIds = $instrumentData['departmentIds'] == '' ? [] : explode(',', $instrumentData['departmentIds']);

                    if (!empty($instrumentDepartmentIds) || !empty($instrumentGroupIds)) {
                        if (!empty(array_diff($instrumentDepartmentIds, $departmentArr)) || !empty(array_diff($instrumentGroupIds, $groupsArr))) {
                            $data['instrument_id'] = $instrumentData['id'];
                            $data['type'] = $type;
                            $file = $this->renderAjax('/popup/instrument_edit_tip.php', $data);
                            return $this->success(['file' => $file, 'isPopup' => 1]);
                        }
                    }
                }
                $result = (new InstrumentServer())->addInstrument($instrumentData);
                if (!$result['status']) {
                    //bug#33372,配置了翻译的会按翻译显示,没有配置翻译的会显示原文,因此不对保存model的错误消息进行处理. mod dx
                    $failInfo = \Yii::t('base', $result["info"]);
                    return $this->fail($failInfo);
                }
                // if (isset($result['errMsg']) && $result['errMsg']) {
                //     $failInfo = \Yii::t('base', $result['errMsg']);
                //     return  $this->fail($failInfo);
                // }
                break;
            case "edit": //编辑仪器
                $instrumentServer = new InstrumentServer();
                $instrumentInfo = $instrumentServer->getInsInfoById($instrumentData['id']);
                $instrumentData['update_by'] = $this->userinfo->id;
                $statusApproval = $instrumentServer->instrumentChangeStatusApproval($instrumentData['id'], $instrumentData['status']);
                $instrumentsManageRead = \Yii::$app->view->params['instruments_manage_read'];
                $instrumentsManageReadMy = \Yii::$app->view->params['instruments_manage_read_my'];

                if ($statusApproval['instrument_status'] != 1 && $instrumentInfo['data']['status'] != $instrumentData['status']) {
                    $userInfoArr = (new CenterInterface())->userDetailsByUserIds($statusApproval['approval_user_ids']);
                    $approvalUser = '';
                    foreach ($userInfoArr as $key => $user) {
                        if ($key != 0) $approvalUser .= ',';
                        $approvalUser .= CommonServer::displayUserName($user);
                    }
                    $data['instrumentStatus'] = $statusApproval['instrument_status'];
                    $data['approvalUser'] = $approvalUser;
                    $data['instrumentId'] = $instrumentData['id'];
                    $file = $this->renderAjax('/popup/instrument_status_approval.php', $data);
                    return $this->success(['file' => $file, 'needApproval' => 1]);
                }
                if ($instrumentData['ispopup'] == 0 && !$instrumentsManageRead) {
                    $departmentArr = array_column($this->userinfo->department, 'id');
                    $groupsArr = array_column($this->userinfo->groups, 'id');
                    $instrumentGroupIds = empty($instrumentData['groupIds']) ? [] : explode(',', $instrumentData['groupIds']);
                    $instrumentDepartmentIds = empty($instrumentData['departmentIds'])  ? [] : explode(',', $instrumentData['departmentIds']);

                    if (!empty($instrumentDepartmentIds) || !empty($instrumentGroupIds)) {
                        if (!empty(array_diff($instrumentDepartmentIds, $departmentArr)) || !empty(array_diff($instrumentGroupIds, $groupsArr))) {
                            $data['instrument_id'] = $instrumentData['id'];
                            $data['type'] = $type;
                            $file = $this->renderAjax('/popup/instrument_edit_tip.php', $data);
                            return $this->success(['file' => $file, 'isPopup' => 1]);
                        }
                    }
                }
                $result = $instrumentServer->editInstrument($instrumentData);
                if (isset($result['errMsg']) && $result['errMsg'] == 'batch_number_conflict') {
                    $failInfo = \Yii::t('base', 'batch_number_conflict');
                    return $this->fail($failInfo);
                } elseif (isset($result['errMsg']) && $result['errMsg'] == 'match_instrument_failed') {
                    $failInfo = \Yii::t('base', 'match_instrument_failed');
                    return $this->fail($failInfo);
                }
                break;
            case "repair": // 报修仪器
                $instrumentData['update_by'] = $this->userinfo->id;
                (new InstrumentServer())->repairInstrument($instrumentData);
                break;
            case "reminder": // 仪器提醒 // add by hkk 2020/4/24
                (new InstrumentServer())->saveReminderSetting($instrumentData);
                break;
            case "record_setting": // 仪器记录校验设置和时间设置

                (new InstrumentServer())->saveRecordSetting($instrumentData);
                break;
            case "record_setting_batch": // 批量仪器记录校验设置和时间设置 // add by hkk 2022/5/27
                // bug#10977, 批量设置仪器操作添加痕迹
                $instrumentIdList = $instrumentData['ids'];
                $instrumentServer = (new InstrumentServer());
                array_walk($instrumentIdList, function ($instrumentId) use ($instrumentServer, $instrumentData) {
                    $singleInstrumentData = array_merge(['id' => $instrumentId], $instrumentData);
                    $instrumentServer->saveRecordSetting($singleInstrumentData);
                });

                // InstrumentsModel::updateAll(['record_setting' => json_encode($instrumentData['setting'])], [
                //     'id' => $instrumentData['ids']
                // ]);

                break;
            case "book": // 预约页面
                $instrumentData['create_by'] = $this->userinfo->id;
                $result = (new InstrumentServer())->bookInstrumentConflict($instrumentData); //  判断是否与历史预约冲突，有冲突返回冲突的记录
                if (isset($result['status']) && $result['status']) {
                    (new InstrumentServer())->bookInstrument($instrumentData); // 更新预约表和历史痕迹表
                } else {
                    $failInfo = \Yii::t('base', 'conflict_time_tip') . $result['conflict_start_time'] . '-' . $result['conflict_end_time'];
                    return $this->fail($failInfo);
                }
                break;
            case "editBook": // 编辑预约页面
                $instrumentData['create_by'] = $this->userinfo->id;
                $result = (new InstrumentServer())->bookInstrumentConflict($instrumentData); //  判断是否与历史预约冲突，有冲突返回冲突的记录
                if (isset($result['status']) && $result['status']) {
                    $instrumentData['update_by'] = $this->userinfo->id;
                    (new InstrumentServer())->editBookInstrument($instrumentData); // 编辑预约表和历史痕迹表
                } else {
                    $failInfo = \Yii::t('base', 'conflict_time_tip') . $result['conflict_start_time'] . '-' . $result['conflict_end_time'];
                    return $this->fail($failInfo);
                }
                break;
            case "addCheck": // 新增校验 // add by hkk 2020/4/27
                //yii\helpers\VarDumper::dump($instrumentData);DIE;
                $instrumentData['create_by'] = $this->userinfo->id; // 当前填写校验人员
                (new InstrumentServer())->saveCheckInfo($instrumentData);
                break;
            default:
                break;
        }


        return $this->success([]);
    }


    /**
     * Notes:删除仪器
     * Author: hkk
     * Date: 2019/10/31 14:55
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionDelete()
    {

        $postData = \Yii::$app->getRequest()->post();
        $instrumentIds = $postData['instrumentIds'];
        $instrumentInfo = (new InstrumentServer())->getInsInfoById($instrumentIds);
        $instrumentInfoData = $instrumentInfo['data'];
        $instrumentRecordSetting = json_decode($instrumentInfoData['record_setting'], true);
        $deleteApproval = [];
        $deleteApproval = @getVar($instrumentRecordSetting['delete_must_check_users'], '');
        $data['approvalUser'] = '';
        if ($deleteApproval) {
            $data['approvalUserId'] = $deleteApproval;
            $userInfoArr = (new CenterInterface())->userDetailsByUserIds($deleteApproval);
            $approvalUser = '';
            foreach ($userInfoArr as $key => $user) {
                if ($key != 0) $approvalUser .= ',';
                $approvalUser .= CommonServer::displayUserName($user);
            }
            $data['approvalUser'] = $approvalUser;
        }
        $data['instrumentName'] = $instrumentInfoData['name'];
        $data['instrumentBatchNumber'] = $instrumentInfoData['batch_number'];
        $data['instrumentId'] = $postData['instrumentIds'];

        $file = $this->renderAjax('/popup/instrument_delete.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes:删除仪器
     * Author: hkk
     * Date: 2019/10/31 14:55
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionDeleteSubmit()
    {
        $postData = \Yii::$app->getRequest()->post();
        $approvalReason = \Yii::$app->request->post('instrument_status_change_reason', '');
        $instrumentIds = $postData['instrumentIds'];
        $Instrument = new InstrumentServer();
        if (!is_array($instrumentIds)) {
            $instrumentIds = [$instrumentIds];
        }
        foreach ($instrumentIds as $id) {
            $Instrument->deleteInstrument($id, $approvalReason);
        }


        return $this->success([]);
    }

    /**
     * Notes: 通过仪器ID获取相关仪器预约内容
     * Author: zsm
     * Date: 2025/5/16 14:00
     * @return \common\controllers\json
     */
    public function actionGetBookById()
    {
        $instrument_id = \Yii::$app->request->post('id', '');
        $day = \Yii::$app->request->post('day', '');

        // 获取查询的数据
        $instrumentsData = (new InstrumentServer())->listBookById($instrument_id, $day);

        $userIdList = array_unique(array_column($instrumentsData['book_list'], 'create_by'));
        $usersRes = (new CenterInterface())->userDetailsByUserIds($userIdList);
        $userDetailArr = yii\helpers\ArrayHelper::index($usersRes, 'id');

        foreach ($instrumentsData['book_list'] as &$item) {

            if (is_array($item)) {
                // 获取 create_by 的值
                $createBy = $item['create_by'];  // 使用对象的属性
                $user = @getVar($userDetailArr[ $createBy ], null);
                // 检查用户是否存在，并将 user_name 插入
                $item['user_name'] = $user ? CommentServer::displayUserName($user): '';
            }

        }

        return $this->success($instrumentsData);

    }
    /**
     * Notes: 模糊搜索根据仪器名称获取仪器信息
     * Author: zsm
     * Date: 2025/5/22 17:00
     * @return \common\controllers\json
     */
    public function actionGetInstrumentByName() {
        $name = \Yii::$app->request->post('name', '');
        $result = (new InstrumentServer())->getInstrumentsByName($name);
        return $this->success(['instruments' => $result['data']]);
    }


    /**
     * Notes: 我的预约界面(我的仪器库)
     * Author: hkk
     * Date: 2019/11/4 16:14
     * @return \common\controllers\json
     */
    public function actionMyBook()
    {

        $postData = \Yii::$app->getRequest()->post();

        // 获取查询条件

        $create_by = $this->userinfo->id; // 之查询我自己的预约

        // 获取查询的数据
        $bookingData = (new InstrumentServer())->listMyBook($create_by);

        $timezone = new \DateTimeZone('Asia/Shanghai');
        $currentTime = new \DateTime('now', $timezone);

        foreach ($bookingData['my_book_list'] as &$booking) {
            $startTime = new \DateTime($booking['start_time'], $timezone);
            $endTime = new \DateTime($booking['end_time'], $timezone);

            // 格式化为 'Y-m-d H:i:s'，确保比较时精度一致
            $formattedStartTime = $startTime->format('Y-m-d H:i:s');
            $formattedEndTime = $endTime->format('Y-m-d H:i:s');
            $formattedCurrentTime = $currentTime->format('Y-m-d H:i:s');

            if ($booking['status'] == 0) {
                $booking['status'] = 4;
                continue;
            }

            if ($formattedEndTime < $formattedCurrentTime) {
                $booking['status'] = 3;
                continue;
            }

            if ($formattedStartTime > $formattedCurrentTime) {
                $booking['status'] = 1;
                continue;
            }

            if ($formattedStartTime <= $formattedCurrentTime && $formattedEndTime >= $formattedCurrentTime) {
                $booking['status'] = 2;
                continue;
            }
        }

        $data['my_book_list'] = $bookingData['my_book_list'];
        $data['totalCount'] = $bookingData['totalCount'];

        $file = $this->renderAjax('/setting/instruments_book_mine.php', $data);


        // 获取用户信息数据
        $allUsers = array_unique(array_merge(
            array_column($bookingData['my_book_list'], 'update_by'),
            array_column($bookingData['my_book_list'], 'create_by')
        ));

        $data['extra'] = (new CenterInterface())->userDetailsByUserIds($allUsers);

        return $this->success(['contentHtml' => $file, 'data' => $data]);

//        // 第一次渲染，更新整个页面
//        if (!empty($postData['needUpdateAllPage'])) {
//            $file = $this->renderAjax('/setting/instruments_book_mine.php', $data);
//            return $this->success(['contentHtml' => $file]);
//        }

//        // 传page参数只更新表格数据
//        $file = $this->renderAjax('/setting/instruments_book_mine_page.php', $data);
//        return $this->success(['file' => $file]);
    }

    /**
     * Notes:删除我的预约
     * Author: hkk
     * Date: 2019/11/5 13:44
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionDeleteBook()
    {

        $postData = \Yii::$app->getRequest()->post();
        $bookIds = $postData['bookIds'];
        (new InstrumentServer())->deleteInstrumentBook($bookIds);
        return $this->success([]);
    }

    /**
     * Notes:下载添加仪器的模板 2021/4/29 修改，增加自定义字段和类型 (2022/5/30作废)
     * Author: hkk
     * Date: 2019/11/11 10:45
     */
    public function actionDownloadInstrumentsTemplate()
    {


        // 查询自定义字段和仪器分类
        $typeString = '';
        $defineField = [];
        $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
        $defineFields = $defineRecord ? $defineRecord : [];
        $type_list = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);
        for ($i = 1; $i <= 20; $i++) {
            if (isset($defineFields['field' . $i]) && !emptyExclude0($defineFields['field' . $i])) {
                $defineField[] = $defineFields['field' . $i];
            }
        }
        foreach ($type_list as $type) {
            $typeString .= ($type['dict_value'] . ' ');
        }
        $lang = \Yii::$app->language;
        if ($lang == 'en-US') {
            $keyResult = [
                'Instrument name(Required)',
                'Device ID',
                'Specification',
                'Instrument type(' . trim($typeString) . ')',
                'Serial Number',
                'Manufacturer',
                'Supplier',
                'Status(normal,suspend use,scrap,repairing,deleted)',
                'Check(No need for validation, need validation)',
                'Location',
                'Group(input group group name,separated by semicolons if many, default blank represents all groups)',
                'Response person(input user name)',
                'In Charge Person(input user name)',
                'Maintainer(input user name)',
                'Notes',
            ];
        } else {
            $keyResult = [
                '仪器名称(必填)',
                '设备ID',
                '规格',
                '类别(' . trim($typeString) . ')',
                '出厂编号',
                '制造商',
                '供应商',
                '状态(正常 停用 报废 维修中 已删除)',
                '校验(无需校验 需要校验)',
                '位置',
                '所属鹰群(输入鹰群名称，多个英文分号隔开，默认空白代表所有鹰群)',
                '责任人(输入用户名)',
                '维护人(输入用户名)',
                '维修人(输入用户名)',
                '备注',
            ];
        }
        $keyResult = array_merge($keyResult, $defineField); // 拼接自定义字段


        // 新建excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 写入标题字段
        foreach ($keyResult as $key => $title) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key) . '1', $title); // 设置内容栏
        }

        // 保存excel文件
        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $filePath = \Yii::getAlias('@filepath') . DS . 'batch_add_instruments' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'batch_add_instruments' . '.xlsx';
            $objWriter->save($tmpFile);
            $filePath = $encryption->encryptFileApi($filePath, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($filePath);
            $filePath = $encryption->encryptFileApi($filePath); // 调用加密接口加密
        }

        //以只读和二进制模式打开文件
        $file = fopen($filePath, "rb");
        //告诉浏览器这是一个文件流格式的文件
        Header("Content-type: application/octet-stream");
        //请求范围的度量单位
        Header("Accept-Ranges: bytes");
        //Content-Length是指定包含于请求或响应中数据的字节长度
        Header("Accept-Length: " . filesize($filePath));
        //用来告诉浏览器，文件是可以当做附件被下载，下载后的文件名称为$file_name该变量的值。
        Header("Content-Disposition: attachment; filename=" . 'batch_add_instruments.xlsx');
        //读取文件内容并直接输出到浏览器
        echo fread($file, filesize($filePath));
        fclose($file);
        exit();
    }

    /**
     * Notes: excel批量添加仪器(2022/5/30作废)
     * Author: hkk
     * Date: 2019/11/11 14:32
     * @return \common\controllers\json
     * @throws \PHPExcel_Reader_Exception
     * @throws yii\db\Exception
     * @deprecated 已作废,迁移至actionSubmitBatchAdd 后续再行删除 mod dx
     */
    public function actionSubmitBatchAddOld()
    {

        $data = \Yii::$app->request->post();

        $filename = \Yii::getAlias('@filepath') . DS . $data['dep_path'] . DS . $data['save_name'];

        $arr = pathinfo($filename);
        $ext = $arr['extension'];

        //需要手工加载这个类
        require_once dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'Excel' . DIRECTORY_SEPARATOR . 'PHPExcel.php';


        // 检验和状态数组
        $checkArr = [
            '无需校验' => 0,
            '需要校验' => 2, // 即未校验，需要手动校验
            'no need for validation' => 0, // 即未校验，需要手动校验
            'need validation' => 2, // 即未校验，需要手动校验
        ];
        $statusArr = [
            '正常' => 1,
            '停用' => 2,
            '报废' => 4,
            '维修中' => 3,
            '已删除' => 0,
            'normal' => 1,
            'suspend use' => 2,
            'scrap' => 4,
            'repairing' => 3,
            'deleted' => 0,
        ];
        $company_id = \Yii::$app->view->params['curr_company_id'];

        // 获取所有鹰群及鹰群id
        $userList = [];
        $allGroupIdsString = '';
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id); // 用户所属鹰群
        foreach ($groupList as $group) {
            $allGroupIdsString .= (',' . $group['id']);
        }
        $allGroupIdsString = substr($allGroupIdsString, 1);
        $groupList = array_column($groupList, 'id', 'group_name'); // 鹰群名数组

        // 获取分类信息
        $typeList = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);
        $typeList = array_column($typeList, 'dict_value');

        //读取excel文件
        $failInfo = [];
        $successNumber = 0;
        if (in_array($ext, array('xls', 'xlsx'))) {

            $fileType = \PHPExcel_IOFactory::identify($filename); //文件名自动判断文件类型
            $excelReader = \PHPExcel_IOFactory::createReader($fileType);

            $phpexcel = $excelReader->load($filename)->getSheet(0); //载入文件并获取第一个sheet
            $total_line = $phpexcel->getHighestRow(); //总行数 1-10-...
            $total_column = $phpexcel->getHighestColumn(); //总列数 A-I-..
            ++$total_column;

            if ($total_line > 1) {

                // 根据第一行标题判断是否是需要的批量添加仪器表
                $data1 = array(); // 标题列
                $row1 = 1;
                for ($column = 'A'; $column != $total_column; $column++) { //遍历列
                    $data1[] = trim($phpexcel->getCell($column . $row1)->getValue());
                }
                if (
                    $data1[0] != "仪器名称(必填)" && $data1[0] != "Instrument name(Required)" ||
                    $data1[1] != "设备ID" && $data1[1] != "Device ID" ||
                    $data1[2] != "规格" && $data1[2] != "Specification" ||
                    $data1[9] != "位置" && $data1[9] != "Location" ||
                    $data1[14] != "备注" && $data1[14] != "Notes"
                ) {
                    return $this->fail(\Yii::t('base', 'excel_wrong_format'));
                }


                for ($row = 2; $row <= $total_line; $row++) { // 遍历行 具体的仪器数据
                    $data = array();
                    for ($column = 'A'; $column != $total_column; $column++) { //遍历列
                        $data[] = trim($phpexcel->getCell($column . $row)->getValue());
                    }

                    if (!empty($data[0])) {

                        // 状态
                        if (!empty($data[7])) {
                            if (array_key_exists($data[7], $statusArr)) {
                                $status_set = $statusArr[$data[7]];
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'status') . \Yii::t('base', 'batch_import_tip');
                                continue;
                            }
                        } else {
                            $status_set = 1;
                        }

                        // 是否受检
                        if (!empty($data[8])) {
                            if (array_key_exists($data[8], $checkArr)) {
                                $check_situation_set = $checkArr[$data[8]];
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'check') . \Yii::t('base', 'batch_import_tip');
                                continue;
                            }
                        } else {
                            $check_situation_set = 2;
                        }

                        $instrumentData = [
                            'name' => $data[0],
                            'batch_number' => $data[1],
                            'specification' => $data[2],
                            'instrument_type' => in_array($data[3], $typeList) ? $data[3] : '', // 仪器类型
                            'model' => $data[4], // 货号型号 出厂编号
                            'manufacturer' => $data[5], // 制造商
                            'supplier' => $data[6], // 供应商
                            'status' => $status_set, // 状态
                            'check_situation' => $check_situation_set, // 检查
                            'position' => $data[9], // 位置
                            'remark' => $data[14], // 备注
                            'create_by' => $this->userinfo->id,
                        ];

                        // 鹰群字段
                        if (!empty($data[10])) {
                            $groupIdsString = '';
                            $groupArr = explode(';', $data[10]);
                            foreach ($groupArr as $groupName) {
                                if (!empty($groupList[$groupName])) {
                                    $groupIdsString .= (',' . $groupList[$groupName]);
                                }
                            }
                            if (!empty($groupIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['groupIds'] = substr($groupIdsString, 1);
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'group') . \Yii::t('base', 'batch_import_tip'); // add by hkk 2021/7/27
                                continue;
                            }
                        } else { // 默认所有鹰群
                            $instrumentData['groupIds'] = $allGroupIdsString;
                        }

                        // 查询用户名数组,只查一次
                        if (empty($userList) && (!empty($data[11]) || !empty($data[12]) || !empty($data[13]))) {
                            $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
                            $userList = isset($userList['list']) ? $userList['list'] : [];
                            $userList = array_column($userList, 'id', 'name'); //  用户名数组
                        }

                        // 责任人
                        if (!empty($data[11])) {
                            $userIdsString = '';
                            $userArr = explode(';', $data[11]);
                            foreach ($userArr as $userName) {
                                if (!empty($userList[$userName])) {
                                    $userIdsString .= (',' . $userList[$userName]);
                                }
                            }
                            if (!empty($userIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['responsible_person'] = substr($userIdsString, 1);
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'response_person') . \Yii::t('base', 'batch_import_tip'); // add by hkk 2021/7/27
                                continue;
                            }
                        }

                        // 维护人
                        if (!empty($data[12])) {
                            $userIdsString = '';
                            $userArr = explode(';', $data[12]);
                            foreach ($userArr as $userName) {
                                if (!empty($userList[$userName])) {
                                    $userIdsString .= (',' . $userList[$userName]);
                                }
                            }

                            if (!empty($userIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['in_charge_person'] = substr($userIdsString, 1);
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'person_in_charge') . \Yii::t('base', 'batch_import_tip'); // add by hkk 2021/7/27
                                continue;
                            }
                        }

                        // 维修人
                        if (!empty($data[13])) {
                            $userIdsString = '';
                            $userArr = explode(';', $data[13]);
                            foreach ($userArr as $userName) {
                                if (!empty($userList[$userName])) {
                                    $userIdsString .= (',' . $userList[$userName]);
                                }
                            }
                            if (!empty($userIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['maintenance_person'] = substr($userIdsString, 1);
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'maintainer') . \Yii::t('base', 'batch_import_tip'); // add by hkk 2021/7/27
                                continue;
                            }
                        }


                        // 15 开始添加自定义的字段
                        for ($i = 15; $i < count($data); $i++) {
                            $instrumentData['data' . ($i - 14)] = $data[$i];
                        }

                        $addResult = (new InstrumentServer())->addInstrument($instrumentData); // 写到数据库

                        if ($addResult['status'] == 0) {
                            $failInfo[$row] = 'Add instrument failed';
                        } else {
                            $successNumber++;
                        }
                    } else {
                        $failInfo[$row] = \Yii::t('base', 'require_name_tip');
                    }
                }
            }
        }

        return $this->success([
            'info' => $failInfo,
            'successNumber' => $successNumber,
        ]);
    }

    /**
     * Notes:编辑时删除仪器文件，记录痕迹
     * Author: hkk
     * Date: 2020/4/29 9:30
     * @return \common\controllers\json
     * @throws \PHPExcel_Reader_Exception
     * @throws yii\db\Exception
     */
    public function actionDeleteInstrumentFile()
    {


        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->deleteInstrumentFile($postData);

        return $this->success([]);
    }

    /**
     * Notes:编辑时删除仪器图片，记录痕迹
     * Author: zwm
     * Date: 2023/4/23
     */
    public function actionDeleteInstrumentPicture()
    {

        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->deleteInstrumentPicture($postData);

        return $this->success([]);
    }

    /**
     * Notes: 上传文件
     * Author: hkk
     * Date: 2020/4/29 10:59
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionUploadInstrumentFile()
    {

        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->uploadInstrumentFile($postData);

        return $this->success([]);
    }

    /**
     * Notes: 上传图片
     * Author: zwm
     * Date: 2023/4/20
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionUploadInstrumentPicture()
    {

        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->uploadInstrumentPicture($postData);

        return $this->success([]);
    }

    /**
     * Notes: 新的仪器痕迹页面，带搜索 get-trace-page
     * Author: hkk
     * Date: 2020/4/30 13:19
     * @return \common\controllers\json
     */
    public function actionGetTracePage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['instrument_id'] = $postData['instrumentId'];
        $where['action'] = \Yii::$app->request->post('action', '');
        $where['action_details'] = \Yii::$app->request->post('action_details', '');
        $where['create_by'] = \Yii::$app->request->post('create_by', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');

        // 获取查询的数据
        $instrumentTraceData = (new InstrumentServer())->listInstrumentTraceView($where, $limit, $page);
        $data['historyList'] = $instrumentTraceData['historyList'];
        $data['totalCount'] = $instrumentTraceData['totalCount'];
        $data['limit'] = $limit;

        //查询该企业下所有用户,用于菜单搜索筛选
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if (!$company_id) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
        $data['all_user'] = isset($userList['list']) ? $userList['list'] : [];
        $data['instrument_id'] = $postData['instrumentId'];


        $insInfo = (new InstrumentServer())->getInsInfoById($postData['instrumentId']);
        $data['name'] = $insInfo['data']['name'];
        $data['batchNumber'] = $insInfo['data']['batch_number'];
        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/setting/instrument_trace.php', $data);
            return $this->success([
                'contentHtml' => $file,
                'name' => $insInfo['data']['name'], // 设备id
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/setting/instrument_trace_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 导出仪器列表
     * Author: hkk
     * Date: 2020/5/6 16:28
     * @return \common\controllers\json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    public function actionInstrumentsExport()
    {

        // 获取查询条件
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['name'] = \Yii::$app->request->post('instrument_name', '');
        $where['status'] = \Yii::$app->request->post('instrument_status', '');
        $where['check_situation'] = \Yii::$app->request->post('instrument_check', '');
        $where['check_status'] = \Yii::$app->request->post('instrument_check', ''); // todo
        $where['create_by'] = \Yii::$app->request->post('instrument_create_user', '');
        $where['responsible_person'] = \Yii::$app->request->post('instrument_responsible_user', '');
        $where['in_charge_person'] = \Yii::$app->request->post('instrument_in_charge_user', '');
        $where['maintenance_person'] = \Yii::$app->request->post('instrument_maintenance_user', '');
        $where['position'] = \Yii::$app->request->post('instrument_position', '');
        $where['supplier'] = \Yii::$app->request->post('instrument_supplier', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['chooseIds'] = \Yii::$app->request->post('chooseIds', []); // bug#28130-仪器库管理，如果有勾选，导出的仅包括勾选的条目 jiangdm 2022/8/9
        $type = \Yii::$app->request->post('export_type', 'instrument_manage');
        //显示的列
        $fieldConfigShowFields = \Yii::$app->request->post('show_field', []);


        // 可见范围筛选，同我的仪器库，只显示所属鹰群或部门的仪器
//        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $where['groups'] = array_column($this->userinfo->groups, 'id');

//        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $where['departments'] = array_column($this->userinfo->department, 'id');


        if ($type == 'my_instruments') {
            $where['viewAuth'] = '2';
        } else {
            // 获取查看权限
            $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
            if ($userAuth['company_feature']['view_instruments_manage']) {
                $where['viewAuth'] = '1'; // 能查看所有仪器
            } else if ($userAuth['company_feature']['view_instruments_manage_my']) {
                $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
            } else {
                return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
            }
        }


        // 获取查询的数据
        $instrumentsData = (new InstrumentServer())->listInstrumentsView($where, $limit, $page, true);
        // 查询字段配置 add by hkk 2022/8/10
        $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
        $fieldMap = $fieldConfig['data']['fieldMap'];
        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");
        // $objPHPExcel->setActiveSheetIndex(0); // 标题栏;

        $key = 0;
        // 标题栏 兼容字段配置
        foreach ($fieldConfigShowFields as $showField) {
            if ($showField == 'files') {
                continue;
            }
            if ($showField == 'picture') {
                continue;
            }
            $showName = $fieldMap[$showField];
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($key) . '1', $showName); // 仪器名称
            $key++;
        }

        $i = 0;
        foreach ($instrumentsData['instruments_list'] as $key1 => $value) {
            $cellNum = $i + 2;
            $i++;
            $key2 = 0;
            foreach ($fieldConfigShowFields as $showField) {
                if ($showField == 'files') {
                    continue;
                }
                if ($showField == 'picture') {
                    continue;
                }
                if ($showField == 'status') {
                    $showValue = $value['status_string'];
                } else if ($showField == 'check_situation') {
                    $showValue = $value['check_situation_string'];
                } else if ($showField == 'create_by') {
                    $showValue = $value['create_name'];
                } else if ($showField == 'groupIds') {
                    $showValue = $value['group_name_string'];
                } else if ($showField == 'departmentIds') {
                    $showValue = $value['department_name_string'];
                } else if ($showField == 'responsible_person') {
                    $showValue = $value['responsible_person_string'];
                } else if ($showField == 'in_charge_person') {
                    $showValue = $value['in_charge_person_string'];
                } else if ($showField == 'maintenance_person') {
                    $showValue = $value['maintenance_person_string'];
                } else if ($showField == 'data_type') {
                    $showValue = $value['data_type_string'];
                } else if (substr($showField, 0, 5) == 'field') { // 自定义字段
                    $showValue = $value['data' . substr($showField, 5)];
                } else if ($showField == 'instrument_type') {
                    $showValue = $value['instrument_type'] != 0 ?: '';
                } else {
                    $showValue = $value[$showField];
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($key2) . $cellNum, $showValue);
                $key2++;
            }
        }
        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        $file = \Yii::getAlias('@filepath') . DS . 'instruments' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instruments' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }

        return $this->success([]);
    }

    public function actionInstrumentsTraceExport()
    {

        // 获取查询条件
        $page = \Yii::$app->request->get('page', 1);
        $limit = \Yii::$app->request->get('limit', \Yii::$app->params['default_page_size']);
        $where['instrument_id'] = \Yii::$app->request->get('instrumentId');
        $where['action'] = \Yii::$app->request->get('action', '');
        $where['action_details'] = \Yii::$app->request->get('action_details', '');
        $where['create_by'] = \Yii::$app->request->get('create_by', '');
        $where['start_time'] = \Yii::$app->request->get('start_time', '');
        $where['end_time'] = \Yii::$app->request->get('end_time', '');

        // 获取查询的数据
        $instrumentServer = new InstrumentServer();
        $instrumentTraceData = $instrumentServer->listInstrumentTraceView($where, $limit, $page, true);
        $insInfo = $instrumentServer->getInsInfoById($where['instrument_id']);
        if(empty($insInfo['data']['batch_number'])){
            $fileName = $insInfo['data']['name'] . '_Audit Trail ';
        }else{
            $fileName = $insInfo['data']['name'] . '(' . $insInfo['data']['batch_number'] . ')' . '_Audit Trail ';
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");
        $objPHPExcel->setActiveSheetIndex(0) // 标题栏
        ->setCellValue('A1', \Yii::t('base', 'person')) // 人员
        ->setCellValue('B1', \Yii::t('base', 'create_time')) // 时间
        ->setCellValue('C1', \Yii::t('base', 'operation_type')) // 类别
        ->setCellValue('D1', \Yii::t('base', 'operation_details')); // 详情

        $i = 0;
        foreach ($instrumentTraceData['historyList'] as $key1 => $value) {
            $cellNum = $i + 2;
            $i++;
            $objPHPExcel->setActiveSheetIndex(0)
                ->setCellValueExplicit('A' . $cellNum, $value['create_name'])
                ->setCellValueExplicit('B' . $cellNum, $value['create_time'])
                ->setCellValueExplicit('C' . $cellNum, $value['action_string'])
                ->setCellValueExplicit('D' . $cellNum, str_replace('<br>', "\n", $value['action_details']));
        }

        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . $fileName . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . $fileName . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $fileName . '.xlsx"');

        header('Cache-Control: max-age=0');
        readfile($file);

    }


    /**
     * Notes:  获取 Inscada 汇总页面 （仪器管理 - 更多 - Inscada）
     */
    public function actionGetInscadaSummaryPage()
    {
        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();

        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['dataType'] = \Yii::$app->request->post('dataType', '1');
        $type = \Yii::$app->request->post('inscada_type', 'my-instruments');
        $data['inscadaType'] = $type;

        // 权限判断-开始
        // 缓存读取所有可见鹰群
        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $data['visibleGroups'] = $visibleGroups;

        // 缓存读取所有可见部门
        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $data['visibleDepartments'] = $visibleDepartments;

        // 缓存读取所有可见用户
        $visibleUsers = (new CenterInterface())->getVisibleUsers($this->userinfo->id);
        $data['all_user'] = $visibleUsers;

        // 用于查询可见鹰群或部门
        $currentUserGroupIds = array_column($data['visibleGroups'], 'id');
        $currentUserDepartmentIds = array_column($data['visibleDepartments'], 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;
        // 能查看所有仪器

        $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        // 获取查看权限
        if (Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        }


        // 权限判断-结束

        // 搜索与筛选条件
        $where['status'] = isset($postData['status']) ? $postData['status'] : "1";
        $where['keyword'] = \Yii::$app->request->post('keyword', '');
        $where['operate_users'] = \Yii::$app->request->post('inscada_operator', '');
        $where['inscada_signer'] = \Yii::$app->request->post('inscada_signer', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['instrument'] = \Yii::$app->request->post('instrument', '');
        $where['numerical_instrument_type'] = \Yii::$app->request->post('numerical_instrument_type', 1);
        $where['filter_unclaimed'] = \Yii::$app->request->post('filterUnclaimed', 1);
        $where['curr_user_id'] = $this->userinfo->id;
        // 获取数据-开始
        $inscadaData = (new InstrumentServer())->listInscadaSummary($where, $limit, $page);
        $data['inscadaList'] = $inscadaData['inscada_list'];
        // print_r($data['inscadaList']);die;
        $data['limit'] = $limit;
        $data['page'] = $page;
        $data['totalCount'] = $inscadaData['totalCount'];
        $data['dataType'] = $where['dataType'];
        $signWhere['inscada_id'] = array_column($data['inscadaList'], 'id');
        $signWhere['dataType'] = $where['dataType'];
        // 获取对应的签字数据
        if ($where['dataType'] == 1) {
            $inscadaSignHistory = (new InstrumentServer())->listInsDataNumHistory($signWhere, $limit, $page, true);
        } else if ($where['dataType'] == 2) {
            $inscadaSignHistory = (new InstrumentServer())->listInsDataFileHistory($signWhere, $limit, $page, true);
        }
        $data['numericalInstrumentType'] = $where['numerical_instrument_type'];
        $inscadaSignHistory = ArrayHelper::index($inscadaSignHistory['data'], 'inscada_id', true);
        // 签字数据
        foreach ($data['inscadaList'] as $id => $inscadaData) {
            if (isset($inscadaSignHistory[$inscadaData['id']])) {
                $data['inscadaList'][$id]['sign_history'] = $inscadaSignHistory[$inscadaData['id']];
            } else {
                $data['inscadaList'][$id]['sign_history'] = [];
            }
        }
        $file = '';
        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            // 获取仪器列表用于根据仪器筛选数据
            $instruments = (new InstrumentServer())->listAllInstruments($where);
            $data['instruments'] = $instruments['instruments'];
            //判断是我的仪器库还是仪器库管理
            $data['inscadaType'] = $instruments['dataType'];
            $file = $this->renderAjax('/instrument/instrument_inscada_summary.php', $data);
            return $this->success([
                'contentHtml' => $file,
            ]);
        }
        // 传page参数只更新表格数据

        $file = $this->renderAjax('/instrument/instrument_inscada_summary_page.php', $data);
        return $this->success([
            'file' => $file,
        ]);
    }

    /**
     * Notes: 获取Inscada统计数据，统计仪器产生的数据量
     */
    public function actionGetInscadaGraphPage()
    {
        // 获取查询条件，获取两种类型的数据
        $postData = \Yii::$app->getRequest()->post();
        $where['dataType'] = \Yii::$app->request->post('dataType', '1');
        $type = \Yii::$app->request->post('inscada_type', 'instrument_manage');


        // 权限判断-开始
        // 缓存读取所有可见鹰群
        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $data['visibleGroups'] = $visibleGroups;

        // 缓存读取所有可见部门
        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $data['visibleDepartments'] = $visibleDepartments;

        // 缓存读取所有可见用户
        $visibleUsers = (new CenterInterface())->getVisibleUsers($this->userinfo->id);
        $data['all_user'] = $visibleUsers;

        // 用于查询可见鹰群或部门
        $currentUserGroupIds = array_column($data['visibleGroups'], 'id');
        $currentUserDepartmentIds = array_column($data['visibleDepartments'], 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;
        // 能查看所有仪器

        if ($type == 'my-instruments') {
            $where['viewAuth'] = '2';
        } else {
            // 获取查看权限
            if (\Yii::$app->view->params['instruments_manage_read']) {
                $where['viewAuth'] = '1'; // 能查看所有仪器
            } else if (\Yii::$app->view->params['instruments_manage_read_my']) {
                $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
            } else {
                return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
            }
        }


        // 权限判断-结束

        // 搜索与筛选条件
        $where['status'] = isset($postData['status']) ? $postData['status'] : "1";
        $where['keyword'] = \Yii::$app->request->post('instrument_keywords', '');
        $where['operate_users'] = \Yii::$app->request->post('inscada_operator', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['position'] = \Yii::$app->request->post('position', '');


        // 获取数据-开始
        $inscadaData = (new InstrumentServer())->getInscadaStatistics($where);
        $data['data'] = $inscadaData['data'];

        // 构造绘图数据
        $nameArr = []; // 仪器名称
        $dataNumArr = []; // 产生的数据量
        foreach ($data['data'] as $i => $val) {

            $nameArr[] = $val['name'] . '(' . $val['batch_number'] . ')';
            $dataNumArr[] = $val['data_num'];
        }
        $data['nameArr'] = $nameArr;
        $data['dataNumArr'] = $dataNumArr;


        $renderData['data'] = $data;
        $file = '';
        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_inscada_sum_graph.php', $renderData);
            return $this->success([
                'contentHtml' => $file,
            ]);
        }
        // 传page参数只更新表格数据

        $file = $this->renderAjax('/instrument/instrument_inscada_sum_graph_page.php', $renderData);
        return $this->success(['file' => $file]);
    }


    /**
     * Notes: 获取数据对接新标签页
     * Author: Sun Yizhou
     * Date: 2022/09/08
     * @return \common\controllers\json
     */
    public function actionGetInscadaPage()
    {
        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        if(\Yii::$app->request->post('source', 'page') == 'popup'){
            // 如果是弹窗则每页50个
            $limit = 50;
        }
        // 获取查询的数据
        $dataType = $postData['dataType'];
        // 优先根据仪器ID获取仪器信息，没有仪器ID，用instruments表id获取

        $insWhere = empty($postData['batch_number']) ? ['id' => $postData['instrumentId']] : ['batch_number' => $postData['batch_number']];
        $insInfo = InstrumentsModel::find()->where($insWhere)->asArray()->one();

        $where['batch_number'] = $insInfo['batch_number'];
        $where['status'] = isset($postData['status']) ? $postData['status'] : "1";
        $where['keyword'] = \Yii::$app->request->post('keyword', '');
        $where['operate_users'] = \Yii::$app->request->post('inscada_operator', '');
        $where['inscada_signer'] = \Yii::$app->request->post('inscada_signer', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['data_id'] = \Yii::$app->request->post('data_id', '');
        $where['instrument_recent_time'] = \Yii::$app->request->post('instrument_recent_time', '');
        $where['recent_data_filter'] = \Yii::$app->request->post('recent_data_filter', '');
        $where['filter_unclaimed'] = \Yii::$app->request->post('filterUnclaimed', 1);
        $where['curr_user_id'] = $this->userinfo->id;
        $signWhere['dataType'] = $dataType;
        // 获取仪器对接的数据
        if ($dataType == "1") {
            // 数据对接类型：数值
            $data['dataType'] = 1;
            // 仪器对接类型细分
            $where['numerical_instrument_type'] = \Yii::$app->request->post('numerical_instrument_type', 1);
            $data['inscadaList'] = (new InstrumentServer())->listInsDataNumericalView($where, $limit, $page);
            $signWhere['inscada_id'] = array_column($data['inscadaList']['data'],'id');
            $inscadaSignHistory = (new InstrumentServer())->listInsDataNumHistory($signWhere, $limit, $page, true);

        } else {
            // 数据对接类型：文件
            $data['dataType'] = 2;
            $data['inscadaList'] = (new InstrumentServer())->listInsDataFileView($where, $limit, $page);
            $signWhere['inscada_id'] = array_column($data['inscadaList']['data'],'id');
            $inscadaSignHistory = (new InstrumentServer())->listInsDataFileHistory($signWhere, $limit, $page, true);

        }
        $inscadaSignHistory = ArrayHelper::index($inscadaSignHistory['data'],'inscada_id', true);
        foreach ($data['inscadaList']['data'] as $id => $inscadaData) {
            if (isset($inscadaSignHistory[$inscadaData['id']])) {
                $data['inscadaList']['data'][$id]['sign_history'] = $inscadaSignHistory[$inscadaData['id']];
            }else{
                $data['inscadaList']['data'][$id]['sign_history'] = [];
            }
        }
        $data['data_id'] = $where['data_id'];
        $data['insId'] = $insInfo['id'];
        $data['limit'] = $limit;
        $data['page'] = $page;
        $data['totalCount'] = $data['inscadaList']['totalCount'];
        $data['source'] = \Yii::$app->request->post('source', 'page'); // 请求来自标签页or弹窗
        //print_r($data);exit;
        $data['name'] = $insInfo['name'];
        $data['batchNumber'] = $insInfo['batch_number'];
        // $data['instrumentId'] = $postData['instrumentId'];
        // 第一次渲染，更新整个页面
//        print_r($data);die;
        if (isset($postData['needUpdateAllPage']) && $postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_inscada.php', $data);
            return $this->success([
                'contentHtml' => $file,
                'insName' => $data['name'], // 设备名称
                'insId' => $data['batchNumber'], // 设备id
                'data_id' => $data['data_id'],
            ]);
        }
        // 传page参数只更新表格数据

        $file = $this->renderAjax('/instrument/instrument_inscada_page.php', $data);
        return $this->success([
            'file' => $file,
            'ins_info' => $insInfo,
            'source' => $data['source'],
            'data_id' => $data['data_id'],
            'total_count' => $data['totalCount'],
            'limit' => $limit,
        ]);
    }


    /**
     * Notes: 获取inscada操作按钮弹窗。
     */
    public function actionGetInscadaOperatePage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $type = $postData['type']; // 操作类型 receive edit delete history
        // $data['type'] = $type;
        $dataType = $postData['dataType']; // 数值类1 文件类2
        $inscadaId = $postData['inscadaId']; // 数组


        switch ($type) {
            case "edit":

                if ($dataType == '1') {
                    // 数值类
                    $inscadaDetail = InstrumentDataNumericalModel::findOne(['id' => $inscadaId])->toArray();
                    $expPageArr = explode(' ', $inscadaDetail['exp_pages']);
                    $expPageArr = array_unique($expPageArr);
                    $inscadaDetail['exp_pages'] = implode(' ', $expPageArr);
                    $inscadaDetail['type'] = 'edit';
                    $inscadaDetail['recipient'] = explode(',', $inscadaDetail['operate_users']);
                    $inscadaDetail['name'] = $this->userinfo->name;
                    $file = $this->renderAjax('/instrument/instrument_inscada_operate.php', $inscadaDetail);
                    return $this->success(['file' => $file]);
                } else {
                    // 文件类
                    $inscadaDetail = InstrumentDataFileModel::findOne(['id' => $inscadaId])->toArray();
                    $expPageArr = explode(' ', $inscadaDetail['exp_pages']);
                    $expPageArr = array_unique($expPageArr);
                    $inscadaDetail['exp_pages'] = implode(' ', $expPageArr);
                    $inscadaDetail['type'] = 'edit';
                    $inscadaDetail['recipient'] = explode(',', $inscadaDetail['operate_users']);
                    $inscadaDetail['name'] = $this->userinfo->name;
                    $file = $this->renderAjax('/instrument/instrument_inscada_operate.php', $inscadaDetail);
                    return $this->success(['file' => $file]);
                }
                break;
            case "delete":
            case 'instrument_sign':
            case 'batch_sign':
            case 'batch_delete':
                // 不需要获取后端数据
                $inscadaDetail['type'] = $type;
                $inscadaDetail['dataType'] = $dataType;
                $inscadaDetail['name'] = $this->userinfo->name;

                if ($dataType == '1') {
                    // 数值类
                    $file = $this->renderAjax('/instrument/instrument_inscada_operate.php', $inscadaDetail);
                    return $this->success(['file' => $file]);
                } else {
                    // 文件类
                    $file = $this->renderAjax('/instrument/instrument_inscada_operate.php', $inscadaDetail);
                    return $this->success(['file' => $file]);
                }
                break;
            case 'history':
                $where['inscada_id'] = $inscadaId;
                $where['dataType'] = $dataType;
                $limit = empty($postData['limit']) ? 10 : $postData['limit'];
                $page = empty($postData['page']) ? 1 : $postData['page'];
                if ($dataType == '1') {
                    $inscadaHistory = (new InstrumentServer())->listInsDataNumHistory($where, $limit, $page);

                } else {
                    $inscadaHistory = (new InstrumentServer())->listInsDataFileHistory($where, $limit, $page);
                }
                $totalCount = $inscadaHistory['totalCount'];
                if (empty($postData['page'])) {
                    $file = $this->renderAjax('/instrument/instrument_inscada_history.php', [
                        'inscadaHistory' => $inscadaHistory,
                        'totalCount' => $totalCount,
                        'limit' => $limit,
                        'page' => $page,
                        'inscadaId' => $inscadaId
                    ]);
                } else {
                    $file = $this->renderAjax('/instrument/instrument_inscada_history_data.php', [
                        'inscadaHistory' => $inscadaHistory,
                        'totalCount' => $totalCount,
                        'limit' => $limit,
                        'page' => $page,
                        'inscadaId' => $inscadaId
                    ]);
                }
                return $this->success(['file' => $file]);
            case 'receive':
            case 'batch_receive':
                $inscadaDetail['type'] = $type;
                $inscadaDetail['dataType'] = $dataType;
                if ($dataType == '1') {
                    // 数值类
                    $newData = [
                        'exp_pages'=>'',
                        'remark'=>'',
                    ];
                    $result = (new InstrumentServer())->editInsDataNumerical($newData, $inscadaId, $this->userinfo->id, $type);
                    if ($result['status'] == 1) {
                        return $this->success([
                            'name' => $this->userinfo->name,
                            'real_name' => $this->userinfo->real_name,
                        ]);
                    }
                } else {
                    // 文件类
                    $file = $this->renderAjax('/instrument/instrument_inscada_operate.php', $inscadaDetail);
                    return $this->success(['file' => $file]);
                }
                break;
            default:
                # code...
                break;
        }
    }


    /**
     * Notes: inscada 操作前需要输入用户名密码，调用该页面收集用户账户
     */
    function authInscadaOperate($account, $password)
    {
        return (new CenterInterface())->checkAccountPassword($account, $password);
    }

    /**
     * Notes: 检验用户输入的用户名和密码，然后更新 inscada 数据：实验记录本、状态（正常、删除）、备注
     */
    public function actionUpdateInscada()
    {
        //
        $postData = \Yii::$app->getRequest()->post();
        // 检查用户名和密码
        $type = $postData['type']; // 操作类型 receive edit delete history
        if ($type == 'instant_receive' ||
            $type == 'instant_receive_file' ||
            $type == 'receive' ||
            $type == 'batch_receive') {
            $auth = ['id' => $this->userinfo->id]; // 来自弹窗的插入操作不需要验证用户信息
        } else {
            $auth = $this->authInscadaOperate($postData['username'], $postData['password']);
        }
        if (!$auth) {
            return $this->fail(\Yii::t('base', 'usrname_pwd_err'));
        }
        // file_put_contents('log2.txt', json_encode($auth));
        $dataType = $postData['dataType'];  // 数值类1 文件类2
        $inscadaIds = $postData['inscadaId']; // 数组
        // $user = $this->userinfo->id;
        $instrumentServer = new InstrumentServer();
        switch ($type) {
            case 'edit':
                $data['exp_pages'] = $postData['exp_pages'];
                $data['status'] = $postData['status'];
                $data['remark'] = $postData['remark'];
                $data['operate_users'] = $postData['recipient'];
                if ($dataType == '1') {
                    $result = (new InstrumentServer())->editInsDataNumerical($data, $inscadaIds, $auth['id'], $type);
                } else {
                    $result = (new InstrumentServer())->editInsDataFile($data, $inscadaIds, $auth['id'], $type);
                }
                if ($result['status'] == 1) return $this->success([]);
                break;
            case 'delete':
            case 'batch_delete':
                $data['status'] = 0;
                $data['instrument_delete_remark'] = $postData['instrument_delete_remark'];
                if ($dataType == '1') {
                    $result = (new InstrumentServer())->editInsDataNumerical($data, $inscadaIds, $auth['id'], $type);
                } else {
                    $result = (new InstrumentServer())->editInsDataFile($data, $inscadaIds, $auth['id'], $type);
                }
                if ($result['status'] == 1) return $this->success([]);
                break;
            case 'receive':
            case 'batch_receive':
            case 'instant_receive':
            case 'instant_receive_file':
                $data['exp_pages'] = @getVar($postData['exp_pages'], '');
                $data['remark'] = @getVar($postData['remark'], '');
                if ($dataType == '1') {
                    $result = (new InstrumentServer())->editInsDataNumerical($data, $inscadaIds, $auth['id'], $type);
                    if ($result['status'] == 1) return $this->success([]);
                } else {
                    $needUploadFile = empty($postData['needUploadFile']) ? true : $postData['needUploadFile'];
                    $result = (new InstrumentServer())->editInsDataFile($data, $inscadaIds, $auth['id'], $type, $needUploadFile);
                    $state = $result['data']['receiveState'];
                    // 无法找到上传模块
                    if ($result['status'] == 1 && $state > 2) return $this->success(['receiveState' => $state]);
                    // 单个上传模块
                    if ($result['status'] == 1 && $state == 2) return $this->success($result['data']);
                    // 多个上传模块
                    if ($result['status'] == 1) {
                        $result['data']['file'] = $this->renderAjax('/instrument/instrument_inscada_operate_uploadfile.php', $result['data']);
                        return $this->success($result['data']);
                    }
                    return $this->fail($result['info']);
                }
                break;
            case 'instrument_sign':
            case 'batch_sign':
                // 添加痕迹
                $data['instrument_sign_remark'] = $postData['instrument_sign_remark'];
                $data['data_type'] = $postData['dataType'];
                foreach ($inscadaIds as $inscadaId){
                    $inscadaDetail['id'] = $inscadaId;
                    $isAddHistory = $instrumentServer->addInsDataHistory($auth['id'], $type, $data, $inscadaDetail);
                }
                break;
            default:
                # code...
                break;
        }
        return $this->success([]);
    }

    /**
     * Notes: 设置链接弹窗
     * Author: jiangdm
     * Date: 2022/10/19
     */
    public function actionGetAvailableInstrumentListForInsert()
    {
        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $currentUserGroupIds = array_column($visibleGroups, 'id');
        $currentUserDepartmentIds = array_column($visibleDepartments, 'id');
        $query = InstrumentsModel::find()->select('id, name, batch_number, position, data_type')
            ->where(['status' => [1, 2, 3, 4], 'data_type' => [1, 2]]);
        $FindStr = "";
        foreach ($currentUserGroupIds as $key => $groupId) {
            $FindStr .= "FIND_IN_SET(:groupId" . $key . ", groupIds) or ";
            $query->addParams([':groupId' . $key => $groupId]);
        }
        foreach ($currentUserDepartmentIds as $key => $departmentId) {
            $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", departmentIds) or ";
            $query->addParams([':departmentId' . $key => $departmentId]);
        }
        $FindStr .= "FIND_IN_SET('all', groupIds)";
        $query->andWhere(new Expression($FindStr));
        $instrumentsData = $query->asArray()->all();

        $html = $this->renderAjax('/popup/instrument_data_insert.php', [
            'instrumentList' => $instrumentsData,
        ]);

        return $this->success([
            'html' => $html,
            'instrumentList' => $instrumentsData,
        ]);
    }

    /**
     * Notes: 获取仪器数据
     * jiangdm 2022/10/18
     */
    public function actionReadInstrumentData()
    {
        // 获取实体仪器注册信息
        $instrumentInfo = InstrumentsModel::find()->select('ib.ip, ib.port, ib.path, ib.read_command')
            ->from(InstrumentsModel::tableName() . ' i')
            ->innerJoin(InstrumentBindingModel::tableName() . ' ib', 'i.batch_number = ib.batch_number')
            ->where(['i.id' => $_POST['instrument_id'], 'ib.status' => 1])->asArray()->one();
        if (empty($instrumentInfo)) return $this->fail(\Yii::t('base', 'no_bind_instrument'));
        $instrumentSite = $instrumentInfo['ip'] . ':' . $instrumentInfo['port'];
        // 发送测量请求
        try {
            $socket = stream_socket_client("tcp://" . $instrumentSite, $errno, $errstr, 2);
            fwrite($socket, 'INTEGLE_READ');
            $response = fread($socket, 8192);
        } catch (\Exception $e) {
            return $this->fail(\Yii::t('base', 'read_instrument_data_failed'));
        }
        $socket && fclose($socket);
        // 保存读取的数据，返回到前端
        $insData = array_merge(json_decode($response, true), [
            'instrument_id' => '',
            'exp_pages' => '',
            'remark' => '',
            'status' => 1,
            'operate_users' => '',
        ]);
        $insDataModel = new InstrumentDataNumericalModel();
        $insDataModel->setAttributes($insData);
        if (!$insDataModel->save()) {
            Yii::info($insDataModel->getFirstErrors());
        }
        $dataId = $insDataModel->id;
        $insertData = $insDataModel->find()->where(['id' => $dataId])->asArray()->one();
        return $this->success(['insert_data' => $insertData, 'site' => $instrumentSite], 'JSON', \Yii::t('base', 'read_instrument_data_success'));
    }


    /**
     * Notes: 将选择的仪器数据文件插入到选择的实验记录本的上传文件模块
     */
    public function actionInsertFileToModule()
    {
        //
        $postData = \Yii::$app->getRequest()->post();
        $fileList = $postData['fileList'];
        $moduleId = $postData['moduleId'];
        $type = $postData['type']; // 操作类型 receive edit delete history
        // 用户名和密码
        if ($type == 'instant_receive' || $type == 'instant_receive_file' || $type == 'receive') {
            $auth = ['id' => $this->userinfo->id]; // 来自弹窗的插入操作不需要验证用户信息
        } else {
            $auth = $this->authInscadaOperate($postData['username'], $postData['password']);
        }
        $dataType = $postData['dataType'];  // 数值类1 文件类2
        $inscadaIds = $postData['inscadaId']; // 数组
        // $result1 = (new InstrumentServer)->addFilesToModule($moduleId, $fileList);
        $data['exp_pages'] = $postData['exp_pages'];
        $expId = $postData['expId'];
        $result = (new InstrumentServer())->addFilesToModule($moduleId, $fileList, $data, $inscadaIds, $auth['id'], $type, $expId);
        // 单个上传模块
        if ($result['status'] == 1 && $result['data']['receiveState'] == 2) {
            $result['data']['addFilesRes'] = [
                'moduleArr' => [['id' => $moduleId[0]]],
                'fileList' => $fileList,
                'expId' => $expId,
            ];
            return $this->success($result['data']);
        }
        if ($result['status'] == 1) return $this->success([$result]);
        return $this->fail($result['info']);
    }

    /**
     * Notes: 导出 inscada 数据
     * *
     * @return \common\controllers\json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    public function actionExportInscada()
    {
        //
        $postData = \Yii::$app->getRequest()->post();
        // $data['type'] = $type;
        $dataType = $postData['dataType']; // 数值类1 文件类2
        if (isset($postData['inscadaId'])) {
            $inscadaIds = $postData['inscadaId']; // 数组
        } else {
            $inscadaIds = array();
        }

        // 获取查询条件
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['status'] = isset($postData['status']) ? $postData['status'] : "1";
        $where['keyword'] = \Yii::$app->request->post('keyword', '');
        $where['operate_users'] = \Yii::$app->request->post('inscada_operator', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['filter_unclaimed'] = \Yii::$app->request->post('filterUnclaimed', 1);
        $where['numerical_instrument_type'] = \Yii::$app->request->post('numerical_instrument_type', 0);
        $where['curr_user_id'] = $this->userinfo->id;
        $where['chooseIds'] = $inscadaIds;
        $where['dataType'] = $dataType;
        $dataList = null;
        // 是否是由详情页进入的
        if (isset($postData['instrumentId'])) {
            // 获取仪器信息，此处是否需要筛选一下数据列？
            $insInfo = (new InstrumentServer())->getInsInfoById($postData['instrumentId']);
            $where['batch_number'] = $insInfo['data']['batch_number'];
            if ($dataType == "1") {
                $dataList = (new InstrumentServer())->listInsDataNumericalView($where, $limit, $page, true)['data'];
            } else if ($dataType == "2") {
                $dataList = (new InstrumentServer())->listInsDataFileView($where, $limit, $page, true)['data'];
            }
        } else {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
            // 获取查看权限
            if (Yii::$app->view->params['instruments_manage_read']) {
                $where['viewAuth'] = '1'; // 能查看所有仪器
            }
            // 权限判断-开始 详细注释见actionGetInscadaSummaryPage
            $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
            $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
            $currentUserGroupIds = array_column($visibleGroups, 'id');
            $currentUserDepartmentIds = array_column($visibleDepartments, 'id');
            $where['groups'] = $currentUserGroupIds;
            $where['departments'] = $currentUserDepartmentIds;
            $dataList = (new InstrumentServer())->listInscadaSummary($where, $limit, $page, true)['inscada_list'];
        }
        $signWhere['dataType'] = $dataType;
        $signWhere['inscada_id'] = array_column($dataList, 'id');
        if ($dataType == "1") {
            $inscadaSignHistory = (new InstrumentServer())->listInsDataNumHistory($signWhere, $limit, $page, true);
        } else if ($dataType == "2") {
            $inscadaSignHistory = (new InstrumentServer())->listInsDataFileHistory($signWhere, $limit, $page, true);
        }
        $inscadaSignHistory = ArrayHelper::index($inscadaSignHistory['data'], 'inscada_id', true);
        foreach ($dataList as $id => $inscadaData) {
            if (isset($inscadaSignHistory[$inscadaData['id']])) {
                $dataList[$id]['sign_history'] = $inscadaSignHistory[$inscadaData['id']];
            } else {
                $dataList[$id]['sign_history'] = [];
            }
        }
        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new \PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");
        // 要导出的字段
//        $exportFields = array('timestamp', 'numerical_value', 'unit', 'raw_data', 'operate_users', 'exp_pages', 'remark', 'status');
        if (!isset($postData['instrumentId'])) {
            if ($dataType == "1") {
                $exportFields = array('name', 'batch_number', 'position', 'timestamp', 'numerical_value', 'raw_data', 'recipient', 'sign_reason', 'signer', 'exp_pages', 'remark', 'status');
            } else if ($dataType == "2") {
                $exportFields = array('name', 'batch_number', 'position', 'timestamp', 'filename', 'filepath', 'recipient', 'sign_reason', 'signer', 'exp_pages', 'remark', 'status');
            }
        }else{
            if ($dataType == "1") {
                $exportFields = array('timestamp', 'numerical_value', 'raw_data', 'recipient', 'sign_reason', 'signer', 'exp_pages', 'remark', 'status');
            } else if ($dataType == "2") {
                $exportFields = array('timestamp', 'filename', 'recipient', 'sign_reason', 'signer', 'exp_pages', 'remark', 'status');
            }
        }
        // 标题栏 第1行
        foreach ($exportFields as $key => $fieldName) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($key) . '1', \Yii::t('base', $fieldName));
        }
        // 内容
        foreach ($dataList as $row => $data) {
            // row从0开始，但是需要从第2行开始输出数据
            $cellNum = $row + 2;
            foreach ($exportFields as $col => $fieldName) {
                if ($fieldName == 'recipient') {
                    $fieldName = 'operate_users_string';
                }
                if ($fieldName == 'status') {
                    $fieldName = 'status_string';
                }
                if ($fieldName == 'exp_pages') {
                    $fieldName = 'exp_pages_arr_str';
                }
                if ($fieldName == 'sign_reason') {
                    $data['sign_reason'] = '';
                    if(!empty($data['sign_history'])){
                        foreach ($data['sign_history'] as $key => $signData) {
                            if (empty($signData)) {
                                continue;
                            }
                            $data['sign_reason'] .= strip_tags(Yii::t('base', 'inscada_sign_detail',
                                [
                                    $signData['operate_user_string'],
                                    $signData['timestamp'],
                                    $signData['detail']
                                ]));
                            // bug 2629 plug，仪器对接--签字记录，每一条签字记录之间用中文逗号隔开
                            if ($key != count($data['sign_history']) - 1) {
                                $data['sign_reason'] .= '，';
                            }
                        }
                    }
                }
                if ($fieldName == 'signer') {
                    $data['signer'] = '';
                    if (!empty($data['sign_history'])) {
                        $data['signer'] = implode(',', array_unique(array_column($data['sign_history'], 'operate_user_string')));
                    }
                }

                if ($dataType == "1") {

                    // bug#31558，如果是数值，修改单元格数值样式
                    if ($fieldName == 'numerical_value') {
                        $num_format = preg_replace('/(\d+(?=\.)|^\d+|\d)/i', '0', $data[$fieldName]);//将整数部分和小数数字全部替换为0
                        $objPHPExcel->getActiveSheet()->getStyle(IntToChr($col))->getNumberFormat()->setFormatCode($num_format);//修改单元格数字格式
                        $data['numerical_value'] .= $data['unit']; // 将数值和单位字段合并
                    }
                }

                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($col) . $cellNum, $data[$fieldName]);
            }
        }

        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . 'inscada' . '.xlsx"');
        header('Cache-Control: max-age=0');
        ob_start();
//        $objWriter->save('php://output');
        $file = \Yii::getAlias('@filepath') . DS . 'inscada' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'inscada' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        readfile($file);
        $xlsData = ob_get_contents();
        ob_end_clean();
        $data = [
            'file' => "data:application/vnd.ms-excel;base64," . base64_encode($xlsData),
            'name' => "inscada.xlsx"
        ];
        return $this->success($data);
    }

    // 将列序号转化为excel字母列序号
    function IntToChr($index, $start = 65)
    {
        $str = '';
        if (floor($index / 26) > 0) {
            $str .= $this->IntToChr(floor($index / 26) - 1);
        }
        return $str . chr($index % 26 + $start);
    }


    /**
     * Notes:维修记录页面 get-repair-record-page
     * Author: hkk
     * Date: 2020/7/17 17:59
     * @return \common\controllers\json
     */
    public function actionGetRepairRecordPage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $repairStatus = \Yii::$app->request->post('repair_type', 0);
        $where['instrument_id'] = $postData['instrumentId'];
        $where['repair_type'] = $repairStatus;

        // 获取查询的数据
        $instrumentRepairRecordData = (new InstrumentServer())->listInsRepairRecordView($where, $limit, $page);
        $data['keyResult'] = $instrumentRepairRecordData['data']['keyResult'];
        $data['dataResult'] = $instrumentRepairRecordData['data']['dataResult'];
        $data['totalCount'] = $instrumentRepairRecordData['data']['totalCount'];
        $data['limit'] = $limit;
        $data['instrumentId'] = $postData['instrumentId'];
        $insInfo = (new InstrumentServer())->getInsInfoById($postData['instrumentId']);


        $data['name'] = $insInfo['data']['name']; // add by hkk 2022/6/23
        $data['batchNumber'] = $insInfo['data']['batch_number']; // add by hkk 2022/6/23

        $data['isCanWrite'] = \Yii::$app->view->params['instruments_manage_write']; // add by hkk 2022/8/11  查看是否有编辑权限

        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_repair_record.php', $data);
            return $this->success([
                'contentHtml' => $file,
                'insName' => $insInfo['data']['name'], // 设备名称
                'insId' => $insInfo['data']['batch_number'], // 用户看见页面里的设备id,不是数据库中的设备id
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_repair_record_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 新增仪器维修记录弹框页面 2021/4/14 加上编辑维修页面 get-repair-record-operate-page
     * Author: hkk
     * Date: 2020/7/20 11:33
     * @return \common\controllers\json
     */
    public function actionGetRepairRecordOperatePage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['addRepairRecordArray'] = $postData['addRepairRecordArray'];
        $data['type'] = $postData['type'];
        $data['runningId'] = \Yii::$app->request->post('runningId', 0); // 新增没有记录id
        $data['orderNumber'] = \Yii::$app->request->post('orderNumber', 0); // 新增没有记录序号
        $data['requireKey'] = [];
        $data['hiddenKey'] = ['operation_time', 'operator', 'reviewer', 'if_end', 'review_conclusion', 'review_record', 'review_time']; // 固定隐藏不显示的列(自动填充内容列和复核信息列)
        $instrumentServer = new InstrumentServer();
        $instrumentInfo = InstrumentsModel::find()->select('name,batch_number,status,record_setting')->where([
            'id' => $data['instrumentId'],
        ])->asArray()->one();
        if ($instrumentInfo['status'] != 1 && $instrumentInfo['status'] != 3) { // 状态非正常
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip1_1'));
        }
        $data['name'] = $instrumentInfo['name'];
        $data['batch_number'] = $instrumentInfo['batch_number'];
        if ($data['type'] == 'addRepairRecord') {
            //bug 2036 新增记录，其它两大记录有未结束的进行拦截
            $company_id = \Yii::$app->view->params['curr_company_id'];
            $unFinishedResult = $instrumentServer->unFinishedRecord($data['instrumentId'], 'repairRecord', $company_id);
            if ($unFinishedResult['status'] == 0) {
                return $this->fail($unFinishedResult['info'], 'JSON', ['tipType' => 'popContent']);
            }
            $data['requireKey'] = ['repair_type', 'repair_start_time', 'repair_person']; // 开始维保必填项
            $data['operationTime'] = date("Y-m-d H:i:s"); //维保开始时间为点击按钮的时间 Bug1259
        }

        if ($data['type'] == 'editBeforeEndRepairRecord') {
            $data['requireKey'] = ['repair_type', 'repair_start_time', 'repair_person']; // 结束前编辑的必填项
            $dataResult = $instrumentServer->getOperateDataById($data['runningId'], 1);
            $data['dataResult'] = $dataResult['data'];
            $data['operationTime'] = $data['dataResult']['operation_time'];
        }

        if ($data['type'] == 'editAfterEndRepairRecord') { //结束后编辑的必填项
            $dataResult = $instrumentServer->getOperateDataById($data['runningId'], 1);
            $data['dataResult'] = $dataResult['data'];
            $data['operationTime'] = $data['dataResult']['operation_time'];
            if ($data['dataResult']['repair_type'] == '2') {
                //保养结果不必填
                $data['requireKey'] = ['repair_type', 'repair_start_time', 'repair_end_time', 'repair_content', 'repair_person'];
            } else {
                $data['requireKey'] = ['repair_type', 'repair_start_time', 'repair_end_time', 'repair_content', 'repair_result', 'repair_person'];
            }
        }
        //判断显示仪器状态改为维修还是改为正常
        $ifInputFull = false;
        if ($data['type'] != 'addRepairRecord') {
            $repairData = $data['dataResult'];
            // 如果维保开始时间 结束时间 维保内容 维保人员不为空 则显示
            $ifInputFull = !empty($repairData['repair_start_time']) && !empty($repairData['repair_end_time']) && !empty($repairData['repair_content']) && !empty($repairData['repair_person']);
            if ($repairData['repair_type'] == '1') { //如果维保类型是维修，加上维保结果进行判断
                $ifInputFull = $ifInputFull && !empty($repairData['repair_result']);
            }
        }
        if (!$ifInputFull) {
            $data['changeInsStatus'] = Yii::t('base', 'ins_status_to_repair');
        } else {
            $data['changeInsStatus'] = Yii::t('base', 'ins_status_to_normal');
        }


        // 读取复核人设置，如果开启，读取人员姓名
        $data['hasRepairRecordCheck'] = false;//有没有设置复核人
        $data['showRepairRecordChek'] = $ifInputFull;//需不需要显示复核人

        if (!empty($instrumentInfo['record_setting'])) {
            $recordSetting = json_decode($instrumentInfo['record_setting'], true);
            if (!empty($recordSetting['repair_check_users'])) {
                $data['hasRepairRecordCheck'] = true;
                $repairRecordCheckUserIds = $recordSetting['repair_check_users'];

                // 获取复核人姓名和id字符串
                $defaultAllUser = (new CenterInterface())->userDetailsByUserIds($repairRecordCheckUserIds);
                $defaultAllUser = yii\helpers\ArrayHelper::index($defaultAllUser, 'user_id');
                $data['repairRecordCheckUserNames'] = '';
                foreach ($recordSetting['repair_check_users'] as $user) {
                    $userName = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                    $data['repairRecordCheckUserNames'] = $data['repairRecordCheckUserNames'] . ";" . $userName;
                }
                $data['repairRecordCheckUserNames'] = substr($data['repairRecordCheckUserNames'], 1);
                $data['repairRecordCheckUserIds'] = join(',', $repairRecordCheckUserIds);
            }
        }


        $repairUserDetail = (new CenterInterface())->getUserByUserId($this->userinfo->id);
        $data['repairUserName'] = $repairUserDetail ? CommentServer::displayUserName($repairUserDetail) : '';

        $html = $this->renderAjax('/instrument/instrument_repair_record_add.php', $data);
        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * Notes: 新增维修记录确认 2021/4/14 增加编辑维修记录确认 submit-add-repair-record
     * Author: hkk
     * Date: 2020/7/20 16:51
     * @return \common\controllers\json
     */
    public function actionSubmitAddRepairRecord()
    {
        $data = \Yii::$app->getRequest()->post();
        $data['operator'] = $this->userinfo->id; //当前操作人
        $saveResult = (new InstrumentServer())->saveRepairRecord($data); //数据库处理
        if ($saveResult['status'] == 0 && $saveResult['info'] == 'timeConflict') {
            return $this->fail($saveResult['data'], 'JSON',['tipType' => 'popContent']);
        }
        $resData = @getVar($saveResult['data'], []);
        if ($saveResult['status'] == 0) {
            return $this->fail($saveResult['info'], 'JSON', ['type' => @getVar($resData['type'], '')]);
        }

        return $this->success([]);
    }

    /**
     * Notes: 维修记录页面新添加列  add-repair-record-column
     * Author: hkk
     * Date: 2020/7/21 10:41
     * @return \common\controllers\json
     */
    public function actionAddRepairRecordColumn()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['field'] = $postData['field'];
        (new InstrumentServer())->addRepairRecordColumn($data); //数据库处理
        return $this->success([]);
    }

    /**
     * Notes: 维修记录/运行记录/校验记录更改列标题 2021/4/28 加上仪器页面标题修改
     * Author: hkk
     * Date: 2020/7/21 13:29
     * @return \common\controllers\json
     */
    public function actionModifyRepairRecordTitle()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['field'] = $postData['field'];
        $data['fieldValue'] = $postData['fieldValue'];
        switch ($postData['type']) {
            case 'instruments':
                InstrumentDefineFields::updateAll([$data['field'] => $data['fieldValue']], ['type' => 1, 'status' => 1]);
                break;
            case '1':
                (new InstrumentServer()) -> updateInstrumentRecord(InstrumentRepairRecordExtendFieldModel::class, 'repair_record', $data['instrumentId'], $data['field'], $data['fieldValue']);
                break;
            case '2':
                (new InstrumentServer()) ->updateInstrumentRecord(InstrumentCheckRecordExtendFieldModel::class, 'check_record', $data['instrumentId'], $data['field'], $data['fieldValue']);
                break;
            case '3':
                (new InstrumentServer()) ->updateInstrumentRecord(InstrumentRunningRecordExtendFieldModel::class, 'operate_record', $data['instrumentId'], $data['field'], $data['fieldValue']);
                break;
            default:
                break;
        }

        return $this->success([]);
    }

    /**
     * Notes: 批量复核维修记录/运行记录/校验记录  已弃用
     * Author: hkk
     * Date: 2020/7/21 16:16
     * @return \common\controllers\json
     */
    public function actionCheckRepairRecord()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['chooseIds'] = $postData['chooseIds'];
        $data['userAccount'] = $postData['userAccount'];
        $data['userPassword'] = $postData['userPassword'];
        $data['checkResult'] = $postData['checkResult'];
        $data['checkReason'] = $postData['checkReason'];

        // 验证账号密码并且返回用户id
        $isRight = (new CenterInterface())->checkAccountPassword($postData['userAccount'], $postData['userPassword']);
        if (!$isRight) {
            return $this->fail(\Yii::t('sign', 'pass_error'));
        }
        $data['userId'] = $isRight['id'];


        (new InstrumentServer())->checkRepairRecord($data); //数据库处理
        return $this->success([]);
    }

    /**
     * Notes: 导出仪器维保记录
     * Author: hkk
     * Date: 2020/7/22 9:16
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionInstrumentsRepairRecordExport()
    {

        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['instrument_id'] = $postData['instrumentId'];
        $where['repair_type'] = $postData['repair_type'];

        // 获取需要导出的数据
        $exportData = (new InstrumentServer())->listInsRepairRecordView($where, $limit, $page, true);
        $keyResult = $exportData['data']['keyResult'];
        $dataResult = $exportData['data']['dataResult'];
        $keyResult['if_void'] = Yii::t('base', 'status');
        $postData['repairRecordArray'][] = 'if_void';
        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        $startColIndex = 0;
        foreach ($keyResult as $field => $thField) {
            if (!empty($thField) && $field !== 'file' && in_array($field, $postData['repairRecordArray'])) { //文件栏不导出
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($startColIndex) . '1', $thField); // 设置标题栏
                $startRowIndex = 2;
                foreach ($dataResult as $index => $data) {
                    if ($field == 'repair_type') {
                        if ($data[$field] == '1') {
                            $data[$field] = \Yii::t('base', 'repair');
                        } elseif ($data[$field] == '2') {
                            $data[$field] = \Yii::t('base', 'Maintenance');
                        }
                    }
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($startColIndex) . $startRowIndex, $data[$field]); // 设置内容栏
                    $startRowIndex++;
                }
                $startColIndex++;
            }
        }

        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . 'instrumentsRepairRecord' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instrumentsRepairRecord' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }


        return $this->success([]);
    }

    /**
     * Notes:运行记录页面 get-operate-record-page
     * Author: hkk
     * Date: 2020/7/17 17:59
     * @return \common\controllers\json
     */
    public function actionGetOperateRecordPage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $expId = \Yii::$app->request->post('expId', 0);
        $where['instrument_id'] = $postData['instrumentId'];

        if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }
        // 获取查询的数据 具体详情,仪器数据和痕迹相关
        $instrumentOperateRecordData = (new InstrumentServer())->listInsOperateRecordView($where, $limit, $page);
        $data['keyResult'] = $instrumentOperateRecordData['data']['keyResult'];
        $data['dataResult'] = $instrumentOperateRecordData['data']['dataResult'];
        $data['totalCount'] = $instrumentOperateRecordData['data']['totalCount'];
        $data['operateStatus'] = $instrumentOperateRecordData['data']['operateStatus'];
        $data['limit'] = $limit;
        $data['instrumentId'] = $postData['instrumentId'];
        $insInfo = (new InstrumentServer())->getInsInfoById($postData['instrumentId']);


        if ($expId != 0) {
            $experiment = (new ExperimentServer())->getExpById($expId);
            $bookExpPage = empty($experiment['data']['base_data']['exp_code']) ? '' : $experiment['data']['base_data']['exp_code'];
        } else {
            $bookExpPage = '';
        }

        $data['expId'] = $expId;
        $data['bookExpPage'] = $bookExpPage; // add by hsl 2024/10/9  实验页面id,用于显示背景色
        $data['name'] = $insInfo['data']['name']; // add by hkk 2022/6/23
        $data['batchNumber'] = $insInfo['data']['batch_number']; // add by hkk 2022/6/23:

        $data['isCanWrite'] = \Yii::$app->view->params['instruments_manage_write']; // add by hkk 2022/8/11  查看是否有编辑权限

        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_operate_record.php', $data);
            return $this->success([
                'contentHtml' => $file,
                'insName' => $insInfo['data']['name'], // 设备名称
                'insId' => $insInfo['data']['batch_number'], // 设备id
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_operate_record_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 运行记录开始运行页面 get-begin-operate-record-page
     * Author: hkk
     * Date: 2020/7/22 11:33
     * @return \common\controllers\json
     */
    public function actionGetBeginOperateRecordPage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['addRepairRecordArray'] = $postData['addRepairRecordArray'];
        $data['type'] = $postData['type'];
        $data['hiddenKey'] = ['start_operator_time', 'end_operator_time', 'start_operator', 'end_operator', 'review_record']; // 固定隐藏不显示的列(自动填充内容列和复核信息列)
        $data['orderNumber'] = \Yii::$app->request->post('orderNumber', 0); // 新增没有记录序号
        $data['runningId'] = \Yii::$app->request->post('runningId', 0); // 新增没有记录id
        $data['needOperateRecordCheck'] = false;


        // 先查询当前真实的运行状态，如果不对提示用户刷新页面 (实验页面也会更改运行状态) // add by hkk 2021/4/30 // add by hkk 2022/6/16 只能新增
        $insInfo = InstrumentsModel::find()
            ->select('name,batch_number,status,check_situation,responsible_person,in_charge_person,create_by,start_expiry_time,end_expiry_time,running_id,record_setting')
            ->where([
                'id' => $data['instrumentId'],
            ])->asArray()->one();
        if ($insInfo['status'] != 1) { // 状态非正常无法运行提示
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip1_1'));
        }
        if ($insInfo['check_situation'] == 2) { // 未校验提示
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip2_2'));
        }
        $data['name'] = $insInfo['name'];
        $data['batch_number'] = $insInfo['batch_number'];
        if ($data['type'] === 'begin') {
            //bug 2036 新增记录，其它两大记录有未结束的进行拦截
            $company_id = \Yii::$app->view->params['curr_company_id'];
            $unFinishedResult = (new InstrumentServer())->unFinishedRecord($data['instrumentId'], 'runningRecord', $company_id);
            if ($unFinishedResult['status'] == 0) {
                return $this->fail($unFinishedResult['info'], 'JSON', ['tipType' => 'popContent']);
            }
            $currentUser = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]); // 默认填写操作人字段
            $data['currentUserName'] = CommentServer::displayUserName($currentUser[0]);
            $data['requireKey'] = ['start_running_time', 'operator']; // 固定必填项 开始运行时间,操作人
            $data['startOperatorTime'] = date("Y-m-d H:i:s"); //运行开始时间为点击按钮的时间 Bug1259
        } else {

            $data['requireKey'] = ['start_running_time', 'end_running_time', 'running_content', 'operator']; // 固定必填项 开始运行时间 结束运行时间 操作内容 操作人
            $dataResult = (new InstrumentServer())->getOperateDataById($data['runningId'], 3);
            $data['dataResult'] = $dataResult['data'];
            $data['startOperatorTime'] = $data['dataResult']['start_operator_time'];
            if ($data['type'] === 'end' && $data['dataResult']['review_conclusion'] == '1') { // add by hkk 2022/6/20  被其他人或实验页面关闭，则提示已被关闭，刷新页面
                return $this->fail(\Yii::t('base', 'record_conflict_tip'));
            }

            // 读取复核人设置，如果开启，读取人员姓名
            $data['needOperateRecordCheck'] = false;
            //如果有复核设置 且 当结束时 或者 结束后再编辑时 才进入复核
            if (!empty($insInfo['record_setting']) &&
                ($data['type'] == 'end' || (!empty($data['dataResult']['end_operator_time']) || !empty($data['dataResult']['end_operator'])) && $data['type'] == 'edit')) {
                $recordSetting = json_decode($insInfo['record_setting'], true);
                if (!empty($recordSetting['operate_check_users'])) {
                    $data['needOperateRecordCheck'] = true;
                    $operateRecordCheckUserIds = $recordSetting['operate_check_users'];

                    // 获取复核人姓名和id字符串
                    $defaultAllUser = (new CenterInterface())->userDetailsByUserIds($operateRecordCheckUserIds);
                    $defaultAllUser = yii\helpers\ArrayHelper::index($defaultAllUser, 'user_id');
                    $data['operateRecordCheckUserNames'] = '';
                    foreach ($recordSetting['operate_check_users'] as $user) {
                        $userName = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        $data['operateRecordCheckUserNames'] = $data['operateRecordCheckUserNames'] . ";" . $userName;
                    }
                    $data['operateRecordCheckUserNames'] = substr($data['operateRecordCheckUserNames'], 1);
                    $data['operateRecordCheckUserIds'] = join(',', $operateRecordCheckUserIds);
                }
            }

            $data['beforeClose'] = $data['type'] == 'edit' && ($data['dataResult']['if_end'] == 0);
        }
        $html = $this->renderAjax('/instrument/instrument_begin_operate_record.php', $data);


        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * Notes: 运行记录开始运行结束页面 实验页面
     * Author: hkk
     * Date: 2020/7/22 11:33
     * @return \common\controllers\json
     */
    public function actionGetOperateRecordPageByExp()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['type'] = $postData['type'];

        // 根据仪器id查询是否结束运行和复核设置
        $insInfo = InstrumentsModel::find()->select(['name', 'batch_number', 'running_id', 'record_setting'])->where([
            'id' => $data['instrumentId'],
        ])->asArray()->one();

        // 根据仪器id 查询运行id
        // 结束为当前的runningId 新增记录runningId为0
        $data['runningId'] = $data['type'] === 'end' ? $postData['runningId'] : 0;
        // $data['type'] = !empty($insInfo['running_id']) ? 'end' : 'begin'; // 有running_id说明需要结束运行
        // 固定隐藏不显示的列(自动填充内容列和复核信息列)
        $data['hiddenKey'] = ['start_operator_time', 'start_operator', 'end_operator_time',
            'end_operator', 'reviewer', 'if_end', 'review_conclusion', 'review_record'];
        // 新增没有记录序号
        $data['orderNumber'] = \Yii::$app->request->post('orderNumber', 0);
        $data['needOperateRecordCheck'] = false;
        $data['addRepairRecordArray'] = [];
        $data['forExp'] = true;
        $data['name'] = $insInfo['name'];
        $data['batch_number'] = $insInfo['batch_number'];
        //获取运行记录的字段
        $data['addRepairRecordArray'] = (new InstrumentServer())->getRecordField($data['instrumentId'], 3);
        $instrumentServer = new InstrumentServer();
        if ($data['type'] === 'begin') {
            //bug 2036 新增记录，其它两大记录有未结束的进行拦截
            $company_id = \Yii::$app->view->params['curr_company_id'];
            $unFinishedResult = $instrumentServer->unFinishedRecord($data['instrumentId'], 'runningRecord', $company_id);
            if ($unFinishedResult['status'] == 0) {
                return $this->fail($unFinishedResult['info'], 'JSON', ['tipType' => 'popContent']);
            }
            $data['requireKey'] = ['start_running_time']; // 固定必填项 开始运行时间
            $currentUser = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]); // 默认填写操作人字段
            $data['currentUserName'] = CommentServer::displayUserName($currentUser[0]);
            $data['startOperatorTime'] = date("Y-m-d H:i:s"); //运行开始时间为点击按钮的时间 Bug1259
        } else {
            $dataResult = $instrumentServer->getOperateDataById($data['runningId'], 3);
            $data['dataResult'] = $dataResult['data'];
            $data['startOperatorTime'] = $data['dataResult']['start_operator_time'];
            if ($data['type'] === 'end' && $data['dataResult']['if_void'] == '1') { //如果已经作废 提示
                return $this->fail(\Yii::t('base', 'record_canceled'));
            }
            if ($data['type'] === 'end' && $data['dataResult']['if_end'] == '1') { // add by hkk 2022/6/20  被其他人或实验页面关闭，则提示已被关闭，刷新页面
                return $this->fail(\Yii::t('base', 'record_conflict_tip'));
            }

            // 读取复核人设置，如果开启，读取人员姓名
            if (!empty($insInfo['record_setting'])) {
                $recordSetting = json_decode($insInfo['record_setting'], true);
                if (!empty($recordSetting['operate_check_users'])) {
                    $data['needOperateRecordCheck'] = true;
                    $operateRecordCheckUserIds = $recordSetting['operate_check_users'];

                    // 获取复核人姓名和id字符串
                    $defaultAllUser = (new CenterInterface())->userDetailsByUserIds($operateRecordCheckUserIds);
                    $defaultAllUser = yii\helpers\ArrayHelper::index($defaultAllUser, 'user_id');
                    $data['operateRecordCheckUserNames'] = '';
                    foreach ($recordSetting['operate_check_users'] as $user) {
                        $userName = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                        $data['operateRecordCheckUserNames'] = $data['operateRecordCheckUserNames'] . ";" . $userName;
                    }
                    $data['operateRecordCheckUserNames'] = substr($data['operateRecordCheckUserNames'], 1);
                    $data['operateRecordCheckUserIds'] = join(',', $operateRecordCheckUserIds);
                }
            }
            $data['requireKey'] = ['start_running_time', 'end_running_time', 'running_content', 'operator']; // 固定必填项 开始运行时间 结束运行时间 操作内容 操作人
        }
        $data['beforeClose'] = false;  // add by hkk 2022/6/16 增加一个结束前编辑标记
        $html = $this->renderAjax('/instrument/instrument_begin_operate_record.php', $data);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * Notes: 运行记录页面/开始运行 结束运行确认 增加编辑记录提交 submit-begin-operate-record
     * Author: hkk
     * Date: 2020/7/20 16:51
     * @return \common\controllers\json
     */
    public function actionSubmitBeginOperateRecord()
    {

        //return $this->fail('nihao','JSON', ['tipType' => 'popContent']);
        $postData = \Yii::$app->getRequest()->post();
        $data = $postData;
        $data['create_by'] = $this->userinfo->id; // 当前填写校验人员

        $saveResult = (new InstrumentServer())->saveOperateRecord($data); //数据库处理
        if ($saveResult['status'] == 0 && $saveResult['info'] == 'timeConflict') {
            return $this->fail($saveResult['data'], 'JSON',['tipType' => 'popContent']);
        }
        $resData = @getVar($saveResult['data'], []);
        if ($saveResult['status'] == 0) {
            return $this->fail($saveResult['info'], 'JSON', ['type' => @getVar($resData['type'], '')]);
        }

        return $this->success(['runningId' => $saveResult['data']['running_id']]);
    }

    /**
     * Notes: 运行记录页面新添加列 add-operate-record-column
     * Author: hkk
     * Date: 2020/7/21 10:41
     * @return \common\controllers\json
     */
    public function actionAddOperateRecordColumn()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['field'] = $postData['field'];
        (new InstrumentServer())->addOperateRecordColumn($data); //数据库处理

        return $this->success([]);
    }

    /**
     * Notes: 导出仪器运行记录
     * Author: hkk
     * Date: 2020/7/22 9:16
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionInstrumentsOperateRecordExport()
    {

        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);

        if (!empty($postData['chooseIds'])) { // add by hkk 2022/8/12  不勾选导出报错
            $where['chooseIds'] = $postData['chooseIds']; // bug#28132-仪器库管理-运行记录，如果有勾选，导出的仅包括勾选的条目 jiangdm 2022/8/9
        }

        $where['instrument_id'] = $postData['instrumentId'];

        // 获取需要导出的数据
        $exportData = (new InstrumentServer())->listInsOperateRecordView($where, $limit, $page, true);
        $keyResult = $exportData['data']['keyResult'];
        $dataResult = $exportData['data']['dataResult'];
        $keyResult['if_void'] = Yii::t('base', 'status');
        $postData['operateRecordArray'][] = 'if_void';
        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        $startColIndex = 0;
        foreach ($keyResult as $field => $thField) {
            if (!empty($thField) && $field !== 'file' && in_array($field, $postData['operateRecordArray'])) {
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($startColIndex) . '1', $thField); // 设置标题栏
                $startRowIndex = 2;
                foreach ($dataResult as $key => $value) {
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($startColIndex) . $startRowIndex, $value[$field]); // 设置内容栏
                    $startRowIndex++;
                }
                $startColIndex++;
            }
        }

        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . 'instrumentsOperateRecord' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instrumentsOperateRecord' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);
    }

    /**
     * Notes:校验记录页面 get-check-record-page
     * Author: hkk
     * Date: 2020/7/23 17:59
     * @return \common\controllers\json
     */
    public function actionGetCheckRecordPage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['instrument_id'] = $postData['instrumentId'];

        // 获取查询的数据
        $instrumentCheckRecordData = (new InstrumentServer())->listInsCheckRecordView($where, $limit, $page);
        $data['keyResult'] = $instrumentCheckRecordData['data']['keyResult'];
        $data['dataResult'] = $instrumentCheckRecordData['data']['dataResult'];
        $data['totalCount'] = $instrumentCheckRecordData['data']['totalCount'];
        $data['limit'] = $limit;
        $data['instrumentId'] = $postData['instrumentId'];
        $insInfo = (new InstrumentServer())->getInsInfoById($postData['instrumentId']);


        $data['name'] = $insInfo['data']['name']; // add by hkk 2022/6/23
        $data['batchNumber'] = $insInfo['data']['batch_number']; // add by hkk 2022/6/23

        $data['isCanWrite'] = \Yii::$app->view->params['instruments_manage_write']; // add by hkk 2022/8/11  查看是否有编辑权限

        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_check_record.php', $data);
            return $this->success([
                'contentHtml' => $file,
                'insName' => $insInfo['data']['name'], // 设备名称
                'insId' => $insInfo['data']['batch_number'], // 设备id
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_check_record_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 新增仪器校验记录弹框页面 2021/4/14 加上编辑校验页面 get-check-record-operate-page
     * Author: hkk
     * Date: 2020/7/20 11:33
     * @return \common\controllers\json
     */
    public function actionGetCheckRecordOperatePage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['addCheckRecordArray'] = $postData['addCheckRecordArray'];
        $data['type'] = $postData['type'];
        $data['hiddenKey'] = ['operator', 'operation_time', 'reviewer', 'if_end', 'review_conclusion', 'review_record', 'review_time']; // 固定隐藏不显示的列  操作人员 (自动填充内容列和复核信息列)
        $data['runningId'] = \Yii::$app->request->post('runningId', 0); // 新增没有记录id
        $data['orderNumber'] = \Yii::$app->request->post('orderNumber', 0); // 新增没有记录序号

        //检验仪器状态
        $instrumentInfo = InstrumentsModel::find()
            ->select('name,batch_number,status')
            ->where([
                'id' => $data['instrumentId'],
            ])->asArray()->one();
        if ($instrumentInfo['status'] != 1) { // 状态非正常
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip1_1'));
        }
        $data['name'] = $instrumentInfo['name'];
        $data['batch_number'] = $instrumentInfo['batch_number'];
        $instrumentServer = new InstrumentServer();
        if ($data['type'] == 'addCheckRecord') {
            //bug 2036 新增记录，其它两大记录有未结束的进行拦截
            $company_id = \Yii::$app->view->params['curr_company_id'];
            $unFinishedResult = $instrumentServer->unFinishedRecord($data['instrumentId'], 'checkRecord', $company_id);
            if ($unFinishedResult['status'] == 0) {
                return $this->fail($unFinishedResult['info'], 'JSON', ['tipType' => 'popContent']);
            }
            $currentUser = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]); // 默认填写操作人字段
            $data['currentUserName'] = CommentServer::displayUserName($currentUser[0]);
            $data['requireKey'] = ['start_check_time', 'check_person']; // 开始校验必填项
            $data['operationTime'] = date("Y-m-d H:i:s");
        }

        if ($data['type'] == 'editBeforeEndCheckRecord') {
            $data['requireKey'] = ['start_check_time', 'check_person']; // 开始校验必填项
            $dataResult = $instrumentServer->getOperateDataById($data['runningId'], 2);
            $data['dataResult'] = $dataResult['data'];
            $data['operationTime'] = $data['dataResult']['operation_time'];
        }

        if ($data['type'] == 'editAfterEndCheckRecord') { //结束后编辑的必填项
            $data['requireKey'] = ['start_check_time', 'end_check_time', 'check_person', 'start_end_expiry_time']; // 固定必填项 开始时间 结束时间 校验人 有效期
            $dataResult = $instrumentServer->getOperateDataById($data['runningId'], 2);
            $data['dataResult'] = $dataResult['data'];
            $data['operationTime'] = $data['dataResult']['operation_time'];
        }

        // 读取复核人设置，如果开启，读取人员姓名
        $data['hasCheckRecordCheck'] = false;
        $data['showCheckRecordChek'] = false;//需不需要显示复核人
        $recordSetting = InstrumentsModel::find()->select('record_setting')->where([
            'id' => $data['instrumentId'],
        ])->asArray()->one();
        if (!empty($recordSetting['record_setting'])) {
            $recordSetting = json_decode($recordSetting['record_setting'], true);
            if (!empty($recordSetting['check_check_users'])) {
                $data['hasCheckRecordCheck'] = true;
                $checkRecordCheckUserIds = $recordSetting['check_check_users'];

                // 获取复核人姓名和id字符串
                $defaultAllUser = (new CenterInterface())->userDetailsByUserIds($checkRecordCheckUserIds);
                $defaultAllUser = yii\helpers\ArrayHelper::index($defaultAllUser, 'user_id');
                $data['checkRecordCheckUserNames'] = '';
                foreach ($recordSetting['check_check_users'] as $user) {
                    $userName = $defaultAllUser[$user] ? CommentServer::displayUserName($defaultAllUser[$user]) : '';
                    $data['checkRecordCheckUserNames'] = $data['checkRecordCheckUserNames'] . ";" . $userName;
                }
                $data['checkRecordCheckUserNames'] = substr($data['checkRecordCheckUserNames'], 1);
                $data['checkRecordCheckUserIds'] = join(',', $checkRecordCheckUserIds);

                if ($data['type'] != 'addCheckRecord') { //如果是编辑状态
                    $checkData = $data['dataResult'];
                    // 如果开始校验时间 结束时间 校验人 校验有效期不为空 则显示
                    $data['showCheckRecordChek'] = !empty($checkData['start_check_time']) && !empty($checkData['end_check_time']) && !empty($checkData['check_person']) && !empty($checkData['start_expiry_time']) && !empty($checkData['end_expiry_time']);

                }
            }
        }

        $html = $this->renderAjax('/instrument/instrument_check_record_add.php', $data);
        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * Notes: 新增校验记录确认 2021/4/14 加上编辑校验页面提交 submit-add-check-record
     * Author: hkk
     * Date: 2020/7/20 16:51
     * @return \common\controllers\json
     */
    public function actionSubmitAddCheckRecord()
    {

        $data = \Yii::$app->getRequest()->post();
        $data['recordData']['file'] = \yii\helpers\Html::decode($data['recordData']['file']);
        $data['operator'] = $this->userinfo->id; // 当前填写校验人员

        $saveResult = (new InstrumentServer())->saveCheckRecord($data); //数据库处理
        if ($saveResult['status'] == 0 && $saveResult['info'] == 'timeConflict') {
            return $this->fail($saveResult['data'], 'JSON',['tipType' => 'popContent']);
        }
        $resData = @getVar($saveResult['data'], []);
        if ($saveResult['status'] == 0) {
            return $this->fail($saveResult['info'], 'JSON', ['type' => @getVar($resData['type'], '')]);
        }

        return $this->success([]);
    }

    /**
     * @Notes:检查是否已经复核通过
     * @return \common\controllers\json
     * <AUTHOR>
     * @DateTime: 2023/12/15 10:17
     */
    public function actionBeforeUndoCheckRecord() {
        $postData = \Yii::$app->getRequest()->post();
        $runningId = $postData['runningId'];
        $recordType = $postData['recordType'];
        if ($recordType == '1') {
            $recordInfo = InstrumentRepairRecordModel::findOne([
                'id' => $runningId,
            ]);
        }
        if ($recordType == '2') {
            $recordInfo = InstrumentCheckRecordModel::findOne([
                'id' => $runningId,
            ]);
        }
        if ($recordType == '3') {
            $recordInfo = InstrumentRunningRecordModel::findOne([
                'id' => $runningId,
            ]);
        }

        if (!empty($recordInfo['review_conclusion']) && $recordInfo['review_conclusion'] != '3') {
            return $this->fail(\Yii::t('base', 'undo_check_conflict_tip'));
        }

        return $this->success([]);

    }


    /**
     * Notes: 撤销校验复核记录
     * Author: hkk
     * Date: 2022/6/14 11:03
     * @return \common\controllers\json
     */
    public function actionUndoCheckRecord()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['runningId'] = $postData['runningId'];
        $data['instrumentId'] = $postData['instrumentId'];
        $data['undoReason'] = $postData['undoReason'];
        $data['recordType'] = $postData['recordType'];

        $saveResult = (new InstrumentServer())->undoCheckRecord($data); //数据库处理
        if ($saveResult['status'] == 0) {
            return $this->fail($saveResult['info']);
        }

        return $this->success([]);
    }

    /**
     * Notes: 校验记录页面新添加列
     * Author: hkk
     * Date: 2020/7/21 10:41
     * @return \common\controllers\json
     */
    public function actionAddCheckRecordColumn()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['field'] = $postData['field'];
        (new InstrumentServer())->addCheckRecordColumn($data); //数据库处理

        return $this->success([]);
    }

    /**
     * Notes: 导出仪器校验记录
     * Author: hkk
     * Date: 2020/7/22 9:16
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionInstrumentsCheckRecordExport()
    {

        $postData = \Yii::$app->getRequest()->post();
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['instrument_id'] = $postData['instrumentId'];

        // 获取需要导出的数据
        $exportData = (new InstrumentServer())->listInsCheckRecordView($where, $limit, $page, true);
        $keyResult = $exportData['data']['keyResult'];
        $dataResult = $exportData['data']['dataResult'];
        $keyResult['if_void'] = Yii::t('base', 'status');
        $postData['recordArray'][] = 'if_void';
        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        $startColIndex = 0;
        foreach ($keyResult as $field => $thField) {
            if (!empty($thField) && $field !== 'file' && in_array($field, $postData['recordArray'])) { //文件栏不导出
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($startColIndex) . '1', $thField); // 设置标题栏
                $startRowIndex = 2;
                foreach ($dataResult as $index => $data) {
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($startColIndex) . $startRowIndex, $data[$field]); // 设置内容栏
                    $startRowIndex++;
                }
                $startColIndex++;
            }
        }

        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . 'instrumentsCheckRecord' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instrumentsCheckRecord' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);
    }

    /**
     * Notes: 获取审批复核页面点击查看 运行/校验/维修 三大记录详情页
     * Author: hkk
     * Date: 2021/4/19 15:11
     * @return \common\controllers\json
     */
    public function actionGetRecordPage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['runningId'] = $postData['runningId']; // 操作id
        $data['approveId'] = $postData['approveId']; // 复核id
        $dataResult = (new InstrumentServer())->getInstrumentRecordDataById($data['runningId'], $data['approveId']);
        $data = $dataResult['data'];
        $html = $this->renderAjax('/instrument/instrument_record_page.php', $data);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * Notes:仪器记录字段配置页面弹出获取数据
     * Author: hkk
     * Date: 2020/7/17 17:59
     * @return \common\controllers\json
     */
    public function actionGetInstrumentRecordSettingPage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $data['chooseIds'] = $postData['chooseIds'];

        // 缓存读取所有可见鹰群
        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $data['visibleGroups'] = $visibleGroups;

        // 缓存读取所有可见部门
        $visibleDepartments = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $data['visibleDepartments'] = $visibleDepartments;

        // 用于查询可见鹰群或部门
        $currentUserGroupIds = array_column($data['visibleGroups'], 'id');
        $currentUserDepartmentIds = array_column($data['visibleDepartments'], 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;

        $where['viewAuth'] = '2'; // 默认能查看可见鹰群和部门仪器
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        } else if (\Yii::$app->view->params['instruments_manage_read_my']) {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        } else {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }

        $query = InstrumentsModel::find()->select(['id', 'name', 'batch_number']);

        if ($where['viewAuth'] == '2') { // add by hkk 2022/6/27 只能查看可见鹰群和我的部门仪器
            $FindStr = "";
            foreach ($where['groups'] as $key => $groupId) {
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            foreach ($where['departments'] as $key => $departmentId) {
                $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", departmentIds) or ";
                $query->addParams([':departmentId' . $key => $departmentId]);
            }
            $FindStr = substr($FindStr, 0, -4);
            $query->andWhere(new Expression($FindStr));
        }

        // 查询所有仪器资料
        $data['instruments'] = $query->asArray()->all();

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_record_field_config.php', $data);

        return $this->success(['file' => $file]);
    }

    /**
     * Notes:仪器记录字段配置页面切换仪器获取仪器的配置数据
     * Author: hkk
     * Date: 2021/4/22 11:07
     * @return \common\controllers\json
     */
    public function actionGetInstrumentRecordSettingData()
    {
        $instrumentServer = new InstrumentServer();
        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $instrumentId = $postData['instrumentId'];

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $instrumentId = $postData['instrumentId'];
        // 查询所有仪器记录
        $data['operateRecordField'] = $instrumentServer->getRecordField($instrumentId, 3);
        $data['repairRecordField'] = $instrumentServer->getRecordField($instrumentId, 1);
        $data['checkRecordField'] = $instrumentServer->getRecordField($instrumentId, 2);

        return $this->success($data);
    }

    /**
     * Notes:仪器记录字段配置页面提交操作
     * Author: hkk
     * Date: 2021/4/22 17:25
     * @return \common\controllers\json
     */
    public function actionSubmitInstrumentRecordSetting()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $instrumentId = $postData['instrumentId'];
        $chooseIds = explode(",", $postData['chooseIds']);
        $changeOperateConfig = $postData['changeOperateConfig'];
        $changeRepairConfig = $postData['changeRepairConfig'];
        $changeCheckConfig = $postData['changeCheckConfig'];

        if ($changeOperateConfig == 'true') {
            $operateRecordFields = InstrumentRunningRecordExtendFieldModel::find()->where(['instrument_id' => $instrumentId])->asArray()->one();
            $changeArr = [];
            $changeArr['field_sort'] = @getVar($operateRecordFields['field_sort']);
            for ($i = 1; $i <= 50; $i++) {
                $changeArr['extend_field_' . $i] = !emptyExclude0($operateRecordFields['extend_field_' . $i]) ? $operateRecordFields['extend_field_' . $i] : null;
            }

            // 若原仪器没有点开过运行记录，需要新增记录,若有则时更新记录
            $updateIds = [];
            foreach ($chooseIds as $originInsId) {
                $keyResult = InstrumentRunningRecordExtendFieldModel::find()->where(['instrument_id' => $originInsId])->asArray()->one();
                if (empty($keyResult)) {
                    $model = new InstrumentRunningRecordExtendFieldModel();
                    $changeArr['instrument_id'] = $originInsId;
                    $model->setAttributes($changeArr);
                    $model->save();
                } else {
                    $updateIds[] = $originInsId;
                }
            }
            if (count($updateIds) > 0) {
                InstrumentRunningRecordExtendFieldModel::updateAll($changeArr, ['instrument_id' => $updateIds]);
            }
        }

        if ($changeRepairConfig == 'true') {
            $repairRecordFields = InstrumentRepairRecordExtendFieldModel::find()->where(['instrument_id' => $instrumentId])->asArray()->one();
            $changeArr = [];
            $changeArr['field_sort'] = @getVar($operateRecordFields['field_sort']);
            for ($i = 1; $i <= 50; $i++) {
                $changeArr['extend_field_' . $i] = !emptyExclude0($repairRecordFields['extend_field_' . $i]) ? $repairRecordFields['extend_field_' . $i] : null;
            }

            // 若原仪器没有点开过维保记录，需要新增记录,若有则时更新记录
            $updateIds = [];
            foreach ($chooseIds as $originInsId) {
                $keyResult = InstrumentRepairRecordExtendFieldModel::find()->where(['instrument_id' => $originInsId])->asArray()->one();
                if (empty($keyResult)) {
                    $model = new InstrumentRepairRecordExtendFieldModel();
                    $changeArr['instrument_id'] = $originInsId;
                    $model->setAttributes($changeArr);
                    $model->save();
                } else {
                    $updateIds[] = $originInsId;
                }
            }
            if (count($updateIds) > 0) {
                InstrumentRepairRecordExtendFieldModel::updateAll($changeArr, ['instrument_id' => $updateIds]);
            }
        }

        if ($changeCheckConfig == 'true') {
            $checkRecordFields = InstrumentCheckRecordExtendFieldModel::find()->where(['instrument_id' => $instrumentId])->asArray()->one();
            $changeArr = [];
            $changeArr['field_sort'] = @getVar($operateRecordFields['field_sort']);
            for ($i = 1; $i <= 50; $i++) {
                $changeArr['extend_field_' . $i] = !emptyExclude0($checkRecordFields['extend_field_' . $i]) ? $checkRecordFields['extend_field_' . $i] : null;
            }

            // 若原仪器没有点开过维修记录，需要新增记录,若有则时更新记录
            $updateIds = [];
            foreach ($chooseIds as $originInsId) {
                $keyResult = InstrumentCheckRecordExtendFieldModel::find()->where(['instrument_id' => $originInsId])->asArray()->one();
                if (empty($keyResult)) {
                    $model = new InstrumentCheckRecordExtendFieldModel();
                    $changeArr['instrument_id'] = $originInsId;
                    $model->setAttributes($changeArr);
                    $model->save();
                } else {
                    $updateIds[] = $originInsId;
                }
            }
            if (count($updateIds) > 0) {
                InstrumentCheckRecordExtendFieldModel::updateAll($changeArr, ['instrument_id' => $updateIds]);
            }
        }

        return $this->success([]);
    }

    /**
     * Notes:获取仪器使用统计图表页面
     * Author: hkk
     * Date: 2021/4/23 14:05
     * @return \common\controllers\json
     */
    public function actionGetUseGraphPage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $where['keywords'] = \Yii::$app->request->post('instrument_keywords', '');
        $where['category'] = \Yii::$app->request->post('instrument_type', '');
        $where['creator'] = \Yii::$app->request->post('instrument_create_user', '');
        $where['supplier'] = \Yii::$app->request->post('instrument_supplier', '');
        $where['position'] = \Yii::$app->request->post('instrument_position', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['open_time'] = \Yii::$app->request->post('instrument_open_time', 24);
        $where['instrument_type'] = \Yii::$app->request->post('instrument_type', '');


        // 查询仪器统计数据
        $instrumentList = (new InstrumentServer())->getInstrumentUseStatistics($where);
        $data['usageData'] = json_encode($instrumentList['data']);

        // 查询该企业下所有用户,用于菜单搜索筛选
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
        $data['all_user'] = isset($userList['list']) ? $userList['list'] : [];

        // 查询仪器分类用于筛选
        $data['typeList'] = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);

        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_use_graph.php', $data);
            return $this->success([
                'contentHtml' => $file,
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_use_graph_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 导出仪器使用统计
     * Author: hkk
     * Date: 2021/5/6 9:16
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionInstrumentsUsageStatisticsExport()
    {


        $usageData = \Yii::$app->request->post('usageData', []);
        $fieldArr = \Yii::$app->request->post('fieldArr', []);; // 标题字段
        $nameArr = !empty($usageData['nameArr']) ? $usageData['nameArr'] : []; // 仪器名称
        $dataArr = !empty($usageData['dataArr']) ? $usageData['dataArr'] : []; // 使用率
        $timesArr = !empty($usageData['timesArr']) ? $usageData['timesArr'] : []; // 使用次数
        $memberArr = !empty($usageData['memberArr']) ? $usageData['memberArr'] : []; // 使用人数
        $checkTimesArr = !empty($usageData['checkTimesArr']) ? $usageData['checkTimesArr'] : [];  // 校验次数
        $repairTimesArr = !empty($usageData['repairTimesArr']) ? $usageData['repairTimesArr'] : []; // 维修次数
        $allDataArr = [$nameArr, $dataArr, $timesArr, $memberArr, $checkTimesArr, $repairTimesArr];

        // 新建excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        $startColIndex = 0;
        for ($i = 0; $i < count($fieldArr); $i++) {
            $field = $fieldArr[$i];
            if ($field !== null) {

                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($startColIndex) . '1', $field); // 写入标题栏
                $startRowIndex = 2;
                $valueArr = $allDataArr[$i];
                foreach ($valueArr as $key => $value) { // 写入每行内容栏
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($startColIndex) . $startRowIndex, $value);
                    $startRowIndex++;
                }

                $startColIndex++;
            }
        }

        // 保存excel文件
        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . 'instruments_usage_statistics' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instruments_usage_statistics' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);
    }

    /**
     * Notes: 导出仪器使用统计(单个仪器)
     * Author: hkk
     * Date: 2021/5/6 9:16
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionInstrumentUsageStatisticsExport()
    {


        $usageData = \Yii::$app->request->post('usageData', []);
        $type = \Yii::$app->request->post('type', 'date');; // 类型


        // 新建excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        if ($type == 'date') { // 日期时长表
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('A1', \Yii::t('base', 'use_date')); // 写入标题栏 日期
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('B1', \Yii::t('base', 'use_time')); // 写入标题栏 时长
            foreach ($usageData as $key => $item) { // 写入每行的内容
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue('A' . ($key + 2), $item[0]);
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue('B' . ($key + 2), $item[1]);
            }
        } else { // 人员时长表

            $fieldArr = \Yii::$app->request->post('fieldArr', []);; // 标题字段
            $nameArr = !empty($usageData['nameArr']) ? $usageData['nameArr'] : []; // 仪器名称
            $dataArr = !empty($usageData['dataArr']) ? $usageData['dataArr'] : []; // 使用率
            $timeArr = !empty($usageData['timeArr']) ? $usageData['timeArr'] : []; // 使用时长
            $timesArr = !empty($usageData['timesArr']) ? $usageData['timesArr'] : []; // 使用次数
            $allDataArr = [$nameArr, $dataArr, $timeArr, $timesArr];
            $startColIndex = 0;
            for ($i = 0; $i < count($fieldArr); $i++) {
                $field = $fieldArr[$i];
                if ($field !== null) {

                    $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($startColIndex) . '1', $field); // 写入标题栏
                    $startRowIndex = 2;
                    $valueArr = $allDataArr[$i];
                    foreach ($valueArr as $key => $value) { // 写入每行内容栏
                        $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($startColIndex) . $startRowIndex, $value);
                        $startRowIndex++;
                    }
                    $startColIndex++;
                }
            }
        }


        // 保存excel文件
        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . 'instrument_usage_statistics' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instrument_usage_statistics' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);
    }

    /**
     * Notes:获取仪器预约统计图表页面
     * Author: hkk
     * Date: 2021/4/26 14:05
     * @return \common\controllers\json
     */
    public function actionGetBookGraphPage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $where['keywords'] = \Yii::$app->request->post('instrument_keywords', '');
        $where['category'] = \Yii::$app->request->post('instrument_type', '');
        $where['creator'] = \Yii::$app->request->post('instrument_create_user', '');
        $where['supplier'] = \Yii::$app->request->post('instrument_supplier', '');
        $where['position'] = \Yii::$app->request->post('instrument_position', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['open_time'] = \Yii::$app->request->post('instrument_open_time', 24);
        $where['instrument_type'] = \Yii::$app->request->post('instrument_type', '');


        // 查询预约统计数据
        $instrumentList = (new InstrumentServer())->getInstrumentBookStatistics($where);
        $data['usageData'] = json_encode($instrumentList['data']);

        //查询该企业下所有用户,用于菜单搜索筛选
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
        $data['all_user'] = isset($userList['list']) ? $userList['list'] : [];

        // 查询仪器分类用于筛选
        $data['typeList'] = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);

        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_book_graph.php', $data);
            return $this->success([
                'contentHtml' => $file,
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_book_graph_page.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 导出仪器预约统计
     * Author: hkk
     * Date: 2021/5/6 9:16
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionInstrumentsBookStatisticsExport()
    {


        $usageData = \Yii::$app->request->post('usageData', []);
        $fieldArr = \Yii::$app->request->post('fieldArr', []);; // 标题字段
        $nameArr = !empty($usageData['nameArr']) ? $usageData['nameArr'] : []; // 仪器名称
        $dataArr = !empty($usageData['dataArr']) ? $usageData['dataArr'] : []; // 使用率
        $timesArr = !empty($usageData['timesArr']) ? $usageData['timesArr'] : []; // 使用次数
        $memberArr = !empty($usageData['memberArr']) ? $usageData['memberArr'] : []; // 使用人数

        $allDataArr = [$nameArr, $dataArr, $timesArr, $memberArr];

        // 新建excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        $startColIndex = 0;
        for ($i = 0; $i < count($fieldArr); $i++) {
            $field = $fieldArr[$i];
            if ($field !== null) {

                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(IntToChr($startColIndex) . '1', $field); // 写入标题栏
                $startRowIndex = 2;
                $valueArr = $allDataArr[$i];
                foreach ($valueArr as $key => $value) { // 写入每行内容栏
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($startColIndex) . $startRowIndex, $value);
                    $startRowIndex++;
                }

                $startColIndex++;
            }
        }

        // 保存excel文件
        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $file = \Yii::getAlias('@filepath') . DS . 'instruments_book_statistics' . '.xlsx';

        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'instruments_book_statistics' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);
    }

    /**
     * Notes:获取单个仪器使用情况
     * Author: hkk
     * Date: 2021/4/26 14:05
     * @return \common\controllers\json
     */
    public function actionGetSingleUsagePage()
    {

        // 获取查询条件
        $postData = \Yii::$app->getRequest()->post();
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['open_time'] = \Yii::$app->request->post('instrument_open_time', 24);
        $where['instrument_display_type'] = \Yii::$app->request->post('instrument_display_type', 2); // 默认日期时长表


        $instrumentList = (new InstrumentServer())->getInstrumentSingleUsageStatistics($postData['instrumentId'], $where);
        $data['usageData'] = json_encode($instrumentList['data']);


        $insInfo = (new InstrumentServer())->getInsInfoById($postData['instrumentId']);
        $data['instrumentId'] = $insInfo['data']['id'];

        $data['name'] = $insInfo['data']['name']; // add by hkk 2022/6/23
        $data['batchNumber'] = $insInfo['data']['batch_number']; // add by hkk 2022/6/23

        // 第一次渲染，更新整个页面
        if ($postData['needUpdateAllPage'] === "yes") {
            $file = $this->renderAjax('/instrument/instrument_use_graph_single.php', $data);
            return $this->success([
                'contentHtml' => $file,
                'insName' => $insInfo['data']['name'], // 设备名称
                'insId' => $insInfo['data']['batch_number'], // 设备id
            ]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/instrument/instrument_use_graph_page_single.php', $data);
        return $this->success(['file' => $file]);
    }

    /**
     * Notes: 仪器增加自定义的新列
     * Author: hkk
     * Date: 2020/7/21 10:41
     * @return \common\controllers\json
     */
    public function actionAddInstrumentColumn()
    {

        $result = (new InstrumentServer())->addInstrumentColumn(); //数据库处理
        if ($result['status'] == 0) {
            return $this->fail($result['info']);
        }

        return $this->success($result['data']);
    }

    /**
     * Notes: 增加字段配置存储
     * Author: hkk
     * Date: 2022/8/9 14:45
     * @return \common\controllers\json
     */
    public function actionGetInstrumentFieldConfig()
    {

        $result = (new InstrumentServer())->viewInstrumentFieldConfig();
        if ($result['status'] == 0) {
            return $this->fail($result['info']);
        }
        return $this->success($result['data']);
    }



    /**
     * Notes: 设置字段配置存储
     * Author: hkk
     * Date: 2022/8/9 14:45
     * @return \common\controllers\json
     */
    public function actionSetInstrumentFieldConfig()
    {

        $fieldConfig = \Yii::$app->getRequest()->post('fieldConfig', '');
        $result = (new InstrumentServer())->setInstrumentFieldConfig($fieldConfig); //数据库处理
        if ($result['status'] == 0) {
            return $this->fail($result['info']);
        }
        return $this->success([]);
    }


    /**
     * Notes:批量编辑弹出页面
     * Author: hkk
     * Date: 2021/6/1 16:22
     * @return \common\controllers\json
     */
    public function actionGetBatchEditPage()
    {
        $postData = \Yii::$app->getRequest()->post();
        $data = [];
        $html = $this->renderAjax('/instrument/instrument_batch_edit_page.php', $data);
        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * Notes:批量编辑导出excel
     * Author: hkk
     * Date: 2019/11/11 10:45
     */
    public function actionExportBatchEditExcel()
    {

        // 选中结构
        $unCheckedField = \Yii::$app->request->post('unCheckedField', '');
        $chooseIds = \Yii::$app->request->post('chooseIds', []);
        $postData = \Yii::$app->getRequest()->post();

        // 筛选条件
        if (!empty($chooseIds)) {
            $where['chooseIds'] = $chooseIds;
            $where['viewAuth'] = '1'; // add by hkk 2022/8/3  不限其他权限
            $where['status'] = \Yii::$app->request->post('instrument_status', '');
        } else {
            $where['name'] = \Yii::$app->request->post('instrument_name', '');
            $where['status'] = \Yii::$app->request->post('instrument_status', '');
            $where['check_situation'] = \Yii::$app->request->post('instrument_check', '');
            $where['check_status'] = \Yii::$app->request->post('instrument_check', '');
            $where['create_by'] = \Yii::$app->request->post('instrument_create_user', '');
            $where['responsible_person'] = \Yii::$app->request->post('instrument_responsible_user', '');
            $where['in_charge_person'] = \Yii::$app->request->post('instrument_in_charge_user', '');
            $where['maintenance_person'] = \Yii::$app->request->post('instrument_maintenance_user', '');
            $where['position'] = \Yii::$app->request->post('instrument_position', '');
            $where['supplier'] = \Yii::$app->request->post('instrument_supplier', '');
            $where['start_time'] = \Yii::$app->request->post('start_time', '');
            $where['end_time'] = \Yii::$app->request->post('end_time', '');
            $where['instrument_type'] = \Yii::$app->request->post('instrument_type', ''); // add by hkk 2021/4/28  类别筛选

            // 获取查看权限
            $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);

            // 用于查询我加入的鹰群或部门
            $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
            $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
            $where['groups'] = $currentUserGroupIds;
            $where['departments'] = $currentUserDepartmentIds;
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器

            if ($userAuth['company_feature']['view_instruments_manage']) {
                $where['viewAuth'] = '1'; // 能查看所有仪器
            }
        }

        // 获取查询的数据
        $instrumentsData = (new InstrumentServer())->listInstrumentsView($where, null, null, true);


        // 解析隐藏不导出的字段
        $hideDefineField = [];
        $hideGeneralField = [];
        $unCheckedField = explode(',', $unCheckedField);


        // 构造标题数据，过滤隐藏字段，分通用和自定义字段
        $typeString = '';
        $defineField = [];
        $generalField = [];
        $hideExcelField = []; // excel 第一行的隐藏字段
        $type_list = $instrumentsData['type_list'];
        $defineFields = $instrumentsData['defineFields'];

        // 需要隐藏的字段，无论是否是有效字段
        $defaultHideFields = [
            'picture', 'create_by', 'start_expiry_time', 'end_expiry_time', 'start_check_time', 'end_check_time',
            'start_running_time', 'end_running_time', 'repair_start_time', 'repair_end_time', 'files', 'create_time'
        ];
        // add by hkk 2022/8/10  增加字段配置过滤
        // 查询字段配置 add by hkk 2022/8/10
        $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
        $fieldConfigShowFields = explode(',', $fieldConfig['data']['showFields']);
        array_unshift($fieldConfigShowFields, 'instrument_id');
        foreach ($type_list as $type) {
            $typeString .= ($type['dict_value'] . ' ');
        }
        foreach ($fieldConfigShowFields as $key => $field) {
            if (!in_array($field, $unCheckedField) && !in_array($field, $defaultHideFields)) {
                $enField = $field;
                if ($field == 'instrument_id') {
                    $enField = 'instrument_id_excel';
                }
                if ($field == 'name') {
                    $enField = 'instrument_name_excel';
                }
                if ($field == 'status') {
                    $enField = 'instrument_status_excel';
                }
                if ($field == 'groupIds') {
                    $enField = 'instrument_group_excel';
                }
                if ($field == 'departmentIds') {
                    $enField = 'instrument_department_excel';
                }
                if ($field == 'check_situation') {
                    $enField = 'instrument_check_excel';
                }
                if ($field == 'response_person' || $field == 'responsible_person') {
                    $enField = 'instrument_response_excel';
                }
                if ($field == 'person_in_charge' || $field == 'in_charge_person') {
                    $enField = 'instrument_charge_excel';
                }
                if ($field == 'maintainer' || $field == 'maintenance_person') {
                    $enField = 'instrument_maintainer_excel';
                }
                if ($field == 'instrument_type') {
                    $generalField[] = Yii::t('base', $enField) . '(' . trim($typeString) . ')';
                } else {
                    $generalField[] = Yii::t('base', $enField);
                }

                $hideExcelField[] = $field;

                if (substr($field, 0, 5) == 'field') {
                    if (!in_array($field, $unCheckedField)) {
                        $generalField[count($generalField) - 1] = $defineFields[$field];
                        $defineString = str_replace("field", "define_", $field);
                        $hideExcelField[count($generalField) - 1] = $defineString;
                    }
                }


            }
        }


        $keyResult = $generalField;


        // 新建excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 1 写入隐藏行字段
        foreach ($hideExcelField as $key => $title) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key) . '1', $title); // 设置隐藏行
        }

        // 2 写入标题字段
        foreach ($keyResult as $key => $title) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key) . '2', $title); // 设置标题字段
        }


        // 3 写入内容字段
        $instrumentList = $instrumentsData['instruments_list'];

        foreach ($instrumentList as $row => $insItem) {
            foreach ($hideExcelField as $colIndex => $keyItem) {
                $value = '';
                switch ($keyItem) {
                    case "instrument_id":
                        $value = $insItem['id'];
                        break;
                    case "status":
                        $value = $insItem['status_string'];
                        break;
                    case "check_situation":
                        if ($insItem['check_situation'] != '0') {
                            $value = \Yii::t('base', 'needCheck');
                        } else {
                            $value = $insItem['check_situation_string'];
                        }
                        break;
                    case "groupIds":
                        $value = $insItem['group_name_string'];
                        break;
                    case "departmentIds":
                        $value = $insItem['department_name_string'];
                        break;
                    case "response_person":
                    case "responsible_person":
                        $value = $insItem['responsible_person_string'];
                        break;
                    case "person_in_charge":
                    case "in_charge_person":
                        $value = $insItem['in_charge_person_string'];
                        break;
                    case "maintainer":
                    case "maintenance_person":
                        $value = $insItem['maintenance_person_string'];
                        break;
                    case "data_type":
                        $value = $insItem['data_type_string'];
                        break;
                    default:
                        if (preg_match('/^define\_(\d+)/', $keyItem, $matches)) {
                            $value = !empty($insItem['data' . $matches[1]]) ? $insItem['data' . $matches[1]] : '';
                        } else {
                            $value = !empty($insItem[$keyItem]) ? $insItem[$keyItem] : '';
                        }
                        break;
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($colIndex) . ($row + 3), $value);
            }
        }


        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objPHPExcel->getActiveSheet()->getRowDimension(1)->setRowHeight(0); // 隐藏第一行
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        $file = \Yii::getAlias('@filepath') . DS . 'batch_edit_instruments' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'batch_edit_instruments' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);
    }

    /**
     * Notes: 批量编辑 上传excel
     * Author: hkk
     * Date: 2021/6/7 16:52
     * @return \common\controllers\json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionSubmitBatchEdit()
    {

        $data = \Yii::$app->request->post();
        $unCheckedField = \Yii::$app->request->post('unCheckedField', '');
        $currentPath = $data['dep_path'];
        $currentName = $data['save_name'];
        $filename = \Yii::getAlias('@filepath') . DS . $currentPath . DS . $currentName;


        $arr = pathinfo($filename);
        $ext = $arr['extension'];

        //需要手工加载这个类
        require_once dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'Excel' . DIRECTORY_SEPARATOR . 'PHPExcel.php';

        // 检验和状态数组
        $checkArr = [
            '无需校验' => 0,
            '需要校验' => 1,
            'no need for validation' => 0,
            'need validation' => 1,
        ];
        $statusArr = [
            '正常' => 1,
            '停用' => 2,
            '报废' => 4,
            '维修中' => 3,
            '已删除' => 0,
            'normal' => 1,
            'suspend use' => 2,
            'scrap' => 4,
            'repairing' => 3,
            'deleted' => 0,
        ];
        $company_id = \Yii::$app->view->params['curr_company_id'];

        // 获取所有鹰群及鹰群id
        $userList = [];
        $userListInfo = [];
        $allGroupIdsString = '';
        $allDepartmentIdsString = '';
        $originInstrumentIds = []; // 存储上传的所有仪器id用于记录上传前的数据
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id); // 用户所属鹰群
        $departmentList = (new CenterInterface())->getDepartmentListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 所在部门
        if (isset($departmentList['list']) && !empty($departmentList['list'])) {
            $departmentList = $departmentList['list'];
        } else {
            $departmentList = [];
        }

        $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1])->asArray()->one();

        foreach ($groupList as $group) {
            $allGroupIdsString .= (',' . $group['id']);
        }
        $allGroupIdsString = substr($allGroupIdsString, 1);
        $groupList = array_column($groupList, 'id', 'group_name'); // 鹰群名数组

        foreach ($departmentList as $department) {
            $allDepartmentIdsString .= (',' . $department['id']);
        }
        $allDepartmentIdsString = substr($allDepartmentIdsString, 1);
        $departmentList = array_column($departmentList, 'id', 'department_name'); // 鹰群名数组

        // 获取分类信息
        $typeList = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);
        $typeList = array_column($typeList, 'dict_value');

        //读取excel文件
        if (in_array($ext, array('xls', 'xlsx'))) {

            $fileType = \PHPExcel_IOFactory::identify($filename); //文件名自动判断文件类型
            $excelReader = \PHPExcel_IOFactory::createReader($fileType);

            $phpexcel = $excelReader->load($filename)->getSheet(0); //载入文件并获取第一个sheet
            $total_line = $phpexcel->getHighestRow(); //总行数 1-10-...
            $total_column = $phpexcel->getHighestColumn(); //总列数 A-I-..
            ++$total_column;

            if ($total_line > 2) {

                // 根据第一行标题判断是否是需要的批量添加仪器表
                $data1 = array(); // 字段key列
                $row1 = 1;
                for ($column = 'A'; $column != $total_column; $column++) { //遍历列
                    $data1[] = trim($phpexcel->getCell($column . $row1)->getValue());
                }

                // 简单校验
                if ($data1[0] != "instrument_id") {
                    return $this->fail(\Yii::t('base', 'excel_wrong_format'));
                }

                // 先遍历首列记录仪器ids
                for ($row = 3; $row <= $total_line; $row++) {
                    $instrumentId = trim($phpexcel->getCell('A' . $row)->getValue());
                    if (!empty($instrumentId)) {
                        $originInstrumentIds[] = $instrumentId;
                    }
                }

                // 生成编辑前的EXCEL,返回路径
                $instrumentServer = new InstrumentServer();
                $originPath = $instrumentServer->generalBatchEditOriginExcel($originInstrumentIds, $unCheckedField);

                // 开始导入数据行，并存痕迹
                $failCnt = 0;
                $failInfo = [];

                $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
                $elnPaidItems = $companyData['data']['elnPaidItems'];
                $inscadaNumAll = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);

                $instrumentNum = InstrumentsModel::find()
                    ->where(['data_type' => 1])
                    ->orWhere(['data_type' => 2])
                    ->asArray()->all();
                $inscadaNum = count($instrumentNum);
                for ($row = 3; $row <= $total_line; $row++) { // 遍历行 具体的仪器数据
                    $data = array();
                    for ($column = 'A'; $column != $total_column; $column++) { //遍历列
                        $data[] = trim($phpexcel->getCell($column . $row)->getValue());
                    }
                    if (!empty($data[0])) {
                        $instrumentData = [];
                        $defineFieldIndex = [];
                        $failInfo[$row] = '';
                        foreach ($data1 as $index => $field) {
                            switch ($field) {
                                case "instrument_id":
                                    $instrumentData['id'] = $data[$index];
                                    break;
                                case "instrument_type":
                                    $instrumentData['instrument_type'] = in_array($data[$index], $typeList) ? $data[$index] : '';
                                    break;
                                case "batch_number":
                                    if (!empty($data[$index])) {
                                        $repeatBatchNumber = $instrumentServer->getRepeatBatchNumber($data[$index], $instrumentData['id']);
                                    }
                                    if (empty($repeatBatchNumber) || empty($data[$index])) {
                                        $instrumentData['batch_number'] = $data[$index];
                                    } else {
                                        $failInfo[$row] .= \Yii::t('base', 'batch_number_conflict');
                                    }
                                    break;
                                case "status":
                                    if (array_key_exists($data[$index], $statusArr)) {
                                        $instrumentData['status'] = $statusArr[$data[$index]];
                                    } else {
                                        $failInfo[$row] .= \Yii::t('base', 'status') . \Yii::t('base', 'batch_import_tip');
                                    }
                                    break;
                                case "check_situation":
                                    if (array_key_exists($data[$index], $checkArr)) {
                                        $instrumentData['check_status'] = $checkArr[$data[$index]];
                                    } else {
                                        $failInfo[$row] .= \Yii::t('base', 'check') . \Yii::t('base', 'batch_import_tip');
                                    }
                                    break;
                                case "groupIds":
                                    if (!empty($data[$index])) {
                                        $groupIdsString = '';
                                        $groupArr = explode(';', $data[$index]);
                                        foreach ($groupArr as $groupName) {
                                            if (!empty($groupList[$groupName])) {
                                                $groupIdsString .= (',' . $groupList[$groupName]);
                                            }
                                        }
                                        if (!empty($groupIdsString)) {
                                            $instrumentData['groupIds'] = substr($groupIdsString, 1);
                                        } else {
                                            $failInfo[$row] .= \Yii::t('base', 'group') . \Yii::t('base', 'batch_import_tip');
                                        }
                                    } else {
                                        $instrumentData['groupIds'] = '';
                                    }
                                    break;
                                case "departmentIds":
                                    if (!empty($data[$index])) {
                                        $departmentIdsString = '';
                                        $departmentArr = explode(';', $data[$index]);

                                        foreach ($departmentArr as $departmentName) {
                                            if (!empty($departmentList[$departmentName])) {
                                                $departmentIdsString .= (',' . $departmentList[$departmentName]);
                                            }
                                        }

                                        if (!empty($departmentIdsString)) {
                                            $instrumentData['departmentIds'] = substr($departmentIdsString, 1);
                                        } else {
                                            $failInfo[$row] .= \Yii::t('base', 'department') . \Yii::t('base', 'batch_import_tip');
                                        }
                                    } else {
                                        $instrumentData['departmentIds'] = '';
                                    }
                                    break;
                                case "response_person":
                                case "responsible_person": // 责任人字段名与导出的文件保持一致，不然存到数据库的值不对，显示不出来 jiangdm 2023/3/30
                                    if (empty($userList)) { // 查询用户名数组,只查一次
                                        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
                                        $userList = isset($userList['list']) ? $userList['list'] : [];
                                        $userListInfo = yii\helpers\ArrayHelper::index($userList, 'id');
                                        $userList = array_column($userList, 'id', 'name'); //  用户名数组
                                    }
                                    $userIdsString = '';
                                    $userArr = explode(';', $data[$index]);
                                    foreach ($userArr as $userName) {

                                        // add by hkk 2022/6/24  有括号兼容括号内名称
                                        if (preg_match('/\(([^)]*)\)/', $userName, $matches) && !empty($matches[1])) {
                                            $userName = $matches[1];
                                        }

                                        if (!empty($userList[$userName])) {
                                            $userIdsString .= (',' . $userList[$userName]);
                                        }
                                    }
                                    if (!empty($userIdsString)) {
                                        $instrumentData['responsible_person'] = substr($userIdsString, 1);
                                    } elseif (!empty($data[$index])) {
                                        $failInfo[$row] .= \Yii::t('base', 'response_person') . \Yii::t('base', 'batch_import_tip');
                                    }
                                    break;
                                case "in_charge_person":
                                case "person_in_charge":
                                    if (empty($userList)) {
                                        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
                                        $userList = isset($userList['list']) ? $userList['list'] : [];
                                        $userListInfo = yii\helpers\ArrayHelper::index($userList, 'id');
                                        $userList = array_column($userList, 'id', 'name'); //  用户名数组
                                    }
                                    $userIdsString = '';
                                    $userArr = explode(';', $data[$index]);
                                    foreach ($userArr as $userName) {
                                        // add by hkk 2022/6/24  有括号兼容括号内名称
                                        if (preg_match('/\(([^)]*)\)/', $userName, $matches) && !empty($matches[1])) {
                                            $userName = $matches[1];
                                        }
                                        if (!empty($userList[$userName])) {
                                            $userIdsString .= (',' . $userList[$userName]);
                                        }
                                    }
                                    if (!empty($userIdsString)) {
                                        $instrumentData['in_charge_person'] = substr($userIdsString, 1);
                                    } elseif (!empty($data[$index])) {
                                        $failInfo[$row] .= \Yii::t('base', 'person_in_charge') . \Yii::t('base', 'batch_import_tip');
                                    }
                                    break;
                                case "maintainer":
                                case "maintenance_person":
                                    if (empty($userList)) {
                                        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
                                        $userList = isset($userList['list']) ? $userList['list'] : [];
                                        $userListInfo = yii\helpers\ArrayHelper::index($userList, 'id');
                                        $userList = array_column($userList, 'id', 'name'); //  用户名数组
                                    }
                                    $userIdsString = '';
                                    $userArr = explode(';', $data[$index]);
                                    foreach ($userArr as $userName) {
                                        // add by hkk 2022/6/24  有括号兼容括号内名称
                                        if (preg_match('/\(([^)]*)\)/', $userName, $matches) && !empty($matches[1])) {
                                            $userName = $matches[1];
                                        }
                                        if (!empty($userList[$userName])) {
                                            $userIdsString .= (',' . $userList[$userName]);
                                        }
                                    }
                                    if (!empty($userIdsString)) {
                                        $instrumentData['maintenance_person'] = substr($userIdsString, 1);
                                    } elseif (!empty($data[$index])) {
                                        $failInfo[$row] .= \Yii::t('base', 'maintainer') . \Yii::t('base', 'batch_import_tip');
                                    }
                                    break;
                                case "data_type":
                                    //仪器对接类型
                                    $data_type = $data[$index];

                                    if (InstrumentsModel::isNumericDataType($data_type)) {
                                        //仪器对接类型为数值
                                        if ($inscadaNumAll == 0) {
                                            return $this->fail(\Yii::t('base', 'inscada_not_activited'));
                                        } elseif ($inscadaNum >= $inscadaNumAll) {
                                            $failInfo[$row] .= \Yii::t('base', 'match_instrument_failed');
                                        } else {
                                            $numericalInstrumentTypeMap = [
                                                \Yii::t('base', 'numerical_instrument1') => 1,
                                                \Yii::t('base', 'numerical_instrument2') => 2,
                                                strtolower(\Yii::t('base', 'numerical_instrument3')) => 3,
                                                \Yii::t('base', 'numerical_instrument4') => 4,
                                            ];
                                            $instrumentData['data_type'] = InstrumentsModel::$data_type_numerical;
                                            // 根据填写的细分数值类对接类型来保存数据，pH计需要不分大小写模糊匹配所以加一个strtolower
                                            $instrumentData['numerical_instrument_type'] = $numericalInstrumentTypeMap[strtolower($data_type)];
                                            $inscadaNum++;
                                        }
                                    } elseif (InstrumentsModel::isFileDataType($data_type)) {
                                        //仪器对接类型为文件
                                        if ($inscadaNumAll == 0) {
                                            return $this->fail(\Yii::t('base', 'inscada_not_activited'));
                                        } elseif ($inscadaNum >= $inscadaNumAll) {
                                            $failInfo[$row] .= \Yii::t('base', 'match_instrument_failed');
                                        } else {
                                            $instrumentData['data_type'] = InstrumentsModel::$data_type_file;
                                            $instrumentData['numerical_instrument_type'] = 0;
                                            $inscadaNum++;
                                        }
                                    } elseif (InstrumentsModel::isNoneDataType($data_type)) {
                                        //仪器对接类型为无
                                        $instrumentData['data_type'] = InstrumentsModel::$data_type_none;
                                        $instrumentData['numerical_instrument_type'] = 0;
                                    } else {
                                        //仪器对接类型 错误
                                        $failInfo[$row] .= \Yii::t('base', 'instrument_type_incorrect');
                                        $instrumentData['data_type'] = InstrumentsModel::$data_type_none;
                                        $instrumentData['numerical_instrument_type'] = 0;
                                    }
                                    break;
                                default:
                                    if (preg_match('/^define\_(\d+)/', $field, $matches) && !empty($matches[1])) {
                                        $instrumentData['data' . $matches[1]] = $data[$index];
                                        $defineFieldIndex[] = $matches[1];
                                    } else {
                                        $instrumentData[$field] = $data[$index];
                                    }
                                    break;
                            }
                        }
                        try {

                            // add by hkk 2022/8/8  标记'不限仪器'
                            if (@getVar($instrumentData['groupIds']) == '' && @getVar($instrumentData['departmentIds']) == '') {
                                $instrumentData['groupIds'] = 'all';
                            }

                            if (!empty($failInfo[$row])) {
                                $failCnt++;
                                continue;
                            } else {
                                unset($failInfo[$row]);
                            }
                            (new InstrumentServer())->editBatchInstrument($instrumentData, $userListInfo, $defineRecord, $defineFieldIndex); // 写到数据库并存些痕迹
                        } catch (yii\db\Exception $e) {
                            $failCnt++;
                            continue;
                        }
                    }
                }

                // 更新后增加编辑历史信息
                $editData = [
                    'dep_path' => $currentPath,
                    'save_name' => $currentName,
                    'total_row' => $total_line - 2,
                    'failed_row' => $failCnt,
                    'original_file_name' => $originPath['data'],
                    'create_person' => \Yii::$app->view->params['curr_user_id'],
                    'type' => 1, // add by hkk 2022/5/30
                ];
                $addHis = (new InstrumentServer())->addInstrumentBatchEditHistory($editData);
                if ($addHis['status'] == 1) { // 记录每行的错误信息
                    $addErrorInfo = (new InstrumentServer())->addInstrumentBatchErrorInfo($addHis['data']['id'], $failInfo);
                }
            }
        }

        return $this->success([]);
    }

    /**
     * Notes:批量编辑导出历史 2022/5/31 加添加历史
     * Author: hkk
     * Date: 2021/6/7 16:43
     * @return \common\controllers\json
     */
    public function actionGetBatchEditHistory()
    {

        $type = \Yii::$app->request->post('type', 1);

        // 读取批量编辑历史的数据
        $editHistory = InstrumentsBatchEditFile::find()->where(['status' => 1, 'type' => $type])->orderBy('create_time DESC')->asArray()->all();
        $user_ids = array_unique(array_column($editHistory, 'create_person'));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = yii\helpers\ArrayHelper::index($userList, 'user_id');
        foreach ($editHistory as $key => $editItem) {
            if (!empty($userList[$editItem['create_person']])) {
                $editHistory[$key]['create_name'] = CommentServer::displayUserName($userList[$editItem['create_person']]);
            } else {
                $editHistory[$key]['create_name'] = '';
            }
        }

        $data = [
            'editHistory' => $editHistory,
            'type' => $type
        ];

        $html = $this->renderAjax('/instrument/instrument_batch_edit_history.php', $data);
        return $this->success([
            'file' => $html
        ]);
    }


    /**
     * Notes:新的批量添加导出excel 按列表显示的字段导出
     * Author: hkk
     * Date: 2022/5/127 10:45
     */
    public function actionExportBatchAddExcel()
    {


        $exportFields = \Yii::$app->request->get('exportFields', '');
        $exportFields = explode(',', $exportFields);

        // 查询自定义字段和仪器分类
        $typeString = '';
        $defineField = [];
        $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
        $defineFields = $defineRecord ? $defineRecord : [];
        $type_list = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);
        for ($i = 1; $i <= 20; $i++) {
            if (isset($defineFields['field' . $i]) && !emptyExclude0($defineFields['field' . $i])) {
                $defineField[] = $defineFields['field' . $i];
            }
        }
        foreach ($type_list as $type) {
            $typeString .= ($type['dict_value'] . ' ');
        }
        $lang = \Yii::$app->language;
        if ($lang == 'en-US') {
            $keyResult = [
                'Instrument name(Required)',
                'Device ID',
                'Specification',
                'Instrument type(' . trim($typeString) . ')',
                'Serial Number',
                'Manufacturer',
                'Supplier',
                'Status(normal,suspend use,scrap,repairing,deleted)',
                'Check(No need for validation, need validation)',
                'Location',
                'Group(input group group name,separated by semicolons if many, default blank represents all groups)',
                'Response person(input user name)',
                'In Charge Person(input user name)',
                'Maintainer(input user name)',
                'Notes',
            ];
            $exportFields[] = 'Group(input group name,separated by semicolons if many,blank represents none,  if both Group and Department are none, all groups and all departments are visible)';
            $exportFields[] = 'Department(input department name,separated by semicolons if many,blank represents none,if both Group and Department are none, all groups and all departments are visible)';
        } else {
            $keyResult = [
                '仪器名称(必填)',
                '设备ID',
                '规格',
                '类别(' . trim($typeString) . ')',
                '出厂编号',
                '制造商',
                '状态(正常 停用 报废 维修中 已删除)',
                '校验(无需校验 需要校验)',
                '位置',
                '创建人',
                '责任人(输入用户名)',
                '维护人(输入用户名)',
                '维修人(输入用户名)',
                '供应商',
                '备注',
            ];
            $exportFields[] = '所属鹰群(输入鹰群名称，多个英文分号隔开，空白代表不选鹰群，若鹰群和部门都空白，代表所有鹰群和部门都可见)';
            $exportFields[] = '所属部门(输入部门名称，多个英文分号隔开，空白代表不选部门，若鹰群和部门都空白，代表所有鹰群和部门都可见)';
        }
        $exportFields = $this->modifyInstrumentExportFieldsTitle($exportFields);

        $keyResult = array_merge($keyResult, $defineField); // 拼接自定义字段


        // 新建excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 写入标题字段
        foreach ($exportFields as $key => $title) {

            $value = $title;
            switch ($title) {
                case '名称':
                    $value = '仪器名称(必填)';
                    break;
                case '类别':
                    $value = '类别(' . trim($typeString) . ')';
                    break;
                case '状态':
                    $value = '状态(正常 停用 报废 维修中 已删除)';
                    break;
                case '校验情况':
                    $value = '校验(无需校验 需要校验)';
                    break;
                case '责任人':
                case '维护人':
                case '维修人':
                    $value = $title . '(输入用户名)';
                    break;
                default:
                    break;
            }

            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key) . '1', $value); // 设置内容栏
        }

        // 导出
        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

//        $objWriter->save('php://output');
        $file = \Yii::getAlias('@filepath') . DS . 'batch_add_instruments' . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'batch_add_instruments' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="batch_add_instruments.xlsx"');
        header('Cache-Control: max-age=0');
        readfile($file);
    }

    /** 修改批量添加仪器数据时导出的excel模板的title字段的内容
     * @param $export_fields
     * @return mixed
     * @auther: dx
     * @date:2023/1/7
     */
    private function modifyInstrumentExportFieldsTitle($export_fields)
    {
        // 对接数据类型字段
        $data_type_en = \Yii::t('base', 'data_type', 'en-US');
        $data_type_zh = \Yii::t('base', 'data_type', 'zh-CN');

        foreach ($export_fields as &$export_field) {
            //如果是中文或英文的 对接类型 字段，修改字段的内容, 增加解释文字
            if ($export_field == $data_type_en || $export_field == $data_type_zh) {
                $export_field .= sprintf('(%1$s)', \Yii::t('base', 'data_type_tip', [
                    'numeric_data_type' => \Yii::t('base', 'numerical_instrument'),
                    'file_data_type' => \Yii::t('base', 'file_instrument'),
                ]));
            }
        }
        return $export_fields;
    }

    /**
     * Notes:新的批量添加添导入excel 按列表显示的字段导入
     * Author: hkk
     * Date: 2022/5/127 10:45
     * @throws \PHPExcel_Reader_Exception
     */
    public function actionSubmitBatchAdd()
    {

        $data = \Yii::$app->request->post();

        // add by hkk 2022/5/30  存储原始上传的文件
        $currentPath = $data['dep_path'];
        $currentName = $data['save_name'];
        $filename = \Yii::getAlias('@filepath') . DS . $currentPath . DS . $currentName;

        $filename = \Yii::getAlias('@filepath') . DS . $data['dep_path'] . DS . $data['save_name'];

        $arr = pathinfo($filename);
        $ext = $arr['extension'];

        //需要手工加载这个类
        require_once dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'Excel' . DIRECTORY_SEPARATOR . 'PHPExcel.php';


        // 检验和状态数组
        $checkArr = [
            '无需校验' => 0,
            '需要校验' => 2, // 即未校验，需要手动校验
            'no need for validation' => 0, // 即未校验，需要手动校验
            'need validation' => 2, // 即未校验，需要手动校验
        ];
        $statusArr = [
            '正常' => 1,
            '停用' => 2,
            '报废' => 4,
            '维修中' => 3,
            '已删除' => 0,
            'normal' => 1,
            'suspend use' => 2,
            'scrap' => 4,
            'repairing' => 3,
            'deleted' => 0,
        ];
        $company_id = \Yii::$app->view->params['curr_company_id'];


        // 查询所有自定义字段内容
        $defineFieldMap = [];
        $defineFields = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
        $defineFields = $defineFields ? $defineFields : [];
        for ($i = 1; $i <= 20; $i++) {
            if (isset($defineFields['field' . $i]) && !emptyExclude0($defineFields['field' . $i])) {
                $defineFieldMap[$defineFields['field' . $i]] = $i;
            }
        }


        // 获取所有鹰群及鹰群id
        $userList = [];
        $visibleGroups = (new CenterInterface())->getVisibleGroups($this->userinfo->id);
        $allGroupIdsString = implode(',', array_column($visibleGroups, 'id'));
        $groupList = array_column($visibleGroups, 'id', 'group_name'); // 鹰群名数组

        // 获取所有可见部门信息
        $departmentList = (new CenterInterface())->getVisibleDepartments($this->userinfo->id);
        $allDepartmentIdsString = implode(',', array_column($departmentList, 'id'));
        $departmentList = array_column($departmentList, 'id', 'department_name');

        // 获取分类信息
        $typeList = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);
        $typeList = array_column($typeList, 'dict_value');

        //查询仪器对接使用数量和总量
        $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
        $elnPaidItems = $companyData['data']['elnPaidItems'];

//        if(!isset($elnPaidItems['instrumentsInterfacingNumber']) || empty($elnPaidItems['instrumentsInterfacingNumber'])){
//            return $this->fail(\Yii::t('base', 'inscada_not_activited'));
//        }

        $inscadaNumAll = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);

        $instrumentNum = InstrumentsModel::find()
            ->where(['data_type' => 1])
            ->orWhere(['data_type' => 2])
            ->asArray()->all();
        $inscadaNum = count($instrumentNum);

        //读取excel文件
        $failInfo = [];
        $successNumber = 0;
        if (in_array($ext, array('xls', 'xlsx'))) {

            $fileType = \PHPExcel_IOFactory::identify($filename); //文件名自动判断文件类型
            $excelReader = \PHPExcel_IOFactory::createReader($fileType);

            try {
                $phpexcel = $excelReader->load($filename)->getSheet(0); //载入文件并获取第一个sheet
            } catch (\Exception $e) {
                return $this->fail(\Yii::t('base', 'excel_wrong'));
            }
            $total_line = $phpexcel->getHighestRow(); //总行数 1-10-...
            $total_column = $phpexcel->getHighestColumn(); //总列数 A-I-..
            ++$total_column;

            if ($total_line > 1) {

                $tIndex = 0;
                $titleMap = []; // 标题行映射,添加的字段
                $allTitleArr = [];

                // 获取第一行数据来判断仪器名称是否存在
                $row = 1; // 第一行
                $range = 'A' . $row . ':' . $total_column . $row;
                $rowData = $phpexcel->rangeToArray($range, NULL, TRUE, FALSE);
                $allTitleArr = $rowData[0];
                if (!in_array("仪器名称(必填)", $allTitleArr) &&
                    !in_array("Instrument name(Required)", $allTitleArr)) {
                    return $this->fail(\Yii::t('base', 'excel_wrong_format'));
                }

                for ($column = 'A'; $column != $total_column; $column++) { //遍历列
                    $titleValue = trim($phpexcel->getCell($column . '1')->getValue());
                    $titleValue = trim(preg_replace('/\(.*?\)/', '', $titleValue));
                    switch ($titleValue) {
                        case 'Instrument name':
                        case '仪器名称':
                            $titleMap['name'] = $tIndex;
                            break;
                        case 'Device ID':
                        case '设备ID':
                            $titleMap['batch_number'] = $tIndex;
                            break;
                        case 'Specification':
                        case '规格':
                            $titleMap['specification'] = $tIndex;
                            break;
                        case 'Instrument type':
                        case '类别':
                            $titleMap['instrument_type'] = $tIndex;
                            break;
                        case 'Serial Number':
                        case '出厂编号':
                            $titleMap['model'] = $tIndex;
                            break;
                        case 'Manufacturer':
                        case '制造商':
                            $titleMap['manufacturer'] = $tIndex;
                            break;
                        case 'Status':
                        case '状态':
                            $titleMap['status'] = $tIndex;
                            break;
                        case 'Check':
                        case '校验':
                            $titleMap['check_situation'] = $tIndex;
                            break;
                        case 'Location':
                        case '位置':
                            $titleMap['position'] = $tIndex;
                            break;
                        case 'In Charge Person':
                        case '维护人':
                            $titleMap['in_charge_person'] = $tIndex;
                            break;
                        case 'Response person':
                        case '责任人':
                            $titleMap['responsible_person'] = $tIndex;
                            break;
                        case 'Maintainer':
                        case '维修人':
                            $titleMap['maintenance_person'] = $tIndex;
                            break;
                        case '供应商':
                            $titleMap['supplier'] = $tIndex;
                            break;
                        case 'Notes':
                        case '备注信息':
                            $titleMap['remark'] = $tIndex;
                            break;
                        case 'Group':
                        case '所属鹰群':
                            $titleMap['groupIds'] = $tIndex;
                            break;
                        case 'Department':
                        case '所属部门':
                            $titleMap['departmentIds'] = $tIndex;
                            break;
                        case \Yii::t('base', 'data_type', 'en-US'):
                        case \Yii::t('base', 'data_type', 'zh-CN'):
                            $titleMap['data_type'] = $tIndex;
                        default:   // 自定义字段
                            if (isset($defineFieldMap[$titleValue])) {
                                $titleMap['data' . $defineFieldMap[$titleValue]] = $tIndex;
                            }
                    }
                    $tIndex++;
                }


                // 遍历行记录 具体的仪器数据
                for ($row = 2; $row <= $total_line; $row++) {
                    $failInfo[$row] = '';
                    $data = array();
                    for ($column = 'A'; $column != $total_column; $column++) { //遍历列读取每一个仪器的数据
                        $data[] = trim($phpexcel->getCell($column . $row)->getFormattedValue());
                    }

                    if (!empty($data[$titleMap['name']])) { // 名称栏必填

                        // 状态
                        if (isset($titleMap['status']) && !empty($data[$titleMap['status']])) {
                            if (array_key_exists($data[$titleMap['status']], $statusArr)) {
                                $status_set = $statusArr[$data[$titleMap['status']]];
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'status') . \Yii::t('base', 'batch_import_tip');
                                $status_set = '';
                            }
                        } else {
                            $status_set = 1;
                        }

                        // 是否受检 check_situation
                        if (isset($titleMap['check_situation']) && !empty($data[$titleMap['check_situation']])) {
                            if (array_key_exists($data[$titleMap['check_situation']], $checkArr)) {
                                $check_situation_set = $checkArr[$data[$titleMap['check_situation']]];
                            } else {
                                $failInfo[$row] = \Yii::t('base', 'check') . \Yii::t('base', 'batch_import_tip');
                                $check_situation_set = '';
                            }
                        } else {
                            // bug 878 Plug/人福普克：批量添加仪器，未填写表格中的【校验】/或表格中没有【校验】，导入后，仪器会默认显示【需要校验】，应该默认显示【无需校验】
                            $check_situation_set = 0;
                        }

                        // 仪器类型
                        if (isset($titleMap['instrument_type']) && !empty($data[$titleMap['instrument_type']])) {
                            if (in_array($data[$titleMap['instrument_type']], $typeList)) {
                                $instrumentType = $data[$titleMap['instrument_type']];
                            } else {
                                $failInfo[$row] .= \Yii::t('base', 'instrument_category') . \Yii::t('base', 'batch_import_tip');
                                $instrumentType = '';
                            }
                        } else {
                            $instrumentType = '';
                        }

                        $instrumentData = [
                            'name' => $data[$titleMap['name']],
                            'batch_number' => isset($titleMap['batch_number']) ? $data[$titleMap['batch_number']] : '',
                            'specification' => isset($titleMap['specification']) ? $data[$titleMap['specification']] : '',
                            'instrument_type' => $instrumentType, // 仪器类型
                            'model' => isset($titleMap['model']) ? $data[$titleMap['model']] : '', // 货号型号 出厂编号
                            'manufacturer' => isset($titleMap['manufacturer']) ? $data[$titleMap['manufacturer']] : '', // 制造商
                            'supplier' => isset($titleMap['supplier']) ? $data[$titleMap['supplier']] : '', // 供应商
                            'status' => $status_set, // 状态
                            'check_situation' => $check_situation_set, // 检查
                            'position' => isset($titleMap['position']) ? $data[$titleMap['position']] : '', // 位置
                            'remark' => isset($titleMap['remark']) ? $data[$titleMap['remark']] : '', // 备注
                            'create_by' => $this->userinfo->id,
                        ];

                        //仪器对接类型
                        if (isset($titleMap['data_type']) && !empty($data[$titleMap['data_type']])) {
                            $data_type = $data[$titleMap['data_type']];

                            if (InstrumentsModel::isNumericDataType($data_type)) {
                                //仪器对接类型为数值
                                if ($inscadaNumAll == 0) {
                                    return $this->fail(\Yii::t('base', 'inscada_not_activited'));
                                } elseif ($inscadaNum >= $inscadaNumAll) {
                                    $failInfo[$row] .= \Yii::t('base', 'match_instrument_failed');
                                } else {
                                    $numericalInstrumentTypeMap = [
                                        \Yii::t('base', 'numerical_instrument1') => 1,
                                        \Yii::t('base', 'numerical_instrument2') => 2,
                                        strtolower(\Yii::t('base', 'numerical_instrument3')) => 3,
                                        \Yii::t('base', 'numerical_instrument4') => 4,
                                    ];
                                    $instrumentData['data_type'] = InstrumentsModel::$data_type_numerical;
                                    // 根据填写的细分数值类对接类型来保存数据，pH计需要不分大小写模糊匹配所以加一个strtolower
                                    $instrumentData['numerical_instrument_type'] = $numericalInstrumentTypeMap[strtolower($data_type)];
                                    $inscadaNum++;
                                }
                            } elseif (InstrumentsModel::isFileDataType($data_type)) {
                                //仪器对接类型为文件
                                if ($inscadaNumAll == 0) {
                                    return $this->fail(\Yii::t('base', 'inscada_not_activited'));
                                } elseif ($inscadaNum >= $inscadaNumAll) {
                                    $failInfo[$row] .= \Yii::t('base', 'match_instrument_failed');
                                } else {
                                    $instrumentData['data_type'] = InstrumentsModel::$data_type_file;
                                    $instrumentData['numerical_instrument_type'] = 0;
                                    $inscadaNum++;
                                }
                            } elseif (InstrumentsModel::isNoneDataType($data_type)) {
                                //仪器对接类型为无
                                $instrumentData['data_type'] = InstrumentsModel::$data_type_none;
                                $instrumentData['numerical_instrument_type'] = 0;
                            } else {
                                //仪器对接类型 错误
                                $failInfo[$row] .= \Yii::t('base', 'instrument_type_incorrect');
                                $instrumentData['data_type'] = InstrumentsModel::$data_type_none;
                                $instrumentData['numerical_instrument_type'] = 0;
                            }
                        }

                        // 鹰群字段
                        if (isset($titleMap['groupIds']) && !empty($data[$titleMap['groupIds']])) {
                            $groupIdsString = '';
                            $groupArr = explode(';', $data[$titleMap['groupIds']]);
                            foreach ($groupArr as $groupName) {
                                if (!empty($groupList[$groupName])) {
                                    $groupIdsString .= (',' . $groupList[$groupName]);
                                }
                            }
                            if (!empty($groupIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['groupIds'] = substr($groupIdsString, 1);
                            } else {
                                $failInfo[$row] .= \Yii::t('base', 'group') . \Yii::t('base', 'batch_import_tip');
                            }
                        } else { // 默认所有鹰群
                            $instrumentData['groupIds'] = '';
                        }

                        // 部门字段
                        if (isset($titleMap['departmentIds']) && !empty($data[$titleMap['departmentIds']])) {
                            $departmentIdsString = '';
                            $departmentArr = explode(';', $data[$titleMap['departmentIds']]);
                            foreach ($departmentArr as $departmentName) {
                                if (!empty($departmentList[$departmentName])) {
                                    $departmentIdsString .= (',' . $departmentList[$departmentName]);
                                }
                            }
                            if (!empty($departmentIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['departmentIds'] = substr($departmentIdsString, 1);
                            } else {
                                $failInfo[$row] .= \Yii::t('base', 'department') . \Yii::t('base', 'batch_import_tip');
                            }
                        } else { // 默认所有部门
                            $instrumentData['departmentIds'] = '';
                        }

                        // add by hkk 2022/8/8  标记'不限仪器'
                        if (isset($instrumentData['groupIds']) && $instrumentData['groupIds'] == '' && isset($instrumentData['departmentIds']) && $instrumentData['departmentIds'] == '') {
                            $instrumentData['groupIds'] = 'all';
                        }


                        // 查询用户名数组,只查一次
                        if (
                            empty($userList)
                            && ((isset($titleMap['in_charge_person']) && !empty($data[$titleMap['in_charge_person']]))
                                || (isset($titleMap['responsible_person']) && !empty($data[$titleMap['responsible_person']]))
                                || (isset($titleMap['maintenance_person']) && !empty($data[$titleMap['maintenance_person']])))
                        ) {
                            $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
                            $userList = isset($userList['list']) ? $userList['list'] : [];
                            $userList = array_column($userList, 'id', 'name'); //  用户名数组
                        }


                        // 责任人
                        if (isset($titleMap['responsible_person']) && !empty($data[$titleMap['responsible_person']])) {
                            $userIdsString = '';
                            $userArr = explode(';', $data[$titleMap['responsible_person']]);
                            foreach ($userArr as $userName) {
                                if (!empty($userList[$userName])) {
                                    $userIdsString .= (',' . $userList[$userName]);
                                }
                            }
                            if (!empty($userIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['responsible_person'] = substr($userIdsString, 1);
                            } else {
                                $failInfo[$row] .= \Yii::t('base', 'response_person') . \Yii::t('base', 'batch_import_tip');
                            }
                        }

                        // 维护人
                        if (isset($titleMap['in_charge_person']) && !empty($data[$titleMap['in_charge_person']])) {
                            $userIdsString = '';
                            $userArr = explode(';', $data[$titleMap['in_charge_person']]);
                            foreach ($userArr as $userName) {
                                if (!empty($userList[$userName])) {
                                    $userIdsString .= (',' . $userList[$userName]);
                                }
                            }

                            if (!empty($userIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['in_charge_person'] = substr($userIdsString, 1);
                            } else {
                                $failInfo[$row] .= \Yii::t('base', 'person_in_charge') . \Yii::t('base', 'batch_import_tip');
                            }
                        }

                        // 维修人
                        if (isset($titleMap['maintenance_person']) && !empty($data[$titleMap['maintenance_person']])) {
                            $userIdsString = '';
                            $userArr = explode(';', $data[$titleMap['maintenance_person']]);
                            foreach ($userArr as $userName) {
                                if (!empty($userList[$userName])) {
                                    $userIdsString .= (',' . $userList[$userName]);
                                }
                            }
                            if (!empty($userIdsString)) { // add by hkk 2021/7/27
                                $instrumentData['maintenance_person'] = substr($userIdsString, 1);
                            } else {
                                $failInfo[$row] .= \Yii::t('base', 'maintainer') . \Yii::t('base', 'batch_import_tip');
                            }
                        }

                        // 开始添加自定义的字段
                        for ($i = 1; $i <= 20; $i++) {
                            if (isset($titleMap['data' . $i]) && !empty($data[$titleMap['data' . $i]])) {
                                $instrumentData['data' . $i] = $data[$titleMap['data' . $i]];
                            }
                        }

                        if (!empty($failInfo[$row])) { // add by hkk 2022/5/30
                            continue;
                        }


                        $addResult = (new InstrumentServer())->addInstrument($instrumentData); // 写到数据库

                        if ($addResult['status'] == 0) {
                            //! 获取model校验的错误结果(只有第一个字段的第一条)
                            $failInfo[$row] .= "{$addResult['info']}; ";
                            // $failInfo[$row] .= \Yii::t('base', 'instrument_id_repeat', [$instrumentData['batch_number']]) . '; ';
                        } else {
                            $successNumber++;
                        }
                    } else {
                        $failInfo[$row] = \Yii::t('base', 'require_name_tip') . '; ';
                    }

                    if (empty($failInfo[$row])) {
                        unset($failInfo[$row]);
                    }
                }


                // 完成后增加上传历史信息和错误信息
                $addHistoryData = [
                    'dep_path' => $currentPath,
                    'save_name' => $currentName,
                    'total_row' => $total_line - 1,
                    'start_row' => 2,
                    'failed_row' => $total_line - 1 - $successNumber,
                    'create_person' => \Yii::$app->view->params['curr_user_id'],
                    'type' => 2,
                ];
                $addHis = (new InstrumentServer())->addInstrumentBatchEditHistory($addHistoryData);
                if ($addHis['status'] == 1) { // 记录每行的错误信息
                    $addErrorInfo = (new InstrumentServer())->addInstrumentBatchErrorInfo($addHis['data']['id'], $failInfo);
                }
            }
        }

        return $this->success([
            'info' => $failInfo,
            'successNumber' => $successNumber,
        ]);
    }

    /**
     * Notes:批量添加查看具体错误信息
     * Author: hkk
     * Date: 2022/5/31 13:06
     * @return \common\controllers\json
     */
    public function actionGetBatchAddError()
    {
        $id = \Yii::$app->request->post('id', 1);
        $result = InstrumentBatchUploadInfo::find()->where(['batch_file_id' => $id])->asArray()->all();
        return $this->success([
            'errorInfo' => $result
        ]);
    }

    /**
     * Notes: 设置预约规则字段
     *  Author: zsm
     *  Date: 2025/5/8 17:30
     * @return \common\controllers\json
     */
    public function actionSetInstrumentBookingConfig()
    {
        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->instrumentBookingConfig($postData);
        return $this->success([]);
    }


    /**
     * Notes: 创建/编辑仪器预约
     *  Author: zsm
     *  Date: 2025/5/21 19:30
     * @return \common\controllers\json
     */
    public function actionHandleInstrumentBooking()
    {
        $postData = \Yii::$app->getRequest()->post();
        $detail = $postData['detail'];
        $result = (new InstrumentServer())->handleInstrumentBooking($detail['instrumentId'], $detail['type'], $postData['timeArr'], $detail, $postData['id']);
        if (isset($result['info']) && $result['status'] === 0) {
            return $this->fail($result['info']);
        }
        return $this->success([]);
    }

    /**
     * Notes:批量调整   鹰群 部门 状态 类别 维护人 维修人 责任人
     * Author: hkk
     * Date: 2022/6/23 14:55
     * @return \common\controllers\json
     */
    public function actionBatchAdjust()
    {

        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->batchAdjustInstrument($postData);
        return $this->success([]);
    }

    /**
     * Notes:仪器状态调整审批弹窗
     * Author: zhouweiming
     * Date: 2023/4/12
     */
    public function actionCreateStatusApproval()
    {
        $postData = \Yii::$app->getRequest()->post();
        (new InstrumentServer())->instrumentStatusCreateApproval($postData['instrument_id'], $postData['instrument_status'], $postData['instrument_status_change_reason']);
        return $this->success([]);
    }

    /**
     * Notes:仪器复核界面状态调整弹窗
     * Author: zhouweiming
     * Date: 2023/4/12
     * @return \common\controllers\json
     */
    public function actionGetStatusChangePage()
    {

        $postData = \Yii::$app->getRequest()->post();
        $data['instrumentId'] = $postData['instrumentId'];
        $data['approveId'] = $postData['approveId']; // 复核id
        $approval = ApprovalModel::findOne(['id' => $data['approveId'],]);
        $node = ApprovalNodeModel::findOne(['approval_id' => $data['approveId'], 'node_status' => 1]);
        $extraData = json_decode($approval['extra_data'], true);
        switch ($extraData['instrument_status']) {
            case 1:
                $data['instrumentNewStatus'] = 'normal';
                break;
            case 2:
                $data['instrumentNewStatus'] = 'suspend_use';
                break;
            case 3:
                $data['instrumentNewStatus'] = 'repairing';
                break;
            case 4:
                $data['instrumentNewStatus'] = 'scrap';
                break;
            case 0:
                $data['instrumentNewStatus'] = 'already_deleted';
                break;
        }
        $data['approvalReason'] = $extraData['reason'];
        $data['approvalNodeId'] = $node['id'];

        $dataResult = (new InstrumentServer())->getInsInfoById($data['instrumentId']);
        $data['dataResult'] = $dataResult['data'];

        switch ($data['dataResult']['status']) {
            case 1:
                $data['instrumentStatus'] = 'normal';
                break;
            case 2:
                $data['instrumentStatus'] = 'suspend_use';
                break;
            case 3:
                $data['instrumentStatus'] = 'repairing';
                break;
            case 4:
                $data['instrumentStatus'] = 'scrap';
                break;
            case 0:
                $data['instrumentStatus'] = 'already_deleted';
                break;
        }
        $html = $this->renderAjax('/popup/view_status_change.php', $data);

        return $this->success(['file' => $html]);
    }

    /**
     * Notes:仪器库管理-批量调整状态
     * Author: zhouweiming
     * Date: 2023/4/12
     * @return \common\controllers\json
     */
    public function actionInstrumentStatusBatchChange()
    {
        $instrumentIds = \Yii::$app->request->post('instrumentIds', []);
        $data['instrumentIds'] = implode(',', $instrumentIds);

        $html = $this->renderAjax('/popup/instrument_batch_adjust_status.php', $data);

        return $this->success(['file' => $html]);
    }

    /**
     * Notes:仪器库管理-批量调整状态判断是否需要审批
     * Author: zhouweiming
     * Date: 2023/4/12
     * @return \common\controllers\json
     */
    public function actionInstrumentStatusBatchNeedApproval()
    {
        $instrumentIds = \Yii::$app->request->post('instrumentIds');
        $instrumentStatus = \Yii::$app->request->post('instrumentStatus', 1);
        switch ($instrumentStatus) {
            case 0:
                $approvalUser = 'delete_must_check_users';
                break;
            case 2:
                $approvalUser = 'suspend_use_must_check_users';
                break;
            case 3:
                $approvalUser = 'apply_repair_must_check_users';
                break;
            case 4:
                $approvalUser = 'scrap_must_check_users';
                break;

        }
        $needApprovalInstrumentIds = [];
        $approvalUsersArr = [];
        $instrument = new InstrumentServer();
        if ($instrumentStatus != 1) {
            foreach (explode(',', $instrumentIds) as $key => $id) {
                $instrumentDetail = $instrument->getInsInfoById($id);
                $instrumentDetail = $instrumentDetail['data'];
                $recordSetting = json_decode($instrumentDetail['record_setting'], true);
                if (isset($recordSetting[$approvalUser])) {
                    sort($recordSetting[$approvalUser]);
                    $approvalUsersArr[] = implode(',', $recordSetting[$approvalUser]);
                    $needApprovalInstrumentIds[] = $id;
                } else {
                    $approvalUsersArr[] = '';
                }
            }
        }

        $data['needApprovalInstrumentIds'] = $needApprovalInstrumentIds;
        $data['approvalUserIds'] = [];
        $data['approvalUsers'] = '';
        $approvalUsersArr = array_unique($approvalUsersArr);
        if (count($approvalUsersArr) == 1 && $approvalUsersArr[0] != '') {
            $data['approvalUserIds'] = $approvalUsersArr[0];
            $usersRes = (new CenterInterface())->userDetailsByUserIds(explode(',', $approvalUsersArr[0]));
            $userDetailArr = yii\helpers\ArrayHelper::index($usersRes, 'id');
            foreach (explode(',', $approvalUsersArr[0]) as $id) {
                $approvalUsers[] = CommonServer::displayUserName($userDetailArr[$id]);
            }
            $data['approvalUsers'] = implode(',', $approvalUsers);
        }


        return $this->success([
            'approvalUser' => $data['approvalUsers'],
            'approvalUserIds' => $data['approvalUserIds'],
            'needApprovalInstrumentIds' => $data['needApprovalInstrumentIds']
        ]);
    }

    /**
     * Notes:批量调整状态提交
     * Author: zhouweiming
     * Date: 2023/4/19
     * @return \common\controllers\json
     */
    public function actionInstrumentBatchAdjustStatusSubmit()
    {
        $instrumentIds = \Yii::$app->request->post('instrumentIds', []);
        $instrumentStatus = \Yii::$app->request->post('instrumentStatus', 1);
        $needApprovalInstrumentIds = \Yii::$app->request->post('needApprovalInstrumentIds');
        $statusChangeReason = \Yii::$app->request->post('statusChangeReason', '');
        $instrumentIds = explode(',', $instrumentIds);
        $needApprovalInstrumentIds = $needApprovalInstrumentIds == '' ? [] : explode(',', $needApprovalInstrumentIds);
        $instrument = new InstrumentServer();
        $needApprovalInstruments = [];
        $data['instrumentIds'] = [];
        if ($instrumentStatus != 1 && $needApprovalInstrumentIds != []) {
            foreach ($instrumentIds as $id) {
                if (in_array($id, $needApprovalInstrumentIds)) {
                    //有审批走审批
                    $instrument->instrumentStatusCreateApproval($id, $instrumentStatus, $statusChangeReason);
                    $instrumentInfo = $instrument->getInsInfoById($id);
                    $needApprovalInstruments[] = $instrumentInfo['data']['name'] . '(' . $instrumentInfo['data']['batch_number'] . ')';
                } else {
                    //没有审批更新状态
                    $data['instrumentIds'][] = $id;
                }
            }
        } else {
            //没有需要审批的仪器
            $data['instrumentIds'] = $instrumentIds;
        }
        $data['type'] = 'status';
        $data['value'] = $instrumentStatus;
        $instrument->batchAdjustInstrument($data);

        return $this->success(['needApprovalInstruments' => $needApprovalInstruments]);
    }

    /**
     * Notes:仪器库管理-批量删除判断是否有审批
     * Author: zhouweiming
     * Date: 2023/6/13
     * @return \common\controllers\json
     */
    public function actionInstrumentBatchDelete()
    {
        $instrumentIds = \Yii::$app->request->post('instrumentIds', []);
        $instrument = new InstrumentServer();
        $instrumentInfo = $instrument->getInstrumentDataByIds($instrumentIds);
        $recordSettingArr = array_column($instrumentInfo['data'], 'record_setting');
        $hasApprovalInstrumentIds = false;
        foreach ($recordSettingArr as $key => $value) {
            $recordSetting = json_decode($value, true);
            if (isset($recordSetting['delete_must_check_users'])) {
                sort($recordSetting['delete_must_check_users']);
                $approvalUsersArr[] = implode(',', $recordSetting['delete_must_check_users']);
                $hasApprovalInstrumentIds = true;
            } else {
                $approvalUsersArr[] = '';
            }
        }
        $data['approvalUserIds'] = [];
        $data['approvalUsers'] = '';
        $approvalUsersArr = array_unique($approvalUsersArr);
        if (count($approvalUsersArr) == 1 && $approvalUsersArr[0] != '') {
            $data['approvalUserIds'] = $approvalUsersArr[0];
            $usersRes = (new CenterInterface())->userDetailsByUserIds(explode(',', $approvalUsersArr[0]));
            $userDetailArr = yii\helpers\ArrayHelper::index($usersRes, 'id');
            foreach (explode(',', $approvalUsersArr[0]) as $id) {
                $approvalUsers[] = CommonServer::displayUserName($userDetailArr[$id]);
            }
            $data['approvalUsers'] = implode(',', $approvalUsers);
        }


        return $this->success([
            'approvalUser' => $data['approvalUsers'],
            'approvalUserIds' => $data['approvalUserIds'],
            'hasApprovalInstrumentIds' => $hasApprovalInstrumentIds
        ]);
    }

    /**
     * Notes:三大记录字段顺序保存
     * Author: zhouweiming
     * Date: 2023/7/13
     * @return \common\controllers\json
     */
    public function actionSaveSortField()
    {
        $type = \Yii::$app->request->post('type', '');
        $instrumentId = \Yii::$app->request->post('instrumentId', '');
        $fieldConfig = \Yii::$app->request->post('fieldConfig');
        $instrument = new InstrumentServer();
        $instrument->SaveRecordSortField($fieldConfig, $instrumentId, $type);

        return $this->success([]);
    }

    /**
     * @Notes: 仪器对接自动插入 auto-insert-inscada-data
     * @return \common\controllers\json
     * @author: zwm
     * @DateTime: 2024/1/26 16:21:02
     */
    public function actionAutoInsertInscadaData()
    {
        $postData = \Yii::$app->request->post();
        $dataType = $postData['data_type'];

        $lastFetchTime = $postData['last_fetch_time'];

        $insWhere = empty($postData['batch_number']) ? ['id' => $postData['instrumentId']] : ['batch_number' => $postData['batch_number']];
        $insInfo = InstrumentsModel::find()->where($insWhere)->asArray()->one();
        $instrumentServer = new InstrumentServer();
        $where['batch_number'] = $insInfo['batch_number'];
        $where['status'] = "1";
        $where['last_fetch_time'] = date("Y-m-d H:i:s", $lastFetchTime/1000);

        if ($dataType == "1") {
            // 数据对接类型：数值
            $data['dataType'] = 1;
            $newDataList = (new InstrumentServer())->listInsDataNumericalView($where);
        } else {
            // 自动插入只有数值，如果非数值仪器就扔出报错
            return $this->fail(\Yii::t('base', 'auto_insert_tip1'));
        }

        // 获取最近一条数据的创建时间
        $lastCreatedTime = '';
        if (!empty($newDataList['data'])) {
            $firstData = reset($newDataList['data']);
            $lastCreatedTime = strtotime($firstData['timestamp']) * 1000;
        }

        return $this->success([
            'new_data_list' => $newDataList['data'],
            'last_data_timestamp' => $lastCreatedTime,
        ]);

    }

    /**
     * @Notes: 保存备注save-inscada-remark-data
     * @return \common\controllers\json
     * @author: zwm
     * @DateTime: 2024/2/20 14:38:52
     */
    public function actionSaveInscadaRemarkData()
    {
        $dataId = \Yii::$app->request->post('data_id', '');
        $dataType = \Yii::$app->request->post('data_type', '');
        $remarkData = \Yii::$app->request->post('newValue', '');
        if (empty($dataId)) {
            return $this->fail('数据id为空');
        }
        $instrument = new InstrumentServer();
        $instrument->saveInscadaRemarkData($dataId, $dataType, $remarkData);

        return $this->success([]);
    }

    /**
     * @Notes:作废记录
     * @return \common\controllers\json
     * @throws yii\db\Exception
     * <AUTHOR>
     * @DateTime: 2024/3/26 15:36
     */
    public function actionCancelRecord() {

        $postData = \Yii::$app->getRequest()->post();
        $recordType = $postData['recordType'];
        $instrumentId = $postData['instrumentId'];
        $recordId_order = $postData['recordId_order'];
        $recordIds = array_keys($recordId_order);
        $instrumentServer = new InstrumentServer();
        if ($recordType == 'operate') {
            $recordModel = new InstrumentRunningRecordModel;
            $logInfo = Yii::t('base', 'operate_record');
        } else if ($recordType == 'check') {
            $recordModel = new InstrumentCheckRecordModel();
            $logInfo = Yii::t('base', 'check_record');
        } else if ($recordType == 'repair') {
            $recordModel = new InstrumentRepairRecordModel();
            $logInfo = Yii::t('base', 'repair_record');
        } else {
            return $this->fail('操作类别错误');
        }

        $transaction = \Yii::$app->getDb()->beginTransaction();
        $recordInfo = $recordModel->find()
            ->where([
                'instrument_id' => $instrumentId,
                'id' => $recordIds,
            ])->all();

        $dealId = [];
        foreach ($recordInfo as $record) {
            if ($record->review_conclusion == 3) { //正在复核拒绝作废
                $transaction->rollBack();
                return $this->fail(Yii::t('base', 'checking_refuse_cancel'));
            }
            if ($record->if_void == 1) { //已经作废的不重复作废
                continue;
            }
            $record->if_void = 1;
            $dealId[] = $record->id;
            if (!$record->save()) {
                $transaction->rollBack();
                return $this->fail(json_encode($record->getErrors()));
            }
        }
        $transaction->commit();
        $instrumentServer->updateInstrumentsTimeRecord($postData['instrumentId']);

        //添加痕迹
        if (count($dealId)) {
            $orderId = [];
            foreach ($dealId as $recordId) {
                $orderId[] = $recordId_order[$recordId];
            }
            $action_details = Yii::t('base', 'record_number') . ': ' . implode(',', $orderId) . '</br>'
                . $logInfo . Yii::t('base', 'cancel_reason') . ': ' . $postData['cancelReason'];
            $instrumentServer->addInstrumentHistory($instrumentId, 19, ['action_details' => $action_details, 'orderNumber' => implode(',', $orderId)]);
        }
        return $this->success([]);
    }


    /**
     * @Notes:恢复记录
     * @return \common\controllers\json
     * @throws yii\db\Exception
     * <AUTHOR>
     * @DateTime: 2024/3/27 11:22
     */
    public function actionRecoverRecord() {

        $postData = \Yii::$app->getRequest()->post();
        $instrumentId = $postData['instrumentId']; //仪器id
        $recordType = $postData['recordType']; //记录的类型
        $recordId = $postData['recordId']; //记录的id
        $orderNumber = $postData['orderNumber']; //记录序号
        $instrumentServer = new InstrumentServer();

        $currentTimeInfo = [];//待检测的时间信息
        $currentTimeInfo['current_id'] = $recordId;
        if ($recordType == '3') {
            $recordModel = InstrumentRunningRecordModel::findOne(['id' => $recordId]);
            $record = 'runningRecord';
            $currentTimeInfo['deal_type'] = 'operate';
            $currentTimeInfo['start_time'] = $recordModel->start_running_time;
            $currentTimeInfo['end_time'] = $recordModel->end_running_time;
            $logInfo = Yii::t('base', 'operate_record') . Yii::t('base','recover');
        } else if ($recordType == '2') {
            $record = 'checkRecord';
            $recordModel = InstrumentCheckRecordModel::findOne(['id' => $recordId]);
            $currentTimeInfo['deal_type'] = 'check';
            $currentTimeInfo['start_time'] = $recordModel->start_check_time;
            $currentTimeInfo['end_time'] = $recordModel->end_check_time;
            $logInfo = Yii::t('base', 'check_record'). Yii::t('base','recover');
        } else if ($recordType == '1') {
            $record = 'repairRecord';
            $recordModel = InstrumentRepairRecordModel::findOne(['id' => $recordId]);
            $currentTimeInfo['deal_type'] = 'repair';
            $currentTimeInfo['start_time'] = $recordModel->repair_start_time;
            $currentTimeInfo['end_time'] = $recordModel->repair_end_time;
            $logInfo = Yii::t('base', 'repair_record') . Yii::t('base','recover');
        } else {
            return $this->fail('操作类别错误');
        }

        //校验是否有两条未结束记录
        if ($recordModel->if_end == 0) {
            $company_id = \Yii::$app->view->params['curr_company_id'];
            $unFinishedResult = $instrumentServer->unFinishedRecord($instrumentId, $record, $company_id, 'recover');
            if ($unFinishedResult['status'] == 0) {
                return $this->fail($unFinishedResult['info'], 'JSON', ['tipType' => 'popContent']);
            }
        }

        //校验时间冲突
        $instrumentSet = InstrumentsModel::find()->select('record_setting')->where([
            'id' => $instrumentId
        ])->asArray()->one();
        $conflictInfo = $instrumentServer->checkTimeConflict($instrumentId, $instrumentSet['record_setting'], $currentTimeInfo);
        if ($conflictInfo !== '') {
            return $this->fail($conflictInfo, 'JSON', ['tipType' => 'popContent']);
        }
        //更新数据
        $recordModel->if_void = 0;
        if (!$recordModel->save()) {
            return $this->fail(json_encode($recordModel->getErrors()));
        }
        $instrumentServer->updateInstrumentsTimeRecord($instrumentId);
        //痕迹记录
        $action_details = Yii::t('base', 'record_number') . ': ' . $orderNumber . '</br>'
            . $logInfo;
        $instrumentServer->addInstrumentHistory($instrumentId, 20, ['action_details' => $action_details, 'orderNumber' => $orderNumber]);
        return $this->success([]);
    }
}
