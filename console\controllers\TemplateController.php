<?php

namespace console\controllers;
use yii\console\Controller;
use frontend\models\TemplateModel;
use frontend\interfaces\CenterInterface;
use frontend\models\TemplateForGroupModel;
use yii\base\UserException;
use frontend\models\TemplateHistoryNewModel;
use frontend\services\TempleServer;

class TemplateController extends Controller{
    
    /**
     * 数据处理
     * 
     * @date: 2018年7月2日 上午11:47:15
     * <AUTHOR>
     */
    public function actionDataDeal(){
        echo 'start_time:'.date('Y-m-d H:i:s');
        $limit = 1000;
        $page = 0;
        // 一、获取模板列表
        $query = TemplateModel::find()->where(['status'=>1])->andWhere(['is_system'=>0]);
        
        while ($tList = $query->limit($limit)->offset($page*$limit)->all()){
            $page++;
            foreach ($tList as $item){
                // 获取模板所有人所在的鹰群列表
                $interFace = new CenterInterface();
                $groups = $interFace->elnGroupListByUserId($item->user_id);
                foreach ($groups as $group){
                       
                       $tmpForGroup = TemplateForGroupModel::find()->where([
                           'group_id'=>$group['id'],
                           'template_id'=>$item['id'],
                           'status'=>1
                       ])->one();
                       
                       if ( empty( $tmpForGroup ) ){
                           $tmpForGroup = new TemplateForGroupModel();
                       }else {
                           continue;
                       }
                       
                       $tmpForGroup->setAttributes([
                           'group_id'=>$group['id'],
                           'template_id'=>$item['id']
                       ]);
                       if ( !$tmpForGroup->save() ){
                           throw new UserException(current($tmpForGroup->getFirstErrors()));
                       }
                }
            }
        }
        
        echo PHP_EOL, 'end_time:'.date('Y-m-d H:i:s');
    }
    
    /**
     * 初始化子模板
     * 
     * @date: 2018年7月2日 上午11:47:15
     * <AUTHOR>
     */
    public function actionInitTemplate(){
        echo 'start_time:'.date('Y-m-d H:i:s');
         /*更新以前闯将的子模板的记录*/
        $update_status="update template set `content`=REPLACE (`content`, '<span class=\"add_materiel_btn\">[REACTANT]', '<span class=\"add_materiel_btn\" type=\"Reactant\">[REACTANT]') where type=2 and email!='<EMAIL>' ";
        \Yii::$app->integle_ineln->createCommand($update_status)->execute();
        $update_status="update template set `content`=REPLACE (`content`, '<span class=\"add_materiel_btn\">[PRODUCT]', '<span class=\"add_materiel_btn\" type=\"Product\">[PRODUCT]') where type=2 and email!='<EMAIL>' ";
        \Yii::$app->integle_ineln->createCommand($update_status)->execute();
        $update_status="update template set `content`=REPLACE (`content`, '<span class=\"add_materiel_btn\">[SOLVENT]', '<span class=\"add_materiel_btn\" type=\"Solvent0\">[SOLVENT]') where type=2 and email!='<EMAIL>' ";
        \Yii::$app->integle_ineln->createCommand($update_status)->execute();
        $update_status="update template set `content`=REPLACE (`content`, '<span class=\"add_materiel_btn\">[CATALYST]', '<span class=\"add_materiel_btn\" type=\"Product\">[CATALYST]') where type=2 and email!='<EMAIL>' ";

	\Yii::$app->integle_ineln->createCommand($update_status)->execute();

        //exit;
        
        /*删除旧子模板*/
        $template = new TemplateModel();
        $template->deleteAll(['email'=>'<EMAIL>']);
        
       
        //逐条初始化子模板信息        
        //初始化第一条子模板
        $template = new TemplateModel();
        $template->name = 'Full reaction 1';
        $template->descript = 'Full reaction 1';
        $template->content = 'To a solution of <span class="add_materiel_btn" type="Reactant">[REACTANT]</span> in <span class="solvent"  type="Solvent0">[SOLVENT]</span> were added <span class="add_materiel_btn" type="Reactant">[REACTANT]</span>, <span class="add_materiel_btn" type="Reactant">[REACTANT]</span>, and <span class="add_materiel_btn" type="Reactant">[REACTANT]</span>, and the reaction was stirred at room temperature for <span class="time"  type="Time">[TIME]</span>. The reaction was diluted with <span class="solvent"  type="Solvent">[SOLVENT]</span> and <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>. The organic layer was separated, washed with further <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>, and concentrated in vacuo. The residue was purified using silica gel column chromatography eluting with <span class="column_eluent" type="Column Eluent">[COLUMN ELUENT]</span>. The residue was dissolved in <span class="solvent"  type="Solvent">[SOLVENT]</span> and further washed with <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span> three times. The organic layer was collected, concentrated in vacuo, and dried to afford the title compound <span class="add_materiel_btn" type="Product">[PRODUCT]</span>.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();   
        echo PHP_EOL, 'no.1 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二条子模板
        $template = new TemplateModel();
        $template->name = 'Full reation 2';
        $template->descript = 'Full reation 2';
        $template->content = 'To a flask containing <span class="add_materiel_btn" type="Reactant">[REACTANT]</span> was added <span class="solvent"  type="Solvent0">[SOLVENT]</span> followed by the addition of <span class="add_materiel_btn" type="Reactant">[REACTANT]</span>. The mixture was stirred at <span class="temperature" type="Temperature">[TEMPERATURE]</span> <span class="time"  type="Time">[TIME]</span>. The resulting <span class="color" type="Color">[COLOR]</span> solid was collected by filtration. The filter cake was washed with <span class="solvent"  type="Solvent">[SOLVENT]</span> and then dried under vacuum over <span class="time"  type="Time">[TIME]</span> to provide the title compound <span class="add_materiel_btn" type="Product">[PRODUCT]</span>. ';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save(); 
        echo PHP_EOL, 'no.2 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
        //初始化第三条子模板
        $template = new TemplateModel();
        $template->name = 'Set up reaction 1';
        $template->descript = 'Set up reaction 1';
        $template->content = 'In a <span class="volume" type="Volume">[VOLUME]</span> <span class="vessel" type="Vessel">[VESSEL]</span> was added <span class="add_materiel_btn" type="Reactant">[REACTANT]</span> and <span class="add_materiel_btn" type="Reactant">[REACTANT]</span> in <span class="solvent"  type="Solvent0">[SOLVENT]</span> to give a <span class="color" type="Color">[COLOR]</span> <span class="solution_suspension" type="Solution Suspension">[SOLUTION/SUSPENSION]</span>.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.3 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
        //初始化第四条子模板
        $template = new TemplateModel();
        $template->name = '开始反应1';
        $template->descript = '开始反应1';
        $template->content = '在<span class="volume" type="Volume">[VOLUME]</span><span class="vessel" type="Vessel">[VESSEL]</span>中加入反应物<span class="add_materiel_btn" type="Reactant">[REACTANT]</span>，<span class="add_materiel_btn" type="Reactant">[REACTANT]</span>和溶剂 <span class="solvent"  type="Solvent0">[SOLVENT]</span>，得到<span class="color" type="Color">[COLOR]</span> <span class="solution_suspension" type="Solution Suspension">[SOLUTION/SUSPENSION]</span>。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.4 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
        //初始化第五条子模板
        $template = new TemplateModel();
        $template->name = 'Set up reaction 2';
        $template->descript = 'Set up reaction 2';
        $template->content = '<span class="add_materiel_btn" type="Reactant">[REACTANT]</span> was added.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.5 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第六条子模板
        $template = new TemplateModel();
        $template->name = '开始反应2';
        $template->descript = '开始反应2';
        $template->content = '添加反应物<span class="add_materiel_btn" type="Reactant">[REACTANT]</span>。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.6 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第七条子模板
        $template = new TemplateModel();
        $template->name = 'Set up reaction 3';
        $template->descript = 'Set up reaction 3';
        $template->content = '<span class="add_materiel_btn" type="Reactant">[REACTANT]</span> and <span class="add_materiel_btn" type="Reactant">[REACTANT]</span> were added.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.7 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第八条子模板
        $template = new TemplateModel();
        $template->name = '开始反应4';
        $template->descript = '开始反应4';
        $template->content = '添加反应物<span class="add_materiel_btn" type="Reactant">[REACTANT]</span>和<span class="add_materiel_btn" type="Reactant">[REACTANT]</span>。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.8 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第九条子模板
        $template = new TemplateModel();
        $template->name = 'Reaction condition 1';
        $template->descript = 'Reaction condition 1';
        $template->content = 'The reaction mixture stirred at <span class="temperature" type="Temperature">[TEMPERATURE]</span>℃.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.9 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第十条子模板
        $template = new TemplateModel();
        $template->name = '反应条件1';
        $template->descript = '反应条件1';
        $template->content = '反应液在<span class="temperature" type="Temperature">[TEMPERATURE]</span>℃搅拌。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.10 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
        //初始化第十一条子模板
        $template = new TemplateModel();
        $template->name = 'Reaction condition 2';
        $template->descript = 'Reaction condition 2';
        $template->content = 'The reaction mixture waw stirred at <span class="temperature" type="Temperature">[TEMPERATURE]</span>℃ at <span class="protection_gas" type="Protection Gas">[PROTECTION GAS]</span> atmosphere.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.11 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第十二条子模板
        $template = new TemplateModel();
        $template->name = '反应条件2';
        $template->descript = '反应条件2';
        $template->content = '反应液在<span class="protection_gas" type="Protection Gas">[PROTECTION GAS]</span>，<span class="temperature" type="Temperature">[TEMPERATURE]</span>℃中搅拌。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.12 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第十三条子模板
        $template = new TemplateModel();
        $template->name = 'Reaction condition 3';
        $template->descript = 'Reaction condition 3';
        $template->content = 'The reaction mixture stirred in microwave at <span class="temperature" type="Temperature">[TEMPERATURE]</span>℃.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.13 init complete ; end_time:'.date('Y-m-d H:i:s');
        
       //初始化第十四条子模板
        $template = new TemplateModel();
        $template->name = '反应条件3';
        $template->descript = '反应条件3';
        $template->content = '反应液在微波中搅拌反应，温度<span class="temperature" type="Temperature">[TEMPERATURE]</span>℃。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.14 init complete ; end_time:'.date('Y-m-d H:i:s');
        
       //初始化第十五条子模板
        $template = new TemplateModel();
        $template->name = 'Dilute';
        $template->descript = 'Dilute';
        $template->content = 'The reaction mixture was diluted with <span class="solvent"  type="Solvent">[SOLVENT]</span>.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.15 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
        //初始化第十六条子模板
        $template = new TemplateModel();
        $template->name = '稀释';
        $template->descript = '稀释';
        $template->content = '反应液用<span class="solvent"  type="Solvent">[SOLVENT]</span>稀释。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.16 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
        //初始化第十七条子模板
        $template = new TemplateModel();
        $template->name = 'Wash';
        $template->descript = 'Wash';
        $template->content = 'Washed with <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>, <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span> and <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>. The <span class="layer" type="Layer">[LAYER]</span> was dried by <span class="drying_agent" type="Drying Agent">[DRYING AGENT]</span>, filtered and concentrated.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.17 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第十八条子模板
        $template = new TemplateModel();
        $template->name = '后处理—洗涤';
        $template->descript = '后处理—洗涤';
        $template->content = '用<span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>，<span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span> 和 <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>洗涤。<span class="layer" type="Layer">[LAYER]</span>用<span class="drying_agent" type="Drying Agent">[DRYING AGENT]</span>干燥，过滤和浓缩。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.18 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第十九条子模板
        $template = new TemplateModel();
        $template->name = 'Extraction';
        $template->descript = 'Extraction';
        $template->content = 'The aqueous layer was backextracted with  <span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>). Combined the  <span class="layer" type="Layer">[LAYER]</span> and washed with <span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>). The <span class="layer" type="Layer">[LAYER]</span> was dried by <span class="drying_agent" type="Drying Agent">[DRYING AGENT]</span>, filtered and concentrated.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.19 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十条子模板
        $template = new TemplateModel();
        $template->name = '后处理—反萃取';
        $template->descript = '后处理—反萃取';
        $template->content = '水层用<span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>)萃取。合并<span class="layer" type="Layer">[LAYER]</span>并且用<span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>)洗涤。<span class="layer" type="Layer">[LAYER]</span>用<span class="drying agent" type="Drying Agent">[DRYING AGENT]</span>干燥，过滤和浓缩。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.20 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十一条子模板
        $template = new TemplateModel();
        $template->name = 'Combine and extract';
        $template->descript = 'Combine and extract';
        $template->content = 'Combined the <span class="layer" type="Layer">[LAYER]</span> and extracted with <span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>).';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.21 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十二条子模板
        $template = new TemplateModel();
        $template->name = '合并萃取';
        $template->descript = '合并萃取';
        $template->content = '合并<span class="layer" type="Layer">[LAYER]</span>用<span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>)萃取。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.22 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十三条子模板
        $template = new TemplateModel();
        $template->name = 'Combine and wash';
        $template->descript = 'Combine and wash';
        $template->content = 'Combined the <span class="layer" type="Layer">[LAYER]</span> and washed with <span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>).';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.23 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        
         //初始化第二十四条子模板
        $template = new TemplateModel();
        $template->name = '合并洗涤';
        $template->descript = '合并洗涤';
        $template->content = '合并<span class="layer" type="Layer">[LAYER]</span>用<span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>)洗涤。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.24 init complete ; end_time:'.date('Y-m-d H:i:s');
        
         //初始化第二十五条子模板
        $template = new TemplateModel();
        $template->name = 'Filter';
        $template->descript = 'Filter';
        $template->content = 'The reaction mixture was filtered through <span class="filter" type="Filter">[FILTER]</span>.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.25 init complete ; end_time:'.date('Y-m-d H:i:s');
        
         //初始化第二十六条子模板
        $template = new TemplateModel();
        $template->name = '过滤';
        $template->descript = '过滤';
        $template->content = '反应液用<span class="filter" type="Filter">[FILTER]</span>过滤。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.26 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十七条子模板
        $template = new TemplateModel();
        $template->name = 'Biotage';
        $template->descript = 'Biotage';
        $template->content = 'The residue was purified via Biotage(<span class="ratio" type="Ratio">[RATIO]</span><span class="eluant" type="Eluant">[ELUANT]</span>; <span class="column_size" type="Column Size">[COLUMN SIZE]</span>column). Collected fractions.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.27 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十八条子模板
        $template = new TemplateModel();
        $template->name = 'Biotage';
        $template->descript = 'Biotage';
        $template->content = '剩余物用Biotage(<span class="ratio" type="Ratio">[RATIO]</span><span class="eluant" type="Eluant">[ELUANT]</span>; <span class="column_size" type="Column Size">[COLUMN SIZE]</span>柱)纯化。收集样品。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.28 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十九条子模板
        $template = new TemplateModel();
        $template->name = 'Trituration';
        $template->descript = 'Trituration';
        $template->content = 'The residue was triturated with <span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="blue">times</span>×<span class="volume" type="Volume">[VOLUME]</span>). The resulting solid was filtered through a <span class="filter" type="Filter">[FILTER]</span>, rinsed with <span class="solvent"  type="Solvent">[SOLVENT]</span> and collected.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.29 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第三十条子模板
        $template = new TemplateModel();
        $template->name = '研碎';
        $template->descript = '研碎';
        $template->content = '剩余物用<span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="blue">times</span>×<span class="volume" type="Volume">[VOLUME]</span>)研碎。残余固体用<span class="filter" type="Filter">[FILTER]</span>过滤，用<span class="solvent"  type="Solvent">[SOLVENT]</span>冲洗，并收集。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.30 init complete ; end_time:'.date('Y-m-d H:i:s');
        
         //初始化第三十一条子模板
        $template = new TemplateModel();
        $template->name = 'Chromatography';
        $template->descript = 'Chromatography';
        $template->content = 'The crude material was added to a <span class="column" type="Column">[COLUMN]</span> column and was eluted with <span class="ratio" type="Ratio">[RATIO]</span><span class="eluant" type="Eluant">[ELUANT]</span> , collected fractions.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.31 init complete ; end_time:'.date('Y-m-d H:i:s');
        
         //初始化第三十二条子模板
        $template = new TemplateModel();
        $template->name = '层析';
        $template->descript = '层析';
        $template->content = '粗品加入<span class="column" type="Column">[COLUMN]</span>用<span class="ratio" type="Ratio">[RATIO]</span><span class="eluant" type="Eluant">[ELUANT]</span>过柱，收集样品。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.32 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第三十三条子模板
        $template = new TemplateModel();
        $template->name = 'Prepare TLC';
        $template->descript = 'Prepare TLC';
        $template->content = 'The crude material was loaded on a silica gel plate. The plate was developed using <span class="ratio" type="Ratio">[RATIO]</span> <span class="eluant" type="Eluant">[ELUANT]</span>.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.33 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第三十四条子模板
        $template = new TemplateModel();
        $template->name = 'TLC制备';
        $template->descript = 'TLC制备';
        $template->content = '粗品用硅胶板上样并用<span class="ratio" type="Ratio">[RATIO]</span> <span class="eluant" type="Eluant">[ELUANT]</span>展开。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.34 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第三十五条子模板
        $template = new TemplateModel();
        $template->name = 'Analysis';
        $template->descript = 'Analysis';
        $template->content = '1 H NMR (<span class="blue">400</span> MHz, <span class="deuterium_reagents" type="Deuterium Reagents">[DEUTERIUM REAGENTS]</span>): δ ppm (s, H), (d, H), (t, H), (m, H), (br, H). MS m/z 325 [M + H]+; LCMS m/z 390 [M + H]+ HPLC <span class="purity" type="Purity">[PURITY]</span> at 254 nM (<span class="blue">XBridge RP18 50 mm × 2.1 mm × 5 μm</span>) Rt = <span class="blue">3.55</span> min; HRMS [M + H] for <span class="blue">C18H22F2N7O</span>, calcd, <span class="blue">390.1848</span>; found, <span class="blue">390.1850</span>; [α]D 20  <span class="blue">50.1 (c 1.27, EtOH)</span>. Analytical chiral SFC EE = <span class="blue">98.25</span>%, Rt = <span class="blue">7.695</span> min, <span class="blue">220 nM Chiralcel OJ-H 250 mm × 4.6 mm × 5 μm</span>, <span class="percent" type="Percent">[%]</span> <span class="column_eluent" type="Column Eluent">[COLUMN ELUENT]</span>.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.35 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第三十六条子模板
        $template = new TemplateModel();
        $template->name = 'Synthetic Methods and Characterization';
        $template->descript = 'Synthetic Methods and Characterization';
        $template->content = 'Reactions were carried out under <span class="blue">nitrogen</span> at <span class="blue">ambient temperature</span> with reagents and solvents typically obtained from <span class="blue">Sigma-Aldrich</span> unless otherwise noted. Thin-layer chromatog- raphy (TLC) was visualized with <span class="blue">ultraviolet (UV) light</span> on <span class="blue">60 Å F254</span> glass plates precoated with a <span class="blue">0.25 mm</span> thickness of silica gel purchased from <span class="blue">Sigma-Aldrich</span>. Silica gel column chromatography refers to normal phase chromatography using a <span class="blue">biotage</span> or <span class="blue">ISCO system</span> using prepacked commercial columns under medium pressure. Low- resolution mass spectroscopy was carried out using liquid chromatography−mass spectrometry (LC/MS) on a <span class="blue">Waters Acquity UPLC</span> instrument using <span class="blue">atmospheric pressure chemical ionization (APCI)</span> or <span class="blue">electrospray ionization (ESI)</span>. Proton (1 H NMR) magnetic resonance spectra were obtained on a <span class="blue">Varian or Bruker</span> instrument in <span class="blue">CDCl3, CD3OD, or DMSO-d6</span> at <span class="blue">400</span> MHz at <span class="blue">298 K</span> unless otherwise noted. The following abbreviations were utilized to describe peak patterns when appropriate: br = broad, s = singlet, d = doublet, and m = multiplet. Optical rotations were measured on an <span class="blue">Autopol Iv Automatic Polarimeter</span> at ambient temperature and reported as an average of three runs unless otherwise noted. High-resolution mass measurements were obtained on an <span class="blue">Agilent 6200 or 6500 series ToF</span> or <span class="blue">Thermo Scientific Q Exactive mass spectrometer</span>. Compounds used in biological studies had purities that were >95%, determined by HPLC, UPLC, or LC/MS based on ultraviolet detection at <span class="blue">214</span> or <span class="blue">254</span> nm.  ';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.36 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第三十七条子模板
        $template = new TemplateModel();
        $template->name = 'X-ray Crystallography';
        $template->descript = 'X-ray Crystallography';
        $template->content = 'Data collection for all complexes was conducted at <span class="blue">100 K</span>, beamline 17ID outfitted with a <span class="blue">Pilatus 6 M detector</span> (<span class="blue">Dectris Ltd., Switzerland</span>). Data processing was performed using <span class="blue">AUTOPROC33</span> and <span class="blue">XDS</span>. All other data manipulations utilized the <span class="blue">CCP4 suite</span> of programs. A common <span class="blue">set (5%)</span> of test reflections was used throughout refinement for cross- validation. Structures were determined by rigid body and all-atom positional refinement using <span class="blue">REFMAC36</span> and an <span class="blue">isomorphous</span> model from which all waters and bound ligands had been removed. Binding of the compound of interest was confirmed visually, and the compound was fit automatically using the program <span class="blue">RHOFIT</span>. ';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->save();
        echo PHP_EOL, 'no.37 init complete ; end_time:'.date('Y-m-d H:i:s');
        
       // $query = TemplateModel::find()->where(['status'=>1])->andWhere(['is_system'=>0]);
        
       
        
        echo PHP_EOL, 'end_time:'.date('Y-m-d H:i:s');
    }
    
    
    
    /**
     * 初始化子模板
     * 
     * @date: 2018年7月2日 上午11:47:15
     * <AUTHOR>
     */
    public function actionFixTemplate(){
        echo 'start_time:'.date('Y-m-d H:i:s');
         /*更新以前闯将的子模板的记录*/
        
        //初始化第十九条子模板
        $template = new TemplateModel();
        $template->name = 'Extraction';
        $template->descript = 'Extraction';
        $template->content = 'The aqueous layer was backextracted with  <span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>). Combined the  <span class="layer" type="Layer">[LAYER]</span> and washed with <span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>). The <span class="layer" type="Layer">[LAYER]</span> was dried by <span class="drying_agent" type="Drying Agent">[DRYING AGENT]</span>, filtered and concentrated.';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->updateAll(['content'=>$template->content],['name'=>$template->name] );
        echo PHP_EOL, 'no.19 init complete ; end_time:'.date('Y-m-d H:i:s');
        
        //初始化第二十条子模板
        $template = new TemplateModel();
        $template->name = '后处理—反萃取';
        $template->descript = '后处理—反萃取';
        $template->content = '水层用<span class="solvent"  type="Solvent">[SOLVENT]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>)萃取。合并<span class="layer" type="Layer">[LAYER]</span>并且用<span class="workup_solution" type="Workup Solution">[WORKUP SOLUTION]</span>( <span class="times" type="Times" >times</span>×<span class="volume" type="Volume">[VOLUME]</span>)洗涤。<span class="layer" type="Layer">[LAYER]</span>用<span class="drying agent" type="Drying Agent">[DRYING AGENT]</span>干燥，过滤和浓缩。';
        $template->user_id = 0;
        $template->email = '<EMAIL>';
        $template->type = 2;        
        $template->updateAll(['content'=>$template->content],['name'=>$template->name] );
        echo PHP_EOL, 'no.20 init complete ; end_time:'.date('Y-m-d H:i:s');
        
               
       // $query = TemplateModel::find()->where(['status'=>1])->andWhere(['is_system'=>0]);
        
       
        
        echo PHP_EOL, 'end_time:'.date('Y-m-d H:i:s');
    }


    /**
     * 将所有今天生效的is_pending（待生效）状态的模板历史设为生效
     * php yii template/deal-pending-history
     */
    public function actionDealPendingHistory(){
        // 设置输出编码
        if (DIRECTORY_SEPARATOR === '\\') {
            system('chcp 65001'); // Windows环境设置UTF-8编码
        }
        // 使用echo打印到终端
        echo "开始处理待生效模板历史记录 - 开始时间: " . date('Y-m-d H:i:s') . PHP_EOL;
        //为了测试暂时把生效时间判断标准设置为2025/10/01
        $today = '2025-10-01';//date('Y-m-d');//为了测试暂时把生效时间判断标准设置为2025/10/01，正式上线后需要改为date('Y-m-d')
        $pendingHistories = TemplateHistoryNewModel::find()
            ->where(['status' => TemplateHistoryNewModel::$status['is_pending']])
            ->andWhere(['<=', 'effect_date', $today])
            ->all();
        
        echo "找到 " . count($pendingHistories) . " 条待生效的模板历史记录" . PHP_EOL;
        
        $successCount = 0;
        $failCount = 0;
        
        foreach ($pendingHistories as $history) {
            echo "处理模板ID: {$history->template_id}, 历史记录ID: {$history->id}, 版本: {$history->user_version}" . PHP_EOL;
            
            try {
                // 调用审核通过的方法
                $server = new TempleServer();
                $result = $server->agreeHistory($history->id, 0);
                
                if ($result['status'] == 1) {
                    $versionResult = $server->handleTemplateVersionsOnPublish($history->template_id, $history->id, true);
                    $history->status = TemplateHistoryNewModel::$status['is_agreed'];
                    $history->actual_effect_time = date('Y-m-d H:i:s');
                    
                    if ($history->save()) {
                        echo "模板ID: {$history->template_id} 处理成功" . PHP_EOL;
                        $successCount++;
                    } else {
                        echo "模板ID: {$history->template_id} 保存失败: " . json_encode($history->getErrors()) . PHP_EOL;
                        $failCount++;
                    }
                } else {
                    echo "模板ID: {$history->template_id} 审核失败: {$result['info']}" . PHP_EOL;
                    $failCount++;
                }
            } catch (\Exception $e) {
                echo "模板ID: {$history->template_id} 处理异常: {$e->getMessage()}" . PHP_EOL;
                $failCount++;
            }
        }
        
        echo "处理完成，成功: {$successCount}，失败: {$failCount}" . PHP_EOL;
        echo "结束时间: " . date('Y-m-d H:i:s') . PHP_EOL;
    }
}
