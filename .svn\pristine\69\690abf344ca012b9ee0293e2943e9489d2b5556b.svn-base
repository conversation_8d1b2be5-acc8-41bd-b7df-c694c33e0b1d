<?php

namespace frontend\controllers;

use frontend\controllers\MyController;
use frontend\interfaces\CenterInterface;
use frontend\models\InstrumentsBookModel;
use frontend\services\InstrumentServer;
use yii\helpers\ArrayHelper;

class InstrumentBookingController extends MyController
{
    public function actionGetInstrumentsData()
    {
        $postData = \Yii::$app->getRequest()->post();

        // 用于查询我加入的鹰群或部门
        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;

        // 获取查看权限
        // $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        } else {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        }
        if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }
        // 获取查询条件
        $curPage = isset($postData['curPage']) ? $postData['curPage'] : 1;
        $limit = isset($postData['pageSize']) ? $postData['pageSize'] : \Yii::$app->params['default_page_size'];
        // instrumentParam可能是仪器名称也可能是仪器ID，现根据名称模糊查询再根据仪器ID模糊查询
        $where['instrumentParam'] = isset($postData['instrumentParam']) ? $postData['instrumentParam'] : '';
        $where['bookTime'] = isset($postData['bookTime']) ? $postData['bookTime'] : '';
        $where['onlyBooked'] = isset($postData['onlyBooked']) ? $postData['onlyBooked'] : false;
        $where['lang'] = isset($postData['lang']) ? $postData['lang'] : 'cn';
        // 获取查询的数据
        $data = (new InstrumentServer())->queryInstrumentsWithBooking($where, $limit, $curPage);
        return $this->success($data);
    }

    /**
     * Notes: 撤销仪器预约记录（将status字段设置为0）
     * Author: ChenQi
     * Date: 2025/6/10 18:15
     * @return \common\controllers\json
     */
    public function actionDeleteInstrumentBooking()
    {
        $postData = \Yii::$app->getRequest()->post();
        $id = isset($postData['id']) ? $postData['id'] : null;
        // 验证ID参数
        if (empty($id)) {
            return $this->fail('预约记录ID不能为空');
        }

        try {
            // 先查找记录，确保存在且可以撤销
            $booking = InstrumentsBookModel::findOne($id);

            if (!$booking) {
                return $this->fail('未找到指定的预约记录');
            }
            // 更新状态
            $booking->status = 0; // 已撤销
            $booking->update_by = $this->userinfo->id;
            $booking->update_time = date('Y-m-d H:i:s');
            if ($booking->save()) {

                return $this->success(['message' => '预约记录已成功撤销']);
            } else {
                return $this->fail('撤销预约记录失败：' . json_encode($booking->getErrors(), JSON_UNESCAPED_UNICODE));
            }

        } catch (\Exception $e) {
            return $this->fail('操作失败：' . $e->getMessage());
        }
    }

    /**
     * Notes: 批量撤销仪器预约记录（将status字段设置为0）
     * Author: ZSM
     * Date: 2025/6/12 10:50
     * @return \common\controllers\json
     */
    public function actionBatchDeleteInstrumentBooking()
    {
        $postData = \Yii::$app->getRequest()->post();
        $ids = isset($postData['idList']) ? $postData['idList'] : null; // Expecting an array of IDs

        // 验证IDs参数
        if (empty($ids) || !is_array($ids)) {
            return $this->fail('预约记录ID不能为空且必须是数组');
        }

        try {
            // 查找所有符合的预约记录
            $bookings = InstrumentsBookModel::findAll(['id' => $ids]);

            if (empty($bookings)) {
                return $this->fail('未找到指定的预约记录');
            }

            // 批量更新状态
            foreach ($bookings as $booking) {
                $booking->status = 0; // 已撤销
                $booking->update_by = $this->userinfo->id;
                $booking->update_time = date('Y-m-d H:i:s');

                if (!$booking->save()) {
                    return $this->fail('撤销预约记录失败：' . json_encode($booking->getErrors(), JSON_UNESCAPED_UNICODE));
                }
            }

            return $this->success(['message' => '预约记录已成功撤销']);

        } catch (\Exception $e) {
            return $this->fail('操作失败：' . $e->getMessage());
        }
    }

    /**
     * Notes: 获取我的预约记录页面下所需要的数据 以及 用户权限信息（给inform匹配）
     * Author: zsm
     * Date: 2025/6/5 14:30
     * @return \common\controllers\json
     */
    public function actionGetMyInstrumentBookingInfo()
    {

        $create_by = $this->userinfo->id; // 只查询我自己的预约

        // 获取我的预约记录数据（不分页）
        $bookingData = (new InstrumentServer())->listMyBook($create_by);

        // 判断借用记录当前的状态 1 已预约 2 已撤销 3 使用者 4 已完成
        $currentTime = date('Y-m-d H:i:s'); // 获取当前时间

        foreach ($bookingData['my_book_list'] as &$book) {
            if ($book['status'] == 0) {
                $book['status'] = 2; // 如果 status 为 0，则修改为 2
            } elseif ($book['status'] == 1) { // 只有当 status 为 1 时才需要判断
                if (strtotime($currentTime) < strtotime($book['start_time'])) {
                    $book['status'] = 1; // 当前时间小于 start_time，status 仍为 1
                } elseif (strtotime($currentTime) > strtotime($book['end_time'])) {
                    $book['status'] = 4; // 当前时间大于 end_time，status 设置为 4
                } else {
                    $book['status'] = 3; // 当前时间介于 start_time 和 end_time 之间，status 设置为 3
                }
            }
        }

        // 获取用户信息数据
        $allUsers = array_unique(array_merge(
            array_column($bookingData['my_book_list'], 'update_by'),
            array_column($bookingData['my_book_list'], 'create_by')
        ));

        $usersRes = (new CenterInterface())->userDetailsByUserIds($allUsers);

        return $this->success(['bookingList' => $bookingData, 'extra' => $usersRes]);
    }

    /**
     * Notes: 仪器库管理 -> 预约记录 inform 查询接口 有权限的仪器的预约记录
     * Author: zsm
     * Date: 2025/6/6 17:30
     * @return \common\controllers\json
     */
    public function actionGetInstrumentBookingInfo()
    {
        // TODO
        $postData = \Yii::$app->getRequest()->post();

        // 用于查询我加入的鹰群或部门
        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;

        // 获取查看权限
        // $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        } else {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        }

        if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }
        // 是否可编辑
        $isCanWrite = \Yii::$app->view->params['instruments_manage_write'];

        $bookingData = (new InstrumentServer())->queryInstrumentsManageWithBooking($where);

        $currentTime = new \DateTime();  // 获取当前时间

        foreach ($bookingData as &$booking) {
            // 确保start_time和end_time的格式正确
            $startTime = new \DateTime($booking['start_time']);
            $endTime = new \DateTime($booking['end_time']);

            // 如果status为0，设置为4
            if ($booking['status'] == 0) {
                $booking['status'] = 4;
                continue; // 跳过这次循环
            }

            // 如果end_time小于当前时间，设置status为3
            if ($endTime < $currentTime) {
                $booking['status'] = 3;
            }
            // 如果当前时间在start_time之前，设置status为1
            if ($startTime > $currentTime) {
                $booking['status'] = 1;
            }
            // 如果当前时间在start_time和end_time之间，设置status为2
            if ($startTime <= $currentTime && $endTime >= $currentTime) {
                $booking['status'] = 2;
            }

        }


        $file = $this->renderAjax('/setting/instrument_book_record.php', []);


        // 获取用户信息数据
        $allUsers = array_unique(array_merge(
            array_column($bookingData, 'update_by'),
            array_column($bookingData, 'create_by')
        ));

        $usersRes = (new CenterInterface())->userDetailsByUserIds($allUsers);

        $data['my_book_list'] = $bookingData;
        $data['extra'] = $usersRes;
        $data['isCanWrite'] = $isCanWrite;

        return $this->success(['contentHtml' => $file, 'data' => $data]);

    }


}