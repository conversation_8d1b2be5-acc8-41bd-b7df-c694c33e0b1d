<template>
  <ElDialog
      v-model="dialogVisible"
      title=""
      width="430px"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :modal="false"
      :append-to-body="true"
      modal-class="delete-booking-dialog"
      align-center
      @close="handleCancel"
  >
  <template #header>
      <div class="delete-dialog-content">
        <div class="header-left">
          <div class="delete-dialog-icon">
            <ElIcon class="warning-icon">
              <Delete />
            </ElIcon>
          </div>
        </div>
        <div class="header-right">
          <div class="delete-dialog-title">{{ t('bookInstruments.deleteConfirmTitle') }}</div>
          <div class="delete-dialog-text">{{ t('bookInstruments.deleteConfirmText', { instrument: bookingInfo.instrument, time: bookingInfo.bookedTime?.join(' - ') }) }}</div>
        </div>
      </div>
  </template>
    <template #footer>
      <div class="delete-dialog-footer">
        <ElButton @click="handleCancel">{{ t('bookInstruments.cancel') }}</ElButton>
        <ElButton type="primary" @click="handleConfirm" :loading="loading">{{ t('bookInstruments.confirm') }}</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, withDefaults } from 'vue'
import { ElDialog, ElButton, ElIcon } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

// 定义 props
interface BookingInfo {
  bookingId: string | number
  experimenter: string
  bookedTime: string[]
  instrument?: string
}

interface Props {
  visible: boolean
  bookingInfo: BookingInfo | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  bookingInfo: null,
  loading: false
})

// 国际化
const { t } = useI18n()

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [bookingInfo: BookingInfo]
  'cancel': []
}>()

// 内部状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value)
  }
})

// 事件处理
const handleConfirm = () => {
  if (props.bookingInfo) {
    emit('confirm', props.bookingInfo)
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}
</script>

<style scoped>
/* 删除确认对话框样式 */
.delete-dialog-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.header-right {
  flex: 1; /* 占据剩余空间 */
  min-width: 0; /* 允许文字换行 */
}
.header-left{
  width: 50px;
  height: 50px;
  background-color: #FAEEEB;
  border-radius: 48px;
  margin-top: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止被压缩 */
}
.delete-dialog-icon {
  width: 36px;
  height: 36px;
  border-radius: 15px;
  background-color: #FDE4DD;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止被压缩 */
}

.warning-icon {
  font-size: 20px;
  font-weight: bold;
  color: #FF6B47;
}

.delete-dialog-title {
  line-height: 25px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.delete-dialog-text {
  font-size: 14px;
  color: #606266;
}

.delete-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.delete-dialog-footer .el-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
}

.delete-dialog-footer .el-button--default {
  color: #606266;
  border-color: #DCDFE6;
  background-color: #FFFFFF;
}

.delete-dialog-footer .el-button--primary {
  background-color: #FF6B47;
  border-color: #FF6B47;
  color: #FFFFFF;
}

.delete-dialog-footer .el-button--primary:hover {
  background-color: #FF5722;
  border-color: #FF5722;
}

:deep(.delete-booking-dialog .el-dialog__close) {
  color: #C0C4CC;
  font-size: 16px;
}

:deep(.delete-booking-dialog .el-dialog__close:hover) {
  color: #909399;
}
</style>
<style>
.delete-booking-dialog {
  border-radius: 12px;
  padding: 0;
}
/* 全局样式 - 用于修改 Element Plus 对话框的 footer */
.delete-booking-dialog footer {
  padding: 36px 10px 0;
  border: none ;
  background-color: transparent;
  margin: 0;
  height: 70px;
}
.delete-booking-dialog .el-dialog {
  border-radius: 12px;
}
</style>