<?php

use frontend\services\CompanyServer;

$userInfo = Yii::$app->session->get('userinfo');
$recycleSetting = (new CompanyServer())->getCompanySetting([
    'ALLOW_CREATER_MOVE_BOOKK_TO_RECYCLE',
]);
$canMoveToRecycle = $recycleSetting['data']['ALLOW_CREATER_MOVE_BOOKK_TO_RECYCLE']['value'];

?>

<table class="user_module_list">
    <tr class="tr_bg">
        <td class="w50"><input type="checkbox" id="template_checkbox_all" /><label for="template_checkbox_all"></label></td>
        <td><?php echo Yii::t('temp', 'name')?></td>
        <td><?php echo Yii::t('temp', 'temp_descript')?></td>
        <!-- 只在发布管控模式下显示版本号列 -->
        <?php if (isset($templateEffectMode) && $templateEffectMode == 2): ?>
        <td><?php echo Yii::t('temp', 'version')?></td>
        <?php endif; ?>
        <td><?php echo Yii::t('temp', 'temp_type')?></td>
        <td><?php echo Yii::t('temp', 'temp_from')?></td>
        <td><?php echo Yii::t('base', 'create_time')?></td>
        <td><?php echo Yii::t('base', 'update_time')?></td>        
        <td><?php echo Yii::t('base', 'action')?></td>
    </tr>
    <?php if(!empty($tempLateData)){?>
        <?php foreach($tempLateData as $val){ ?>
            <tr data-id="<?php echo $val['id']?>">
                <td>
                    <?php
                    if ($val['is_system'] != 1) {
                        echo '<label><input type="checkbox" class="template-checkbox" value="' . $val['id'] . '" /></label>';
                    }
                    ?>
                </td>
                <td><?php echo '<a class="opts_btn" temp-type="'.$val['type'].'" temp-id="'.$val['id'].'">'.$val['name'].'</a>';?></td>
                <td class="truncate" title="<?php echo $val['descript']; ?>"><?php echo $val['descript']; ?></td>
                <!-- 只在发布管控模式下显示版本号 -->
                <?php if (isset($templateEffectMode) && $templateEffectMode == 2): ?>
                <td><?php echo $val['active_version'];?></td>
                <?php endif; ?>
                <!--模板类型-->
                <td>
                    <?php

                    $templateSubtypeList = isset($templateSubtypeList) && is_array($templateSubtypeList) ? 
                        array_column($templateSubtypeList, null, 'id') : [];

                    
                    $subtypeName = !isset($templateSubtypeList[$val['subtype_id']])||
                    $templateSubtypeList[$val['subtype_id']]['id'] == 0 ? Yii::t('temp', 'uncategorized') : $templateSubtypeList[$val['subtype_id']]['name'] ;
                    $subtypeName = $subtypeName == Yii::t('temp', 'uncategorized') ? '' : ' - ' . $subtypeName;
                    //如果子类型名称为空，则不显示
                    if(1 == $val['is_system'] || $val['type']==3 || $val['email'] == '<EMAIL>'){
                        $templateType = Yii::t('temp', 'normal_temp');
                    }else if(2 == $val['type']){
                        $templateType = Yii::t('temp', 'intext_template') . $subtypeName;
                    }else if( 4 == $val['type']){
                        $templateType = Yii::t('temp', 'intable_template'). $subtypeName;
                    }else if(1 == $val['type']){
                        $templateType = Yii::t('temp', 'all_temp'). $subtypeName;
                    }
                    
                    // 限制显示长度，超过则显示省略号
                    $maxLength = 20; // 最大显示字符数
                    $displayType = mb_strlen($templateType) > $maxLength ? 
                        mb_substr($templateType, 0, $maxLength) . '...' : 
                        $templateType;
                    
                    echo '<span class="truncate" title="' . htmlspecialchars($templateType) . '">' . $displayType . '</span>';
                    ?>
                </td>
                <td>
                    <?php if (1 == $val['is_system'] || empty($val['user_id'])) {
                        echo Yii::t('temp', 'integle_share');
                    } else {
                        if ($val['is_company'] === '1') {
                            echo Yii::t('base', 'company_temp') . '<br/>(' . $userList[$val['user_id']]['real_name'] . ')';
                        }
                        else if (!empty($val['previous_id'])&&isset($val['tfrom'])&&$val['tfrom']==4) {//added by xieyuxiang 2022.8.22 增加转让来源
                            echo Yii::t('temp','transfer'). '<br/>(' . $userList[$val['previous_id']]['real_name'] . ')';
                        }
                        else if (isset($val['user_id']) && isset($this->params['curr_user_id']) && ($this->params['curr_user_id'] == $val['user_id'])&&isset($val['tfrom'])&&$val['tfrom']==2 ) {
                            echo  $userList[$val['user_id']]['real_name'] ;
                        }
                        else if(isset($val['tfrom'])&&$val['tfrom']==1)
                        {
                            echo  $userList[$val['user_id']]['real_name'] ;

                        }
                        else if(isset($val['tfrom'])&&$val['tfrom']==3)
                        {
                            echo Yii::t('temp', 'copy').'<br/>(' . $userList[$val['user_id']]['real_name'] . ')';
                        }
                        else{
                            echo $val['email'] . Yii::t('base', 'shared');
                        }
                    } ?>
                </td>
                <td><?php echo $val['create_time'];?> <?php echo $val['edit_user_id'];?></td>
                <td><?php echo $val['update_time'];?></td>                
                <td>
                    <a href="javascript:void(0)" class="opts_btn table-ico look-ico" temp-type="<?php echo $val['type'];?>" temp-id="<?php echo $val['id'];?>" data-type="show" title="<?php echo Yii::t('base', 'view_or_edit');?>"></a>
                    <?php if('share' != $type && 1 != $val['is_system']){ ?>
                        <!--bug#30918只有审核已通过的模板才可以分享 mod dx-->
                        <?php if ($val['step'] == 3) { ?>
                            <a href="javascript:void(0)" class="share_module table-ico share-ico" temp-id="<?php echo $val['id'];?>" title="<?php echo Yii::t('base', 'share');?>"></a>
                        <?php } ?>
                        <?php if (($val['user_id'] == \Yii::$app->view->params['curr_user_id']) && ($canMoveToRecycle == 1)) { ?>
                            <a href="javascript:void(0)" class="del_module_temp table-ico file-ico"
                               temp-id="<?php echo $val['id']; ?>"
                               title="<?php echo Yii::t('base', 'move_recycle'); ?>"></a>
                        <?php } ?>
                        <?php if(!empty($val['needApproval'])) {?>
                            <?php if (in_array($val['step'], [1, 4])) { // 草稿状态可以提交审核 ?>
                                <a href="javascript:void(0)" class="submit-temp-audit table-ico"  temp-id="<?php echo $val['id'];?>" title="<?php echo Yii::t('temp', 'submit_temp_audit');?>"></a>
                            <?php } else if ($val['step'] == 2) { // 审核状态可以撤销审核 ?>
                                <a href="javascript:void(0)" class="cancel-temp-audit table-ico"  temp-id="<?php echo $val['id'];?>" title="<?php echo Yii::t('temp', 'cancel_temp_audit');?>"></a>
                            <?php }
                                    //   else if ($val['step'] == 5){ //模板当前正在企业模板审批，显示撤销企业模板审批按钮 ?>
                                    <!--      <a href="javascript:void(0)" class="cancel-temp-audit table-ico"  temp-id="--><?php //= $val['id'];?><!--" title="--><?php //= Yii::t('temp', 'cancel_company_temp_audit');?><!--"></a>-->
                                    <!--  --><?php //}?>
                        <?php }?>

                        <!--模板当前正在企业模板审批，显示撤销企业模板审批按钮-->
                        <!--bug#33527,提交企业模板审批后，模板仍为普通模板，根据审批设置可能不会被表记为审批，因此将撤销企业模板审批的按钮迁移到需要审批的情况之外-->
                        <?php if ($val['step'] == 5) { ?>
                            <a href="javascript:void(0)" class="cancel-temp-audit table-ico"  temp-id="<?= $val['id'];?>" title="<?= Yii::t('temp', 'cancel_company_temp_audit');?>"></a>
                        <?php } ?>

<!--                        过滤掉方法模板-->
                        <?php if ($val['type'] == 1): ?>
                            <a href="javascript:void(0)" class="view-temp-history table-ico" temp-id="<?php echo $val['id'];?>" temp-power="<?php echo isset($edit_template_manage) ? $edit_template_manage : 0; ?>" effect-mode="<?php echo isset($templateEffectMode) ? $templateEffectMode : 1; ?>" update-time="<?php echo isset($val['update_time']) ? $val['update_time'] : ''; ?>"><i class="ico history"></i></a>      
                            <div id="template-history-dialog" style="display: none; position: absolute;"></div>                  
                        <?php endif; ?>
                    <?php }?>

                    <?php if(1 != $val['is_system'] && $set_conmany_template_write){ ?>
                        <a style="<?=3==$val['step']?'':'display:none'?>" href="javascript:void(0)" class="<?=3==$val['step']?'':'hidden'?> set_company_temp table-ico <?php echo $val['is_company']?'company-ico':'cancel-company-ico';?> " temp-id="<?php echo $val['id'];?>" data-val='<?php echo $val['is_company'];?>' title="<?php echo $val['is_company']?Yii::t('base', 'cancel_is_company'):Yii::t('base', 'set_is_company');?>"></a>
                    <?php }?>

                    <!--                        过滤掉方法模板-->
                    <?php if ($val['type'] != 2 && $val['type'] != 4): ?>
                        <a href="javascript:void(0)" class="hide_module table-ico <?=empty($val['isHide']) ? 'temp_show' : 'temp_hide'?>" data-hide="<?=empty($val['isHide']) ? 0 : 1?>" data-id="<?=$val['id']?>" title="<?=empty($val['isHide']) ? Yii::t('base', 'hide_temp') : Yii::t('base', 'show_temp')?>"></a>
                    <?php endif; ?>

                    <!--转让模板 , 只有userid是自己才有转让功能-->
                    <?php if ( $val['user_id']==$userInfo['id']): ?>
                        <a href="javascript:void(0)" class="transfer_module table-ico transfer_ico" data-id="<?=$val['id']?>" data-owner_id="<?=$val['user_id']?>" data-operator_id="<?=$userInfo['id']?>" title=" <?=Yii::t('temp', 'transfer') ?>"></a>
                    <?php endif; ?>


                </td>
            </tr>
        <?php } }?>
</table>

<div class="page_box"  style="padding-right:60px;" data-limit="<?php echo $limit;?>"  data-num="<?php echo !empty($totalCount) ? $totalCount: 0; ?>" ></div>
