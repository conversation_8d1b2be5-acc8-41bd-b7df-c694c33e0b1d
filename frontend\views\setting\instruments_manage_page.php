<?php

use yii\helpers\Url;
use common\components\Picture;

?>

<style>
    /* 占位符元素背景色 */
    .sortable-ghost {
        background-color: #e7f4ff !important;
    }
</style>

<div class="fixHeaderOuter "
     id="instrument_<?= ($type == "instruments_manage" ? "manage" : "mine") ?>_table_showHideColumn"
     data-type="instruments">
    <div id="instrumentBookCreate"></div>
    <div class="thead-fixed">
        <table data-type="instruments" class = "<?= $type == "instruments_manage" ? "instruments_manage_page" : " " ?>">
            <thead>
            <?php
            $orderType = !empty($orderType) ? ($orderType == 'asc' ? 'ascending' : ($orderType == 'desc' ? 'descending' : '')) : '';
            $orderField = !empty($orderField) ? $orderField : '';
            ?>
            <tr>

                <th class="w50">
                    <input id="checkAll" type="checkbox" name="">
                    <!--                    <label for="checkAll">-->
                    <? //= Yii::t('base', 'select_all'); ?><!--</label>-->
                </th>
                <?php foreach ($fieldConfigShowFields as $field): ?>
                    <?php switch ($field):
                        case 'picture': ?>
                            <th class="basic_field w155 <?= $orderField == 'picture' ? $orderType : '' ?>"
                                data-field="picture">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"> <?= Yii::t('base', 'picture'); ?></div>
                                <span class="sort-field"></span>
                            </th>
                            <?php break; ?>
                        <?php case 'name' : ?>
                            <th class="basic_field w155 <?= $orderField == 'name' ? $orderType : '' ?>" data-field="name">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"> <?= Yii::t('base', 'name'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'batch_number' : ?>
                            <th class="basic_field w155 <?= $orderField == 'batch_number' ? $orderType : '' ?> "
                                data-field="batch_number">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'batch_number'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'available_slots' : ?>
                            <th class="basic_field w155 <?= $orderField == 'available_slots' ? $orderType : '' ?>" data-field="available_slots">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"> <?= Yii::t('base', 'available_slots'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'max_advance_day' : ?>
                            <th class="basic_field w155 <?= $orderField == 'max_advance_day' ? $orderType : '' ?>" data-field="max_advance_day">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"> <?= Yii::t('base', 'max_advance_day'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'min_advance' : ?>
                            <th class="basic_field w155 <?= $orderField == 'min_advance' ? $orderType : '' ?>" data-field="min_advance">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"> <?= Yii::t('base', 'min_advance'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'max_booking_duration' : ?>
                            <th class="basic_field w155 <?= $orderField == 'max_booking_duration' ? $orderType : '' ?>" data-field="max_booking_duration">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"> <?= Yii::t('base', 'max_booking_duration'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'specification' : ?>
                            <th class="basic_field w155 <?= $orderField == 'specification' ? $orderType : '' ?> "
                                data-field="specification">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'specification'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'instrument_type' : ?>
                            <th class="basic_field w155 <?= $orderField == 'instrument_type' ? $orderType : '' ?> "
                                data-field="instrument_type">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'instrument_type'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'model' : ?>
                            <th class="basic_field w155 <?= $orderField == 'model' ? $orderType : '' ?> "
                                data-field="model">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'model'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'manufacturer' : ?>
                            <th class="basic_field w155 <?= $orderField == 'manufacturer' ? $orderType : '' ?> "
                                data-field="manufacturer">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'manufacturer'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'supplier' : ?>
                            <th class="basic_field w155 <?= $orderField == 'supplier' ? $orderType : '' ?> "
                                data-field="supplier">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'supplier'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'position' : ?>
                            <th class="basic_field w155 <?= $orderField == 'position' ? $orderType : '' ?> "
                                data-field="position">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'position'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'status' : ?>
                            <th class="basic_field w155 <?= $orderField == 'status' ? $orderType : '' ?> "
                                data-field="status">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'status'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'check_situation' : ?>
                            <th class="basic_field w200 <?= $orderField == 'check_situation' ? $orderType : '' ?> "
                                data-field="check_situation">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'check_situation'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'data_type' : ?>
                            <th class="basic_field w155 <?= $orderField == 'data_type' ? $orderType : '' ?> "
                                data-field="data_type">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'data_type'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'create_by' : ?>
                            <th class="basic_field w155 <?= $orderField == 'create_by' ? $orderType : '' ?> "
                                data-field="create_by">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'create_person'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'create_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'create_time' ? $orderType : '' ?> "
                                data-field="create_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'create_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'responsible_person' : ?>
                            <th class="basic_field w180 <?= $orderField == 'responsible_person' ? $orderType : '' ?>"
                                data-field="responsible_person">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'response_person_1'); ?></div>
                            </th>
                            <?php break; ?>
                        <?php case 'in_charge_person' : ?>
                            <th class="basic_field w180 <?= $orderField == 'in_charge_person' ? $orderType : '' ?>"
                                data-field="in_charge_person">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'person_in_charge'); ?></div>
                            </th>
                            <?php break; ?>
                        <?php case 'maintenance_person' : ?>
                            <th class="basic_field w180 <?= $orderField == 'maintenance_person' ? $orderType : '' ?>"
                                data-field="maintenance_person">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'maintainer'); ?></div>
                            </th>
                            <?php break; ?>
                        <?php case 'remark' : ?>
                            <th class="basic_field w155 <?= $orderField == 'remark' ? $orderType : '' ?>"
                                data-field="remark">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'remark'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'groupIds' : ?>
                            <th class="basic_field w155 <?= $orderField == 'groupIds' ? $orderType : '' ?>"
                                data-field="groupIds">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'group_search'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'departmentIds' : ?>
                            <th class="basic_field w155 <?= $orderField == 'departmentIds' ? $orderType : '' ?>"
                                data-field="departmentIds">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'belong_department'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>

                        <?php case 'start_check_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'start_check_time' ? $orderType : '' ?>"
                                data-field="start_check_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'check_start_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'end_check_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'end_check_time' ? $orderType : '' ?>"
                                data-field="end_check_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'check_end_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'start_expiry_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'start_expiry_time' ? $orderType : '' ?>"
                                data-field="start_expiry_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'expiry_data_start_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'end_expiry_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'end_expiry_time' ? $orderType : '' ?>"
                                data-field="end_expiry_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'expiry_data_end_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'start_running_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'start_running_time' ? $orderType : '' ?>"
                                data-field="start_running_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'start_running_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'end_running_time' : ?>
                            <th class="basic_field w200 <?= $orderField == 'end_running_time' ? $orderType : '' ?>"
                                data-field="end_running_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'end_running_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'repair_start_time' : ?>
                            <th class="basic_field w240 <?= $orderField == 'repair_start_time' ? $orderType : '' ?>"
                                data-field="repair_start_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'repair_start_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php case 'repair_end_time' : ?>
                            <th class="basic_field w240 <?= $orderField == 'repair_end_time' ? $orderType : '' ?>"
                                data-field="repair_end_time">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= Yii::t('base', 'repair_end_time'); ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                            <?php break; ?>
                        <?php endswitch; ?>
                    <?php if (preg_match('/^field(?:[1-9]|1\d|20)$/', $field) && !empty($defineFields[$field])): ?>
                        <?php if ($type == "my_instruments"): ?>
                            <th class="basic_field basic_define_field w155  <?= $orderField == str_replace('field', '', $field) ? $orderType : '' ?> "
                                data-field="<?php echo $field; ?>">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <div class="text sortThDiv"><?= $defineFields[$field]; ?></div>
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                             </span>
                            </th>
                        <?php else: ?>
                            <th class="basic_field basic_define_field w155  <?= $orderField == str_replace('field', '', $field) ? $orderType : '' ?> "
                                data-field="<?php echo $field; ?>">
                                <div class="ico-drag-box"><i class="ico-drag"></i></div>
                                <span class="instrument_def_title  instrument_ext_title  text"
                                      style="display: inline-block;"
                                      title="<?= $defineFields[$field]; ?>"><?= $defineFields[$field]; ?></span>
                                <input class="instrument_title_input" type="text" value="<?= $defineFields[$field]; ?>"
                                       style="display: none;width: 90%">
                                <span class="sort-field">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                            </th>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; ?>

                <th class="w260" data-field="operation"><?= Yii::t('base', 'operation'); ?> </th>
            </tr>

            </thead>
        </table>
    </div>
    <div class="tbody-scroll">
        <table class="project-view-table project-view-tbody-table">
            <tbody class="project-view-tbody">
            <div id="instrument-booking-config-dialog"></div>

            <?php foreach ($instrumentsList as $instrument) { ?>
                <tr data-id="<?= $instrument['id']; ?>"
                    data-name="<?= $instrument['name']; ?>"
                    data-maxAdvanceDay='<?= $instrument['max_advance_day']; ?>'
                    data-minAdvance='<?= json_encode($instrument['min_advance']); ?>'
                    data-maxBookingDuration='<?= json_encode($instrument['max_booking_duration']); ?>'
                    data-availableSlots='<?= json_encode($instrument['available_slots']); ?>'
                    >
                    <td class="w50">
                        <input class="instrument_id" type="hidden" value="<? /*= $in strument['id']; */ ?>">
                        <input class="checkboxBtn" type="checkbox" name="">
                    </td>

                    <?php foreach ($fieldConfigShowFields as $field): ?>
                        <?php switch ($field):
                            case 'picture': ?>
                                    <td class="operateInstrument w155" data-type="view" data-field="picture">
                                        <?php if ($instrument['pictures'] != '[]' && !empty($instrument['pictures'])): ?>
                                            <img class="show-big-img" style="max-width: 120px;max-height: 60px;" src="<?php
                                            $imgData = json_decode($instrument['pictures'], true)[0];
                                            $file = $imgData != '' ? PIC_URL . (new Picture())->getSourcePicture($imgData['dep_path'], $imgData['save_name']) : '';
                                            echo $file ?>">
                                        <?php endif ?>
                                    </td>
                                <?php break; ?>
                            <?php case 'name' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="name"><?php echo $instrument['name'] ?></td>
                                <?php break; ?>
                            <?php case 'batch_number' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="batch_number"><?php echo $instrument['batch_number'] ?></td>
                                <?php break; ?>
                            <?php case 'available_slots' : ?>
                                <td class="operateInstrument w155" data-type="view" data-field="available_slots">
                                    <?php
                                    // 兼容PHP7.0的写法
                                    $json = isset($instrument['available_slots']) ? $instrument['available_slots'] : '[]';
                                    $time_slots = json_decode($json, true);

                                    // 确保 $time_slots 是一个数组并且不是空的
                                    if (is_array($time_slots)) {
                                        foreach ($time_slots as $slot) {
                                            // 这里假设每个 $slot 是一个包含两个元素的数组，第0个是start，第1个是end
                                            $start = isset($slot[0]) ? $slot[0] : '';
                                            $end = isset($slot[1]) ? $slot[1] : '';
                                            echo htmlspecialchars($start . ' - ' . $end) . '<br>';
                                        }
                                    }
                                    ?>
                                </td>
                                <?php break; ?>
                            <?php case 'max_advance_day' : ?>
                                <td class="operateInstrument w155" data-type="view"
                                    data-field="max_advance_day">
                                    <?= isset($instrument['max_advance_day']) && $instrument['max_advance_day'] !== null
                                        ? htmlspecialchars($instrument['max_advance_day']). Yii::t('base', 'day')
                                        : Yii::t('base', 'unlimited') ?>
                                </td>
                                <?php break; ?>
                            <?php case 'min_advance' : ?>
                                <td class="operateInstrument w155" data-type="view" data-field="min_advance">
                                    <?php
                                    $json = isset($instrument['min_advance']) ? $instrument['min_advance'] : '';
                                    if ($data = json_decode($json, true)) {
                                        // 检查 'value' 是否为空
                                        if (empty($data['value'])) {
                                            echo Yii::t('base', 'unlimited');
                                        } else {
                                            echo htmlspecialchars($data['value']). Yii::t('base', $data['unit']);
                                        }
                                    } else {
                                        echo Yii::t('base', 'unlimited');
                                    }
                                    ?>
                                </td>
                                <?php break; ?>
                            <?php case 'max_booking_duration' : ?>
                                <td class="operateInstrument w155" data-type="view" data-field="max_booking_duration">
                                    <?php
                                    $json = isset($instrument['max_booking_duration']) ? $instrument['max_booking_duration'] : '';
                                    if ($data = json_decode($json, true)) {
                                        // 检查 'value' 是否为空
                                        if (empty($data['value'])) {
                                            echo Yii::t('base', 'unlimited');
                                        } else {
                                            echo htmlspecialchars($data['value']). Yii::t('base', $data['unit']);
                                        }
                                    } else {
                                        echo Yii::t('base', 'unlimited');
                                    }
                                    ?>
                                </td>
                                <?php break; ?>
                            <?php case 'specification' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="specification"><?php echo $instrument['specification'] ?></td>
                                <?php break; ?>
                            <?php case 'instrument_type' : ?>
                                    <td class="operateInstrument w155" data-type="view" data-field="instrument_type">
                                        <?= !empty($instrument['instrument_type']) ? $instrument['instrument_type'] : ''; ?>
                                    </td>
                                <?php break; ?>
                            <?php case 'model' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="model"><?php echo $instrument['model'] ?></td>
                                <?php break; ?>
                            <?php case 'manufacturer' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="manufacturer"><?php echo $instrument['manufacturer'] ?></td>
                                <?php break; ?>
                            <?php case 'supplier' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="supplier"><?php echo $instrument['supplier'] ?></td>
                                <?php break; ?>
                            <?php case 'position' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="position"><?php echo $instrument['position'] ?></td>

                                <?php break; ?>
                            <?php case 'status' : ?>
                                    <td class="operateInstrument w155 <?php echo 'status-' . $instrument['status'] ?>"
                                        data-type="view" data-field="status">
                                        <?php
                                        if ($instrument['status'] == "1") {
                                            $status = Yii::t('base', 'normal');
                                        } else if ($instrument['status'] == "0") {
                                            $status = Yii::t('base', 'already_deleted');
                                        } else if ($instrument['status'] == "2") {
                                            $status = Yii::t('base', 'suspend_use');
                                        } else if ($instrument['status'] == "3") {
                                            $status = Yii::t('base', 'repairing');
                                        } else if ($instrument['status'] == "4") {
                                            $status = Yii::t('base', 'scrap');
                                        }
                                        ?>
                                        <?= $status ?>
                                    </td>

                                <?php break; ?>
                            <?php case 'check_situation' : ?>
                                    <?php //根据有效期和check_situation判断校验状态
                                    if ($instrument['check_situation'] == "0") {
                                        $situation = Yii::t('base', 'noNeedCheck');
                                        $styleSign = 0;
                                    } else if ($instrument['check_situation'] == "1") { // 加上有效期判断 expiry_date

                                        if (in_array($instrument['id'], $checkedIds)) {
                                            $situation = Yii::t('base', 'checked');
                                            $styleSign = 1;
                                        } else {
                                            $situation = Yii::t('base', 'unchecked');
                                            $styleSign = 2;
                                        }
                                    } else {
                                        $situation = Yii::t('base', 'unchecked');
                                        $styleSign = 2;
                                    }
                                    ?>
                                    <td class="operateInstrument w200 check-situation" data-checkSituation="<?= $styleSign ?>"
                                        data-type="view" data-field="check_situation">
                                        <?= $situation ?>
                                    </td>

                                <?php break; ?>
                            <?php case 'data_type' : ?>
                                    <?php
                                    if (isset($instrument['data_type'])) {
                                        if ($instrument['data_type'] == "0") {
                                            $dataType = Yii::t('base', 'none');
                                        } else if ($instrument['data_type'] == "1") {
                                            if($instrument['numerical_instrument_type'] != 0) {
                                                $numericalInstrumentTypeMap = [
                                                    1 => 'numerical_instrument1',
                                                    2 => 'numerical_instrument2',
                                                    3 => 'numerical_instrument3',
                                                    4 => 'numerical_instrument4',
                                                ];
                                                $dataType = Yii::t('base', $numericalInstrumentTypeMap[$instrument['numerical_instrument_type']]);
                                            } else {
                                                $dataType = Yii::t('base', 'numerical_instrument1');
                                            }
                                        } else if ($instrument['data_type'] == "2") {
                                            $dataType = Yii::t('base', 'file');
                                        }
                                    } else {
                                        $instrument['data_type'] = 0;
                                        $dataType = Yii::t('base', 'none');
                                    }

                                    ?>
                                    <td class="operateInstrument w155 <?php echo 'data-type-' . $instrument['data_type'] ?>"
                                        data-type="view" data-field="data_type">
                                        <?= $dataType ?>
                                    </td>

                                <?php break; ?>
                            <?php case 'create_by' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="create_by"><?php echo $instrument['create_name'] ?></td>
                                <?php break; ?>
                            <?php case 'create_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="create_time"><?php echo $instrument['create_time'] ?></td>

                                <?php break; ?>
                            <?php case 'responsible_person' : ?>
                                    <td class="operateInstrument w180" data-type="view"
                                        data-field="responsible_person"><?php echo $instrument['responsible_person_string'] ?></td>

                                <?php break; ?>
                            <?php case 'in_charge_person' : ?>
                                    <td class="operateInstrument w180" data-type="view"
                                        data-field="in_charge_person"><?php echo $instrument['in_charge_person_string'] ?></td>

                                <?php break; ?>
                            <?php case 'maintenance_person' : ?>
                                    <td class="operateInstrument w180" data-type="view"
                                        data-field="maintenance_person"><?php echo $instrument['maintenance_person_string'] ?></td>

                                <?php break; ?>
                            <?php case 'remark' : ?>
                                    <td class="operateInstrument w155" data-type="view"
                                        data-field="remark"><?php echo $instrument['remark'] ?></td>

                                <?php break; ?>
                            <?php case 'groupIds' : ?>
                                    <?php
                                    if ($instrument['groupIds'] != 'all') {
                                        $groupIds = explode(',', $instrument['groupIds']);
                                        $groupNames = array_map(function ($groupId) use ($allGroups) {
                                            return @getVar($allGroups[$groupId]['group_name']);
                                        },$groupIds);
                                        $groupStr = join(',', $groupNames);
                                        echo sprintf('<td class="operateInstrument w155" data-type="view" data-field="groupIds" title="%1$s" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">%1$s</td>',$groupStr );
                                    }else{
                                        echo '<td class="operateInstrument w155" data-type="view" data-field="groupIds"  style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"></td>';
                                    }
                                    ?>

                                <?php break; ?>
                            <?php case 'departmentIds' : ?>
                                    <?php
                                    $departmentIds = explode(',', $instrument['departmentIds']);
                                    $departmentNames = array_map(function ($departmentId) use ($allDepartments) {
                                        return @getVar($allDepartments[$departmentId]['department_name']);
                                    },$departmentIds);
                                    $departmentStr = join(',', $departmentNames);
                                    echo sprintf('<td class="operateInstrument w155" data-type="view" data-field="departmentIds" title="%1$s" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">%1$s</td>',$departmentStr );

                                    ?>

                                <?php break; ?>


                            <?php case 'start_check_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="start_check_time"><?= !empty($instrument['start_check_time']) ? $instrument['start_check_time'] : '' ?></td>

                                <?php break; ?>
                            <?php case 'end_check_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="end_check_time"><?= !empty($instrument['end_check_time']) ? $instrument['end_check_time'] : '' ?></td>

                                <?php break; ?>
                            <?php case 'start_expiry_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="start_expiry_time"><?= !empty($instrument['start_expiry_time']) ? $instrument['start_expiry_time'] : '' ?></td>

                                <?php break; ?>
                            <?php case 'end_expiry_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="end_expiry_time"><?= !empty($instrument['end_expiry_time']) ? $instrument['end_expiry_time'] : '' ?></td>

                                <?php break; ?>
                            <?php case 'start_running_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="start_running_time"><?= !empty($instrument['start_running_time']) ? $instrument['start_running_time'] : ''  ?></td>

                                <?php break; ?>
                            <?php case 'end_running_time' : ?>
                                    <td class="operateInstrument w200" data-type="view"
                                        data-field="end_running_time"><?= !empty($instrument['end_running_time']) ? $instrument['end_running_time'] : ''  ?></td>

                                <?php break; ?>
                            <?php case 'repair_start_time' : ?>
                                    <td class="operateInstrument w240" data-type="view"
                                        data-field="repair_start_time"><?= !empty($instrument['repair_start_time']) ? $instrument['repair_start_time'] : '' ?></td>

                                <?php break; ?>
                            <?php case 'repair_end_time' : ?>
                                    <td class="operateInstrument w240" data-type="view"
                                        data-field="repair_end_time"><?= !empty($instrument['repair_end_time']) ? $instrument['repair_end_time'] : '' ?></td>

                                <?php break; ?>
                            <?php endswitch; ?>


                        <?php if (preg_match('/^field(?:[1-9]|1\d|20)$/', $field) && !empty($defineFields[$field])):
                            $i = str_replace('field', '', $field); ?>
                            <td class="w155" width="" data-field="<?= $field ?>">
                                <?php echo $instrument['data' . $i] ? $instrument['data' . $i] : ''; ?>
                            </td>
                        <?php endif; ?>

                    <?php endforeach;?>

                    <?php if ($type == "my_instruments"): ?>
                        <!-- 我的仪器库（没有编辑功能） -->
                        <td data-field="operation" class="operation_style  w260" data-name="<?= $instrument['name'] ?>"
                            data-batch="<?= $instrument['batch_number'] ?>">
                            <div class="instrumentOperationStyle">
                                <i class="instrument-running-record-ico operateOperateRecord >"
                                   data-id="<?= $instrument['id']; ?>" data-type="operation_record"
                                   title="<?= Yii::t('base', "operate_record") ?>"> </i>
                                <i class="instrument-book-ico operateInstrument<?php if ($instrument['status'] !== "1") {
                                    echo " disabled";
                                } ?>" data-id="<?= $instrument['id']; ?>" data-type="book"
                                   title="<?= Yii::t('base', "instrument_book") ?>"> </i>
                                <i class="instrument-repair-ico operateInstrument <?= $instrument['status'] == "3" ? 'disabled' : '' ?> " data-id="<?= $instrument['id']; ?>"
                                   data-type="repair" title="<?= Yii::t('base', "instrument_repair") ?>"> </i>
                                <i class="instrument-reminder-ico operateInstrument" data-id="<?= $instrument['id']; ?>"
                                   data-type="reminder" title="<?= Yii::t('base', "instrument_reminder") ?>"> </i>
                                <?php if ($instrument['check_situation'] !== "0"): ?>
                                    <i class="instrument-check-ico operateCheckRecord"
                                       data-id="<?= $instrument['id']; ?>"
                                       title="<?= Yii::t('base', "newCheck") ?>"> </i>
                                <?php else: ?>
                                    <i class="instrument-check-ico-no-need"
                                       title="<?= Yii::t('base', "noNeedCheck") ?>"> </i>
                                <?php endif; ?>
                                <i class="instrument-repair-record-ico operateRepairRecord"
                                   data-id="<?= $instrument['id']; ?>" data-type="repair_record"
                                   title="<?= Yii::t('base', "repair_record") ?>"> </i>
                                <!-- InScada 详情按钮 -->
                                <?php if (isset($instrument['data_type']) && $instrument['data_type'] != 0): ?>
                                    <i class="instrument-inscada-ico operateInScada"
                                       data-id="<?= $instrument['id']; ?>" data-type="<?=$instrument['data_type'];?>"
                                       title="<?= Yii::t('base', "InScada") ?>">
                                    </i>
                                <?php endif; ?>
                            </div>
                        </td>
                    <?php else: ?>
                        <!-- 仪器库管理 -->
                        <td data-field="operation" class="w260" data-name="<?= $instrument['name'] ?>"
                            data-batch="<?= $instrument['batch_number'] ?>">
                            <div class="instrumentOperationStyle">
                                <i class="instrument-running-record-ico operateOperateRecord"
                                   data-id="<?= $instrument['id']; ?>" data-type="operation_record"
                                   title="<?= Yii::t('base', "operate_record") ?>"> </i>
                                   <!-- 编辑按钮 -->
                                <i class="instrument-edit-ico <?= $isCanWrite ? 'operateInstrument' : 'gray' ?>"
                                   data-id="<?= $instrument['id']; ?>" data-type="edit"
                                   title="<?= $isCanWrite ? Yii::t('base', "instrument_edit") : Yii::t('base', "no_auth") ?>"> </i>
                                   <!-- 删除 -->
                                   <i class="instrument-delete-ico <?= $isCanWrite ? 'deleteInstrument' : 'gray' ?>"
                                   data-id="<?= $instrument['id']; ?>" data-type="delete"
                                   title="<?= $isCanWrite ? Yii::t('base', "delete") : Yii::t('base', "no_auth") ?>"> </i>
                                <i class="instrument-repair-ico operateInstrument" data-id="<?= $instrument['id']; ?>"
                                   data-type="repair" title="<?= Yii::t('base', "instrument_repair") ?>"> </i>
                                <i class="instrument-history-ico operateInstrumentTrace"
                                   data-id="<?= $instrument['id']; ?>" data-type="trace"
                                   title="<?= Yii::t('base', "history") ?>"> </i>
                                <i class="instrument-reminder-ico operateInstrument" data-id="<?= $instrument['id']; ?>"
                                   data-type="reminder" title="<?= Yii::t('base', "instrument_reminder") ?>"> </i>
                                <?php if ($instrument['check_situation'] !== "0"): ?>
                                    <i class="instrument-check-ico operateCheckRecord"
                                       data-id="<?= $instrument['id']; ?>"
                                       title="<?= Yii::t('base', "newCheck") ?>"> </i>
                                <?php else: ?>
                                    <i class="instrument-check-ico-no-need gray"
                                       title="<?= Yii::t('base', "noNeedCheck") ?>"> </i>
                                <?php endif; ?>
                                <i class="instrument-repair-record-ico  operateRepairRecord"
                                   data-id="<?= $instrument['id']; ?>" data-type="repair_record"
                                   title="<?= Yii::t('base', "repair_record") ?>"> </i>
                                <i class="instrument-setting-ico <?= $isCanWrite ? 'operateInstrument' : 'gray' ?>"
                                   data-id="<?= $instrument['id']; ?>" data-type="record_setting"
                                   title="<?= $isCanWrite ? Yii::t('base', "record_setting") : Yii::t('base', "no_auth") ?>"> </i>
                                <i class="instrument-usage-statistics-ico operateSingleUsage"
                                   data-id="<?= $instrument['id']; ?>" data-type="usage"
                                   title="<?= Yii::t('base', "usage_statistics") ?>"> </i>
                                <!-- InScada 详情按钮 -->
                                <?php if (isset($instrument['data_type']) && $instrument['data_type'] != 0): ?>
                                    <i class="instrument-inscada-ico operateInScada"
                                       data-id="<?= $instrument['id']; ?>" data-type="<?=$instrument['data_type'];?>"
                                       title="<?= Yii::t('base', "InScada") ?>">
                                    </i>
                                <?php endif; ?>
                            </div>
                        </td>
                    <?php endif; ?>

                </tr>
            <?php } ?>

            <?php if ($type == "my_instruments" && count($instrumentsList) == 0): ?>
                <tr>
                    <td class="no_instruments_result_td" width="100%" colspan="17" style="border-left: 1px solid #ddd;">
                        <div class="no_instruments_result">
                            <p class="no_instruments_result1"><?= Yii::t('base', "no_instrument_result_tip1") ?></p>
                            <p class="no_instruments_result2"><?= Yii::t('base', "no_instrument_result_tip2") ?></p>
                            <p class="no_instruments_result2"><?= Yii::t('base', "no_instrument_result_tip3") ?></p>
                            <p class="no_instruments_result2"><?= Yii::t('base', "no_instrument_result_tip4") ?></p>
                        </div>

                    </td>
                </tr>
            <?php endif; ?>

            <?php if ($type == "instruments_manage" && count($instrumentsList) == 0): ?>
                <tr>
                    <td class="no_instruments_result_td" width="100%" colspan="17" style="border-left: 1px solid #ddd;">
                        <div class="no_instruments_result">
                            <p class="no_instruments_result1"><?= Yii::t('base', "no_instrument_result_tip1") ?></p>
                        </div>

                    </td>
                </tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>


</div>
<div class="page_box" style="padding-bottom: 0px" data-limit="<?php echo $limit; ?>"
     data-num="<?php echo !empty($totalCount) ? $totalCount : 0; ?>"></div>
<?php
//如果本地存储
//instrument_manage_table_showHideColumn_cols_index为空，需要显示四个列，其余隐藏
$ifNull = array_diff($fieldConfigShowFields,[
        'name','batch_number','status','create_time'
]);
//为空时需要保留在本地存储的字符串
$needHideFields = implode(",", $ifNull);
?>

<!--处理表格列的显示隐藏-->
<script type="text/javascript">

    var type = '<?= $type?>';
    var needHideFields = '<?= $needHideFields ?>';

    if (type === 'instruments_manage') { // 初始化显示隐藏列 仪器库管理
        var listCols = localStorage.getItem('instrument_manage_table_showHideColumn_cols_index');

        if (listCols === null) { // 新用户只显示名称、设备ID、状态、创建时间、操作栏。其他都隐藏
            listCols = needHideFields;
            localStorage.setItem('instrument_manage_table_showHideColumn_cols_index',listCols);
        }
        if (listCols) {
            listCols = listCols.split(',');
            listCols.forEach(function (ele) {
                if (ele) {
                    $('#instrument_manage_table_showHideColumn').find(`td[data-field="${ele}"]`).hide();
                    $('#instrument_manage_table_showHideColumn').find(`th[data-field="${ele}"]`).hide();
                }
            });
        }
    }

    if (type === 'my_instruments') { // 初始化显示隐藏列 我的仪器库
        var listCols2 = localStorage.getItem('instrument_mine_table_showHideColumn_cols_index');
        if (listCols2 === null) { // 新用户只显示名称、设备ID、状态、创建时间、操作栏。其他都隐藏
            listCols2 = 'picture,instrument_type,check_situation,create_by,responsible_person,in_charge_person,maintenance_person,remark,' +
                'field1,field2,field3,field4,field5,field6,field7,field8,field9,field10,' +
                'field11,field12,field13,field14,field15,field16,field17,field18,field19,' +
                'start_check_time,end_check_time,start_expiry_time,end_expiry_time,start_running_time,end_running_time,repair_start_time,repair_end_time';
        }
        if (listCols2) {
            listCols2 = listCols2.split(',');
            listCols2.forEach(function (ele) {
                if (ele) {
                    $('#instrument_mine_table_showHideColumn').find(`td[data-field="${ele}"]`).hide();
                    $('#instrument_mine_table_showHideColumn').find(`th[data-field="${ele}"]`).hide();
                }

            });
        }
    }


    // 无数据时调整隐藏列属性
    if ($('.exp_conetnt.active td.no_instruments_result_td').length > 0) {
        $('.exp_conetnt.active td.no_instruments_result_td').attr('colspan', $('.exp_conetnt.active .instruments_table th:visible').length.toString())
    }


    // 调用分页插件 add by hkk 2020/6/28
    require('instrument').pageData = '<?= $page ?>';
    require('instrument').instrumentsManagePageFn('<?= $type ?>'); // modified by hkk 2020/6/28


    // 初始化日历 add by hkk 2020/6/28
    var dateOpts = {
        format: 'yyyy-mm-dd',
        autoclose: true,
        minView: 2,
        clearBtn: true,
    };

    $.fn.datetimepicker ? $('.instrument_manage #start_time').datetimepicker(dateOpts).on('click', function () {
        if ($('.instrument_manage [name="end_time"]').val() != '') {
            var startTimer = $('.instrument_manage [name="end_time"]').val() + ' 01:00';
            $('.instrument_manage [name="start_time"]').datetimepicker('setEndDate', startTimer);
        }
    }) : '';

    $.fn.datetimepicker ? $('.instrument_manage #end_time').datetimepicker(dateOpts).on('click', function () {
        if ($('.instrument_manage [name="start_time"]').val() != '') {
            var endTimer = $('.instrument_manage [name="start_time"]').val() + ' 01:00';
            $('.instrument_manage [name="end_time"]').datetimepicker('setStartDate', endTimer);
        }
    }) : '';

    // 初始化多选框 add by hkk 2020/6/28
    $('.fs_select_instrument').fSelect({
        placeholder: mainLang('no_chosen_user'),
        numDisplayed: 3,
        overflowText: '{n} selected',
        noResultsText: mainLang('no_search_result'),
        searchText: mainLang('search'),
        showSearch: true
    });
    $('.fs_select_instrument').parents('.fs-wrap').css('height', '28px');
    $('.fs_select_instrument').parents('.fs-wrap').css('line-height', '1.2');
    $('.fs_select_instrument').parents('.fs-wrap').css('vertical-align', 'bottom');

    //动态生成仪器库表格的高度
    function setTabHeight() {
        //为padding+yii的toolbar高度
        var paddingHeight = 0;
        var yiiBarHeight = $('.yii-debug-toolbar__bar').outerHeight();
        if (!yiiBarHeight) {
            paddingHeight = 35;
        } else {
            paddingHeight = yiiBarHeight + 35;
        }
        //为我的仪器库：top-group-tab高度获取，仪器库管理和我的仪器库模块数量不一致
        var toolGroupTabHeight = $('.top-group-tab').outerHeight();
        if (!toolGroupTabHeight) {
            toolGroupTabHeight = 0;
        }
        var topNavHeight = (localStorage.getItem('hide_top_nav')=='true'?0:$('.top_nav').outerHeight());
        var restHeight = topNavHeight + $('.tag_bar').outerHeight() + $('.exp_conetnt.active .ineln_title1').outerHeight() + 52 + toolGroupTabHeight + paddingHeight;//52为pagebox的高度
        var tableHeight = $.getWindowH() - restHeight;
        return tableHeight;
    };
    function getWindowH() {
        var h = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        return h;
    };
    $(window).resize(function () {
        var tableHeight = setTabHeight();
        $('.instruments_table .fixHeaderOuter').css('height', tableHeight + 'px');
    });
    $(function () {
        //设置表格的高度
        var tableHeight = setTabHeight();
        $('.instruments_table .fixHeaderOuter').css('height', tableHeight + 'px');
    });
    //点击更多筛选时 表格高度更新
    $('body').on('click', '.instrument_need_more_filter', function () {
        var tableHeight = setTabHeight();
        $('.instruments_table .fixHeaderOuter').css('height', tableHeight + 'px');
    });
    // 在仪器库管理 和 我的仪器库同时打开时 样式会有矛盾
    $('body').on('click','.iblock.tag',function (){
        if($(this).attr("title") == '仪器库管理' || $(this).attr("title") == '我的仪器库'){
            var tableHeight = setTabHeight();
            $('.instruments_table .fixHeaderOuter').css('height', tableHeight + 'px');
        }
    });
    // 显示/隐藏导航栏时，更新页面高度
    $(".hide_nav_btn").on('click', function () {
        var tableHeight = setTabHeight();
        setTimeout(function(){
            $('.instruments_table .fixHeaderOuter').css('height', tableHeight + 'px');
        }, 200) // 防止右边滚动条出现又消失的闪烁情况。等到动画结束后再调整页面高度

    })
    $(".show_nav_btn").on('click', function () {
        var tableHeight = setTabHeight();
        $('.instruments_table .fixHeaderOuter').css('height', tableHeight + 'px');
    })


</script>





