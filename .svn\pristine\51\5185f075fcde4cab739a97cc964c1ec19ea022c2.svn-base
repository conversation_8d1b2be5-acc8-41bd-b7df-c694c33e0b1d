<?php
namespace frontend\services\modules;

use frontend\models\FrontendLogModel;
use frontend\models\in_material\MaterialChemModel;
use frontend\services\modules\in_material\InMaterialServer;
use frontend\services\modules\in_material\queries\MaterialChemUpdaterV1;
use frontend\services\ModuleServer;
use frontend\models\ChemModel;
use frontend\models\ProductModel;
use frontend\models\ReactionDetailsModel;
use frontend\models\SubstrateModel;
use service\models\ineln\ExperimentRelayModel;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\VarDumper;

/**
 * InDraw Server
 *
 * <AUTHOR> huajun
 * @copyright 2019-02-19
 */
class IndrawModuleServer extends ModuleServer {
    /**
     * Notes: 保存模块
     * Author: zhu huajun
     * Date: 2019/2/27 10:26
     * @param $id
     * @param $module
     * @param int $type[1=>实验，2=>模板，3=>痕迹]
     * @return array
     */
    public function save($id, $module, $type) {
        // 更新experiment_relay表
        $saveRelayResult = parent::save($id, $module, $type);
        if ($saveRelayResult['status'] != 1) {
            return $this->fail($saveRelayResult['info']);
        }

        $relayId = $saveRelayResult['data']['relay_id'];

        // 如果模块签字通过且设置了签字通过后不可编辑，跳过这个模块的保存
        $locked = @getVar($saveRelayResult['data']['locked'], 0);
        if ($locked) {
            return $this->success($relayId);
        }

        /* 保存模块数据 begin*/
        // 更新chem表
        $chem = ChemModel::find()->where([
            'parent_id' => $relayId,
            'type' => $type,
            'status' => 1
        ])->one();
        if (!$chem) {
            $chem = new ChemModel();
        }

        $moduleData = ArrayHelper::getValue($module, ['data']);
        $moduleChemData = ArrayHelper::getValue($moduleData, ['chem_data']);
        $materialData = ArrayHelper::getValue($moduleData, ['in_material']);
        if (null == $materialData) {
            return $this->fail('物料表数据异常, 无法保存');
        }
        if (!empty($moduleData) && !empty($materialData)) {
            $materialBaseData = ArrayHelper::getValue($materialData, ['base_data'], []);
            $moduleChemData = array_merge($moduleChemData, $materialBaseData);

            $materialColConfig = ArrayHelper::getValue($materialBaseData, ['material_column_config'], []);
            $materialTblConfig = ArrayHelper::getValue($materialBaseData, ['material_table_config'], []);
            if (is_array($materialColConfig)) {
                $jsonColConfig = json_encode($materialColConfig);
                $materialBaseData['material_column_config'] = $jsonColConfig;
                $moduleChemData['material_column_config'] = $jsonColConfig;
            }
            if (is_array($materialTblConfig)) {
                $jsonTblConfig = json_encode($materialTblConfig);
                $materialBaseData['material_table_config'] = $jsonTblConfig;
                $moduleChemData['material_table_config'] = $jsonTblConfig;
            }
        }
        $chem->setAttributes($moduleChemData);
        $chem->setAttribute('in_material_version', MaterialChemUpdaterV1::MATERIAL_VERSION);

        $chem['parent_id'] = $relayId;
        $chem['type'] = $type;
        
        // 修改这里：确保height字段有值，优先使用module中的值，如果不存在则使用默认值525
        $height = 525; // 默认高度
        
        // 尝试从不同位置获取高度值
        if (isset($module['info']['height'])) {
            $height = (int)$module['info']['height'];
        } elseif (isset($moduleData['height'])) {
            $height = (int)$moduleData['height'];
        } elseif (isset($moduleData['base_data']['height'])) {
            $height = (int)$moduleData['base_data']['height'];
        }
        
        $chem['height'] = $height;
        $chem['dep_path'] = null;
        $chem['img_name'] = null;
        if (!$chem->save()) {
            return $this->fail(current($chem->getFirstErrors()));
        }

        $toSaveMaterialData = isset($materialData) ? $materialData : [];
        $saveMaterial = (new InMaterialServer())->saveMaterial($toSaveMaterialData, $chem);
        if (empty($saveMaterial['status'])) {
            return $this->fail($saveMaterial['info']);
        }
        return $this->success($relayId);


//        // 更新experiment_substrate表
//        $result = $this->_saveSubstrate($chem['id'], $module['data']);
//        if($result['status'] == 0) {
//            return $this->fail($result['info']);
//        }
//        $allMW = $result['data']['mol_weight'];
//
//        // 更新experiment_product表
//        $result = $this->_saveProduct($chem['id'], $module['data']);
//        if($result['status'] == 0) {
//            return $this->fail($result['info']);
//        }
//        $allMW += $result['data']['mol_weight'];
//
//        $chem['all_mw'] = $allMW;
//        if(!$chem->save()) {
//            return $this->fail(current($chem->getFirstErrors()));
//        }
//
//        // 更新反应明显
//        $result = $this->_saveDetails($chem['id'], $module['data']);
//        if($result['status'] == 0) {
//            return $this->fail($result['info']);
//        }
//        /* 保存模块数据 end*/
    }

    /**
     * Notes: 获取模块数据
     * Author: zhu huajun
     * Date: dt
     * @param $relayId
     * @param int $type[1=>实验，2=>模板，3=>痕迹, 4=>模板痕迹]
     * @return array
     */
    public function getData($relayId, $type = 1) {
        $expType = $type;
        // 获取chem表数据（InDraw数据和单位）
        $baseData = ChemModel::find()->where([
            'parent_id' => $relayId,
            'type' => $type,
            'status' => 1
        ])->asArray()->one();

        if (empty($baseData)) {
            return $this->success([
                'base_data' => [
                    'all_mw' => 0,
                    'indraw_data' => false,
                    'time_unit' => 2,
                    'tep_unit' => 1,
                    'pressure_unit' => 1,
                    'mass_unit' => 1,
                    'nb_unit' => 2,
                    'volume_unit' => 2,
                    'reagent_mass_unit' => 1,
                    'reagent_nb_unit' => 2,
                    'reagent_volume_unit' => 2,
                    'solvent_mass_unit' => 1,
                    'solvent_volume_unit' => 2,
                    'produce_mass_unit' => 1,
                    'produce_theo_unit' => 1,
                    'produce_nb_unit' => 2,
                    'show_details' => 0,
                    'height' => 525,
                    'type' => 1,
                    'status' => 1,
                    // 'material_column_config' => '{"substratesConfig":[{"name":"substrates_CAS","show":false,"width":"4.540420819490587%","define":false},{"name":"substrates_Batch_No","show":true,"width":"17.27574750830565%","define":false},{"name":"substrates_Eq","show":true,"width":"6.533776301218162%","define":false},{"name":"substrates_N","show":true,"width":"7.751937984496124%","define":false},{"name":"substrates_Salt","show":false,"width":"7.361376673040153%","define":false},{"name":"substrates_Salt_Eq","show":false,"width":"4.1108986615678775%","define":false},{"name":"substrates_MW","show":true,"width":"8.970099667774086%","define":false},{"name":"substrates_Mass","show":true,"width":"10.188261351052049%","define":false},{"name":"substrates_D","show":true,"width":"6.533776301218162%","define":false},{"name":"substrates_V","show":true,"width":"6.423034330011074%","define":false},{"name":"substrates_C","show":true,"width":"6.423034330011074%","define":false},{"name":"substrates_Purity","show":true,"width":"8.859357696566999%","define":false},{"name":"substrates_Source","show":true,"width":"6.423034330011074%","define":false},{"name":"cLogP","show":false,"width":"5.980066445182724%","define":true,"dict_id":"0"},{"name":"tPSA","show":false,"width":"5.094130675526024%","define":true,"dict_id":"0"},{"name":"substrates_unitPrice","show":false,"width":"10.409745293466223%","define":true,"dict_id":"0"},{"name":"substrates_price","show":false,"width":"9.30232558139535%","define":true,"dict_id":"0"}],"solventConfig":[{"name":"solvent_CAS","show":false,"width":"4.540420819490587%","define":false},{"name":"solvent_Ratio","show":true,"width":"15.060908084163898%","define":false},{"name":"solvent_V","show":true,"width":"15.060908084163898%","define":false},{"name":"solvent_Mass","show":true,"width":"15.060908084163898%","define":false},{"name":"solvent_D","show":true,"width":"11.627906976744185%","define":false},{"name":"solvent_bp","show":true,"width":"13.842746400885936%","define":false},{"name":"solvent_Source","show":true,"width":"16.057585825027687%","define":false},{"name":"cLogP","show":false,"width":"5.980066445182724%","define":true,"dict_id":"0"},{"name":"tPSA","show":false,"width":"5.094130675526024%","define":true,"dict_id":"0"},{"name":"solvent_unitPrice","show":false,"width":"10.409745293466223%","define":true,"dict_id":"0"},{"name":"solvent_price","show":false,"width":"9.30232558139535%","define":true,"dict_id":"0"}],"conditionConfig":[{"name":"condition_temperature","show":true,"width":"15.060908084163898%","define":false},{"name":"condition_time","show":true,"width":"15.060908084163898%","define":false},{"name":"condition_pressure","show":true,"width":"14.950166112956811%","define":false},{"name":"condition_gas","show":true,"width":"11.517165005537098%","define":false},{"name":"condition_heating","show":true,"width":"13.842746400885936%","define":false},{"name":"condition_comment","show":true,"width":"16.27906976744186%","define":false}],"productConfig":[{"name":"product_Batch_No","show":true,"width":"12.73532668881506%","define":false},{"name":"product_N","show":true,"width":"6.644518272425249%","define":false},{"name":"product_Salt","show":true,"width":"7.862679955703212%","define":false},{"name":"product_Salt_Eq","show":true,"width":"3.10077519379845%","define":false},{"name":"product_MW","show":true,"width":"9.080841638981173%","define":false},{"name":"product_Theo_Mass","show":true,"width":"9.080841638981173%","define":false},{"name":"product_Actual_Mass","show":true,"width":"10.299003322259136%","define":false},{"name":"product_Yield","show":true,"width":"6.533776301218162%","define":false},{"name":"product_Purity","show":true,"width":"6.533776301218162%","define":false},{"name":"product_Sample_Id","show":true,"width":"5.3156146179401995%","define":false},{"name":"product_Barcode","show":true,"width":"7.751937984496124%","define":false},{"name":"cLogP","show":false,"width":"5.980066445182724%","define":true,"dict_id":"0"},{"name":"tPSA","show":false,"width":"5.094130675526024%","define":true,"dict_id":"0"},{"name":"product_unitPrice","show":false,"width":"10.409745293466223%","define":true,"dict_id":"0"},{"name":"product_price","show":false,"width":"9.523809523809524%","define":true,"dict_id":"0"},{"name":"product_mass_yield","show":false,"width":"11.849390919158362%","define":true,"dict_id":"0"}],"detailConfig":[{"name":"details_CAS","show":true,"width":"18.49390919158361%","define":false},{"name":"details_Risk_Assessment","show":true,"width":"21.705426356589147%","define":false},{"name":"details_ee","show":true,"width":"7.198228128460687%","define":false},{"name":"details_de","show":true,"width":"7.198228128460687%","define":false},{"name":"details_Comment","show":true,"width":"9.413067552602437%","define":false},{"name":"details_Files","show":true,"width":"21.59468438538206%","define":false},{"name":"cLogP","show":false,"width":"5.980066445182724%","define":true,"dict_id":"0"},{"name":"tPSA","show":false,"width":"4.872646733111849%","define":true,"dict_id":"0"}]}',
                    'material_column_config' => json_encode(MaterialChemModel::getDefaultColConfig())
                ],
                'chem_data' => [],
                'sub_data' => [],
                'pro_data' => [],
                'catalysts_data' => [],
                'solvent_data' => [],
                'condition_data' => [],
                'product_data' => [],
                'details_data' => [],
                'height' => 525
            ]);
        }

        if(!empty($baseData)) {
            $where = [
                'chem_id' => $baseData['id'],
                'status' => 1
            ];
            $subData = SubstrateModel::find()->where($where)->asArray()->all();
            $proData = ProductModel::find()->where($where)->asArray()->all();
            $detailsData = ReactionDetailsModel::find()->where($where)->asArray()->all();

            /*
             * 下面对返回数据的处理是为了和保存实验时前端传过来的数据格式保持一致
             * 保持一致的好处是：读取实验数据后可以直接调用保存实验的方法，从而实现复制实验，从模板创建实验等
             * 更好的办法是优化保存实验时前端传过来的数据，这个需要时间
             *
             * zhu huajun
             * 2019/3/15 17:25
            */
            $substrates_data = [];
            $catalysts_data = [];
            $solvent_data = [];
            $condition_data = [];
            $product_data = [];

            foreach($subData as $sub) {
                switch($sub['part']) {
                    case 1:
                        $type = 'substrates';
                        break;
                    case 2:
                        $type = 'catalysts';
                        break;
                    case 3:
                        $type = 'solvent';
                        break;
                    case 4:
                        $type = 'condition';
                        break;
                }

                $newData = [
                    $type . '_smiles' => $sub['smiles'],
                    $type . '_name' => $sub['name'],
                    $type . '_cas'=>  $sub['cas'], // add by hkk 2019/9/26
                    $type . '_mol'=>  $sub['molstring'], // add by hkk 2019/12/25
                    $type . '_batch_num' => $sub['batch_num'],
                    $type . '_equivalent' => $sub['equivalent'],
                    $type . '_molweight' => $sub['molweight'],
                    $type . '_mass' => $sub['mass'],
                    $type . '_nb' => $sub['nb'],
                    $type . '_density' => $sub['density'],
                    $type . '_volume' => $sub['volume'],
                    $type . '_temperature' => $sub['temperature'],
                    $type . '_pressure' => $sub['pressure'],
                    $type . '_inchi' => $sub['inchi'],
                    $type . '_comment' => $sub['comment'],
                    $type . '_ratio' => $sub['ratio'],
                    $type . '_bp' => $sub['bp'],
                    $type . '_temperature' => $sub['condition_temperature'], // MODIFIED BY ZHJ HKK 2019/9/26 去掉condition否则模板新建实验condition字段失效
                    $type . '_rt' => $sub['condition_rt'],
                    $type . '_pressure' => $sub['condition_pressure'],
                    $type . '_pg' => $sub['condition_pg'],
                    $type . '_heating' => $sub['condition_heating'],
                    $type . '_display_structure' => $sub['display_structure'],
                    $type . '_salt' => $sub['salt'],
                    $type . '_salt_eq' => $sub['salt_eq'], //add by wy 2023/3/2
                    'name_isinput' => $sub['name_isinput'],
                    'mass_isinput' => $sub['mass_isinput'],
                    'volume_isinput' => $sub['volume_isinput'],
                    'eq_isinput' => $sub['eq_isinput'],
                    'nb_isinput' => $sub['nb_isinput'],
                    'come_from' => $sub['come_from'],
                    'salt_id' => $sub['salt_id'],
                    'salt_num' => $sub['salt_num'],
                    'solvent_id' => $sub['solvent_id'],
                    'part' => $sub['part'],
                    'type' => 1,
                    'define_value' => $sub['define_value'], // add by hkk 2019/9/6
                ];

                switch($sub['part']) {
                    case 1:
                        $newData['substrates_pressure']=$sub['pressure'];
                        $substrates_data[] = $newData;
                        break;
                    case 2:
                        $catalysts_data[] = $newData;
                        break;
                    case 3:
                        $solvent_data[] = $newData;
                        break;
                    case 4:
                        $newData['condition_pressure']=$sub['condition_pressure'];
                        $condition_data[] = $newData;
                        break;
                }
            }
        }

        foreach($proData as $pro) {
            $newData = [
                'product_smiles' => $pro['smiles'],
                'product_mol' => $pro['molstring'], // add by hkk 2019/12/25
                'product_atoms' => $pro['atoms'],
                'product_name' => $pro['name'],
                'name_isinput' => $pro['name_isinput'],
                'product_batch_num' => $pro['batch_num'],
                'product_molweight' => $pro['molweight'],
                'product_mass' => $pro['mass'],
                'product_theo' => $pro['theo'],
                'product_nb' => $pro['nb'],
                'mass_isinput' => $pro['mass_isinput'],
                'nb_isinput' => $pro['nb_isinput'],
                'product_yield' => $pro['yield'],
                'yield_isinput' => $pro['yield_isinput'],
                'product_purity' => $pro['purity'],
                'salt_id' => $pro['salt_id'],
                'salt_num' => $pro['salt_num'],
                'solvent_id' => $pro['solvent_id'],
                'product_inchi' => $pro['inchi'],
                'product_sample_id' => $pro['sample_id'],
                'product_purity' => $pro['purity'],
                'product_barcode' => $pro['barcode'],
                'product_salt' => $pro['salt'],
                'product_salt_eq' => $pro['salt_eq'],
                'product_comment' => $pro['comment'],
                'type' => 1,
                'define_value' => $pro['define_value'], // add by hkk 2019/9/20
                'wms_product_id' => $pro['wms_product_id'],
                'wms_batch_id' => $pro['wms_batch_id'],
            ];
            $product_data[] = $newData;
        }

        // 获取模板里物料表数据
        $materialData = (new InMaterialServer())->getMaterialData($relayId, $expType)['data'];

        return $this->success([
            'base_data' => $baseData,
            'chem_data' => $baseData,
            'sub_data' => $subData,
            'pro_data' => $proData,
            'substrates_data' => $substrates_data,
            'catalysts_data' => $catalysts_data,
            'solvent_data' => $solvent_data,
            'condition_data' => $condition_data,
            'product_data' => $product_data,
            'details_data' => $detailsData,
            'height' => $baseData['height'],
            'in_material' => $materialData,
        ]);
    }

    /**
     * Note: 复制模块数据
     * @param $fromRelayId 来源模块ID
     * @param $toRelayId 目标模块ID
     * @param $fromType 来源模块类型，1=>实验，2=>模板，3=>痕迹，4=>模板痕迹
     * @param $toType 目标模块类型，1=>实验，2=>模板，3=>痕迹，4=>模板痕迹
     * @return array
     * <AUTHOR>
     * @date 2023/3/23 15:45
     */
    function pasteData($fromRelayId, $toRelayId, $fromType = 1, $toType = 1) {
        // 处理chem表
        $fromChem = ChemModel::find()->where([
            'parent_id' => $fromRelayId,
            'type' => $fromType,
            'status' => 1
        ])->one();
        if ($fromChem) {
            $chemData = $fromChem->getAttributes(null, ['id', 'create_time', 'update_time']);
            $chemData['parent_id'] = $toRelayId;
            $chemData['type'] = $toType;
            $newChem = new ChemModel();
            $newChem->setAttributes($chemData);
            $newChem->save();

            // 处理experiment_substrate
            $subList = SubstrateModel::find()->where([
                'chem_id' => $fromChem['id'],
                'status' => 1
            ])->all();
            foreach ($subList as $sub) {
                $subData = $sub->getAttributes(null, ['id', 'create_time', 'update_time']);
                $subData['chem_id'] = $newChem['id'];
                $newSub = new SubstrateModel();
                $newSub->setAttributes($subData);
                $newSub->save();
            }

            // 处理experiment_product
            $proList = ProductModel::find()->where([
                'chem_id' => $fromChem['id'],
                'status' => 1
            ])->all();
            foreach ($proList as $pro) {
                $proData = $pro->getAttributes(null, ['id', 'create_time', 'update_time']);
                $proData['chem_id'] = $newChem['id'];
                $newPro = new ProductModel();
                $newPro->setAttributes($proData);
                $newPro->save();
            }

            // 处理reaction_details
            $detailList = ReactionDetailsModel::find()->where([
                'chem_id' => $fromChem['id'],
                'status' => 1
            ])->all();
            foreach ($detailList as $detail) {
                $detailData = $detail->getAttributes(null, ['id']);
                $detailData['chem_id'] = $newChem['id'];
                $newDetail = new ReactionDetailsModel();
                $newDetail->setAttributes($detailData);
                $newDetail->save();
            }
        }

        return $this->success($toRelayId);
    }

    // 更新experiment_substrate表
    private function _saveSubstrate($chemId, $data) {
        // 处理数据
        $newSubstrates = [];

        if (!empty($data['substrates_data'])) {
            $newSubstrates = $this->_getMateilData('substrates', $data['substrates_data'], 1, $chemId);
        }

        $newCatalysts = [];
        if (!empty($data['catalysts_data'])) {
            $newCatalysts = $this->_getMateilData('catalysts', $data['catalysts_data'], 2, $chemId);
        }

        $newSolvent = [];
        if (!empty($data['solvent_data'])) {
            $newSolvent = $this->_getMateilData('solvent', $data['solvent_data'], 3, $chemId);
        }

        $newCondition = [];
        if (!empty($data['condition_data'])) {
            $newCondition = $this->_getMateilData('condition', $data['condition_data'], 4, $chemId);
        }

        $allSubData = array_merge($newSubstrates, $newCatalysts, $newSolvent, $newCondition);


        // 数据验证
        // 表格数据更新调整：先尽量在原有记录上面更新，超出原有记录数的部分插入新增，原有记录还有剩余的话将这些记录status置为0 product和reaction_detail表同理 jiangdm 2022/3/29
        $currentData = SubstrateModel::find()->where(['chem_id' => $chemId, 'status' => 1])->asArray()->all();
        $model = new SubstrateModel();
        $molWeight = 0;
        foreach($allSubData as $key => $subData) {
            $molWeight += !empty($subData['molweight']) ? $subData['molweight'] : 0;

            $_model = clone $model;
            if (isset($currentData[$key])) {
                $_model->updateAll($subData, ['id'=>$currentData[$key]['id']]);
            } else {
                $_model->setAttributes($subData);
                if (!$_model->save()) {
                    return $this->fail(current($_model->getFirstErrors()));
                }
            }
        }

        $invalidRows = array_slice($currentData, count($allSubData));
        if (!empty($invalidRows)) {
            SubstrateModel::updateAll(['status' => 0], ['id' => array_column($invalidRows, 'id')]);
        }

        return $this->success([
            'mol_weight' => $molWeight
        ]);
    }

    // 更新experiment_product表
    private function _saveProduct($chemId, $data) {


        // add by hkk 2022/3/22  先查询是否有cms_chem_code 和 cms_chem_id 字段，有先拿出来再插入
        $proData = ProductModel::find()->select(['batch_num','cms_chem_code','cms_chem_id'])->where([
            'chem_id' => $chemId,
            'status' => 1
        ])->asArray()->all();


        $currentData = ProductModel::find()->where(['chem_id' => $chemId, 'status' => 1])->asArray()->all();
        $productArr = isset($data['product_data']) ? $data['product_data'] : [];
        $model = new ProductModel();
        $molWeight = 0;
        foreach ($productArr as $key => $product) {
            $productData = [
                'chem_id' => $chemId,
                'smiles' => $product['product_smiles'],
                'molstring' => $product['product_mol'], // add by hkk 2019/12/5
                'atoms' => $product['product_atoms'],
                'name' => isset($product['product_name']) ? $product['product_name'] : '',
                'name_isinput' => empty($product['name_isinput']) ? 0 : $product['name_isinput'],
                'batch_num' => isset($product['product_batch_num']) ? $product['product_batch_num'] : '',
                'molweight' => isset($product['product_molweight']) ? $product['product_molweight'] : '',
                'mass' => isset($product['product_mass']) ? $product['product_mass'] : '',
                'theo' => isset($product['product_theo']) ? $product['product_theo'] : '',
                'nb' => isset($product['product_nb']) ? $product['product_nb'] : '',
                'type' => 1,  // 1 实验 2 模板 3 痕迹
                'mass_isinput' => empty($product['mass_isinput']) ? 0 : $product['mass_isinput'],  // TODO 缺少参数
                'nb_isinput' => empty($product['nb_isinput']) ? 0 : $product['nb_isinput'],  // TODO 缺少参数
                'yield' => $product['product_yield'],
                'yield_isinput' => empty($product['yield_isinput']) ? 0 : $product['yield_isinput'],
                'user_input_fields' => empty($product['user_input_fields']) ? null : $product['user_input_fields'], // add by hkk 2019/3/15
                'purity' => isset($product['product_purity']) ? $product['product_purity'] : '',
                'salt_id' => isset($product['salt_id']) ? $product['salt_id'] : '',
                'salt_num' => isset($product['salt_num']) ? $product['salt_num'] : '',
                'solvent_id' => isset($product['solvent_id']) ? $product['solvent_id'] : '',
                // 'analy_data' => isset($product['analy_data']) ? $product['analy_data'] : null,
                'inchi' => isset($product['product_inchi']) ? $product['product_inchi'] : null,
                'sample_id' => isset($product['product_sample_id']) ? $product['product_sample_id'] : null,
                'cms_chem_code' => isset($proData[$key]['cms_chem_code']) ? $proData[$key]['cms_chem_code'] : null, // modified by hkk 2022/3/22
                'cms_chem_id' => isset($proData[$key]['cms_chem_id']) ? intval($proData[$key]['cms_chem_id']) : 0, // modified by hkk 2022/3/22
                'barcode' => isset($product['product_barcode']) ? $product['product_barcode'] : null,
                'salt' => isset($product['product_salt']) ? $product['product_salt'] : null,
                'salt_eq' => isset($product['product_salt_eq']) ? $product['product_salt_eq'] : null,
                'comment' => isset($product['product_comment']) ? $product['product_comment'] : null,
                'define_value' => isset($product['define_value']) ? $product['define_value'] : '', // add by hkk 2019/9/10
               'wms_product_id' => isset($product['wms_product_id']) ? $product['wms_product_id'] : '', // add by hkk 2019/9/10
                'wms_batch_id' => isset($product['wms_batch_id']) ? $product['wms_batch_id'] : '', // add by hkk 2019/9/10
                'instrument_data_info' => isset($product['instrument_data_info']) ? $product['instrument_data_info'] : null,
            ];

            $molWeight += !empty($productData['molweight']) ? $productData['molweight'] : 0;
            $_model = clone $model;
            if (isset($currentData[$key])) {
                $_model->updateAll($productData, ['id'=>$currentData[$key]['id']]);
            } else {
                $_model->setAttributes($productData);
                if (!$_model->save()) {
                    return $this->fail(current($_model->getFirstErrors()));
                }
            }

        }

        $invalidRows = array_slice($currentData, count($productArr));
        if (!empty($invalidRows)) {
            ProductModel::updateAll(['status' => 0], ['id' => array_column($invalidRows, 'id')]);
        }


        return $this->success([
            'mol_weight' => $molWeight
        ]);
    }

    // 更新experiment_product表
    private function _saveDetails($chemId, $data) {

        // 数据验证
        $currentData = ReactionDetailsModel::find()->where(['chem_id' => $chemId, 'status' => 1])->asArray()->all();
        $detailArr = isset($data['details_data']) ? $data['details_data'] : [];
        $model = new ReactionDetailsModel();
        foreach($detailArr as $key => $detailData) {
            $detailData['chem_id'] = $chemId;
            $_model = clone $model;
            if (isset($currentData[$key])) {
                $_model->updateAll($detailData, ['id'=>$currentData[$key]['id']]);
            } else {
                $_model->setAttributes($detailData);
                if (!$_model->save()) {
                    return $this->fail(current($_model->getFirstErrors()));
                }
            }
        }

        $invalidRows = array_slice($currentData, count($detailArr));
        if (!empty($invalidRows)) {
            ReactionDetailsModel::updateAll(['status' => 0], ['id' => array_column($invalidRows, 'id')]);
        }

        return $this->success([]);
    }

    /**
     * 获取底物部分数据
     *
     * <AUTHOR> @copyright 2016-3-22
     * @param string $type 底物类型
     * @param array $data
     * @return multitype:multitype:number unknown Ambigous <string, unknown> Ambigous <number, unknown>
     */
    private function _getMateilData($type, $data, $part, $chemId)
    {
        $newData = [];

        foreach ($data as $da) {
            $newData[] = [
                'chem_id' => $chemId,
                'smiles' => $da[$type . '_smiles'],
                'molstring' => isset($da[$type . '_mol']) ? $da[$type . '_mol'] : null, // add by hkk 2019/12/5
                'name' => $da[$type . '_name'],
                'name_isinput' => empty($da['name_isinput']) ? 0 : $da['name_isinput'],
                'batch_num' => isset($da[$type . '_batch_num']) ? $da[$type . '_batch_num'] : null,
                'equivalent' => isset($da[$type . '_equivalent']) ? $da[$type . '_equivalent'] : '',
                'molweight' => isset($da[$type . '_molweight']) ? $da[$type . '_molweight'] : '',
                'mass' => isset($da[$type . '_mass']) ? $da[$type . '_mass'] : '',
                'nb' => isset($da[$type . '_nb']) ? $da[$type . '_nb'] : '',
                'density' => isset($da[$type . '_density']) ? $da[$type . '_density'] : '',
                'volume' => isset($da[$type . '_volume']) ? $da[$type . '_volume'] : '',
                'temperature' => isset($da[$type . '_temperature']) ? $da[$type . '_temperature'] : '',
                'pressure' => isset($da[$type . '_pressure']) ? $da[$type . '_pressure'] : '',
                'part' => $part,  // 1产物 2催化剂 3溶剂
                'type' => 1,  // 1 实验 2 模板 3 痕迹
                'mass_isinput' => empty($da['mass_isinput']) ? 0 : $da['mass_isinput'],
                'volume_isinput' => empty($da['volume_isinput']) ? 0 : $da['volume_isinput'],
                'eq_isinput' => empty($da['eq_isinput']) ? 0 : $da['eq_isinput'],
                'nb_isinput' => empty($da['nb_isinput']) ? 0 : $da['nb_isinput'],
                'user_input_fields' => empty($da['user_input_fields']) ? null : $da['user_input_fields'], // add by hkk 2019/3/15
                'is_base' => empty($da['is_base']) ? 0 : $da['is_base'], // add by hkk 2019/3/15
                'come_from' => isset($da['come_from']) ? $da['come_from'] : '',
                'salt_id' => isset($da['salt_id']) ? $da['salt_id'] : '',
                'salt_num' => isset($da['salt_num']) ? $da['salt_num'] : '',
                'solvent_id' => isset($da['solvent_id']) ? $da['solvent_id'] : '',
                'inchi' => isset($da[$type . '_inchi']) ? $da[$type . '_inchi'] : null,
                'comment' => isset($da[$type . '_comment']) ? $da[$type . '_comment'] : null,
                'ratio' => isset($da[$type . '_ratio']) ? $da[$type . '_ratio'] : null,
                'bp' => isset($da[$type . '_bp']) ? $da[$type . '_bp'] : null,
                'condition_temperature' => isset($da[$type . '_temperature']) ? $da[$type . '_temperature'] : null, // modified by hkk 2019/9/26
                'condition_rt' => isset($da[$type . '_rt']) ? $da[$type . '_rt'] : null,
                'condition_pressure' => isset($da[$type . '_pressure']) ? $da[$type . '_pressure'] : null,
                'condition_pg' => isset($da[$type . '_pg']) ? $da[$type . '_pg'] : null,
                'condition_heating' => isset($da[$type . '_heating']) ? $da[$type . '_heating'] : null,
                'status' => 1,
                'display_structure' => isset($da[$type . '_display_structure']) ? $da[$type . '_display_structure'] : 1,
                'define_value' => isset($da['define_value']) ? $da['define_value'] : '', // add by hkk 2019/9/5
                'cas' => isset($da[$type . '_cas']) ? $da[$type . '_cas'] : null,// add by hkk 2019/9/17
                'wms_product_id' => isset($da[$type . '_wms_product_id']) ? $da[$type . '_wms_product_id'] : null,// add by hkk 2019/9/17
                'wms_batch_id' => isset($da[$type . '_wms_batch_id']) ? $da[$type . '_wms_batch_id'] : null,// add by hkk 2019/9/17
                'wms_sync_status' => isset($da[$type . '_wms_sync_status']) ? $da[$type . '_wms_sync_status'] : 0,
                'instrument_data_info' => isset($da['instrument_data_info']) ? $da['instrument_data_info'] : null,
                'salt' => isset($da[$type . '_salt']) ? $da[$type . '_salt'] : null,
                'salt_eq' => isset($da[$type . '_salt_eq']) ? $da[$type . '_salt_eq'] : null, //ADD BY WY 2023/3/2
            ];
        }

        return $newData;
    }

    /**
     * Note: 删除实验或模板中的模块
     * @param $relayId integer 模块id
     * @param $type integer (1->实验；2->模板)
     * @return array
     * <AUTHOR>
     * @date 2023/3/9 13:31
     */
    public function remove($relayId = 0, $type = 1) {
        // 删除模块
        $removeResult = parent::remove($relayId, $type);
        if ($removeResult['status'] != 1) {
            return $this->fail($removeResult['info']);
        }

        // 删除模块数据
        $chem = ChemModel::findOne([
            'parent_id' => $relayId,
            'type' => $type,
            'status' => 1
        ]);
        if ($chem) {
            $chem['status'] = 0;
            $chem->save();

            $where = [
                'chem_id' => $chem['id'],
                'type' => $type,
                'status' => 1
            ];

            $subList = SubstrateModel::find()->select('id')->where($where)->asArray()->all();
            if (!empty($subList)) {
                SubstrateModel::updateAll([
                    'status' => 0
                ], ['id' => array_column($subList, 'id')]);
            }

            $proList = ProductModel::find()->select('id')->where($where)->asArray()->all();
            if (!empty($proList)) {
                SubstrateModel::updateAll([
                    'status' => 0
                ], ['id' => array_column($proList, 'id')]);
            }

            $detailList = ReactionDetailsModel::find()->select('id')->where($where)->asArray()->all();
            if (!empty($detailList)) {
                SubstrateModel::updateAll([
                    'status' => 0
                ], ['id' => array_column($detailList, 'id')]);
            }
        }

        return $this->success($relayId);
    }
}
