/**
 * Vue 应用动态加载器
 * 功能：动态挂载/卸载 Vue 组件，管理应用生命周期
 * 特性：
 *  - 自动处理 ElementPlus 注册
 *  - 防止重复挂载
 *  - 内存泄漏防护
 * 实现原理：
 *  1. 创建独立的 Vue 应用实例注册全局插件
 *  2. 通过虚拟节点 (vnode) 共享插件上下文
 *  3. 基于选择器管理组件实例生命周期
 */

import ElementPlus from 'element-plus';
import { createApp, createVNode, render, h } from 'vue';
import i18n from '/vue-ui/dist/i18n.js';

// 创建一个空的App实例，用来注册插件
const pluginApp = createApp({});
pluginApp.use(i18n);
pluginApp.use(ElementPlus);

/**
 * 动态渲染Vue组件到指定DOM容器
 * @param {string} componentPath - 组件路径
 * @param {string} containerSelector - 容器选择器
 * @param {object} props - 传递给组件的属性对象
 * @example
 * renderComponent('/vue-ui/dist/modalA.js', '#vue-modal', {
 *     title: '新建实验',
 *     onCancel: () => {
 *         unrenderComponent('#vue-modal');
 *         $('#vue-modal').hide();
 *     }
 * });
 */
window.renderComponent = async function (componentPath, containerSelector, props = {}) {
    const container = document.querySelector(containerSelector);
    if (!container) {
        console.error(`Container ${containerSelector} not found`);
        return;
    }
    container.classList.add('vue-app-container');

    // 卸载旧vnode
    if (window._dynamicVNodes?.[containerSelector]) {
        render(null, container);
        window._dynamicVNodes[containerSelector] = null;
    }

    const component = await import(componentPath).catch((error) => {
        console.error(`Component load failed: ${error}`);
        container.innerHTML = `<div class="load-error">组件加载失败: ${componentPath}</div>`;
    });

    // 创建vnode，并注入插件上下文
    const vnode = createVNode(
        {
            render() {
                return h(component.default, {
                    ...props,
                });
            }
        }
    );

    // 关键：复用 pluginApp 的上下文
    vnode.appContext = pluginApp._context;

    // 挂载
    render(vnode, container);
    window._dynamicVNodes = window._dynamicVNodes || {};
    window._dynamicVNodes[containerSelector] = vnode;

}

/**
 * 卸载指定容器中的Vue组件
 * @function
 * @param {string} containerSelector - 容器选择器
 * @example
 * unrenderComponent('#vue-modal')
 */
window.unrenderComponent = function (containerSelector) {
    if (window._dynamicVNodes?.[containerSelector]) {
        const container = document.querySelector(containerSelector);
        if (container) {
            render(null, container);
            container._vnode = null;
        }
        delete window._dynamicVNodes[containerSelector];
    }
};

/**
 * 动态挂载 Vue 组件到指定容器
 * @param {string} componentPath - 组件文件路径（支持动态导入）
 * @param {string} containerSelector - DOM 容器选择器
 * @param {Object} events - 事件监听器对象（格式：{ eventName: callback }）
 *
 * @example
 * // 基本使用
 * dynamicMount('/vue-ui/dist/ModalA.js', '#app-container');
 *
 * // 带事件处理的使用
 * dynamicMount('/vue-ui/dist/modalA.js', '#vue-modal', {
 *   onCancel: () => {
 *     unmountDynamicApp('#vue-modal');
 *     $('#vue-modal').hide();
 *   }
 * });
 */
window.dynamicMount = function (componentPath, containerSelector, events = {}) {
    // 初始化应用实例缓存对象
    if (!window._dynamicVueApps) window._dynamicVueApps = {};

    // 获取目标容器元素
    const container = document.querySelector(containerSelector);
    // 添加应用容器标识类（用于统一样式管理）
    container.classList.add('vue-app-container');
    const key = containerSelector;

    // 清理已有实例（防止重复挂载）
    if (window._dynamicVueApps[key]) {
        // 执行 Vue 应用卸载
        window._dynamicVueApps[key].unmount();
        // 清除实例引用
        window._dynamicVueApps[key] = null;
        // 清空容器内容（避免残留 DOM）
        container.innerHTML = '';
    }

    // 动态加载目标组件
    import(componentPath).then((component) => {
        // 创建新 Vue 应用实例
        const app = createApp(component.default);

        // 注册全局事件总线
        app.config.globalProperties.$emitExternal = (eventName, ...args) => {
            if (events[eventName]) {
                events[eventName](...args);
            }
        };

        // 注册i18n和ElementPlus组件库
        app.use(i18n)
        app.use(ElementPlus);

        // 挂载应用到 DOM 容器
        app.mount(container);

        // 缓存应用实例
        window._dynamicVueApps[key] = app;
    });
}

/**
 * 卸载动态挂载的 Vue 应用实例
 * @param {string} containerSelector - 必须指定的目标容器选择器
 */
window.unmountDynamicApp = function (containerSelector) {
    if (!containerSelector) {
        throw new Error('必须指定要卸载的容器选择器');
    }

    const key = containerSelector;
    if (window._dynamicVueApps?.[key]) {
        window._dynamicVueApps[key].unmount();
        window._dynamicVueApps[key] = null;
        document.querySelector(containerSelector).innerHTML = '';
    }
};



// 全局组件状态管理器
const ComponentRegistry = (() => {
    const registry = new Map();       
    const instanceMap = new WeakMap(); 

    return {
        register(component, app) {
            if (!registry.has(component)) {
                registry.set(component, new Set());
            }
            registry.get(component).add(app);
            instanceMap.set(app, component);
        },
        clearOthers(keepComponent) {
            for (const [component, apps] of registry.entries()) {
                if (component !== keepComponent) {
                    for (const app of apps) {
                        this._safeUnmount(app);
                    }
                }
            }
        },
        clearAll() {
            for (const apps of registry.values()) {
                for (const app of apps) {
                    this._safeUnmount(app);
                }
            }
            registry.clear();
        },
        _safeUnmount(app) {
            try {
                const component = instanceMap.get(app);
                app.unmount();
                if (component && registry.has(component)) {
                    registry.get(component).delete(app);
                }
            } catch (e) {
                console.warn("Error unmounting app:", e);
            }
        }
    };
})();

// 存储动态应用实例的容器
window._dynamicCompleteVueApps = window._dynamicCompleteVueApps || {};

/**
 * 动态渲染Vue组件到指定DOM容器（支持完整生命周期）
 * @param {string} componentPath - 组件路径
 * @param {string} containerSelector - 容器选择器
 * @param {object} props - 传递给组件的属性对象
 * @param {boolean} needClear - 是否清除其他相同组件实例（默认为true）
 */
window.mountVueApp = async function (componentPath, containerSelector, props = {}, needClear = true) {
    const container = document.querySelector(containerSelector);
    if (!container) {
        console.error(`Container ${containerSelector} not found`);
        return;
    }

    container.classList.add('vue-app-container');
    const key = containerSelector;

    // 卸载旧应用（如果存在）
    if (window._dynamicCompleteVueApps[key]) {
        window._dynamicCompleteVueApps[key].unmount();
        delete window._dynamicCompleteVueApps[key];
        container.innerHTML = '';
    }

    let component;
    try {
        const module = await import(/* webpackIgnore: true */ componentPath);
        component = module.default || module;
    } catch (e) {
        console.error(`Failed to load component from ${componentPath}`, e);
        container.innerHTML = `<div class="load-error">组件加载失败: ${componentPath}</div>`;
        return;
    }

    // 清除其他相同组件的实例
    if (needClear) {
        ComponentRegistry.clearOthers(component);
        
    }

    // 创建新应用
    const app = createApp({
        render() {
            return h(component, props);
        }
    });
    // 注册当前组件实例到管理器
    if (needClear) {  
      ComponentRegistry.register(component, app);
    }
    

    // 插件
    app.use(i18n);
    app.use(ElementPlus);

    // 挂载
    app.mount(container);

    // 缓存实例
    window._dynamicCompleteVueApps[key] = app;
};

/**
 * 手动卸载某个容器中的 Vue 应用
 * @param {string} containerSelector - 容器选择器
 */
window.unmountVueApp = function (containerSelector) {
    const key = containerSelector;

    if (window._dynamicCompleteVueApps?.[key]) {
        window._dynamicCompleteVueApps[key].unmount();
        delete window._dynamicCompleteVueApps[key];

        const container = document.querySelector(containerSelector);
        if (container) container.innerHTML = '';
    }
};