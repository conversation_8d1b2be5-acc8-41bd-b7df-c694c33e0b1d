<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('templateHistoryDialog.title')"
    width="780px"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
    style="padding: 22px;"
    class="template-history-dialog"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane v-if="Number(props.templateEffectMode) === 2" :label="$t('templateHistoryDialog.versions')" name="versions">
        <div class="version-header">
          <div class="last-edit-time">
            {{ $t('templateHistoryDialog.lastEditTime') }}
            <span>{{ formatDateTime(props.updateTime || new Date()) }}</span>
          </div>
        </div>
        <div class="version-list" v-loading="versionLoading">
          <div v-for="version in versionData" :key="version.id" class="version-item">
            <div class="version-info">
              <div class="version-name-row">
                <div class="version-name">{{ version.user_version }}</div>
                <span :class="['status-tag', getStatusClass(version.status)]">{{ getStatusText(version.status) }}</span>
              </div>
              <div class="version-meta">
                <div class="version-date">
                  <template v-if="version.actual_effect_time">
                    {{ formatDate(version.actual_effect_time) }} {{ $t('templateHistoryDialog.effective') }}
                  </template>
                  <template v-else-if="version.effect_date">
                    <template v-if="version.status == 6 || version.status == 4">
                      {{ $t('templateHistoryDialog.originallyScheduled') }}{{ formatDate(version.effect_date) }}{{ $t('templateHistoryDialog.effective') }}
                    </template>
                    <template v-else-if="version.status == 2 || version.status == 5">
                      {{ $t('templateHistoryDialog.scheduledFor') }}{{ formatDate(version.effect_date) }}{{ $t('templateHistoryDialog.effective') }}
                    </template>
                    <template v-else>
                      {{ formatDate(version.effect_date) }} {{ $t('templateHistoryDialog.effective') }}
                    </template>
                  </template>
                  <template v-else>
                    <template v-if="version.status == 6 || version.status == 4">
                      {{ $t('templateHistoryDialog.notEffective') }}
                    </template>
                    <template v-else-if="version.status == 2 || version.status == 5">
                      {{ $t('templateHistoryDialog.notYetEffective') }}
                    </template>
                    <template v-else>
                      -
                    </template>
                  </template>
                </div>
                <div class="version-creator">
                  <el-avatar :size="24" :src="version.avatar || ''">{{ getInitials(version.creatorName) }}</el-avatar>
                  <span>{{ version.creatorName }}</span>
                </div>
              </div>
            </div>
            <div class="version-actions">
              <!-- 查看按钮 -->
              <el-button 
                type="primary" 
                text 
                class="action-button view-button" 
                @click="handleViewVersion(version)"
              >
                <el-icon><View /></el-icon>
              </el-button>
              
              <!-- 更多操作下拉菜单 -->
              <el-dropdown 
                v-if="canManageVersion(version)" 
                trigger="click" 
                @command="(command) => handleVersionCommand({ action: command, version })"
              >
                <el-button 
                  text 
                  class="action-button more-button"
                >
                  <svg width="16" height="16" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.2764 13.0572C10.2764 13.0572 9.88574 12.6666 9.33301 12.6666C9.33301 12.6666 8.78125 12.6666 8.39062 13.0572C8.39062 13.0572 8 13.4477 8 14C8 14 8 14.5522 8.39062 14.9428C8.39062 14.9428 8.78125 15.3333 9.33301 15.3333C9.33301 15.3333 9.88574 15.3333 10.2764 14.9428C10.2764 14.9428 10.667 14.5522 10.667 14C10.667 14 10.667 13.4477 10.2764 13.0572ZM14 12.6666C14.5527 12.6666 14.9434 13.0572 14.9434 13.0572C15.333 13.4477 15.333 14 15.333 14C15.333 14.5522 14.9434 14.9428 14.9434 14.9428C14.5527 15.3333 14 15.3333 14 15.3333C13.4482 15.3333 13.0576 14.9428 13.0576 14.9428C12.667 14.5522 12.667 14 12.667 14C12.667 13.4477 13.0576 13.0572 13.0576 13.0572C13.4482 12.6666 14 12.6666 14 12.6666ZM19.6094 13.0572C19.6094 13.0572 19.2188 12.6666 18.667 12.6666C18.667 12.6666 18.1143 12.6666 17.7236 13.0572C17.7236 13.0572 17.333 13.4477 17.333 14C17.333 14 17.333 14.5522 17.7236 14.9428C17.7236 14.9428 18.1143 15.3333 18.667 15.3333C18.667 15.3333 19.2188 15.3333 19.6094 14.9428C19.6094 14.9428 20 14.5522 20 14C20 14 20 13.4477 19.6094 13.0572Z" 
                      fill="#1388FF" fill-rule="evenodd" clip-rule="evenodd"/>
                  </svg>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="editVersion">
                      {{ $t('templateHistoryDialog.editVersion') }}
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="canEditEffectiveDate(version)" 
                      command="editEffectiveDate"
                    >
                      {{ $t('templateHistoryDialog.editEffectiveDate') }}
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="canCancelPublish(version)" 
                      command="cancelPublish"
                    >
                      {{ $t('templateHistoryDialog.cancelPublish') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div v-if="versionData.length === 0 && !versionLoading" class="no-data">
            {{ $t('templateHistoryDialog.noVersions') }}
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('templateHistoryDialog.allHistory')" name="allHistory">
        <el-table 
          :data="logData" 
          max-height="500px"
          v-loading="loading"
          element-loading-text="加载中..."
          element-loading-background="rgba(255, 255, 255, 0.8)"
          :border="false"
          class="history-table"
        >
          <el-table-column
            prop="createTime"
            :label="$t('templateHistoryDialog.operationTime')"
            width="180"
          />
          <el-table-column
            prop="userName"
            :label="$t('templateHistoryDialog.operator')"
            width="120"
          />
          <el-table-column
            prop="action"
            :label="$t('templateHistoryDialog.operation')"
            width="120"
          />
          <el-table-column
            :label="$t('templateHistoryDialog.details')"
          >
            <template #default="scope">
              <div v-if="scope.row.details" class="details-text">
                {{ scope.row.details }}
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 修改版本号弹窗 -->
    <el-dialog
      v-model="editVersionVisible"
      :title="$t('templateHistoryDialog.editVersionTitle')"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
      class="template-history-edit-dialog"
    >
      <el-input v-model="newVersionValue" :placeholder="$t('templateHistoryDialog.versionInputPlaceholder')" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editVersionVisible = false" :disabled="editVersionLoading">
            {{ $t('templateHistoryDialog.cancel') }}
          </el-button>
          <el-button type="primary" @click="saveVersionEdit" :loading="editVersionLoading">
            {{ $t('templateHistoryDialog.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 修改生效日期弹窗 -->
    <el-dialog
      v-model="editDateVisible"
      :title="$t('templateHistoryDialog.editEffectiveDateTitle')"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
      class="template-history-edit-dialog"
    >
      <div class="date-picker-container">
        <el-date-picker
          v-model="newEffectiveDate"
          type="date"
          :placeholder="$t('templateHistoryDialog.datePickerPlaceholder')"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
        <div class="date-hint">{{ $t('templateHistoryDialog.dateHint') }}</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDateVisible = false" :disabled="editDateLoading">
            {{ $t('templateHistoryDialog.cancel') }}
          </el-button>
          <el-button type="primary" @click="saveEffectiveDateEdit" :loading="editDateLoading">
            {{ $t('templateHistoryDialog.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
  <!-- 取消发布确认对话框 -->
  <el-dialog
    v-model="cancelDialogVisible"
    :title="$t('templateHistoryDialog.cancelPublishTitle')"
    width="400px"
    append-to-body
    center
    class="template-history-confirm-dialog"
  >
    <span>{{ $t('templateHistoryDialog.cancelPublishConfirm', { version: cancelingVersion?.user_version }) }}</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelDialogVisible = false" :disabled="cancelPublishLoading">
          {{ $t('templateHistoryDialog.cancel') }}
        </el-button>
        <el-button type="primary" @click="handleConfirmCancel" :loading="cancelPublishLoading">
          {{ $t('templateHistoryDialog.confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import axios from 'axios'
import { useI18n } from 'vue-i18n'
import { ArrowDown, View, More } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OperationButton from '../in-form/operation-tool-bar/OperationButton.vue'

const { t } = useI18n()

const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  templateId: {
    type: [String, Number],
    default: ''
  },
  templateEffectMode: {
    type: Number,
    default: 2
  },
  actionLogs: {
    type: Array,
    default: () => []
  },
  onClose: {
    type: Function,
    default: () => {}
  },
  dev: {
    type: Boolean,
    default: false
  },
  updateTime: {
    type: [String, Date],
    default: ''
  },
  tempPower: {
    type: Number,
    default: 0
  },
  openTemplateHistory: {
    type: Function,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'close'])
// 对话框可见性
const dialogVisible = ref(true)
// 当前活动标签页 - 根据模式设置默认标签页
const activeTab = ref(Number(props.templateEffectMode) === 2 ? 'versions' : 'allHistory')
// 原始日志数据
const rawLogData = ref([])
// 加载状态
const loading = ref(false)
// 用户列表
const userList = ref({})
// 版本数据
const versionData = ref([])
// 版本加载状态
const versionLoading = ref(false)
// 编辑版本号弹窗可见性
const editVersionVisible = ref(false)
// 当前编辑的版本
const editingVersion = ref(null)
// 新版本号值
const newVersionValue = ref('')
// 修改生效日期弹窗可见性
const editDateVisible = ref(false)
// 新生效日期值
const newEffectiveDate = ref('')
// 取消发布对话框可见性
const cancelDialogVisible = ref(false)
// 正在取消的版本
const cancelingVersion = ref(null)
// 查看版本的 loading 状态
const viewLoading = ref(false)
// 保存版本编辑的 loading 状态
const editVersionLoading = ref(false)
// 保存生效日期编辑的 loading 状态
const editDateLoading = ref(false)
// 取消发布版本的 loading 状态
const cancelPublishLoading = ref(false)

// 开发环境测试数据 - 操作日志
const testData = [
  {
    "id": "23",
    "template_id": "4379",
    "action_code": "submit_audit",
    "extra_data": null,
    "history_id": "27",
    "create_by": "1134",
    "create_time": "2025-06-04 20:01:17",
    "status": "1"
  },
  {
    "id": "24",
    "template_id": "4379",
    "action_code": "audit_agree",
    "extra_data": null,
    "history_id": "27",
    "create_by": "1134",
    "create_time": "2025-06-04 20:01:30",
    "status": "1"
  },
  {
    "id": "32",
    "template_id": "4379",
    "action_code": "audit_refuse",
    "extra_data": "{\"remark\":\"ttteat\"}",
    "history_id": "27",
    "create_by": "1134",
    "create_time": "2025-06-05 17:15:29",
    "status": "1"
  },
  {
    "id": "41",
    "template_id": "4379",
    "action_code": "transfer",
    "extra_data": "{\"transferFrom\":\"qzytest\",\"transferTo\":\"cqtest1\"}",
    "history_id": "27",
    "create_by": "1134",
    "create_time": "2025-06-05 20:03:46",
    "status": "1"
  },
  {
    "id": "86",
    "template_id": "4378",
    "action_code": "edit_version",
    "extra_data": "{\"versionFrom\":\"v444\",\"versionTo\":\"v5\"}",
    "history_id": "55",
    "create_by": "1134",
    "create_time": "2025-06-16 15:02:02",
    "status": "1"
  },
  {
    "id": "87",
    "template_id": "4378",
    "action_code": "edit_effective_date",
    "extra_data": "{\"dateFrom\":\"2025-06-20 00:00:00\",\"dateTo\":\"2025-06-23\",\"version\":\"v5\"}",
    "history_id": "55",
    "create_by": "1134",
    "create_time": "2025-06-16 15:03:04",
    "status": "1"
  }
]

// 开发环境测试数据 - 版本列表
const testVersionData = [
  {
    "id": "43",
    "template_id": "4379",
    "name": "newnew",
    "descript": "May it be",
    "title": "",
    "keywords": "qzy",
    "define_item": "[]",
    "action_code": "submit_publish_audit",
    "action_id": "67",
    "inner_version": "8",
    "user_version": "V8.0",
    "create_by": "1134",
    "create_time": "2025-06-10 13:30:32",
    "actual_effect_time": null,
    "status": "2",
    "effect_date": null
  },
  {
    "id": "42",
    "template_id": "4379",
    "name": "newnew",
    "descript": "May it be",
    "title": "",
    "keywords": "qzy",
    "define_item": "[]",
    "action_code": "submit_publish_audit",
    "action_id": "64",
    "inner_version": "7",
    "user_version": "V7.0",
    "create_by": "1134",
    "create_time": "2025-06-10 13:28:08",
    "actual_effect_time": null,
    "status": "3",
    "effect_date": null
  },
  {
    "id": "41",
    "template_id": "4379",
    "name": "newnew",
    "descript": "May it be",
    "title": "",
    "keywords": "qzy",
    "define_item": "[]",
    "action_code": "submit_publish_audit",
    "action_id": "62",
    "inner_version": "6",
    "user_version": "V6.0",
    "create_by": "1134",
    "create_time": "2025-06-10 13:13:36",
    "actual_effect_time": null,
    "status": "4",
    "effect_date": null
  },
  {
    "id": "40",
    "template_id": "4379",
    "name": "newnew",
    "descript": "May it be",
    "title": "",
    "keywords": "qzy",
    "define_item": "[]",
    "action_code": "submit_publish_audit",
    "action_id": "59",
    "inner_version": "5",
    "user_version": "V5.0",
    "create_by": "1134",
    "create_time": "2025-06-10 13:10:58",
    "actual_effect_time": null,
    "status": "7",
    "effect_date": null
  }
]

// 开发环境测试用户数据
const testUserList = {
  "1134": {
    "id": "1134",
    "real_name": "吴羽",
    "name": "qzytest",
    "email": "<EMAIL>"
  }
}

// 处理后的日志数据
const logData = computed(() => {
  // 首先映射数据
  const mappedData = rawLogData.value.map(log => {
    // 获取用户名，如果有用户信息的话
    let userName = log.create_by;
    if (userList.value && userList.value[log.create_by]) {
      const user = userList.value[log.create_by];
      // 优先使用真实姓名，然后是用户名，最后是邮箱
      userName = user.real_name || user.name || user.username || user.email || log.create_by;
    }
    
    // 翻译操作代码
    let action = log.action_code;
    // 使用i18n翻译操作代码
    const i18nKey = `templateHistoryDialog.actions.${log.action_code}`;
    const translatedAction = t(i18nKey);
    
    // 如果有翻译，则使用翻译，否则使用原始代码
    if (translatedAction !== i18nKey) {
      action = translatedAction;
    }
    
    // 处理详情 - 始终以操作名称开头
    let details = action;
    let extraData = null;
    
    if (log.extra_data) {
      try {
        extraData = JSON.parse(log.extra_data);
        
        // 根据不同的操作类型，处理不同的详情显示
        switch (log.action_code) {
          case 'audit_refuse':
            // 模板审核驳回，显示原因
            if (extraData.remark) {
              details = `${action}，${t('templateHistoryDialog.reason')}：${extraData.remark}`;
            }
            break;
            
          case 'publish':
          case 'submit_publish_audit':
            // 发布模板，显示版本号和生效日期，如果没有生效日期，则显示立即生效
            const publishInfo = [];
            if (extraData.version) {
              publishInfo.push(`${t('templateHistoryDialog.version')}：${extraData.version}`);
            }
            if (extraData.effect_date) {
              publishInfo.push(`${t('templateHistoryDialog.effectiveDate')}：${extraData.effect_date}`);
            } else {
              publishInfo.push(`${t('templateHistoryDialog.effectiveDate')}：立即生效`);
            }
            if (publishInfo.length > 0) {
              details = `${action}，${publishInfo.join('；')}`;
            }
            break;
            
          case 'publish_audit_agree':
            // 发布审核通过，显示版本号
            if (extraData.version) {
              details = `${action}，${t('templateHistoryDialog.version')}：${extraData.version}`;
            }
            break;
            
          case 'publish_audit_refuse':
            // 发布审核驳回，显示版本号和原因
            const refuseInfo = [];
            if (extraData.version) {
              refuseInfo.push(`${t('templateHistoryDialog.version')}：${extraData.version}`);
            }
            if (extraData.remark) {
              refuseInfo.push(`${t('templateHistoryDialog.reason')}：${extraData.remark}`);
            }
            if (refuseInfo.length > 0) {
              details = `${action}，${refuseInfo.join('；')}`;
            }
            break;
            
          case 'cancel_publish':
            // 取消发布，显示版本号
            if (extraData.version) {
              details = `${action}，${t('templateHistoryDialog.version')}：${extraData.version}`;
            }
            break;
            
          case 'transfer':
            // 转让模板，显示转让人和被转让人
            if (extraData.transferFrom && extraData.transferTo) {
              details = `${action}，${extraData.transferFrom} → ${extraData.transferTo}`;
            }
            break;
          case 'edit_version':
            // 修改版本号，显示旧版本号和新版本号
            if (extraData.versionFrom && extraData.versionTo) {
              details = `${action}，${extraData.versionFrom} → ${extraData.versionTo}`;
            }
            break;
          case 'edit_effective_date':
            // 修改生效日期，显示旧生效日期和新生效日期
            if (extraData.dateFrom && extraData.dateTo && extraData.version) {
              details = `${action}，${extraData.version} ${t('templateHistoryDialog.versionLabel')}：${formatDate(extraData.dateFrom)} → ${formatDate(extraData.dateTo)}`;
            } else if (extraData.dateFrom && extraData.dateTo) {
              // 如果没有版本信息，使用简化格式
              details = `${action}，${formatDate(extraData.dateFrom)} → ${formatDate(extraData.dateTo)}`;
            }
            break;
            
          default:
            // 对于其他操作，如果有额外数据，则显示格式化的JSON
            if (Object.keys(extraData).length > 0) {
              const extraDataStr = JSON.stringify(extraData, null, 2);
              details = `${action}，${extraDataStr}`;
            }
        }
      } catch (e) {
        // 如果解析JSON失败，直接显示原始数据
        details = `${action}，${log.extra_data}`;
      }
    }
    
    return {
      id: log.id,
      createTime: log.create_time,
      userName: userName,
      action: action,
      details: details,
      rawData: log // 保留原始数据，以备后用
    };
  });
  
  // 然后按照创建时间倒序排序
  return mappedData.sort((a, b) => {
    const dateA = new Date(a.createTime);
    const dateB = new Date(b.createTime);
    return dateB - dateA; // 倒序排序
  });
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  if (isNaN(date.getTime())) return dateTime; // 如果日期无效，返回原始字符串
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 格式化日期（不含时间）
const formatDate = (dateStr) => {
  if (!dateStr) return '-';
  // 如果日期字符串包含时间，只取日期部分
  if (dateStr.includes(' ')) {
    return dateStr.split(' ')[0];
  }
  return dateStr;
}

// 获取状态对应的CSS类名
const getStatusClass = (status) => {
  // 状态码映射到CSS类名
  const statusMap = {
    '1': 'draft',           // 草稿
    '2': 'pending-review',  // 审核中
    '3': 'effective',       // 生效中
    '4': 'rejected',        // 已驳回
    '5': 'pending-effect',  // 待生效
    '6': 'canceled',        // 已取消
    '7': 'expired'          // 已失效
  };
  return statusMap[status] || 'unknown';
}

      

// 获取状态文本
const getStatusText = (status) => {
  // 状态码映射到文本
  const statusTextMap = {
    '1': '草稿',
    '2': '审核中',
    '3': '生效',
    '4': '驳回',
    '5': '待生效',
    '6': '已取消',
    '7': '已失效'
  };
  
  return statusTextMap[status] || '未知状态';
}



// 获取版本列表
const fetchVersionList = async () => {
  // 如果是开发环境，直接使用测试数据
  if (props.dev) {
    console.log('开发环境：使用版本测试数据');
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    // 合并测试用户数据
    userList.value = { ...userList.value, ...testUserList };
    
    // 处理版本数据，添加创建者名称
    versionData.value = testVersionData.map(version => {
      let creatorName = version.create_by;
      if (userList.value && userList.value[version.create_by]) {
        const user = userList.value[version.create_by];
        creatorName = user.real_name || user.name || user.username || user.email || version.create_by;
      }
      
      return {
        ...version,
        creatorName
      };
    });
    
    versionLoading.value = false;
    return;
  }
  
  // 生产环境下需要检查模板ID和模式
  if (!props.templateId || Number(props.templateEffectMode) !== 2) {
    return;
  }
  
  versionLoading.value = true;
  
  try {
    const response = await axios.get('/?r=template-history/version-list', {
      params: { temp_id: props.templateId },
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    console.log('获取模板版本列表响应:', response.data);
    
    if (response.data.status === 1) {
      // 修正这里以匹配后端返回的数据结构
      const versions = response.data.data.data || [];
      
      // 合并用户列表数据
      if (response.data.data.userList) {
        userList.value = { ...userList.value, ...response.data.data.userList };
      }
      
      // 处理版本数据，添加创建者名称
      versionData.value = versions.map(version => {
        let creatorName = version.create_by;
        if (userList.value && userList.value[version.create_by]) {
          const user = userList.value[version.create_by];
          creatorName = user.real_name || user.name || user.username || user.email || version.create_by;
        }
        
        return {
          ...version,
          creatorName
        };
      });
    } else {
      console.error('获取模板版本列表失败:', response.data.info);
      ElMessage.error('获取模板版本列表失败');
    }
  } catch (error) {
    console.error('获取模板版本列表请求失败:', error);
    ElMessage.error('获取模板版本列表请求失败');
  } finally {
    versionLoading.value = false;
  }
}

// 获取模板操作日志
const fetchActionLogs = async () => {
  // 如果是开发环境，直接使用测试数据
  if (props.dev) {
    console.log('开发环境：使用测试数据');
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    rawLogData.value = testData;
    loading.value = false;
    return;
  }
  
  // 以下是生产环境的逻辑
  if (!props.templateId) {
    console.error('模板ID为空，无法获取操作日志');
    return;
  }
  
  console.log('正在获取模板操作日志，模板ID:', props.templateId);
  loading.value = true;
  
  try {
    const response = await axios.get('/?r=template-history/action-logs', {
      params: { temp_id: props.templateId },
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    console.log('获取模板操作日志响应:', response.data);
    
    if (response.data.status === 1) {
      // 修改这里以匹配后端返回的数据结构
      rawLogData.value = response.data.data.actionLogs || [];
      
      // 合并用户列表数据
      if (response.data.data.userList) {
        userList.value = { ...userList.value, ...response.data.data.userList };
      }
    } else {
      console.error('获取模板操作日志失败:', response.data.info);
    }
  } catch (error) {
    console.error('获取模板操作日志请求失败:', error);
  } finally {
    loading.value = false;
  }
}


// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.templateId) {
    fetchActionLogs();
    fetchVersionList();
  }
})

// 监听templateId变化
watch(() => props.templateId, (newVal) => {
  if (newVal) {
    console.log('模板ID变化:', newVal);
    fetchActionLogs();
    fetchVersionList();
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
  if (!newVal) {
    props.onClose && props.onClose();
  }
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  emit('close');
  props.onClose && props.onClose();
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('模板历史对话框组件已挂载');
  fetchActionLogs();
  fetchVersionList();
})

// 版本状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case '1': // 审核中
    case '2': // 审核中
      return 'warning';
    case '3': // 已生效
      return 'success';
    case '4': // 已取消
      return 'info';
    case '5': // 审核通过未生效
      return 'primary';
    default:
      return 'info';
  }
}


// 是否可以管理版本
const canManageVersion = (version) => {
  // 检查是否有编辑版本的权限
  const canEditVersion = Number(props.tempPower) === 1;
  return canEditVersion;
}

// 是否可以编辑生效日期
const canEditEffectiveDate = (version) => {
  // 检查是否有编辑生效日期的权限
  const canEditDate = Number(props.tempPower) === 1;
  // 检查版本状态是否允许编辑
  const isEditableStatus = ['2', '5'].includes(version.status);
  // 检查模板是否为定时生效模式
  const isTimedEffect = Number(props.templateEffectMode) === 2;
  return canEditDate && isEditableStatus && isTimedEffect;
}

// 是否可以取消发布
const canCancelPublish = (version) => {
  // 检查是否有取消发布的权限
  const canCancel = Number(props.tempPower) === 1;
  // 检查版本状态是否允许取消
  const isCancellableStatus = ['2', '5'].includes(version.status);
  return canCancel && isCancellableStatus;
}

// 处理版本操作命令
const handleVersionCommand = ({ action, version }) => {
  switch (action) {
    case 'editVersion':
      editVersionVisible.value = true;
      editingVersion.value = version;
      newVersionValue.value = version.user_version;
      break;
    case 'editEffectiveDate':
      editDateVisible.value = true;
      editingVersion.value = version;
      newEffectiveDate.value = version.effect_date;
      break;
    case 'cancelPublish':
      confirmCancelPublish(version);
      break;
    default:
      console.log('Unknown version action:', action);
  }
}

// 保存版本号编辑
const saveVersionEdit = async () => {
  if (!newVersionValue.value) {
    ElMessage.error(t('templateHistoryDialog.versionEmpty'))
    return
  }
  
  try {
    // 如果是开发环境，直接模拟成功
    if (props.dev) {
      console.log('开发环境：模拟修改版本号', {
        versionId: editingVersion.value.id,
        newVersion: newVersionValue.value
      })
      
      // 更新本地数据
      const index = versionData.value.findIndex(v => v.id === editingVersion.value.id)
      if (index !== -1) {
        versionData.value[index].user_version = newVersionValue.value
      }
      
      ElMessage.success(t('templateHistoryDialog.editSuccess'))
      editVersionVisible.value = false
      // 重新获取全部痕迹数据
      fetchActionLogs()
      fetchVersionList()
      return
    }
    
    // 生产环境下发送请求
    const response = await axios.post('/?r=template-history/update-history', {
      history_id: editingVersion.value.id,
      user_version: newVersionValue.value
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    
    if (response.data.status === 1) {
      // 更新本地数据
      const index = versionData.value.findIndex(v => v.id === editingVersion.value.id)
      if (index !== -1) {
        versionData.value[index].user_version = newVersionValue.value
      }
      
      ElMessage.success(t('templateHistoryDialog.editSuccess'))
      // 重新获取全部痕迹数据
      fetchActionLogs()
      fetchVersionList()
    } else {
      ElMessage.error(response.data.info || t('templateHistoryDialog.editFailed'))
    }
  } catch (error) {
    console.error('修改版本号请求失败:', error)
    ElMessage.error(t('templateHistoryDialog.editFailed'))
  } finally {
    editVersionVisible.value = false
  }
}

// 保存生效日期编辑
const saveEffectiveDateEdit = async () => {
  if (!newEffectiveDate.value) {
    ElMessage.error(t('templateHistoryDialog.dateEmpty'))
    return
  }
  
  try {
    // 格式化日期为 YYYY-MM-DD 格式
    const formattedDate = new Date(newEffectiveDate.value).toISOString().split('T')[0]
    
    // 如果是开发环境，直接模拟成功
    if (props.dev) {
      console.log('开发环境：模拟修改生效日期', {
        versionId: editingVersion.value.id,
        effectiveDate: formattedDate
      })
      
      // 更新本地数据
      const index = versionData.value.findIndex(v => v.id === editingVersion.value.id)
      if (index !== -1) {
        versionData.value[index].effect_date = formattedDate
      }
      
      ElMessage.success(t('templateHistoryDialog.editSuccess'))
      editDateVisible.value = false
      // 重新获取全部痕迹数据
      fetchActionLogs()
      fetchVersionList()
      return
    }
    
    // 生产环境下发送请求
    const response = await axios.post('/?r=template-history/update-history', {
      history_id: editingVersion.value.id,
      effect_date: formattedDate
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    
    if (response.data.status === 1) {
      // 更新本地数据
      const index = versionData.value.findIndex(v => v.id === editingVersion.value.id)
      if (index !== -1) {
        versionData.value[index].effect_date = formattedDate
      }
      
      ElMessage.success(t('templateHistoryDialog.editSuccess'))
      // 重新获取全部痕迹数据
      fetchActionLogs()
      fetchVersionList()
    } else {
      ElMessage.error(response.data.info || t('templateHistoryDialog.editFailed'))
    }
  } catch (error) {
    console.error('修改生效日期请求失败:', error)
    ElMessage.error(t('templateHistoryDialog.editFailed'))
  } finally {
    editDateVisible.value = false
  }
}

// 禁用日期选择器的日期
const disabledDate = (time) => {
  // 获取今天的日期（设置时间为00:00:00）
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // 禁用今天及之前的日期，只允许选择明天及以后的日期
  return time.getTime() <= today.getTime();
}

// 取消发布版本
const cancelPublishVersion = async (version) => {
  try {
    // 如果是开发环境，直接模拟成功
    if (props.dev) {
      console.log('开发环境：模拟取消发布', {
        history_id: version.id
      })
      
      // 更新本地数据
      const index = versionData.value.findIndex(v => v.id === version.id)
      if (index !== -1) {
        // 将状态更新为"已取消"(6)
        versionData.value[index].status = 6
      }
      
      ElMessage.success(t('templateHistoryDialog.cancelSuccess'))
      // 重新获取全部痕迹数据
      fetchActionLogs()
      fetchVersionList()
      return
    }
    
    // 生产环境下发送请求
    const response = await axios.post('/?r=template-history/cancel-publish', {
      history_id: version.id
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    
    if (response.data.status === 1) {
      // 更新本地数据
      const index = versionData.value.findIndex(v => v.id === version.id)
      if (index !== -1) {
        // 将状态更新为"已取消"(6)
        versionData.value[index].status = 6
      }
      
      ElMessage.success(t('templateHistoryDialog.cancelSuccess'))
      // 重新获取全部痕迹数据
      fetchActionLogs()
      fetchVersionList()
    } else {
      ElMessage.error(response.data.info || t('templateHistoryDialog.cancelFailed'))
    }
  } catch (error) {
    console.error('取消发布请求失败:', error)
    ElMessage.error(t('templateHistoryDialog.cancelFailed'))
  }
}

const confirmCancelPublish = (version) => {
  // 使用自定义的对话框变量替代 ElMessageBox
  cancelDialogVisible.value = true
  cancelingVersion.value = version
}

const handleConfirmCancel = () => {
  if (cancelingVersion.value) {
    cancelPublishVersion(cancelingVersion.value)
    cancelDialogVisible.value = false
  }
}

// 获取用户名首字母作为头像显示
const getInitials = (name) => {
  if (!name) return '';
  
  // 处理中文名字 - 取第一个字
  if (/[\u4e00-\u9fa5]/.test(name)) {
    return name.charAt(0);
  }
  
  // 处理英文名字 - 取首字母
  const parts = name.split(/\s+/);
  if (parts.length === 1) {
    return name.charAt(0).toUpperCase();
  }
  
  // 如果有多个部分（如 John Doe），取每部分首字母
  return parts.map(part => part.charAt(0).toUpperCase()).join('');
}

// 添加查看版本的处理函数，使用props.openTemplateHistory打开版本历史页面
const handleViewVersion = (version) => {
  console.log('查看版本:', version);
  try {
    if (typeof props.openTemplateHistory === 'function') {
      // 调用打开模板历史的函数
      props.openTemplateHistory(version.id);
      
      // 调用成功后关闭当前对话框
      dialogVisible.value = false;
      
      // 触发关闭事件
      emit('close');
      
      // 如果有onClose回调，也调用它
      if (typeof props.onClose === 'function') {
        props.onClose();
      }
    } else {
      console.error('openTemplateHistory不是一个函数');
      ElMessage.error('查看版本失败：系统错误');
    }
  } catch (error) {
    console.error('调用openTemplateHistory时出错:', error);
    ElMessage.error('查看版本失败：' + (error.message || '未知错误'));
  }
}
</script>
<style>
/* 注意：这里移除了 scoped，让样式可以全局应用 */

/* 覆盖全局输入框样式 */
.template-history-dialog input[type=text],
.template-history-dialog .angle_input,
.template-history-dialog .el-input input,
.template-history-edit-dialog input[type=text],
.template-history-edit-dialog .angle_input,
.template-history-edit-dialog .el-input input,
.template-history-confirm-dialog input[type=text],
.template-history-confirm-dialog .angle_input,
.template-history-confirm-dialog .el-input input {
  height: auto !important;
  padding: initial !important;
  color: inherit !important;
  border: none !important;
  box-shadow: none !important;
  transition: none !important;
}


/* 移除表格所有边框 */
.el-table::before,
.el-table::after {
  display: none !important;
}

.el-table__inner-wrapper::before {
  display: none !important;
}

.el-table__border-left-patch {
  display: none !important;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border: none !important;
}

.el-table--border .el-table__inner-wrapper {
  border: none !important;
}

.el-table--border .el-table__cell {
  border: none !important;
}

.el-table__cell {
  border-bottom: none !important;
}

/* 移除表头底部边框 */
.el-table__header-wrapper {
  border-bottom: none !important;
}

/* 移除表格行之间的边框 */
.el-table__row {
  border-top: none !important;
  border-bottom: none !important;
}

/* 确保对话框 footer 不受全局 footer 样式影响 - 使用更高优先级选择器 */
.template-history-dialog .el-dialog__footer,
.template-history-edit-dialog .el-dialog__footer,
.template-history-confirm-dialog .el-dialog__footer {
  padding: 10px 20px 20px !important;
  text-align: right !important;
  box-sizing: border-box !important;
  background-color: #fff !important;
  border-top: none !important;
  position: static !important;
  width: auto !important;
  height: auto !important;
  z-index: auto !important;
  bottom: auto !important;
}

/* 确保按钮样式正确 */
.template-history-dialog .el-dialog__footer .el-button,
.template-history-edit-dialog .el-dialog__footer .el-button,
.template-history-confirm-dialog .el-dialog__footer .el-button {
  margin-left: 10px !important;
}

/* 保留原有样式 */
/* 详情文本样式 */
.details-text {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: inherit; /* 继承表格字体 */
  font-size: inherit; /* 继承表格字体大小 */
  line-height: 1.5; /* 适当的行高 */
}

/* 版本列表样式 */
.version-header {
  margin-bottom: 16px;
}

.last-edit-time {
  font-size: 14px;
  color: #606266;
}

.last-edit-time span {
  margin-left: 8px;
  font-weight: bold;
}

.version-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.version-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 16px;
  border-radius: 6px;
  /* 移除边框 */
  border: none;
  background-color: #FFFFFF;
  /* 添加底部边距，使项目之间有间隔 */
  margin-bottom: 8px;
}

/* 可以添加hover效果，提高可用性 */
.version-item:hover {
  background-color: #F5F7FA;
}

.version-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  gap: 4px;
  flex: 1;
}

.version-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-name {
  font-size: 14px;
  font-weight: bold;
  color: #303033;
  line-height: 1.2;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  line-height: 16px;
}

/* 草稿 - 灰色 */
.draft {
  background-color: #F4F4F5;
  color: #909399;
}

/* 审核中 - 蓝色 */
.pending-review {
  background-color: #E6F7FF;
  color: #1890FF;
  border: 1px solid #91D5FF;
}

/* 生效 - 绿色 */
.effective {
  background-color: #F0F9EB;
  color: #67C23A;
}

/* 驳回 - 灰色 */
.rejected {
  background-color: #F4F4F5;
  color: #909399;
}

/* 待生效 - 蓝色 */
.pending-effect {
  background-color: #E6F7FF;
  color: #1890FF;
  border: 1px solid #91D5FF;
}

/* 已取消 - 灰色 */
.canceled {
  background-color: #F4F4F5;
  color: #909399;
}

/* 已失效 - 灰色 */
.expired {
  background-color: #F4F4F5;
  color: #909399;
}

.version-meta {
  display: flex;
  align-items: center;
  gap: 12px; /* 增加间距，使布局更清晰 */
}

.version-date {
  font-size: 12px;
  color: #6A6A73;
  min-width: 100px; /* 使用最小宽度而不是固定宽度 */
  white-space: nowrap;
}

.version-creator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6A6A73;
  font-size: 14px;
  white-space: nowrap; /* 防止创建人信息换行 */
}

.version-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 版本操作按钮样式 */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  padding: 0;
  border-radius: 4px;
}

.view-button {
  color: #1388FF;
}

.view-button:hover {
  background-color: rgba(19, 136, 255, 0.1);
}

.more-button {
  color: #1388FF;
}

.more-button:hover {
  background-color: rgba(19, 136, 255, 0.1);
}

:deep(.el-icon) {
  font-size: 16px;
}

/* 版本状态标签样式 */
.version-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 8px;
}

.version-status.pending-effect {
  background-color: #E6F7FF;
  color: #1890FF;
}

.version-status.effective {
  background-color: #F6FFED;
  color: #52C41A;
}

.version-status.draft {
  background-color: #F5F5F5;
  color: #666666;
}

.version-status.rejected {
  background-color: #FFF1F0;
  color: #F5222D;
}

/* 头像样式 */
.el-avatar {
  background-color: #FF7733;
  color: #FFFFFF;
  font-size: 14px;
}

/* 历史记录表格样式 */
.history-table {
  --el-table-border-color: transparent;
  --el-table-border: none;
}

.history-table .el-table__inner-wrapper::before,
.history-table .el-table__inner-wrapper::after {
  background-color: transparent;
}

.history-table .el-table__cell {
  border-bottom-color: var(--el-border-color-lighter);
}

.date-picker-container {
  padding: 10px 0;
}

.date-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
