<?php
namespace frontend\services;

use frontend\core\CommonModel;
use frontend\core\CommonServer;
use frontend\interfaces\CenterInterface;
use frontend\interfaces\PMInterface;
use frontend\models\ApprovalModel;
use frontend\models\ApprovalNodeModel;
use frontend\models\BookModel;
use frontend\models\ElnCollaboration;
use frontend\models\ElnCollaborationAction;
use frontend\models\ElnExperimentModel;
use frontend\models\ExperimentCoauthorModel;
use frontend\models\ExperimentModel;
use frontend\models\HistoryModel;
use frontend\models\InstrumentRunningRecordModel;
use frontend\models\InstrumentRepairRecordModel;
use frontend\models\InstrumentCheckRecordModel;
use frontend\models\InstrumentsModel;
use frontend\models\Link;
use frontend\models\ShareAccessModel;
use frontend\models\ShareDetailLogModel;
use frontend\models\TemplateHistoryModel;
use frontend\models\TemplateHistoryNewModel;
use frontend\models\TemplateModel;
use frontend\models\TemplateActionLogModel;
use frontend\models\UseStaticExp;
use service\models\ineln\ExperimentRelayModel;
use yii;
use yii\base\Exception;
use yii\db\ActiveRecord;
use yii\db\Expression;
use yii\helpers\Curl;

/**
 * 审批
 */
class ApprovalServer extends CommonServer {

    const BATCH_UID_PREFIX = 'batch-';

    public $approvalType = [
        1 => 'sign',
        2 => 'reopen',
        3 => 'signing',
        4 => 'pretrial',
        5 => 'create_book',
        6 => 'template',
        7 => 'coauthor_sign',
        8 => 'module_reedit',
        9 => 'instrument_check', // add by hkk 2021/4/19
        10 => 'share_experiment',
        11 => 'update_book_name',   //修改记录本名称
        12 => 'change_book_group',
        13 => 'template_share',  //模板分享
        14 => 'work_order',  //工单
        15 => 'template_share_batch', // 模板批量分享
        16 => 'set_company_template', // 设为企业模板
        17 => 'publish_template', // 发布模板
    ];

    public $expApprovalType = [1, 2, 3, 4, 7, 8];

    /**
     * @Notes: 处理审批类型的合并
     * @param $type
     * @return array|mixed
     * @author: tianyang
     * @Time: 2023/3/10 13:52
     */
    public function dealApprovalType($type){
        switch ($type) {    //修改筛选类型
            case \Yii::$app->params['approval_type']['create_book']:    //如果是新建记录本，添加上修改记录本名称类型
                $type = [
                    \Yii::$app->params['approval_type']['create_book'],
                    \Yii::$app->params['approval_type']['update_book_name'],
                    \Yii::$app->params['approval_type']['change_book_group'],
                ];
                break;
            case \Yii::$app->params['approval_type']['template']:   //如果是模板审批，添加上记录本分享类型
                $type = [
                    \Yii::$app->params['approval_type']['template'],
                    \Yii::$app->params['approval_type']['template_share'],
                    \Yii::$app->params['approval_type']['template_share_batch'], // 批量分享模板
                    \Yii::$app->params['approval_type']['set_company_template'], // 设为企业模板
                    \Yii::$app->params['approval_type']['publish_template'], // 发布模板
                ];
                break;
        }
        return $type;
    }

    /**
     * @Notes: 统一处理各个审批的计数
     * @param $cntRes
     * @return array
     * @author: tianyang
     * @Time: 2023/3/10 13:31
     */
    public function dealApprovalCnt($cntRes){
        $cntRes = yii\helpers\ArrayHelper::map($cntRes, 'type', 'cnt');

        $total = 0;
        $cntArr = [];
        foreach ($this->approvalType as $key => $type) {
            $typeCnt = isset($cntRes[$key]) ? (int)$cntRes[$key] : 0;
            $cntArr[$type] = $typeCnt;
            $total += $typeCnt;
        }
        $cntArr['total'] = $total;

        $cntArr['create_book'] += isset($cntArr['update_book_name']) ? (int)$cntArr['update_book_name'] : 0;    //将修改记录本的类型计入新建记录本类型
        $cntArr['create_book'] += isset($cntArr['change_book_group']) ? (int)$cntArr['change_book_group'] : 0;  //将记录本换群计入记录本审批

        $cntArr['template'] += isset($cntArr['template_share']) ? (int)$cntArr['template_share'] : 0;   //将分享模板计入新建模板审批
        $cntArr['template'] += isset($cntArr['template_share_batch']) ? intval($cntArr['template_share_batch']) : 0; // 将批量模板分享计入新建模板审批
        $cntArr['template'] += isset($cntArr['set_company_template']) ? intval($cntArr['set_company_template']) : 0; // 将设为企业模板计入新建模板审批
        $cntArr['template'] += isset($cntArr['publish_template']) ? intval($cntArr['publish_template']) : 0; // 将发布模板计入新建模板审批
        return $cntArr;
    }

    /**
     * @Notes: 处理审批数据
     * @param $approvalList
     * @param $type
     * @param array $filter
     * @return array|mixed
     * @author: tianyang
     * @Time: 2023/3/13 13:58
     */
    public function dealApprovalViewData($approvalList, $type, $filter=[]) {

        switch ($type){
            case \Yii::$app->params['approval_type']['create_book']:
                $approvalList = $this -> dealApprovalViewForBook($approvalList);
                break;
            case \Yii::$app->params['approval_type']['instrument_check']:
                $approvalList = $this -> dealApprovalViewForInstrument($approvalList);
                break;
            case \Yii::$app->params['approval_type']['share']:
                $approvalList = $this -> dealApprovalViewForShare($approvalList);
                break;
            case \Yii::$app->params['approval_type']['work_order']:
                $approvalList = $this -> dealApprovalViewForWorkOrder($approvalList);
                break;
            default:
                $approvalList = $this -> dealApprovalViewForExp($approvalList);
                break;
        }

        return $approvalList;
    }

    /**
     * @Notes: 处理审批数据 - 对于实验
     * @param $approvalList
     * @return mixed
     * @author: tianyang
     * @Time: 2023/3/13 13:39
     */
    public function dealApprovalViewForExp($approvalList) {

        $experimentIds = array_column($approvalList, 'business_id');

        $experimentListRes = (new ExperimentServer())->listExpCatalogDataByIds($experimentIds);
        $experimentList = $experimentListRes['data'];
        $experimentList = yii\helpers\ArrayHelper::index($experimentList, 'id');

        foreach ($approvalList as $key => $item) {
            $approvalList[$key]['extra_data'] = json_decode($item['extra_data'], true);
            $approvalList[$key]['exp_data'] = isset($experimentList[$item['business_id']]) ? $experimentList[$item['business_id']] : null;
        }
        unset($item);

        return $approvalList;
    }

    /**
     * @Notes: 处理审批数据 - 对于记录本
     * @param $approvalList
     * @return array
     * @author: tianyang
     * @Time: 2023/3/13 13:40
     */
    public function dealApprovalViewForBook($approvalList) {
        $sBook = new BookServer();

        // 收集需要查询的数据
        $cntGroupIds = [];
        $cntUids = [];
        $book_proj = [];
        foreach ($approvalList as $key => $item) {
            $extraData = json_decode($item['extra_data'], true);

            switch ($item['type']) {    //根据类型获取群内记录本数量
                case \Yii::$app->params['approval_type']['change_book_group']:  //记录本换群
                    $cntGroupId = $extraData['from_group'];
                    $cntUid = $extraData['user_id'];
                    break;
                case \Yii::$app->params['approval_type']['update_book_name']:   //记录本改名
                    $cntGroupId = $extraData['group_id'];
                    $cntUid = $item['user_id'];
                    break;
                default:    //新建记录本
                    $cntGroupId = $extraData['group_id'];
                    $cntUid = $item['user_id'];
            }
            $cntGroupIds[] = $cntGroupId;
            $cntUids[] = $cntUid;

            if (!empty($extraData['project_id'])) {
                $book_proj[] = $extraData['project_id'];
            }
        }

        // 获取用户在鹰群内的记录本计数
        $allBookCntArr = [];
        if (!empty($cntGroupIds) && !empty($cntUids)) {
            $cntGroupIds = array_unique($cntGroupIds);
            $cntUids = array_unique($cntUids);
            $allBookCntRes = $sBook->getAllGroupBookCnt($cntGroupIds, $cntUids)['data'];
            $allBookCntArr = array_column($allBookCntRes, 'book_cnt', 'group_user_index');
        }

        // 获取项目信息
        $projectIdNameArr = [];
        if (!empty($book_proj)) {
            $book_proj = array_unique($book_proj);
            $projectRes = (new PMInterface())->getProjectsAndTasksByIds($book_proj, []);
            $projectList = $projectRes['projects'];
            $projectIdNameArr = array_column($projectList, 'name', 'id');
        }

        foreach ($approvalList as $key => $item) {
            $extraData = json_decode($item['extra_data'], true);

            switch ($item['type']) {    //根据类型获取群内记录本数量
                case \Yii::$app->params['approval_type']['change_book_group']:  //记录本换群
                    $cntGroupId = $extraData['from_group'];
                    $cntUid = $extraData['user_id'];
                    break;
                case \Yii::$app->params['approval_type']['update_book_name']:   //记录本改名
                    $cntGroupId = $extraData['group_id'];
                    $cntUid = $item['user_id'];
                    break;
                default:    //新建记录本
                    $cntGroupId = $extraData['group_id'];
                    $cntUid = $item['user_id'];
            }

            $groupUserIndex = 'group_id_' . $cntGroupId . '_user_id_' . $cntUid;
            $approvalList[$key]['book_cnt'] = @getVar($allBookCntArr[$groupUserIndex], 0);

            $approvalList[$key]['book_name'] = isset($extraData['book_name']) ? $extraData['book_name'] : '';
            $approvalList[$key]['book_code'] = isset($extraData['book_code']) ? $extraData['book_code'] : '';
            $approvalList[$key]['group_id'] = isset($extraData['group_id']) ? $extraData['group_id'] : 0;

            //记录本所属项目
            $projectId = isset($extraData['project_id']) ? $extraData['project_id'] : 0;
            $approvalList[$key]['project_name'] = !empty($projectIdNameArr[$projectId]) ? $projectIdNameArr[$projectId] : '';
        }

        return $approvalList;
    }

    /**
     * @Notes: 处理审批数据 - 对于仪器
     * @param $approvalList
     * @return array
     * @author: tianyang
     * @Time: 2023/3/13 13:40
     */
    public function dealApprovalViewForInstrument($approvalList) {
        // 获取仪器详情和记录数据
        $instrumentIds = [];
        $recordIds = [];
        foreach ($approvalList as $item) {
            $instrumentIds[] = json_decode($item['extra_data'],true)['instrument_id'];
            $recordIds[] = $item['business_id'];
        }

        $instrumentDetails = (new InstrumentServer()) ->getInstrumentDataByIds($instrumentIds);

        foreach ($approvalList as $key => $item) {
            $extraData = json_decode($item['extra_data'],true);
            if (empty($extraData['recordType'])) {
                //状态修改审批
                $approvalList[$key]['instrument_new_status'] = @getVar($extraData['instrument_status'], '1'); // 仪器修改后状态
                $approvalList[$key]['status_change_reason'] = @getVar($extraData['reason'], ''); // 仪器修改原因
                $approvalList[$key]['approvalFromBtn'] = @getVar($extraData['approvalFromBtn'], ''); // 仪器修改原因
            }else{
                switch ($extraData['recordType']) {
                    case 'repair_record':
                        $recordType = 1;
                        break;
                    case 'operate_record':
                        $recordType = 3;
                        break;
                    case 'check_record':
                        $recordType = 2;
                        break;
                }
                //运行，维保，校验记录
                $approvalList[$key]['record_id'] = $item['business_id']; // 记录详情
                $approvalList[$key]['record_type'] = $recordType; // 仪器分类
            }

            $approvalList[$key]['instrumentInfo'] = $instrumentDetails['data'][$instrumentIds[$key]]; // 仪器详情
        }

        return $approvalList;
    }

    /**
     * @Notes: 处理审批数据 - 对于分享
     * @param $approvalList
     * @return mixed
     * @author: tianyang
     * @Time: 2023/3/13 13:40
     */
    public function dealApprovalViewForShare($approvalList) {

        $experimentIds = array_column($approvalList, 'business_id');
        $experimentListRes = (new ExperimentServer())->listExpCatalogDataByIds($experimentIds);
        $experimentList = $experimentListRes['data'];
        $experimentList = yii\helpers\ArrayHelper::index($experimentList, 'id');

        foreach ($approvalList as $key => $item) {
            $extraData = json_decode($item['extra_data'], true);
            $approvalList[$key]['extra_data'] = $extraData;
            $approvalList[$key]['exp_arr'] = [];
            if (isset($extraData['exp_id_arr'])) {
                foreach ($extraData['exp_id_arr'] as $expId) {
                    $exp = (new ExperimentServer())->getExpBaseData($expId);
                    $approvalList[$key]['exp_arr'][] = $exp;
                }
            }

            $approvalList[$key]['exp_data'] = isset($experimentList[$item['business_id']]) ? $experimentList[$item['business_id']] : null;
        }

        return $approvalList;
    }

    /**
     * @Notes: 处理审批数据 - 对于工单
     * @param $approvalList
     * @return mixed
     * @author: zhouweiming
     * @Time: 2023/3/20 16:04
     */
    public function dealApprovalViewForWorkOrder($approvalList) {

        $workOrderIds = array_column($approvalList, 'business_id');
        $workOrderList = ElnCollaboration::find()->where(['id' => $workOrderIds])->asArray()->all();
        $workOrderList = yii\helpers\ArrayHelper::index($workOrderList, 'id');

        foreach ($approvalList as $key => $item) {
            $extraData = json_decode($item['extra_data'], true);
            $approvalList[$key]['extra_data'] = $extraData;
            $approvalList[$key]['wo_data'] = isset($workOrderList[$item['business_id']]) ? $workOrderList[$item['business_id']] : null;
        }

        return $approvalList;
    }

    /**
     * @Notes: 我提交的审批 计数
     * @param $userId
     * @return array
     * @author: tianyang
     * @Time: 2023/2/27 13:04
     */
    public function getMyApprovalCnt($userId) {
        $selectFields = ['approval.type', 'count(*) as cnt'];
        $query = ApprovalModel::find()->select($selectFields)->from(ApprovalModel::tableName() . ' AS approval');

        $query->where(['approval.user_id' => $userId]);

        $query->groupBy('approval.type');

        $cntRes = $query->asArray()->all();

        return $this->dealApprovalCnt($cntRes);
    }

    /**
     * Notes: 提交给我的审批
     * Author: zhu huajun
     * Date: 2019/9/30 15:34
     * @param $userId
     * @return array|yii\db\ActiveRecord[]
     */
    public function getApprovalCnt($userId) {
        $selectFields = ['approval.type', 'count(approval.id) as cnt'];
        $query = ApprovalModel::find()->select($selectFields)->from(ApprovalModel::tableName() . ' AS approval')
            ->innerJoin(ApprovalNodeModel::tableName() . ' AS an', 'an.approval_id = approval.id');

        $query->where([
            //'approval.approval_status' => null,
            'approval.status' => 1,
            'an.node_status' => 1
        ])->andWhere([
            'or',
            [
                'and',
                new yii\db\Expression('FIND_IN_SET(:userId, approver_ids)'),
                ['an.approval_status' => null]
            ],
            [
                'and',
                new yii\db\Expression('FIND_IN_SET(:userId, coapprover_ids)'),
                ['an.coapproval_status' => null]
            ],
        ])->andWhere([
            'or',
            ['approval.approval_status' => ApprovalModel::$approval_waiting],
            ['approval.approval_status' => ApprovalModel::$approval_ing]
        ]);

        $query->addParams([':userId' => $userId]);

        $query->groupBy('approval.type');

        $cntRes = $query->asArray()->all();

        return $this->dealApprovalCnt($cntRes);
    }

    /**
     * Notes: 我的历史审批
     * Author: zhu huajun
     * Date: 2019/9/30 15:34
     * @param $userId
     * @return array|yii\db\ActiveRecord[]
     */
    public function getHistoryApprovalCnt($userId) {
        $selectFields = ['approval.type', 'count(an.id) as cnt'];
        $query = ApprovalModel::find()->select($selectFields)->from(ApprovalModel::tableName() . ' AS approval')
            ->innerJoin(ApprovalNodeModel::tableName() . ' AS an', 'an.approval_id = approval.id');

        $query->where([
            'or',
            ['approver_id' => $userId],
            ['coapprover_id' => $userId]
        ]);

        $query->groupBy('approval.type');

        $cntRes = $query->asArray()->all();

        return $this->dealApprovalCnt($cntRes);
    }


    /**
     * @Notes: 我提交的审批 - 列表
     * @param $userId
     * @param $type
     * @param array $filter
     * @param int $page
     * @param $limit
     * @return array
     * @author: tianyang
     * @Time: 2023/2/27 13:05
     */
    public function listMyApprovalByType($userId, $type, $filter = [], $page = 0, $limit = null) {

        $filterBook = false;
        if (\Yii::$app->params['approval_type']['create_book'] == $type) {
            $filterBook = true;
        }
        $type = $this->dealApprovalType($type);

        $selectFields = ['approval.id approval_id','approval.type', 'approval.business_id', 'approval.extra_data', 'approval.user_id', 'approval.create_time',
                         'approval.approval_status as approval_result', 'approval.status approval_status'];
        $query = ApprovalModel::find()->select($selectFields)->from(ApprovalModel::tableName() . ' AS approval');

        $query->where(['approval.type' => $type]);
        $query->andwhere(['approval.user_id' => $userId]);

        // 筛选 start
        // 业务筛选
        if(!empty($filter['business_id'])) {
            $query->andWhere([
                'approval.business_id' => $filter['business_id']
            ]);
        }

        // 鹰群、项目、任务筛选（针对实验）
        if(!$filterBook && in_array($type, $this->expApprovalType) && (!empty($filter['group_ids']) || !empty($filter['project_ids']) || !empty($filter['task_ids']))) {
            $query->innerJoin(ExperimentModel::tableName() . ' exp', 'exp.id = approval.business_id')
                ->innerJoin(BookModel::tableName() . ' book', 'exp.book_id = book.id');

            if(!empty($filter['group_ids'])) {
                $query->andWhere([
                    'book.group_id' => $filter['group_ids']
                ]);
            }

            if(!empty($filter['project_ids'])) {
                $query->andWhere([
                    'exp.project_id' => $filter['project_ids']
                ]);
            }

            if(!empty($filter['task_ids'])) {
                $query->andWhere([
                    'exp.task_id' => $filter['task_ids']
                ]);
            }
        }

        // 针对仪器审批
        if($type == '9' && !empty($filter['group_ids'])){
            $query->innerJoin(InstrumentsModel::tableName() . ' ins', 'ins.id = approval.business_id');
            $FindStr = "";
            foreach ($filter['group_ids'] as $key => $groupId) { // 所有可见鹰群 -- ins.group
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", ins.groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            $FindStr .=  "FIND_IN_SET('all', ins.groupIds)";
            $query->andWhere(new Expression($FindStr));
        }

        // 针对记录本
        if($filterBook) {
            // 记录本所属鹰群筛选
            if (!empty($filter['group_ids'])) {
                $query->andWhere([
                    'approval.business_id' => $filter['group_ids']
                ]);
            }
        }

        // 按审批类别筛选 --- 操作类型
        if (!empty($filter['operation_type'])) {
            $query->andWhere([
                'approval.type' => $filter['operation_type'],
            ]);
        }

        //申请时间筛选(分享实验的审批)
        if (!empty($filter['start_time'])) {
            $query->andWhere([
                '>=', 'approval.create_time', date('Y-m-d H:i:s',strtotime($filter['start_time']))
            ]);
        }
        if (!empty($filter['end_time'])) {
            $query->andWhere([
                '<=', 'approval.create_time', date('Y-m-d H:i:s',strtotime('+1 day', strtotime($filter['end_time'])))
            ]);
        }

        /**
         * 审批状态筛选
         * '' 全部, 3 待审批(null), 2 审批中(2), 1 审批通过(1), -1 审批拒绝(0), -2 审批已取消
         * 括号内的是对于数据库状态为
         */
        if(!empty($filter['approval_status_filter'])) {
            $orStatusWhere = ['or'];
            foreach ($filter['approval_status_filter'] as $status_filter) {
                if ($status_filter == 3) {
                    // 没有过审批记录  && 审批未完结
                    $orStatusWhere[] = ['approval.status' => 1, 'approval.approval_status' => ApprovalModel::$approval_waiting];
                } elseif ($status_filter == 2) {
                    // 已经有过审批记录 && 审批未完结
                    $orStatusWhere[] = ['approval.status' => 1, 'approval.approval_status' => ApprovalModel::$approval_ing];
                } elseif ($status_filter == 1) {
                    $orStatusWhere[] = ['approval.status' => 1, 'approval.approval_status' => ApprovalModel::$approval_agree];
                } elseif ($status_filter == -1) {
                    $orStatusWhere[] = ['approval.status' => 1, 'approval.approval_status' => ApprovalModel::$approval_refuse];
                } elseif ($status_filter == -2) {
                    $orStatusWhere[] = ['approval.status' => 0];
                }
            }
            $query->andwhere($orStatusWhere);
        }
        // 筛选 end

        // 计数
        $total = $query->count();

        if (!empty($page) && !empty($limit)) {
            // 如果是模板审批就放到模板筛选的方法里分页，这里不分页 bug 1509 Plug--审核--下方的计数错误（实际应该18条数据），且无法翻页，需优化
            if (!self::isTemplateApprovalType($type)) {
                $query->limit($limit)->offset(($page - 1) * $limit);
            }
        }

        $list = $query->orderBy('approval.create_time DESC')->asArray()->all();

        // 如果是模板审批，需要再用里面的方法筛选一下
        if (self::isTemplateApprovalType($type)) {
            $approvalDetail = (new TempleServer())->getShareTemplateData($list, $filter, $page, $limit);
            $list = $approvalDetail['list'];
            $total = $approvalDetail['total'];
        }

        return [
            'total' => $total,
            'list' => $list
        ];
    }

    /**
     * Notes: 获取提交给我的待审批列表
     * Author: zhu huajun
     * Date: 2019/9/24 10:21
     * @param $userId
     * @param $type
     * @param $page
     * @param $limit
     */
    public function listApprovalByType($userId, $type, $filter = [], $page = 0, $limit = null) {
        $filterBook = false;
        if (\Yii::$app->params['approval_type']['create_book'] == $type) {
            $filterBook = true;
        }
        $type = $this->dealApprovalType($type);

        $selectFields = ['an.id', 'approval.id approval_id','approval.type', 'approval.business_id', 'approval.extra_data', 'approval.user_id', 'approval.create_time'];
        $query = ApprovalModel::find()->select($selectFields)->from(ApprovalModel::tableName() . ' AS approval')
            ->innerJoin(ApprovalNodeModel::tableName() . ' AS an', 'an.approval_id = approval.id');

        $query->where([
            'approval.type' => $type,
            //'approval.approval_status' => null,
            'approval.status' => 1,
            'an.node_status' => 1
        ])->andWhere([
            'or',
            [
                'and',
                new yii\db\Expression('FIND_IN_SET(:userId, approver_ids)'),
                ['an.approval_status' => null]
            ],
            [
                'and',
                new yii\db\Expression('FIND_IN_SET(:userId, coapprover_ids)'),
                ['an.coapproval_status' => null]
            ],
        ])->andWhere([
            'or',
            ['approval.approval_status' => ApprovalModel::$approval_waiting],
            ['approval.approval_status' => ApprovalModel::$approval_ing]
        ]);

        // 业务筛选
        if(!empty($filter['business_id'])) {
            $query->andWhere([
                'approval.business_id' => $filter['business_id']
            ]);
        }

        // 申请人筛选
        if(!empty($filter['user_ids'])) {
            $query->andWhere([
                'approval.user_id' => $filter['user_ids']
            ]);
        }

        // 鹰群、项目、任务筛选（针对实验）
        if(in_array($type, $this->expApprovalType) && (!empty($filter['group_ids']) || !empty($filter['project_ids']) || !empty($filter['task_ids']))) {
            $query->innerJoin(ExperimentModel::tableName() . ' exp', 'exp.id = approval.business_id')
                ->innerJoin(BookModel::tableName() . ' book', 'exp.book_id = book.id');

            if(!empty($filter['group_ids'])) {
                $query->andWhere([
                    'book.group_id' => $filter['group_ids']
                ]);
            }

            if(!empty($filter['project_ids'])) {
                $query->andWhere([
                    'exp.project_id' => $filter['project_ids']
                ]);
            }

            if(!empty($filter['task_ids'])) {
                $query->andWhere([
                    'exp.task_id' => $filter['task_ids']
                ]);
            }
        }

        // 针对记录本
        if($filterBook) {
            // 记录本所属鹰群筛选
            if (!empty($filter['group_ids'])) {
                $query->andWhere([
                    'approval.business_id' => $filter['group_ids']
                ]);
            }
        }

        //申请时间筛选(分享实验的审批)
        if (!empty($filter['start_time'])) {
            $query->andWhere([
                '>=', 'approval.create_time', date('Y-m-d H:i:s',strtotime($filter['start_time']))
            ]);
        }
        if (!empty($filter['end_time'])) {
            $query->andWhere([
                '<=', 'approval.create_time', date('Y-m-d H:i:s',strtotime('+1 day', strtotime($filter['end_time'])))
            ]);
        }

        //按审批类别筛选
        if (!empty($filter['operation_type'])) {
            $query->andWhere([
                'approval.type' => $filter['operation_type'],
            ]);
        }

        //工单筛选
        if ($type == Yii::$app->params['approval_type']['work_order']) {
            $query->innerJoin(ElnCollaboration::tableName() . ' AS ec', 'ec.id = approval.business_id');

            //按工单关键字筛选
            if (!empty($filter['approval_keyword_filter'])) {
                $query->andWhere([
                    'or',
                    ['=', 'ec.id', str_replace('WO', '', $filter['approval_keyword_filter'])],
                    ['like', 'ec.exp_title', $filter['approval_keyword_filter']],
                ]);
            }

        }

        $query->addParams([':userId' => $userId]);

        $total = $query->count();

        if (!empty($page) && !empty($limit)) {
            // 如果是模板审批就放到模板筛选的方法里分页，这里不分页 bug 1509 Plug--审核--下方的计数错误（实际应该18条数据），且无法翻页，需优化
            if (!self::isTemplateApprovalType($type)) {
                $query->limit($limit)->offset(($page - 1) * $limit);
            }
        }

        $list = $query->asArray()->all();

        // 如果是模板审批，需要再用里面的方法筛选一下
        if (self::isTemplateApprovalType($type)) {
            $approvalDetail = (new TempleServer())->getShareTemplateData($list, $filter, $page, $limit);
            $list = $approvalDetail['list'];
            $total = $approvalDetail['total'];
        }

        return [
            'total' => $total,
            'list' => $list
        ];
    }

    /**
     * 判断审批类型是否是审批相关的类型
     * @param int|int[] $typeOrTypeList
     * @return bool
     */
    public static function isTemplateApprovalType($typeOrTypeList) {
        $templateTypeList = [
            \Yii::$app->params['approval_type']['template'],
            \Yii::$app->params['approval_type']['template_share'],
            \Yii::$app->params['approval_type']['template_share_batch'],
            \Yii::$app->params['approval_type']['set_company_template'], // 设为企业模板
            \Yii::$app->params['approval_type']['publish_template'], // 发布模板
        ];
        if (is_array($typeOrTypeList)) {
            $intersectTypes = array_intersect($typeOrTypeList, $templateTypeList);
            return count($intersectTypes) > 0;
        } elseif (is_string($typeOrTypeList)) {
            return in_array($typeOrTypeList, $templateTypeList);
        } else {
            return false;
        }
    }

    public function listHistoryApprovalByType($userId, $type, $filter, $page, $limit) {
        $filterBook = false;
        if (\Yii::$app->params['approval_type']['create_book'] == $type) {
            $filterBook = true;
        }
        $type = $this->dealApprovalType($type);

        $selectFields = [
            'an.id', 'approval.type', 'approval.business_id', 'approval.extra_data', 'approval.user_id', 'approval.create_time','approval.id as approval_id', 'approval.approval_status as approval_result', 'approval.status approval_status',
            'if(approver_id=' . $userId . ', an.approval_status, an.coapproval_status) as result_status',
            'if(approver_id=' . $userId . ', an.approval_remark, an.coapproval_remark) as result_remark',
            'if(approver_id=' . $userId . ', an.approval_time, an.coapproval_time) as action_time'
        ];

        $query = ApprovalModel::find()->select($selectFields)->from(ApprovalModel::tableName() . ' AS approval')
            ->innerJoin(ApprovalNodeModel::tableName() . ' AS an', 'an.approval_id = approval.id');

        $query->where([
            'approval.type' => $type
        ])->andWhere([
            'or',
            ['approver_id' => $userId],
            ['coapprover_id' => $userId]
        ]);

        // 复合关闭按提交时间排序
        if (\Yii::$app->params['approval_type']['sign'] == $type) {
            $query->orderBy('an.id desc'); // , approval.create_time asc
        } else {
            $query->orderBy('an.update_time DESC');
        }

        // 申请人筛选
        if(!empty($filter['user_ids'])) {
            $query->andWhere([
                'approval.user_id' => $filter['user_ids']
            ]);
        }

        // 鹰群、项目、任务筛选（针对实验）
        if(in_array($type, $this->expApprovalType) && (!empty($filter['group_ids']) || !empty($filter['project_ids']) || !empty($filter['task_ids']))) {
            $query->innerJoin(ExperimentModel::tableName() . ' exp', 'exp.id = approval.business_id')
                ->innerJoin(BookModel::tableName() . ' book', 'exp.book_id = book.id');

            if(!empty($filter['group_ids'])) {
                $query->andWhere([
                    'book.group_id' => $filter['group_ids']
                ]);
            }

            if(!empty($filter['project_ids'])) {
                $query->andWhere([
                    'exp.project_id' => $filter['project_ids']
                ]);
            }

            if(!empty($filter['task_ids'])) {
                $query->andWhere([
                    'exp.task_id' => $filter['task_ids']
                ]);
            }
        }

        // 针对记录本
        if($filterBook) {
            // 记录本所属鹰群筛选
            if (!empty($filter['group_ids'])) {
                $query->andWhere([
                    'approval.business_id' => $filter['group_ids']
                ]);
            }
        }

        //申请时间筛选(分享实验的审批)
        if (!empty($filter['start_time'])) {
            $query->andWhere([
                '>=', 'approval.create_time', date('Y-m-d H:i:s',strtotime($filter['start_time']))
            ]);
        }
        if (!empty($filter['end_time'])) {
            $query->andWhere([
                '<=', 'approval.create_time', date('Y-m-d H:i:s',strtotime('+1 day', strtotime($filter['end_time'])))
            ]);
        }
        //按审批类别筛选
        if (!empty($filter['operation_type'])) {
            $query->andWhere([
                'approval.type' => $filter['operation_type'],
            ]);
        }

        /**
         * 审批状态筛选
         * '' 全部, 3 待审批(null), 2 审批中(2), 1 审批通过(1), -1 审批拒绝(0), -2 审批已取消
         * 括号内的是对于数据库状态为
         */
        if(!empty($filter['approval_status_filter'])) {
            $orStatusWhere = ['or'];
            foreach ($filter['approval_status_filter'] as $status_filter) {
                if ($status_filter == 1) {
                    $orStatusWhere[] = ['if(approver_id=' . $userId . ', an.approval_status, an.coapproval_status)' => ApprovalModel::$approval_agree];
                } elseif ($status_filter == -1) {
                    $orStatusWhere[] = ['if(approver_id=' . $userId . ', an.approval_status, an.coapproval_status)' => ApprovalModel::$approval_refuse];
                }
            }
            $query->andwhere($orStatusWhere);
        }

        //工单筛选
        if ($type == Yii::$app->params['approval_type']['work_order']) {
            $query->innerJoin(ElnCollaboration::tableName() . ' AS ec', 'ec.id = approval.business_id');

            //按工单关键字筛选
            if (!empty($filter['approval_keyword_filter'])) {
                $query->andWhere([
                        'or',
                        ['=', 'ec.id', str_replace('WO', '', $filter['approval_keyword_filter'])],
                        ['like', 'ec.exp_title', $filter['approval_keyword_filter']],
                ]);
            }

        }

        $total = $query->count();
        if (!empty($page) && !empty($limit)) {
            // 如果是模板审批就放到模板筛选的方法里分页，这里不分页 bug 1509 Plug--审核--下方的计数错误（实际应该18条数据），且无法翻页，需优化
            if (!self::isTemplateApprovalType($type)) {
                $query->limit($limit)->offset(($page - 1) * $limit);
            }
        }

        $list = $query->asArray()->all();

        // 如果是模板审批，需要再用里面的方法筛选一下
        if (self::isTemplateApprovalType($type)) {
            $approvalDetail = (new TempleServer())->getShareTemplateData($list, $filter, $page, $limit);
            $list = $approvalDetail['list'];
            $total = $approvalDetail['total'];
        }
        return [
            'total' => $total,
            'list' => $list
        ];
    }

    public function getApprovalByBusiness($businessType, $businessId, $userId = null) {
        $query = ApprovalModel::find()
            ->select('an.id as node_id')
            ->from(ApprovalModel::tableName() . ' AS a')
            ->innerJoin(ApprovalNodeModel::tableName() . ' AS an', 'an.approval_id = a.id')->where([
                'a.type' => $businessType,
                'a.business_id' => $businessId,
                //'a.approval_status' => null,
                'a.status' => 1,
                'an.node_status' => 1,
                'an.status' => 1,
            ])->andWhere([
                'or',
                ['a.approval_status' => ApprovalModel::$approval_waiting],
                ['a.approval_status' => ApprovalModel::$approval_ing]
            ]);

        if ($userId) {
            $query->andWhere([
                'or',
                [
                    'and',
                    new yii\db\Expression('FIND_IN_SET(:userId, approver_ids)'),
                    ['an.approval_status' => null]
                ],
                [
                    'and',
                    new yii\db\Expression('FIND_IN_SET(:userId, coapprover_ids)'),
                    ['an.coapproval_status' => null]
                ],
            ]);
            $query->addParams([':userId' => $userId]);
        }

        $nodeList = $query->asArray()->all();

        $nodeIds = [];
        if (!empty($nodeList)) {
            $nodeIds = array_column($nodeList, 'node_id');
        }

        return $this->success([
            'node_ids' => $nodeIds
        ]);
    }

    /**
     * Notes: 提交签字
     * Author: zhu huajun
     * Date: 2019/9/23 15:49
     * @param $expId
     * @param $moduleArr
     * @param $nodeArr
     * @param $reason
     * @return array
     * @throws \frontend\models\Exception
     */
    public function submitSigning($expId, $moduleArr, $nodeArr=[], $reason = '') {
        // 获取实验的基础信息
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        if (empty($expBaseData)) {
            return $this->fail('');
        }

        // 获取签字设置
        $signingSetting = CompanyAuthServer::getSigningSetting($expBaseData['user_id'], $expBaseData['group_id']);

        // 获取群审核设置，判断是否发送提醒邮件
        $generalSettingRes = (new GroupSettingServer())->getGeneralSetting($expBaseData['group_id']);
        $generalSetting = $generalSettingRes['data'];
        $sendRemindEmail = 0; // 标记是否发送提醒邮件
        if (!empty($generalSetting['approval_setting'])) {
            $approvalSetting = json_decode($generalSetting['approval_setting'], true);
            if (!empty($approvalSetting['signing']['send_remind_email'])) {
                $sendRemindEmail = 1;
            }
        }

        if (empty($nodeArr)) {
            return $this->fail(\Yii::t('exp', 'select_signing_users'));
        }

        $createApprovalRes = $this->createApproval(\Yii::$app->params['approval_type']['signing'], $expId, $expBaseData['group_id'], json_encode([
            'module_arr' => $moduleArr,
            'send_email' => !empty($sendRemindEmail) ? 1 : 0,
            'reason' => $reason
        ]), $nodeArr, @getVar($signingSetting['approval_nodes_source'], []));
        if (empty($createApprovalRes['status'])) {
            return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
        }

        // 设置模块状态为签字待审核
        ExperimentRelayModel::updateAll([
            'module_status' => 2
        ], [
            'id' => $moduleArr
        ]);

        // 生成一条痕迹记录
        $moduleRes = (new ExperimentServer())->getModuleNames($moduleArr);
        $moduleData = @getVar($moduleRes['data'], []);
        $moduleNames = array_column($moduleData, 'name');

        $approverNames = @getVar($createApprovalRes['data']['approver_names'], []);

        $extraData = [
            'module_ids' => $moduleArr,
            'module_names' => $moduleNames,
            'approver_names' => $approverNames,
        ];
        (new HistoryModel())->addHistoryAllData($expId, \Yii::$app->view->params['curr_user_id'], 'signing', $reason, FALSE, $extraData);

        return $this->success([]);
    }

    /**
     * Notes: 创建一条审批
     * 失败时返回审批提示(联系群主 + 审批流程显示)
     * 成功时返回审批数据(审批流 id + 审批流程显示 (str + array) )
     * Author: zhu huajun
     * Date: 2019/9/23 15:54
     * @param $type
     * @param $businessId
     * @param int $groupId
     * @param $extraData string
     * @param $nodes array
     * @param array $approvalNodesSource
     * @return array
     */
    function createApproval($type, $businessId, $groupId = 0, $extraData, $nodes, $approvalNodesSource = []) {
        // 业务所属鹰群
        $businessGroupId = 0;
        if (!empty($groupId)) {
            $businessGroupId = $groupId;
        } else {
            // 根据业务的类型获取所属鹰群
            if ($type == \Yii::$app->params['approval_type']['share']) { // 实验分享
                $businessData = json_decode($extraData, true);
                if (!empty($businessData['exp_id_arr'])) {
                    // 通过第1个实验ID获取实验所属鹰群ID
                    $expBaseData = (new ExperimentServer())->getExpBaseData($businessData['exp_id_arr'][0]);
                    $businessGroupId = @getVar($expBaseData['group_id'], 0);
                }
            }
        }

        $approvalRouteRes = $this->getApprovalRoute($nodes, $approvalNodesSource, $businessGroupId);
        $approvalRouteResData = $approvalRouteRes['data'];
        if (empty($approvalRouteRes['status'])) {
            return $this->fail($approvalRouteRes['info']);
        }
        // 使用 checkApproval 过滤后的审批流程节点 - 更新 $nodes
        $nodes = $approvalRouteResData['approval_nodes'];

        // 读取记录的提交人的id, 合著审批完结 -> 启动复核审批时的审批流程创建者应该是提交复核的人
        // 33993 plug：有合著签名的情况，合著通过之后，我的-审核/提交的审核-复核关闭实验未显示实验
        $extraDataArr = json_decode($extraData, true);
        $submit_user_id = \Yii::$app->view->params['curr_user_id'];
        if (@getVar($extraDataArr['submit_user_id'], 0)) {
            $submit_user_id = $extraDataArr['submit_user_id'];
        }

        $approval = new ApprovalModel();
        $approval->setAttributes([
            'type' => $type,
            'business_id' => $businessId,
            'group_id' => $groupId,
            'extra_data' => $extraData,
            'user_id' => $submit_user_id
        ]);
        $approval->save();
        $approvalId = $approval['id'];

        $prevNodeId = 0;
        foreach($nodes as $node) {
            $nodeModel = new ApprovalNodeModel();
            $nodeModel['approval_id'] = $approvalId;
            if(isset($node['approval_user_ids'])){
                if(is_array($node['approval_user_ids']) && !empty($node['approval_user_ids'])) {
                    $nodeModel['approver_ids'] = implode(',',  $node['approval_user_ids']);
                }
            }
            if(isset($node['coapproval_user_ids'])){
                if(is_array($node['coapproval_user_ids']) && !empty($node['coapproval_user_ids'])) {
                    $nodeModel['coapprover_ids'] = implode(',', $node['coapproval_user_ids']);
                }
            }
            if($prevNodeId === 0) {
                $nodeModel['node_status'] = 1;
            }
            $nodeModel->save();
            $nodeId = $nodeModel['id'];

            if($prevNodeId != 0) {
                ApprovalNodeModel::updateAll([
                    'next_node_id' => $nodeId
                ], [
                    'id' => $prevNodeId
                ]);
            }
            $prevNodeId = $nodeId;
        }

        $this->sendApprovalRemindMsg($approvalId);


        // 记录痕迹
        if ($type == \Yii::$app->params['approval_type']['template']) {
            (new TempleServer())->addActionLog($businessId, TemplateActionLogModel::SUBMIT_AUDIT);
        }

        return $this->success([
            'approval_id' => $approvalId,
            'approver_names' => $approvalRouteResData['approver_names'],
            'approval_route' => $approvalRouteResData['approval_route'],
        ]);
    }

    /**
     * Notes: 取消业务审批
     *
     * Author: zhu huajun
     * Date: 2019/10/29 16:20
     * @param $type
     * @param $businessId
     * @return array
     */
    public function cancelApproval($type, $businessId, $instrumentId = 0, $recordType = '')
    {
        $approval = null;
        if ($type == 9) { //仪器记录三合一导致business_id可能重复，需要特殊处理
            $approvalLists = ApprovalModel::find()->where([
                'type' => $type,
                'business_id' => $businessId,
                'status' => 1
            ])->andWhere([
                'or',
                ['approval_status' => ApprovalModel::$approval_waiting],
                ['approval_status' => ApprovalModel::$approval_ing]
            ])->asArray()->all();
            if (count($approvalLists)) {
                $approvalId = [];
                foreach ($approvalLists as $approvalList) {
                    $extraData = json_decode($approvalList['extra_data'],true);
                    if (($instrumentId == $extraData['instrument_id']) && ($recordType == $extraData['recordType'])) {
                        $approvalId[] = $approvalList['id'];
                    }
                }
                $approval = ApprovalModel::find()->where(['id' => $approvalId])->one();
            }
        } else {
            $approval = ApprovalModel::find()->where([
                'type' => $type,
                'business_id' => $businessId,
                'status' => 1
            ])->andWhere([
                'or',
                ['approval_status' => ApprovalModel::$approval_waiting],
                ['approval_status' => ApprovalModel::$approval_ing]
            ])->one();
        }

        if (!$approval) {
            return $this->success([]);
        }

        $approval['status'] = 0;
        $approval->save();

        ApprovalNodeModel::updateAll([
            'status' => 0
        ], [
            'approval_id' => $approval['id']
        ]);

        if($type == \Yii::$app->params['approval_type']['template']){
            (new TempleServer())->addActionLog($businessId, TemplateActionLogModel::CANCEL_AUDIT);
        }

        return $this->success(['approval' => $approval]);
    }


    /**
     * Notes: 取消模块重新编辑的审核
     *
     * Author: zhhj
     * Date: 2021/4/20
     * @param $expId
     * @param array $moduleIds
     * @return array
     */
    public function cancelReeditApproval($expId, $moduleIds = []) {
        if (!is_array($moduleIds)) {
            $moduleIds = [$moduleIds];
        }

        $approvalList = ApprovalModel::find()->where([
            'type' => \Yii::$app->params['approval_type']['module_reedit'],
            'business_id' => $expId,
            //'approval_status' => null,
            'status' => 1
        ])->andWhere([
            'or',
            ['approval_status' => ApprovalModel::$approval_waiting],
            ['approval_status' => ApprovalModel::$approval_ing]
        ])->asArray()->all();
        if (empty($approvalList)) {
            return $this->success([]);
        }

        foreach ($approvalList as $approval) {
            $extraData = json_decode($approval['extra_data'], true);
            $moduleArr = @getVar($extraData['module_arr'], []);

            $leftModuleArr = array_diff($moduleArr, $moduleIds);
            if (empty($leftModuleArr)) {
                ApprovalModel::updateAll([
                    'status' => 0
                ], [
                    'id' => $approval['id']
                ]);
                ApprovalNodeModel::updateAll([
                    'status' => 0
                ], [
                    'approval_id' => $approval['id']
                ]);
            } else {
                $extraData['module_arr'] = $leftModuleArr;
                ApprovalModel::updateAll([
                    'extra_data' => json_encode($extraData)
                ], [
                    'id' => $approval['id']
                ]);
            }
        }

        return $this->success([]);
    }

    /**
     * Notes: 审批通过
     *
     * Author: zhu huajun
     * Date: 2019/9/24 20:10
     * @param $userInfo
     * @param $nodeIds
     * @param $remark
     * @return array
     */
    public function agree($userInfo, $nodeIds, $remark) {
        $userId = $userInfo->id;
        $userName = $userInfo->real_name? : $userInfo->name;
        if(!is_array($nodeIds)) {
            $nodeIds = [$nodeIds];
        }

        $noNeedDealNode = [] ; // // add by hkk 2022/8/9  记录已失效或已被复核的状态，(审核页面未刷新导致)
        foreach($nodeIds as $nodeId) {
            $node = ApprovalNodeModel::findOne($nodeId);
            if ($node['status'] == '0') { // 选中状态都被更改，就提示
                $noNeedDealNode[] = $nodeId;
            }
            if (!$node || $node['status'] != 1 || $node['node_status'] != 1) {
                continue;
            }

            $approverIds = !empty($node['approver_ids']) ? explode(',', $node['approver_ids']) : [];
            $coapproverIds = !empty($node['coapprover_ids']) ? explode(',', $node['coapprover_ids']) : [];

            // 用户为审批人
            if(!empty($approverIds) && in_array($userId, $approverIds)) {
                $node['approver_id'] = $userId;
                $node['approval_status'] = 1;
                $node['approval_remark'] = $remark;
                $node['approval_time'] = date('Y-m-d H:i:s');

                // 无需合批或者合批人已经通过
                if(empty($coapproverIds) || $node['coapproval_status'] !== null) {
                    $node['node_status'] = 2;
                }
            }

            // 用户为合批人
            if(!empty($coapproverIds) && in_array($userId, $coapproverIds)) {
                $node['coapprover_id'] = $userId;
                $node['coapproval_status'] = 1;
                $node['coapproval_remark'] = $remark;
                $node['coapproval_time'] = date('Y-m-d H:i:s');

                // 无需合批或者合批人已经通过
                if(empty($approverIds) || $node['approval_status'] !== null) {
                    $node['node_status'] = 2;
                }
            }

            $node->save();

            $approval = ApprovalModel::findOne($node['approval_id']);
            if ($node['node_status'] == 2) { // 当前节点审批完成
                $nextNode = ApprovalNodeModel::findOne($node['next_node_id']);
                if (!$nextNode) {// 没有下一个节点
                    // 审批结束
                    $approval['approval_status'] = ApprovalModel::$approval_agree;
                    $approval->save();

                    //审核后工单需要把状态从审核中改为正常
                    if($approval['type'] == \Yii::$app->params['approval_type']['work_order'] ){
                        $extraData = json_decode($approval['extra_data'], true);
                        $woData = ElnCollaboration::findOne($extraData['wo_id']);
                        $elnMessageServer = new ElnMessageServer();
                        //0创建，1确认，2解决，3关闭
                        switch ($extraData['wo_type']){
                            case 0:
                                $woData->setAttributes([
                                    'status' => 'active',
                                    'approval_status' => 'Approved',
                                    'exp_code' => @getVar($extraData['data']['exp_code']),
                                ]);

                                if ($woData['assigned_to_uid']) {
                                    $createDetail     = (new CenterInterface())->getUserByUserId($woData['user_id']);
                                    $createUserName   = CommonServer::displayUserName($createDetail);
                                    $msgTitle = \Yii::t('msg', 'collaboration_message_title');
                                    $msgContent = \Yii::t('msg', 'collaboration_content_for_create_assigned', [
                                        $createUserName,
                                        ELN_URL . '?route=collaboration&collaboration_id=' . $extraData['wo_id'],
                                        $extraData['wo_id'],
                                        $createUserName,
                                        date('Y-m-d', strtotime($woData['opened_time'])),
                                    ]);

                                    $elnMessageServer->addMessage($woData['user_id'], $woData['assigned_to_uid'], $msgTitle, $msgContent, 20, 0);
                                    $elnMessageServer->addMail($woData['user_id'], $woData['assigned_to_uid'], $woData['cc_to_uid'], $msgTitle, $msgContent,1,  \Yii::$app->language);
                                }
                                break;
                            case 1:
                                $woData->setAttributes([
                                    'status' => 'active',
                                    'approval_status' => 'Approved',
                                    'update_time' => date('Y-m-d H:i:s'),
                                    'confirmed' => 1,
                                    'planned_completion_time' => $extraData['data']['planned_completion_time']]);
                                break;
                            case 2:
                                $newExpCode = @getVar($extraData['data']['exp_code'])?@getVar($extraData['data']['exp_code']):@getVar($extraData['data']['expcode_str']);
                                $postExpCodeArr = array_filter(explode(' ', $newExpCode), function ($v) {
                                    return !empty($v) && $v !== ' ';
                                }); //用空格分割用户输入的实验页码列表
                                $oldExpCodeArr = array_filter(explode(' ', $woData['exp_code']), function ($item) {
                                    return !empty($item) && $item !== ' ';
                                });
                                $newExpCodeArr = array_unique(array_merge($oldExpCodeArr, $postExpCodeArr));

                                $newExpCodeStr = join(' ', $newExpCodeArr);
                                $woData -> setAttributes([
                                    'status' => 'resolved',
                                    'approval_status' => 'Approved',
                                    'resolved_time' => date('Y-m-d H:i:s'),
                                    'resolved_by_uid' => $approval['user_id'],
                                    'resolution' => $extraData['data']['resolution'],
                                    'exp_code' => $newExpCodeStr,
                                ]);
                                break;
                            case 3:
                                $woData -> setAttributes([
                                    'status' => 'closed',
                                    'approval_status' => 'Approved',
                                    'closed_by_uid' => $approval['user_id'],
                                    'closed_time' => date('Y-m-d H:i:s')]);
                                break;
                            case 4:
                                $woData->setAttributes([
                                    'status' => 'active',
                                    'approval_status' => 'Approved',
                                    'activated_by_uid' => $approval['user_id'],
                                    'activated_time' => date('Y-m-d H:i:s')
                                ]);
                                if ($woData['assigned_to_uid']) {
                                    $createDetail = (new CenterInterface())->getUserByUserId($woData['user_id']);
                                    $createUserName = CommonServer::displayUserName($createDetail);
                                    $msgTitle = \Yii::t('msg', 'collaboration_message_title');
                                    $msgContent = \Yii::t('msg', 'collaboration_content_for_active_assigned', [
                                        $createUserName,
                                        ELN_URL . '?route=collaboration&collaboration_id=' . $extraData['wo_id'],
                                        $extraData['wo_id']
                                    ]);

                                    $elnMessageServer->addMessage($woData['user_id'], $woData['assigned_to_uid'], $msgTitle, $msgContent, 20, 0);
                                    $elnMessageServer->addMail($woData['user_id'], $woData['assigned_to_uid'], '', $msgTitle, $msgContent, 1, \Yii::$app->language);

                                }
                                break;
                            default:
                                break;
                        }
                        $woData->save();
                        $link = new Link();
                        $collaborationLinkData = [];
                        $linkData = $link->getLinks(2, $extraData['wo_id']);
                        $collaborationLinkData[] = [
                            'collaboration_id' => $extraData['wo_id'],
                            'sample_links' => $linkData,
                        ];

                        Curl::sendPostCurl(ELN_URL . '?r=async-task/after-collaboration-save', [
                            'collaboration_link_data' => $collaborationLinkData,
                        ], true);
                    }

                    // 处理审批结束后的逻辑
                    $this->afterApprove($approval, $userId);
                } else {
                    // 标记有过审批, 审批进行中
                    $approval['approval_status'] = ApprovalModel::$approval_ing;
                    $approval->save();
                    // 进入下一个节点
                    $nextNode['node_status'] = 1;
                    $nextNode->save();
                }
            }

            $this->sendApprovalResultMsg($node, $userId, 1);

            // 针对实验的审批
            $type = (int)$approval['type'];
            if(in_array($type, $this->expApprovalType)) {
                $action = 'approved';
                $hisExtraData = [];
                switch($type) {
                    case \Yii::$app->params['approval_type']['sign']://复核
                        $action = 'countersigned';
                        break;
                    case \Yii::$app->params['approval_type']['coauthor_sign']://合著签字
                    case \Yii::$app->params['approval_type']['signing']://签字
                    case \Yii::$app->params['approval_type']['module_reedit']://重新编辑
                        if ($type == \Yii::$app->params['approval_type']['coauthor_sign']) {
                            $action = 'coauthor_sign_approved';
                        } else if ($type == \Yii::$app->params['approval_type']['signing']) {
                            $action = 'signing_approved';
                        } else if ($type == \Yii::$app->params['approval_type']['module_reedit']) {
                            $action = 'module_reedit_approved';
                        }
                        $extraData = json_decode($approval['extra_data'], true);
                        $moduleArr = @getVar($extraData['module_arr'], []);
                        $moduleRes = (new ExperimentServer())->getModuleNames($moduleArr);
                        $moduleData = @getVar($moduleRes['data'], []);
                        $moduleNames = array_column($moduleData, 'name');
                        $hisExtraData = [
                            'module_ids' => $moduleArr,
                            'module_names' => $moduleNames,
                        ];
                    break;
                    case \Yii::$app->params['approval_type']['reopen']://重开
                        $action = 'reopen_approved';
                        break;
                    case \Yii::$app->params['approval_type']['pretrial']://预审
                        $action = 'pretrial_approved';
                        break;
                        case \Yii::$app->params['approval_type']['share']://分享

                        break;
                }
                // 生成一条痕迹记录
                (new HistoryModel())->addHistoryAllData($approval['business_id'], \Yii::$app->view->params['curr_user_id'], $action, '', FALSE, $hisExtraData);
            } else if ($type == \Yii::$app->params['approval_type']['template']) {
                //(new TempleServer())->addHistory($approval['business_id'], TemplateHistoryModel::TYPE_AUDIT_AGREE);
                (new TempleServer())->addActionLog($approval['business_id'], TemplateActionLogModel::AUDIT_AGREE);
            } else if ($type == \Yii::$app->params['approval_type']['share']) {
                $extraData = json_decode($approval['extra_data'], true);
                (new ShareServer())->addShareDetailLog([
                    'exp_id_arr' => $extraData['exp_id_arr'],
                    'module_id_arr' => $extraData['module_id_arr'],
                    'applicant_id' => $approval->user_id,
                    'operator_id' => $userId,
                    'type' => $approval['approval_status'] == 1 ? ShareDetailLogModel::COMPLETELY_PASS : ShareDetailLogModel::PASS, //取消分享
                    'user_id_arr' => $extraData['user_id_arr'],
                    'group_id_arr' => $extraData['group_id_arr'],
                    'expire_date' => $extraData['expire_date']
                ]);
            } else if ($type == \Yii::$app->params['approval_type']['publish_template']) {                
                (new TempleServer())->addActionLog(null, TemplateActionLogModel::PUBLISH_AUDIT_AGREE, $userId, null, null, null, null, $approval['business_id']);
            }

            // bug 4098 PLug :intable- 工单审批，设置合批，都审核通过后，只显示了一个用户的名字
            // 工单审批人和合批人各记录一条数据
            if (in_array($node['node_status'], [1, 2])) {
                if ($approval['type'] == \Yii::$app->params['approval_type']['work_order']) {
                    $extraData = json_decode($approval['extra_data'], true);
                    $woData = ElnCollaboration::findOne($extraData['wo_id']);

                    $action['collaboration_id'] = $woData->id;
                    $action['user_name'] = $userName;
                    $action['user_id'] = $userId;
                    $action['action'] = 9;
                    $action['comment'] = '';
                    $elnCollaborationAction = new ElnCollaborationAction();
                    $elnCollaborationAction->setAttributes($action);
                    $elnCollaborationAction->save();
                }
            }
        }

        return $this->success($noNeedDealNode);
    }

    /**
     * Notes: 审批拒绝
     *
     * Author: zhu huajun
     * Date: 2019/9/25 10:16
     * @param $userInfo
     * @param $nodeIds
     * @param $remark
     * @return array
     */
    public function refuse($userInfo, $nodeIds, $remark) {
        $userId = $userInfo->id;
        $userName = $userInfo->real_name? : $userInfo->name;
        if(!is_array($nodeIds)) {
            $nodeIds = [$nodeIds];
        }

        $noNeedDealNode = [] ; // // add by hkk 2022/8/9  记录已失效或已被复核的状态，(审核页面未刷新导致)
        foreach($nodeIds as $nodeId) {
            $node = ApprovalNodeModel::findOne($nodeId);
            if ($node['status'] == '0') { // 选中状态都被更改，就提示
                $noNeedDealNode[] = $nodeId;
            }
            if (!$node || $node['status'] != 1 || $node['node_status'] != 1) {
                continue;
            }

            $approverIds = !empty($node['approver_ids']) ? explode(',', $node['approver_ids']) : [];
            $coapproverIds = !empty($node['coapprover_ids']) ? explode(',', $node['coapprover_ids']) : [];

            // 用户为审批人
            if(!empty($approverIds) && in_array($userId, $approverIds)) {
                $node['approver_id'] = $userId;
                $node['approval_status'] = 0;
                $node['approval_remark'] = $remark;
                $node['approval_time'] = date('Y-m-d H:i:s');
                $node['node_status'] = 3;
            }

            // 用户为合批人
            if(!empty($coapproverIds) && in_array($userId, $coapproverIds)) {
                $node['coapprover_id'] = $userId;
                $node['coapproval_status'] = 0;
                $node['coapproval_remark'] = $remark;
                $node['coapproval_time'] = date('Y-m-d H:i:s');
                $node['node_status'] = 3;
            }

            $node->save();

            $approval = ApprovalModel::findOne($node['approval_id']);
            $approval['approval_status'] = ApprovalModel::$approval_refuse;
            $approval->save();

            // 处理审批结束后的逻辑
            $this->afterApprove($approval, $userId, [
                'remark' => $remark
            ]);

            $this->sendApprovalResultMsg($node, $userId, 0);

            // 针对实验的审批
            $type = (int)$approval['type'];
            if(in_array($type, $this->expApprovalType)) {
                $action = 'rejected';
                $hisExtraData = [];
                switch($type) {
                    case \Yii::$app->params['approval_type']['sign']://复核
                        $action = 'refused';
                        break;
                    case \Yii::$app->params['approval_type']['coauthor_sign']://合著签字
                    case \Yii::$app->params['approval_type']['signing']://签字
                    case \Yii::$app->params['approval_type']['module_reedit']://重新编辑
                        if ($type == \Yii::$app->params['approval_type']['coauthor_sign']) {
                            $action = 'coauthor_sign_rejected';
                        } else if ($type == \Yii::$app->params['approval_type']['signing']) {
                            $action = 'signing_rejected';
                        } else if ($type == \Yii::$app->params['approval_type']['module_reedit']) {
                            $action = 'module_reedit_rejected';
                        }
                        $extraData = json_decode($approval['extra_data'], true);
                        $moduleArr = @getVar($extraData['module_arr'], []);
                        $moduleRes = (new ExperimentServer())->getModuleNames($moduleArr);
                        $moduleData = @getVar($moduleRes['data'], []);
                        $moduleNames = array_column($moduleData, 'name');
                        $hisExtraData = [
                            'module_ids' => $moduleArr,
                            'module_names' => $moduleNames,
                        ];
                        break;
                    case \Yii::$app->params['approval_type']['reopen']://重开
                        $action = 'reopen_rejected';
                        break;
                    case \Yii::$app->params['approval_type']['pretrial']://预审
                        $action = 'pretrial_rejected';
                        break;
                }
                // 生成一条痕迹记录
                (new HistoryModel())->addHistoryAllData($approval['business_id'], \Yii::$app->view->params['curr_user_id'], $action, $remark, FALSE, $hisExtraData);
            }
            else if ($type == \Yii::$app->params['approval_type']['template']) {
               // (new TempleServer())->addHistory($approval['business_id'], TemplateHistoryModel::TYPE_AUDIT_REFUSE, null, $remark);
               //extraData 为json格式 remark: $remark
               $logExtraData = json_encode(['remark' => $remark]);
                (new TempleServer())->addActionLog($approval['business_id'], TemplateActionLogModel::AUDIT_REFUSE, null, $logExtraData);
            }
            else if ($type == \Yii::$app->params['approval_type']['publish_template']) {
                $tempServer = new TempleServer();
                $tempServer -> updateHistory($approval['business_id'], TemplateHistoryNewModel::$status['is_refused']);
                $history = TemplateHistoryNewModel::findOne($approval['business_id']);
                $templateId = $history->template_id;
                $extraData = [
                    'version' => $history['user_version'],
                    'status' => $history['status'],
                    'remark' => $remark,
                ];
                $tempServer->addActionLog($templateId, TemplateActionLogModel::PUBLISH_AUDIT_REFUSE, $userId, json_encode($extraData), null, null, null, $approval['business_id']);
            }
            else if ($type == \Yii::$app->params['approval_type']['share']) {
                $extraData = json_decode($approval['extra_data'], true);
                (new ShareServer())->addShareDetailLog([
                    'exp_id_arr' => $extraData['exp_id_arr'],
                    'module_id_arr' => $extraData['module_id_arr'],
                    'applicant_id' => $approval->user_id,
                    'operator_id' => $userId,
                    'type' => ShareDetailLogModel::REFUSE,
                    'user_id_arr' => $extraData['user_id_arr'],
                    'group_id_arr' => $extraData['group_id_arr'],
                    'expire_date' => $extraData['expire_date'],
                    'reason' => $remark
                ]);
            }else if($type == \Yii::$app->params['approval_type']['work_order'] ){
                $extraData = json_decode($approval['extra_data'], true);
                $woData = ElnCollaboration::findOne($extraData['wo_id']);
                //0创建，1确认，2解决，3关闭
                switch ($extraData['wo_type']){
                    case 0:
                        $woData -> setAttributes(['status' => 'draft', 'approval_status' => 'Rejected']);
                        break;
                    case 1:
                        $woData -> setAttributes(['status' => 'active', 'approval_status' => 'Rejected', 'confirmed' => 0]);
                        break;
                    case 2:
                        $woData -> setAttributes(['status' => 'active', 'approval_status' => 'Rejected']);
                        break;
                    case 3:
                        $woData -> setAttributes(['status' => 'resolved', 'approval_status' => 'Rejected']);
                        break;
                    case 4:
                        $woData -> setAttributes(['status' => 'closed', 'approval_status' => 'Rejected']);
                        break;
                    default:
                        break;
                }
                $woData->save();

                // 生成审批拒绝记录
                $action['collaboration_id'] = $woData->id;
                $action['user_name'] = $userName;
                $action['user_id'] = $userId;
                $action['action'] = 10;
                $action['comment'] = $remark;
                $elnCollaborationAction = new ElnCollaborationAction();
                $elnCollaborationAction->setAttributes($action);
                $elnCollaborationAction->save();
            }
        }

        return $this->success($noNeedDealNode);
    }

    /**
     * Notes:
     *
     * Author: zhu huajun
     * Date: 2019/9/24 20:13
     * @param $approval
     * @param $userId
     */
    public function afterApprove($approval, $userId, $params=[]) {
        $expServer = (new ExperimentServer());
        switch($approval['type']) {
            case \Yii::$app->params['approval_type']['sign']:// 复核
                // 更新实验状态
                //更新实验状态前 先获取实验更新时间 ，根据实验更新时间 更新那一天的复核实验数目
                $experiment = new ExperimentModel();
                $experimentList = $experiment->find()->where(['id'=>$approval['business_id']])->asArray()->all();
                foreach($experimentList as $experimentInfo){
                    $static_day = date('Y-m-d',strtotime($experimentInfo['update_time']));

                    $book = new BookModel();
                    $bookInfo = $book->find()->where(['id'=>$experimentInfo['book_id']])->asArray()->one();

                    $useStaticExp = new UseStaticExp();
                    $useStaticExpInfo= $useStaticExp->find()->where([
                        'user_id'=>$experimentInfo['user_id'],
                        'group_id'=>$bookInfo['group_id'],
                        'static_day'=>$static_day
                    ])->asArray()->one();
                    if(!empty($useStaticExpInfo)){
                        $useStaticExp::updateAllCounters( ['exp_witness_num' => -1], ['id' => $useStaticExpInfo['id']] );
                    }
                }
                //复核中的实验 减一
                $step = ($approval['approval_status'] == 1 ? CommonModel::$step['countersigned'] : CommonModel::$step['draft']);
                $remark = ($approval['approval_status'] == 1 ? '' : $params['remark']);
                $expServer->updateStep($approval['business_id'], $step, $remark);
                break;
            case \Yii::$app->params['approval_type']['coauthor_sign']:// 合著签字
                if ($approval['approval_status'] != 1) { // 审批拒绝
                    // 取消该实验其他未完成的合著签字审批
                    $this->cancelApproval(\Yii::$app->params['approval_type']['coauthor_sign'], $approval['business_id']);

                    // 更新实验状态为草稿
                    $expServer->updateStep($approval['business_id'], CommonModel::$step['draft']);
                } else { // 审批通过
                    // 判断该实验是否有其他未完成的合著签字审批
                    $haveOther = ApprovalModel::find()->where([
                        'type' => \Yii::$app->params['approval_type']['coauthor_sign'],
                        'business_id' => $approval['business_id'],
                        //'approval_status' => null,
                        'status' => 1
                    ])->andWhere([
                        'or',
                        ['approval_status' => ApprovalModel::$approval_waiting],
                        ['approval_status' => ApprovalModel::$approval_ing]
                    ])->asArray()->one();
                    if (empty($haveOther)) { // 全部合著签字审批均通过
                        $extraData = json_decode($approval['extra_data'], true);
                        if (empty($extraData['witness'])) { // 不需要复核，直接关闭
                            $expServer->updateStep($approval['business_id'], CommonModel::$step['signed']);
                        } else { // 生成复核的审批
                            // 获取实验所在鹰群
                            $expId = $approval['business_id'];
                            $expBaseData = $expServer->getExpBaseData($expId);
                            $groupId = $expBaseData['group_id'];
                            $newExtraData = json_encode([
                                'send_email' => @getVar($extraData['send_email'], 0),
                                'reason' => @getVar($extraData['reason'], ''),
                                'submit_user_id' => @getVar($extraData['submit_user_id'], '')
                            ]);
                            $createApprovalRes = $this->createApproval(
                                \Yii::$app->params['approval_type']['sign'], $expId, $groupId, $newExtraData,
                                $extraData['witness_approval_nodes']
                            );
                            if (empty($createApprovalRes['status'])) {
                                //return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                                yii::info('afterApproval 调用创建审批操作失败, 返回值为' . json_encode($createApprovalRes));
                            }
                        }
                    }
                }
                break;
            case \Yii::$app->params['approval_type']['signing']://签字
                // 更新模块状态
                $extraData = json_decode($approval['extra_data'], true);
                ExperimentRelayModel::updateAll([
                    'module_status' => ($approval['approval_status'] == 1) ? 3 : 1
                ], [
                    'id' => $extraData['module_arr']
                ]);
                break;
            case \Yii::$app->params['approval_type']['reopen']:// 重开
                if(($approval['approval_status'] == 1)) {
                    // 更新实验状态为草稿
                    $expServer->updateStep($approval['business_id'], CommonModel::$step['draft']);
                    $reopenStatus = 2;
                } else {
                    $reopenStatus = null;
                }

                // 更新reopen_status
                ExperimentModel::updateAll([
                    'reopen_status' =>$reopenStatus
                ], [
                    'id' => $approval['business_id']
                ]);
                // Pgsql
                ElnExperimentModel::updateAll([
                    'reopen_status' => $reopenStatus
                ], [
                    'experiment_id' => $approval['business_id']
                ]);
                break;
            case \Yii::$app->params['approval_type']['pretrial']:// 预审
                // 更新pretrial_status
                $pretrialStatus = ($approval['approval_status'] == 1) ? 2 : 3;
                ExperimentModel::updateAll([
                    'pretrial_status' =>$pretrialStatus
                ], [
                    'id' => $approval['business_id']
                ]);
                // Pgsql
                ElnExperimentModel::updateAll([
                    'pretrial_status' => $pretrialStatus
                ], [
                    'experiment_id' => $approval['business_id']
                ]);
                break;
            case \Yii::$app->params['approval_type']['create_book']:// 创建记录本
                if($approval['approval_status'] == 1) { // 审批通过
                    $bookData = json_decode($approval['extra_data'], true);

                    $bookName         = (new BookServer())->getCheckedBookName($bookData['book_name']);
                    $bookData['name'] = $bookName;
                    $bookRes          = (new BookServer())->addBook($bookData);

                    // 创建记录本成功后将记录本编号追加写入$approval的extra_data字段中，用于在历史审核中显示记录本编号
                    if($bookRes['status'] == 1) {
                        $extraData = json_decode($approval['extra_data'], true);
                        $extraData['book_id'] = $bookRes['data']['book_id'];
                        $extraData['book_code'] = $bookRes['data']['book_code'];
                        $extraData['book_name'] = $bookName;
                        ApprovalModel::updateAll([
                            'extra_data' => json_encode($extraData)
                        ], [
                            'id' => $approval['id']
                        ]);
                    }
                }
                break;

            case \Yii::$app->params['approval_type']['change_book_group']:  // 记录本换群
                if ($approval['approval_status'] == 1) {
                    $extraData = json_decode($approval['extra_data'], true);
                    $submitUid = $approval['user_id'];  //换群的提交人员
                    //执行记录本换群并将申请提交人添加到鹰群，写入换群日志
                    $res = (new GroupSettingServer())->changeBookGroup($extraData, $submitUid);

                }
                break;
            case \Yii::$app->params['approval_type']['template']:// 模板
                // 更新模板状态
                $extraData = json_decode($approval['extra_data'], true);

                if (!isset($extraData['to_company_status'])) {  //不是修改模板类型，直接修改模板状态
                    $step = ($approval['approval_status'] == 1 ? 3 : 4);
                    (new TempleServer())->updateStep($approval['business_id'], $step);
                }
                else {  //  设置 || 取消 模板为企业模板，修改模板状态为已通过
                    $step = 3;
                    $to_company_status = ((int)$extraData['to_company_status']);
                    $to_company_status = ($approval['approval_status'] == 1) ? $to_company_status : ($to_company_status ^ 1);//如果审批拒绝，通过 异或 获取is_company字段原来的值
                    $row = TemplateModel::updateAll([
                        'step' => $step,
                        'is_company' => $to_company_status,
                    ], ['id' => $approval['business_id']]);
                }
                break;
            case \Yii::$app->params['approval_type']['publish_template']:// 发布模板
                // business_id 为模板历史记录id
                $extraData = json_decode($approval['extra_data'], true);
                $tempServer = new TempleServer();
                //不记录痕迹，因为在审批通过时已经记录，这里只处理审批流程完全结束后的状态更新
                //$tempServer->addActionLog($approval['business_id'], TemplateActionLogModel::PUBLISH_AUDIT_AGREE, $userId);
                $tempServer->agreeHistory($approval['business_id'], $userId);
                break;

            case \Yii::$app->params['approval_type']['module_reedit']:// 模块重新编辑
                // 更新模块状态
                $extraData = json_decode($approval['extra_data'], true);
                ExperimentRelayModel::updateAll([
                    'module_status' => ($approval['approval_status'] == 1) ? 1 : 3
                ], [
                    'id' => $extraData['module_arr']
                ]);
                break;
            case \Yii::$app->params['approval_type']['instrument_check']:
                $extraData = json_decode($approval['extra_data'], true); // 记录类型
                // 如果数据里有instrument_status没有recordType就是状态修改审批，反过来就是仪器记录审批
                if (empty($extraData['recordType'])) {
                    $instrumentNewStatus = '';
                    switch ($extraData['instrument_status']) {
                        case 1:
                            $instrumentNewStatus = Yii::t('base', 'normal');
                            break;
                        case 2:
                            $instrumentNewStatus = Yii::t('base', 'suspend_use');
                            break;
                        case 3:
                            $instrumentNewStatus = Yii::t('base', 'repairing');
                            break;
                        case 4:
                            $instrumentNewStatus = Yii::t('base', 'scrap');
                            break;
                        case 0:
                            $instrumentNewStatus = Yii::t('base', 'delete');
                            break;
                    }
                    $instrumentNewStatus = strtolower($instrumentNewStatus);
                    $instruments = new InstrumentServer();
                    $instrumentInfo = $instruments->getInsInfoById($extraData['instrument_id']);
                    $instrumentOldStatus = $instrumentInfo['data']['status'];
                    switch ($instrumentInfo['data']['status']) {
                        case 0:
                            $instrumentOldStatus = \Yii::t('base', 'already_deleted');
                            break;
                        case 2:
                            $instrumentOldStatus = \Yii::t('base', 'suspend_use');
                            break;
                        case 3:
                            $instrumentOldStatus = \Yii::t('base', 'repairing');
                            break;
                        case 4:
                            $instrumentOldStatus = \Yii::t('base', 'scrap');
                            break;
                        default:
                            $instrumentOldStatus = \Yii::t('base', 'normal');
                            break;
                    }
                    $instrumentOldStatus = strtolower($instrumentOldStatus);
                    $action_details = (\Yii::t('base', 'status') . ":" . $instrumentOldStatus . "=>" . $instrumentNewStatus . '</br>');
                    $action_details .= (\Yii::t('base', 'reason') . ":" . $extraData['reason'] . '</br>');
                    if ($approval['approval_status'] == 1) {
                        InstrumentsModel::updateAll(['status' => $extraData['instrument_status']], ['id' => $extraData['instrument_id']]);
                        $action_details .= (\Yii::t('base', 'approved') . '</br>');
                    } else {
                        $action_details .= (\Yii::t('base', 'rejected') . '</br>');
                    }
                    $instruments->addInstrumentHistory($extraData['instrument_id'], InstrumentServer::ACTION_APPROVAL, ['action_details' => $action_details]);
                } else {
                    switch ($extraData['recordType']) {
                        case 'repair_record':
                            $model = new InstrumentRepairRecordModel();
                            break;
                        case 'operate_record':
                            $model = new InstrumentRunningRecordModel();
                            break;
                        case 'check_record':
                            $model = new InstrumentCheckRecordModel();
                            break;
                    }
                    if ($approval['approval_status'] == 1) { // 仪器记录审核通过事件 hkk 2021/4/20
                        // 复核通过后更新仪器记录的最新信息，显示复核人，符合原因，复核时间和结论
                        $checkInfo = ApprovalNodeModel::find()->select('approver_id,approval_status,approval_remark,approval_time')->where(['approval_id' => $approval['id'],])->asArray()->one();

                        // modified by hkk 2022/6/13 新的仪器复核记录
                        $userInfo = (new CenterInterface())->getUserByUserId($checkInfo['approver_id']);
                        $approver = CommonServer::displayUserName($userInfo);
                        $record = $checkInfo['approval_time'] . ' ' . $approver . \Yii::t('base', 'check_success');
                        $currentRecord = $model::find()->where(['id' => $approval['business_id']])->asArray()->one();
                        if (!empty($currentRecord['review_record'])) {
                            $record .= ';;' . $currentRecord['review_record'];
                        }
                        $saveRecordData = [];
                        $saveRecordData['review_conclusion'] = '1';  // 复核结论
                        $saveRecordData['review_record'] = $record;  // 复核记录
                        $model::updateAll($saveRecordData, ['id' => $approval['business_id']]);

                        // 判断是否更改仪器状态和有效期 这边有效期相当于作废了，下面重新写了仪器库时间复制逻辑
                        $extraData = json_decode($approval['extra_data'], true);
                        if (!empty($extraData['statusNormal']) && $extraData['statusNormal'] == 'true') {
                            InstrumentsModel::updateAll(['status' => 1,], ['id' => $extraData['instrument_id']]);
                        } else if (!empty($extraData['start_expiry_time']) && !empty($extraData['end_expiry_time'])) {
                            InstrumentsModel::updateAll(['check_situation' => 1, 'start_expiry_time' => $extraData['start_expiry_time'], 'end_expiry_time' => $extraData['end_expiry_time'],], ['id' => $extraData['instrument_id']]);
                        }
                    } else { // add by hkk 2021/5/26 仪器记录审核拒绝事件
                        $checkInfo = ApprovalNodeModel::find()->select('approver_id,approval_status,approval_remark,approval_time')->where(['approval_id' => $approval['id'],])->asArray()->one();

                        $userInfo = (new CenterInterface())->getUserByUserId($checkInfo['approver_id']);
                        $approver = CommonServer::displayUserName($userInfo);
                        $record = $checkInfo['approval_time'] . ' ' . $approver . \Yii::t('base', 'check_failed') . '(' . $checkInfo['approval_remark'] . ')';
                        $currentRecord = $model::find()->where(['id' => $approval['business_id']])->asArray()->one();
                        if (!empty($currentRecord['review_record'])) {
                            $record .= ';;' . $currentRecord['review_record'];
                        }
                        $saveRecordData = [];
                        $saveRecordData['review_conclusion'] = '2';  // 复核结论
                        $saveRecordData['review_record'] = $record;  // 复核记录
                        $model::updateAll($saveRecordData, ['id' => $approval['business_id']]);
                    }
                    //更新仪器库数据
                    (new InstrumentServer()) -> updateInstrumentsTimeRecord($extraData['instrument_id']);

                }

                break;
            case \Yii::$app->params['approval_type']['share']: //实验分享
                $extraData = json_decode($approval['extra_data'], true);
                if ($approval['approval_status'] == 1) {
                    (new ShareServer())->setExpShare([
                        'user_id' => $approval['user_id'],
                        'exp_id_arr' => $extraData['exp_id_arr'],
                        'group_id_arr' => $extraData['group_id_arr'],
                        'user_id_arr' => $extraData['user_id_arr'],
                        'share_whole_exp' => $extraData['share_whole_exp'],
                        'module_id_arr' => $extraData['module_id_arr'],
                        'expire_date' => $extraData['expire_date'],
                    ]);
                }
                break;
            case \Yii::$app->params['approval_type']['template_share_batch']:
            case \Yii::$app->params['approval_type']['template_share']: //模板分享
                $extraData = json_decode($approval['extra_data'], true);
                $share_creator = @getVar($extraData['share_creator'], 0);//分享的创建人
                if ($approval['approval_status'] == 1 && $share_creator) {
                    (new ShareServer())->setTempShare([
                        'temp_id_arr' => $extraData['temp_id_arr'],
                        'group_id_arr' => $extraData['group_id_arr'],
                        'user_id_arr' => $extraData['user_id_arr'],
                        'new_part_user_arr' => @getVar($extraData['new_part_user_arr'], $extraData['user_id_arr']), // 兼容老数据，如果没有new_part_user_arr就给所有人发
                        'new_part_group_arr' => @getVar($extraData['new_part_group_arr'], $extraData['group_id_arr']), // 兼容老数据，如果没有new_part_group_arr就给所有人发
                        'share_creator' => $share_creator,
                    ]);
                }
                break;
            case \Yii::$app->params['approval_type']['update_book_name']:
                if ($approval['approval_status'] == 1) {    //修改记录本名称审批通过
                    $extraData = json_decode($approval['extra_data'], true);
                    $bookName         = (new BookServer())->getCheckedBookName($extraData['book_name_new']);
                    $res = (new BookModel())->cacheUpdateAll(
                        [
                            'name' => $bookName,
                        ],
                        ['id' => $extraData['book_id']]
                    );
                }
                break;
        }
    }

    /**
     * Notes: 发送审批提醒（平台消息和邮件）
     * Author: zhu huajun
     * Date: 2019/10/12 13:51
     * @param $approvalId
     */
    public function sendApprovalRemindMsg($approvalId) {
        // 查找审批业务
        $approval = ApprovalModel::findOne($approvalId);
        if(!$approval) {
            return;
        }
        $userId = (int)$approval['user_id'];

        // 当前审批节点
        $node = ApprovalNodeModel::findOne([
            'approval_id' => $approvalId,
            'node_status' => 1
        ]);
        if(!$node) {
            return;
        }

        $approvalType = (int)$approval['type'];
        if(in_array($approvalType, $this->expApprovalType)) { // 针对实验的审批
            $expId = $approval['business_id'];
            $exp = (new ExperimentServer())->getBaseDataById($expId);
            $exp = $exp['data'];
            //根据实验ID获取实验编号
            $expCode = $exp['exp_all_code'];
            $expLink = '<a href="' . ELN_URL . '?exp_id=' . $expId . '" target="_blank">' . $expCode . '</a>';
            $expTitle = $exp['title'];
            $expCreateTime = date('Y-m-d', strtotime($exp['create_time']));
        }
        //构造审批路径
        $approval_route_str = $this->buildApprovalRouteByApproveId($approvalId)['approve_route_str'];
        $approverIds = !empty($node['approver_ids']) ? explode(',', $node['approver_ids']) : [];
        $coapproverIds = !empty($node['coapprover_ids']) ? explode(',', $node['coapprover_ids']) : [];

        $toUserIds = [];
        if (empty($node['approver_id'])) {
            $toUserIds = array_merge($toUserIds, $approverIds);
        }
        if (empty($node['coapprover_id'])) {
            $toUserIds = array_merge($toUserIds, $coapproverIds);
        }
        $toUserIds = array_unique($toUserIds);
        $toUserIds = array_filter($toUserIds, function ($item) {
            return !empty($item);
        });
        $allUserIds = array_unique(array_merge($toUserIds, [$userId]));
        $userInfoArr = (new CenterInterface())->userDetailsByUserIds($allUserIds);
        $userInfoArr = yii\helpers\ArrayHelper::index($userInfoArr, 'id');
        $userName = '';
        if(!empty($userInfoArr[$userId])){
            $userName = CommonServer::displayUserName($userInfoArr[$userId]);
        }
        // 根据业务类型，生成消息和邮件的内容
        $titleKey = '';
        $msgContentKey = '';
        $msgContentParams = [];
        $emailContentKey = '';
        $emailContentParams = [];
        $msgType = 5;
        $msgUrl = ELN_URL;
        $extraData = json_decode($approval['extra_data'], true);
        switch ($approval['type']) {
            case \Yii::$app->params['approval_type']['sign']:// 复核
                $titleKey = 'sign_remind_title';
                $msgContentKey = 'sign_msg_content';
                $msgType = 5;
                $msgUrl = ELN_URL . '?exp_id=' . $expId ;
                $msgContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $approval_route_str
                ];
                $emailContentKey = 'sign_email_content';
                $emailContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $approval_route_str
//                    \Yii::$app->params['eln_url'],
                ];
                break;
            case \Yii::$app->params['approval_type']['reopen']:// 重开
                $titleKey = 'reopen_remind_title';
                $msgContentKey = 'reopen_msg_content';
                $msgType = 14;
                 $msgUrl = ELN_URL . '?exp_id=' . $expId ;
                $msgContentParams = [
                    $userName,
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $extraData['reason'],
                    $approval_route_str
                ];
                $emailContentKey = 'reopen_msg_content';
                $emailContentParams = [
                    $userName,
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $extraData['reason'],
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['signing']:// 签字
                $moduleArrCode = $extraData['module_arr'];
                $moduleArr = [];
                $experimentRelay = new ExperimentRelayModel();
                foreach ($moduleArrCode as $key => $id) {
                    $moduleName = $experimentRelay->find()
                        ->select('name')
                        ->where(['id' => $id])
                        ->asArray()->one();
                    $moduleName = implode(',', $moduleName);
                    $moduleArr[$key] = $moduleName;
                }
                $moduleArr = implode(',', $moduleArr);
                $remark = $extraData['reason'];
                $titleKey = 'signing_remind_title';
                $msgContentKey = 'signing_msg_content';
                $msgType = 15;
                $msgUrl = ELN_URL;
                $msgContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $moduleArr,
                    $remark,
                    $approval_route_str
                ];
                $emailContentKey = 'signing_email_content';
                $emailContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $moduleArr,
                    $remark,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['pretrial']:// 预审
                $pretrialSetting = $extraData['pretrial_setting'];
                $projectArr = [];
                $projectNameArr = [];
                // 根据项目ID获取项目名

                $contentText = '';
                if (!empty($pretrialSetting['all_setting']['approval_nodes'])) {
                    $contentText = \Yii::t('msg', 'all_setting_content');
                } elseif (!empty($pretrialSetting['indraw_setting']['approval_nodes'])) {
                    $contentText = \Yii::t('msg', 'indraw_setting_content');
                } elseif (@getVar($pretrialSetting['project_setting'], [])) {
                        foreach ($pretrialSetting['project_setting'] as $item) {
                            $projectArr = array_merge($projectArr,$item['project_ids']);
                        }
                        foreach (array_unique($projectArr) as $key => $projectId) {
                            $projectInfo = (new \frontend\interfaces\PMInterface())->getProjectById($projectId);
                            $projectNameArr[$key] = isset($projectInfo['project']['name']) ? $projectInfo['project']['name'] : '';
                        }
                    $contentText = \Yii::t('msg', 'project_setting_content', implode(',', $projectNameArr));
                }


                $titleKey = 'pretrial_remind_title';
                $msgContentKey = 'pretrial_msg_content';
                $msgType = 17;
                $msgUrl = ELN_URL . '?exp_id=' . $expId;
                $remark = $extraData['reason'];
                $msgContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $contentText,
                    $remark,
                    $approval_route_str
                ];
                $emailContentKey = 'pretrial_email_content';
                $emailContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $contentText,
                    $remark,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['create_book']:// 创建记录本
                $_userLang = CommonServer::getUserLang($userInfoArr[$toUserIds[0]]);
                /* $bookName = $extraData['book_name']; */
                $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                    sprintf('%1$s?route=approval&type=5', ELN_URL),
                    $extraData['book_name']
                );
                $toUserNames = array_map(function ($toUid) use($userInfoArr) {
                    return CommonServer::displayRealName($userInfoArr[$toUid]);
                }, $toUserIds);//获取接收人
                $toUserNamesStr = join(', ', $toUserNames);

                //记录本所属项目
                $projStr = '';
                if (!empty($extraData['project_id'])) {
                    $book_proj = $extraData['project_id'];
                    $projectInfo = (new PMInterface())->getProjectById($book_proj);
                    if (isset($projectInfo['project']['name'])) {
                        $projStr = sprintf(
                            ', %1$s: %2$s',
                            \Yii::t('msg', 'project', null, $_userLang),
                            $projectInfo['project']['name']);
                    }
                }

                $groupId = $extraData['group_id'];
                $groupInfo = (new CenterInterface())->getGroupByGroupId($groupId);
                $groupName = $groupInfo['group_name'];
                $approval_route_str = $this->buildApprovalRouteByApproveId($approvalId)['approve_route_str'];

                $titleKey = 'book_remind_title';
                $msgContentKey = 'book_msg_content_new';
                $msgType = 16;
                $msgUrl = ELN_URL;
                $book_creator_name = sprintf('<span>%1$s</span>', CommonServer::displayUserName($userInfoArr[$userId]));
                $msgContentParams = [
                    'book_creator' => $book_creator_name,
                    'book_name' => $bookName,
                    'approve_route' => $approval_route_str,
                    'book_group_name' => $groupName,
                    'proj_str' => $projStr,
                ];
                $emailContentKey = 'book_email_content_new';
                $emailContentParams = [
                    'book_creator' => $book_creator_name,
                    'book_name' => $bookName,
                    'approve_route' => $approval_route_str,
                    'book_group_name' => $groupName,
                    'to_user_names_str' => $toUserNamesStr,
                    'proj_str' => $projStr,
                    /*\Yii::$app->params['eln_url'],*/
                ];
                break;
            case \Yii::$app->params['approval_type']['update_book_name']:   //修改记录本名称
                $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                    sprintf('%1$s?route=approval&type=5', ELN_URL),
                    $extraData['book_name_old']
                );
                $groupId = $extraData['group_id'];
                $groupInfo = (new CenterInterface())->getGroupByGroupId($groupId);
                $groupName = $groupInfo['group_name'];
                $titleKey = 'book_remind_title';
                $msgContentKey = 'update_book_name_content';
                $msgType = 16;
                $msgUrl = ELN_URL . '?route=approval&type=5';
                $msgContentParams = [
                    $userName,
                    $bookName,
                    $groupName,
                    $extraData['book_name_old'],
                    $extraData['book_name_new'],
                    $approval_route_str
                ];
                $emailContentKey = 'update_book_name_content';
                $emailContentParams = [
                    $userName,
                    $bookName,
                    $groupName,
                    $extraData['book_name_old'],
                    $extraData['book_name_new'],
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['coauthor_sign']:// 复核
                $titleKey = 'coauthor_sign_msg_title';
                $msgContentKey = 'coauthor_sign_remind_content';
                $msgType = 5;
                $msgUrl = ELN_URL . '?exp_id=' . $expId ;
                $msgContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $approval_route_str
                ];
                $emailContentKey = 'coauthor_sign_remind_content';
                $emailContentParams = [
                    $expLink,
                    $expTitle,
                    $userName,
                    $expCreateTime,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['change_book_group']:   //记录本换群
                $ownerUserInfo = (new CenterInterface())->getUserByUserId($extraData['user_id']);
                $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                    sprintf('%1$s?route=approval&type=5', ELN_URL),
                    $extraData['book_name']
                );
                $titleKey = 'book_remind_title';
                $msgContentKey = 'change_book_group_content';
                $msgType = 16;
                $msgUrl = ELN_URL . '?route=approval&type=5';
                $msgContentParams = [
                    $userName,
                    CommonServer::displayUserName($ownerUserInfo),
                    $bookName,
                    $extraData['book_code'],
                    $extraData['group_name'],
                    $extraData['to_group_name'],
                    $extraData['operate_desc'] ? Yii::t('msg', 'change_remark', $extraData['operate_desc']) : '',
                    $approval_route_str,
                ];
                $emailContentKey = 'change_book_group_email_content';
                $emailContentParams = $msgContentParams;

                break;
            case \Yii::$app->params['approval_type']['template']: // 模板
                $sendEmail = @getVar($extraData['send_email'], 0);
                $templateId = @getVar($extraData['template_id'], 0);
                $templateName = '';
                if (!empty($templateId)) {
                    $templateRes = (new TempleServer())->getBasicById($templateId);
                    $basicData = @getVar($templateRes['data'], []);
                    $templateName = @getVar($basicData['name'], '');

                    $templateCreateTime = date('Y-m-d', strtotime($basicData['create_time']));
                    switch ($basicData['type']) {
                        case 1:
                            $templateType = @getVar($extraData['to_company_status'], 0) == 1 ? \Yii::t('base', 'company_full') : \Yii::t('msg', 'all_temp');
                            $templateNameLink = '<a href="' . ELN_URL . '?route=approval&type=6" target="_blank">' . $templateName . '</a>';
                            break;
                        case 2:
                        case 4:
                            $templateType = @getVar($extraData['to_company_status'], 0) == 1 ? \Yii::t('base', 'company_function') : \Yii::t('msg', 'method_temp');
                            $templateNameLink = '<a href="' . ELN_URL . '?route=approval&type=6" target="_blank">' . $templateName . '</a>';
                            break;
                    }
                }
                $approval_route_str = $this->buildApprovalRouteByApproveId($approvalId)['approve_route_str'];

                $titleKey = 'template_remind_title';
                $msgContentKey = 'template_msg_content';
                $msgType = 23;
                $msgUrl = ELN_URL;
                $msgContentParams = [
                    $userName,
                    $templateType,
                    $templateNameLink,
                    $userName,
                    $templateCreateTime,
                    $approval_route_str
                ];
                $emailContentKey = 'template_email_content';
                $emailContentParams = [
                    $userName,
                    $templateType,
                    $templateNameLink,
                    $userName,
                    $templateCreateTime,
                    $approval_route_str
//                    \Yii::$app->params['eln_url'],
                ] ;
                break;
            case \Yii::$app->params['approval_type']['module_reedit']:// 模块重新编辑
                $titleKey = 'module_reedit_remind_title';
                $msgContentKey = 'module_reedit_msg_content';
                $msgType = 15;
                $msgUrl = ELN_URL;
                $msgContentParams = [
                    $userName,
                    $expLink,
                    $extraData['reason'],
                ];
                $emailContentKey = 'module_reedit_email_content';
                $emailContentParams = [
                    $userName,
                    $expLink,
                    $extraData['reason'],
                    ELN_URL,
                ];
                break;
            case \Yii::$app->params['approval_type']['instrument_check']: // 仪器记录的复核
                $titleKey = 'instrument_record_check_message';
                $msgType = 25;
                $msgUrl = ELN_URL;
                // 如果数据里有instrument_status没有recordType就是状态修改审批，反过来就是仪器记录审批
                if (empty($extraData['recordType'])) {
                    $titleKey = 'instrument_status_message';
                    $instrumentNewStatus = '';
                    switch ($extraData['instrument_status']) {
                        case 1:
                            $instrumentNewStatus = Yii::t('base', 'normal');
                            break;
                        case 2:
                            $instrumentNewStatus = Yii::t('base', 'suspend_use');
                            break;
                        case 3:
                            $instrumentNewStatus = Yii::t('base', 'instrument_repair');
                            break;
                        case 4:
                            $instrumentNewStatus = Yii::t('base', 'scrap');
                            break;
                        case 0:
                            $instrumentNewStatus = Yii::t('base', 'delete');
                            break;
                    }
                    $instrumentNewStatus = strtolower($instrumentNewStatus);
                    $msgContentKey = 'instrument_status_change_message';
                    $msgContentParams = [
                        $userName, // 用户
                        $instrumentNewStatus, // 仪器状态
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        $approval_route_str
                    ];
                    $emailContentKey = 'instrument_status_change_message';
                    $emailContentParams = [
                        $userName, // 用户
                        $instrumentNewStatus, // 仪器状态
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        $approval_route_str
                    ];
                } else {
                    $msgContentKey = 'instrument_record_check_message_content';
                    $msgContentParams = [
                        $userName, // 用户
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        strtolower(\Yii::t('base', @getVar($extraData['recordType']))),
                        $approval_route_str,
                        ];
                    $emailContentKey = 'instrument_record_check_message_content';
                    $emailContentParams = [
                        $userName, // 用户
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        strtolower(\Yii::t('base', @getVar($extraData['recordType']))),
                        $approval_route_str];
                }
                break;
            case \Yii::$app->params['approval_type']['share']: // 实验分享
                //若未开启邮件通知
                if (!$approval['business_id']) {
                    return;
                }
                $shareSetting = (new ShareAccessModel())->getShareAccessById($approval['business_id']);
                if ($shareSetting['send_approval_email'] != 1) {
                    $extraData['send_email'] = 0;
                }
                $msgType = 26;
                $msgUrl = ELN_URL;
                $titleKey = 'share_remind_title';
                $msgContentKey = 'share_remind_content';
                $expCodesStr = '';
                $experimentServer = new ExperimentServer();
                foreach ($extraData['exp_id_arr'] as $k => $expId) {
                    if ($k > 0) $expCodesStr .= ', ';
                    $exp = $experimentServer->getBaseDataById($expId);
                    //根据实验ID获取实验编号
                    $expTitle = $exp['data']['title'];
                    $expCreateTime = date('Y-m-d', strtotime($exp['data']['create_time']));
                    $expCode = $exp['data']['exp_all_code'];
                    $expCodesStr .= '<a href="' . ELN_URL . '?route=approval&type=10  " target="_blank">' . $expCode . '</a>';
                    $expCodesStr .= \Yii::t('msg', 'experiment_info', [
                        $expTitle,
                        $userName,
                        $expCreateTime,
                    ], \Yii::$app->language);
                }
                $msgContentParams = [
                    $userName,
                    $expCodesStr,
                    $approval_route_str
                ];
                $emailContentKey = 'share_remind_email_content';
                $emailContentParams = [
                    $userName,
                    $expCodesStr,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['template_share_batch']: // 模板批量分享
            case \Yii::$app->params['approval_type']['template_share']: // 模板分享
                //若未开启邮件通知
                if (!$approval['business_id']) {
                    return;
                }
                $msgType = 26;
                $msgUrl = ELN_URL;
                $titleKey = 'temp_share_remind_title';
                $msgContentKey = 'temp_share_remind_content';
                $tempNameStr = '';
                $templeServer = new TempleServer();
                foreach ($extraData['temp_id_arr'] as $k => $tempId) {
                    if ($k > 0) $tempNameStr .= ', ';
                    $templateRes = $templeServer->getBasicById($tempId);
                    $basicData = @getVar($templateRes['data'], []);
                    $templateName = @getVar($basicData['name'], '');
                    $tempNameStr .= $templateName;
                    $templateCreateTime = date('Y-m-d', strtotime($basicData['create_time']));
                    switch ($basicData['type']) {
                        case 1:
                            $templateType = \Yii::t('msg', 'all_temp');
                            $templateNameLink = '<a href="' . ELN_URL . '?route=template&type=6" target="_blank">' . $templateName . '</a>';
                            break;
                        case 2:
                        case 3:
                        case 4:
                            $templateType = \Yii::t('msg', 'method_temp');
                            $templateNameLink = '<a href="' . ELN_URL . '?route=template&type=6" target="_blank">' . $templateName . '</a>';
                            break;
                    }
                }
                $msgContentParams = [
                    $userName,
                    $templateType,
                    $templateNameLink,
                    $userName,
                    $templateCreateTime,
                    $approval_route_str
                ];
                $emailContentKey = 'temp_share_remind_content_email';
                $emailContentParams = [
                    $userName,
                    $templateType,
                    $templateNameLink,
                    $userName,
                    $templateCreateTime,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['work_order']:// 工单
                $wo_detail = (new CollaborationServer()) -> getCollaboration($approval['business_id']);
                $wo_title = $wo_detail['exp_title'];
                $wo_create_person = (new CenterInterface())->getUserByUserId($wo_detail['user_id']);
                $wo_assigned_person = $wo_detail['assigned_to'];
                switch ($extraData['wo_type']){
                    case 0:
                        $wo_type = Yii::t('base', 'create');
                        break;
                    case 1:
                        $wo_type = Yii::t('base', 'confirm');
                        break;
                    case 2:
                        $wo_type = Yii::t('base', 'solve');
                        break;
                    case 3:
                        $wo_type = Yii::t('base', 'close');
                        break;
                    case 4:
                        $wo_type = Yii::t('base', 'activate');
                        break;
                }
                $titleKey = 'collaboration_message_title';
                $msgContentKey = 'wo_remind_content';
                $msgContentParams = [
                    $userName,
                    $wo_type,
                    ELN_URL . '?route=collaboration&collaboration_id=' . $approval['business_id'],
                    $approval['business_id'],
                    $wo_title,
                    CommonServer::displayUserName($wo_create_person),
                    $wo_assigned_person,
                    $approval_route_str
                ];
                $emailContentKey = 'wo_remind_content';
                $emailContentParams = [
                    $userName,
                    $wo_type,
                    ELN_URL . '?route=collaboration&collaboration_id=' . $approval['business_id'],
                    $approval['business_id'],
                    $wo_title,
                    CommonServer::displayUserName($wo_create_person),
                    $wo_assigned_person,
                    $approval_route_str
                ];
                break;
        }



        $lang = \Yii::$app->language;
        // 发送平台消息
        $elnMsgSer = new ElnMessageServer();
        $msgTitle = \Yii::t('msg', $titleKey, [], $lang);
        $msgContent = \Yii::t('msg', $msgContentKey, $msgContentParams, $lang);
        foreach ($toUserIds as $toUserId) {
            $elnMsgSer->addMessage($userId, $toUserId, $msgTitle, $msgContent, $msgType, false, \Yii::$app->view->params['curr_company_id'], $msgUrl);
        }

        // 发送邮件
        if (!isset($extraData['send_email']) || !empty($extraData['send_email'])) {
            // 获取第一个收件人的用户语言
            $emailTitle = \Yii::t('msg', $titleKey, [], $lang);
            $emailContent = \Yii::t('msg', $emailContentKey, $emailContentParams, $lang);
            $elnMsgSer->addMail($userId, $toUserIds, [], $emailTitle, $emailContent,1 , $lang);
        }

        ApprovalModel::updateAll([
            'last_remind_time' => date('Y-m-d H:i:s')
        ], ['id' => $approval['id']]);
    }

    /**
     * Notes: 发送审批结果提醒（平台消息和邮件）
     * Author: zhu huajun
     * Date: 2019/10/12 16:14
     * @param $node static 审批节点
     * @param $userId int 审批人ID
     * @param $approvalResult int 审批结果(1->通过；0->拒绝)
     */
    public function sendApprovalResultMsg($node, $userId, $approvalResult) {
        // 查找审批业务
        $approval = ApprovalModel::findOne($node['approval_id']);
        if(!$approval) {
            return;
        }
        $extraData = json_decode($approval['extra_data'], true);
        $toUserId = $approval['user_id'];
        $remark = isset($node['approval_remark']) ? $node['approval_remark'] : '';
        $approvalType = (int)$approval['type'];
        if(in_array($approvalType, $this->expApprovalType)) { // 针对实验的审批
            $expId = $approval['business_id'];
            $exp = (new ExperimentServer())->getBaseDataById($expId);
            //根据实验ID获取实验编号
            $expCode = $exp['data']['exp_all_code'];
            $expLink = '<a href="' . ELN_URL . '?exp_id=' . $expId . '" target="_blank">' . $expCode . '</a>';
            $expTitle = $exp['data']['title'];
            $expCreateTime = date('Y-m-d', strtotime($exp['data']['create_time']));
        }
        //构造审批节点路径
        $approval_route_str = $this->buildApprovalRouteByApproveId($approval['id'])['approve_route_str'];

        // 获取用户信息
        $allUserIds = array_unique([$toUserId, $userId]);
        $userInfoArr = (new CenterInterface())->userDetailsByUserIds($allUserIds);
        $userInfoArr = yii\helpers\ArrayHelper::index($userInfoArr, 'id');
        $userName = '';
        if(!empty($userInfoArr[$userId])){
            $userName = CommonServer::displayUserName($userInfoArr[$userId]);
        }

        // 获取用户语言
        $userLang = \Yii::$app->language;

        // 根据业务类型，生成消息和邮件的内容
        $titleKey = '';
        $msgContentKey = '';
        $msgContentParams = [];
        $emailContentKey = '';
        $emailContentParams = [];
        switch ($approval['type']) {
            case \Yii::$app->params['approval_type']['sign']:// 复核

                $titleKey = 'sign_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'sign_agree_msg_content' : 'sign_refuse_msg_content';
                $msgContentParams = [
                    $userName,
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $remark,
                    $approval_route_str
                ];
                $emailContentKey = ($approvalResult == 1) ? 'sign_agree_email_content' : 'sign_refuse_email_content';
                $emailContentParams = [
                    $expLink,
                    $userName,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $remark,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['reopen']:// 重开
                $titleKey = 'reopen_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'reopen_agree_msg_content' : 'reopen_refuse_msg_content';
                $msgContentParams = [
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $userName,
                    $remark,
                    $approval_route_str
                ];
                $emailContentKey = ($approvalResult == 1) ? 'reopen_agree_email_content' : 'reopen_refuse_email_content';
                $emailContentParams = [
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $userName,
                    $remark,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['signing']:// 签字
                $moduleArrCode = $extraData['module_arr'];
                $moduleArr = [];
                $experimentRelay = new ExperimentRelayModel();
                foreach ($moduleArrCode as $key => $id) {
                    $moduleName = $experimentRelay->find()
                        ->select('name')
                        ->where(['id' => $id])
                        ->asArray()->one();
                    $moduleName = implode(',', $moduleName);
                    $moduleArr[$key] = $moduleName;
                }
                $moduleArr = implode(',', $moduleArr);
                $titleKey = 'signing_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'signing_agree_msg_content' : 'signing_refuse_msg_content';
                $msgContentParams = [
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $moduleArr,
                    $userName,
                    $remark,
                    $approval_route_str
                ];
                $emailContentKey = ($approvalResult == 1) ? 'signing_agree_email_content' : 'signing_refuse_email_content';
                $emailContentParams = [
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $moduleArr,
                    $userName,
                    $remark,
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['pretrial']:// 预审

                $titleKey = 'pretrial_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'pretrial_agree_msg_content' : 'pretrial_refuse_msg_content';
                $msgContentParams = [
                    $userName,
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    \Yii::$app->request->post('remark', ''),
                    $approval_route_str
                ];
                $emailContentKey = ($approvalResult == 1) ? 'pretrial_agree_email_content' : 'pretrial_refuse_email_content';
                $emailContentParams = [
                    $userName,
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    \Yii::$app->request->post('remark', ''),
                    $approval_route_str
                ];
                break;
            case \Yii::$app->params['approval_type']['create_book']:// 创建记录本
                $_userLang = CommonServer::getUserLang($userInfoArr[$toUserId]);
                //记录本所属项目
                $projStr = '';
                if (!empty($extraData['project_id'])) {
                    $book_proj = $extraData['project_id'];
                    $projectInfo = (new PMInterface())->getProjectById($book_proj);
                    if (isset($projectInfo['project']['name'])) {
                        $projStr = sprintf(
                            ', %1$s: %2$s',
                            \Yii::t('msg', 'project', null, $_userLang),
                            $projectInfo['project']['name']);
                    }
                }
                $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                    sprintf('%1$s?route=approval&type=5', ELN_URL),
                    $extraData['book_name']
                );
                $groupId = $extraData['group_id'];
                $groupInfo = (new CenterInterface())->getGroupByGroupId($groupId);
                $groupName = $groupInfo['group_name'];
                $titleKey = 'book_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'book_agree_msg_content' : 'book_refuse_msg_content';
                $msgContentParams = [
                    $userName,
                    $bookName,
                    $groupName,
                    $projStr,
                    $remark,
                    $approval_route_str,

                ];
                $emailContentKey = ($approvalResult == 1) ? 'book_agree_email_content' : 'book_refuse_email_content';
                $emailContentParams = [
                    $userName,
                    $bookName,
                    $groupName,
                    $projStr,
                    $remark,
                    $approval_route_str,

                ];
                if (is_numeric($toUserId)) {   //如果收信人id是数字，转化为字符串，防止添加email时报错
                    $toUserId = strval($toUserId);
                }
                break;
            case \Yii::$app->params['approval_type']['update_book_name']:   //修改记录本名称
                $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                    sprintf('%1$s?book_id='. $extraData['book_id'] .'', ELN_URL),
                    $extraData['book_name_new']
                );
                $groupId = $extraData['group_id'];
                $groupInfo = (new CenterInterface())->getGroupByGroupId($groupId);
                $groupName = $groupInfo['group_name'];
                $titleKey = 'book_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'update_book_name_agree_msg_content' : 'update_book_name_refuse_msg_content';
                $msgContentParams = [
                    $userName,
                    $bookName,
                    $groupName,
                    $extraData['book_name_old'],
                    $extraData['book_name_new'],
                    $approval_route_str,
                    $remark,
                ];
                $emailContentKey = ($approvalResult == 1) ? 'update_book_name_agree_msg_content' : 'update_book_name_refuse_msg_content';
                $emailContentParams = [
                    $userName,
                    $bookName,
                    $groupName,
                    $extraData['book_name_old'],
                    $extraData['book_name_new'],
                    $approval_route_str,
                    $remark,
                ];
                break;
            case \Yii::$app->params['approval_type']['coauthor_sign']:// 复核
                $titleKey = 'coauthor_sign_msg_title';
                $msgContentKey = ($approvalResult == 1) ? 'coauthor_sign_agree_content' : 'coauthor_sign_refuse_content';
                $msgContentParams = [
                    $userName,
                    $expLink,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $approval_route_str,
                    $remark,
                ];
                $emailContentKey = ($approvalResult == 1) ? 'coauthor_sign_agree_content' : 'coauthor_sign_refuse_content';
                $emailContentParams = [
                    $expLink,
                    $userName,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $approval_route_str,
                    $remark,
                ];
                break;
            case \Yii::$app->params['approval_type']['change_book_group']:  //记录本换群
                $ownerUserInfo = (new CenterInterface())->getUserByUserId($extraData['user_id']);
                $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                    sprintf('%1$s?book_id='. $extraData['book_id'] .'', ELN_URL),
                    $extraData['book_name']
                );
                $titleKey = 'book_remind_title';
                $centerInterface = new CenterInterface();
                $approverInfo = $centerInterface->getUserByUserId($userId);
                $approverUserInfo = $centerInterface->getUserByUserId($toUserId);
                $msgContentKey = ($approvalResult == 1) ? 'change_book_group_agree_msg_content' : 'change_book_group_refuse_msg_content';
                $msgContentParams = [
                    $approverInfo['real_name'] . '(' . $approverInfo['name'] . ')',
                    CommonServer::displayUserName($ownerUserInfo),
                    $bookName,
                    $extraData['book_code'],
                    $extraData['group_name'],
                    $extraData['to_group_name'],
                    $remark,
                    $approval_route_str
                ];
                $emailContentKey = ($approvalResult == 1) ? 'change_book_group_agree_email_content' : 'change_book_group_refuse_email_content';
                $emailContentParams = $msgContentParams;

                //记录本创建人和审批发起人不是同一人，将记录本创建人添加到邮件收件人
                if ($extraData['user_id'] != $toUserId) {
                    $toUserId = [$toUserId, $extraData['user_id']];
                }

                break;
            case \Yii::$app->params['approval_type']['template']:// 模板
                $templateId = @getVar($extraData['template_id'], 0);
                $templateName = '';
                $templateType = '';//bug#34468 防止其他类型的模板导致变量未初始化的问题
                $templateNameLink = '';
                if (!empty($templateId)) {
                    $templateRes = (new TempleServer())->getBasicById($templateId);
                    $basicData = @getVar($templateRes['data'], []);
                    $templateName = @getVar($basicData['name'], '');
                    $templateCreateTime = date('Y-m-d', strtotime($basicData['create_time']));
                    switch ($basicData['type']) {
                        case 1:
                            $templateType = @getVar($extraData['to_company_status'], 0) == 1 ? \Yii::t('base', 'company_full') : \Yii::t('msg', 'all_temp');
                            $templateNameLink = '<a href="' . ELN_URL . '?route=template&temp_id=' . $templateId . '&temp_type=1" target="_blank">' . $templateName . '</a>';
                            break;
                        case 2://方法模板
                            $templateType = @getVar($extraData['to_company_status'], 0) == 1 ? \Yii::t('base', 'company_function') : \Yii::t('msg', 'method_temp');
                            $templateNameLink = '<a href="' . ELN_URL . '?route=template&temp_id=' . $templateId . '&temp_type=1" target="_blank">' . $templateName . '</a>';
                            break;
                    }
                }
                $refuseRemark = Yii::t('msg', 'temp_refuse_reason', $remark);

                $titleKey = 'template_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'template_agree_msg_content' : 'template_refuse_msg_content';
                $msgContentParams = [
                    $userName,
                    $templateType,
                    $templateNameLink,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $templateCreateTime,
                    $approval_route_str,
                    $refuseRemark
                ];
                $emailContentKey = ($approvalResult == 1) ? 'template_agree_email_content' : 'template_refuse_email_content';
                $emailContentParams = [
                    $userName,
                    $templateType,
                    $templateNameLink,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $templateCreateTime,
                    $approval_route_str,
                    $refuseRemark
                ];
                break;
            case \Yii::$app->params['approval_type']['module_reedit']:// 签字
                $titleKey = 'module_reedit_remind_title';
                $msgContentKey = ($approvalResult == 1) ? 'module_reedit_agree_msg_content' : 'module_reedit_refuse_msg_content';
                $msgContentParams = [
                    $userName,
                    $expLink,
                    $remark,
                ];
                $emailContentKey = ($approvalResult == 1) ? 'module_reedit_agree_email_content' : 'module_reedit_refuse_email_content';
                $emailContentParams = [
                    $expLink,
                    $userName,
                    $remark,
                    ELN_URL
                ];
                break;
            case \Yii::$app->params['approval_type']['instrument_check']: // 仪器记录

                $approvalLink = '<a href="' . ELN_URL . '?route=approval&type=9" target="_blank">' . (!empty($extraData['recordType'])?$extraData['recordType'] :'record'). '</a>';
                // 如果数据里有instrument_status没有recordType就是状态修改审批，反过来就是仪器记录审批
                if (empty($extraData['recordType'])) {
                    $instrumentNewStatus = '';
                    switch ($extraData['instrument_status']) {
                        case 1:
                            $instrumentNewStatus = Yii::t('base','normal');
                            break;
                        case 2:
                            $instrumentNewStatus = Yii::t('base','suspend_use');
                            break;
                        case 3:
                            $instrumentNewStatus = Yii::t('base','instrument_repair');
                            break;
                        case 4:
                            $instrumentNewStatus = Yii::t('base','scrap');
                            break;
                        case 0:
                            $instrumentNewStatus = Yii::t('base','delete');
                            break;
                    }
                    $instrumentNewStatus = strtolower($instrumentNewStatus);
                    $titleKey = 'instrument_status_message';
                    $msgContentKey = ($approvalResult == 1) ? 'instrument_status_change_agree_msg_content' : 'instrument_status_change_refuse_msg_content';
                    $msgContentParams = [
                        $userName, // 用户
                        $instrumentNewStatus, // 仪器状态
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        $approval_route_str,
                        $node['approval_remark'], // 拒绝原因
                    ];
                    $emailContentKey = $msgContentKey;
                    $emailContentParams = [
                        $userName, // 用户
                        $instrumentNewStatus, // 仪器状态
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        $approval_route_str,
                        $node['approval_remark'], // 拒绝原因
                    ];
                }else{
                    $titleKey = 'instrument_record_check_message';
                    $msgContentKey = ($approvalResult == 1) ? 'instrument_record_check_agree_msg_content' : 'instrument_record_check_refuse_msg_content';
                    $msgContentParams = [
                        $userName, // 用户
                        strtolower(\Yii::t('base', @getVar($extraData['recordType']))), // 仪器记录
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        $approval_route_str,
                        $node['approval_remark'], // 原因
                    ];
                    $emailContentKey = ($approvalResult == 1) ? 'instrument_record_check_agree_msg_content' : 'instrument_record_check_refuse_msg_content';
                    $emailContentParams = [
                        $userName, // 用户
                        strtolower(\Yii::t('base', @getVar($extraData['recordType']))), // 仪器记录
                        @getVar($extraData['instrumentName']), // 仪器名称
                        @getVar($extraData['instrumentBatchNumber']), // 仪器设备编号
                        $approval_route_str,
                        $node['approval_remark'], // 原因
                    ];
                }
                break;
            case Yii::$app->params['approval_type']['share']: //实验分享
                //若未开启邮件通知
                if (!$approval['business_id']) {
                    return;
                }
                $shareSetting = (new ShareAccessModel())->getShareAccessById($approval['business_id']);
                if ($shareSetting['send_approval_email'] != 1) {
                    $extraData['remind_result_with_email'] = 0;
                }
                $expCodesStr = '';
                foreach ($extraData['exp_id_arr'] as $k => $expId) {
                    if ($k > 0) $expCodesStr .= ', ';
                    $exp = (new ExperimentServer())->getBaseDataById($expId);
                    //根据实验ID获取实验编号
                    $expCode = $exp['data']['exp_all_code'];
                    $expCodesStr .= '<a href="' . ELN_URL . '?exp_id=' . $expId . '" target="_blank">' . $expCode . '</a>';
                    $expTitle = $exp['data']['title'];
                    $expCreateTime = date('Y-m-d', strtotime($exp['data']['create_time']));
                }
                $msgType = 27;
                $msgUrl = ELN_URL;
                $titleKey = 'share_result_title';
                $msgContentKey = ($approvalResult == 1) ? 'share_agree_content' : 'share_refuse_content';
                $msgContentParams = [
                    $expCodesStr,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $node['approval_remark'],
                    $approval_route_str
                ];
                $emailContentKey = ($approvalResult == 1) ? 'share_agree_email_content' : 'share_refuse_email_content';
                $emailContentParams = [
                    $expCodesStr,
                    $expTitle,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $expCreateTime,
                    $node['approval_remark'],
                    $approval_route_str
                ];

                break;
            case Yii::$app->params['approval_type']['template_share_batch']: //模板分享
            case Yii::$app->params['approval_type']['template_share']: //模板分享
                //若未开启邮件通知
                if (!$approval['business_id']) {
                    return;
                }
                $tempNameStr = '';
                foreach ($extraData['temp_id_arr'] as $k => $tempId) {
                    if ($k > 0) $tempNameStr .= ', ';
                    $templateRes = (new TempleServer())->getBasicById($tempId);
                    $basicData = @getVar($templateRes['data'], []);
                    $templateName = @getVar($basicData['name'], '');
                    $templateNameLink = '<a href="' . ELN_URL . '?route=template&temp_id=' . $tempId . '&temp_type='. $basicData['type'] .'" target="_blank">' . $templateName . '</a>';
                    $tempNameStr .= $templateNameLink;
                    $templateCreateTime = date('Y-m-d', strtotime($basicData['create_time']));
                    switch ($basicData['type']) {
                        case 1:
                            $templateType = \Yii::t('msg', 'all_temp');
                            break;
                        case 2:
                            $templateType = \Yii::t('msg', 'method_temp');
                            break;
                        case 4://表格模板
                            $templateType = \Yii::t('msg', 'table_temp');
                    }
                }
                $refuseRemark = Yii::t('msg', 'temp_refuse_reason', $remark);
                $approval_route_str = $this->buildApprovalRouteByApproveId($approval['id'])['approve_route_str'];
                $msgType = 26;
                $msgUrl = ELN_URL;
                $titleKey = 'temp_share_result_title';
                $msgContentKey = ($approvalResult == 1) ? 'temp_share_agree_content' : 'temp_share_refuse_content';
                $msgContentParams = [
                    $userName,
                    $templateType,
                    $tempNameStr,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $templateCreateTime,
                    $approval_route_str,
                    $refuseRemark
                ];
                $emailContentKey = ($approvalResult == 1) ? 'temp_share_agree_email_content' : 'temp_share_refuse_email_content';
                $emailContentParams = [
                    $userName,
                    $templateType,
                    $tempNameStr,
                    CommonServer::displayUserName($userInfoArr[$toUserId]),
                    $templateCreateTime,
                    $approval_route_str,
                    $refuseRemark
                ];

                break;
            case \Yii::$app->params['approval_type']['work_order']:// 工单
                $wo_detail = (new CollaborationServer()) -> getCollaboration($approval['business_id']);
                $wo_title = $wo_detail['exp_title'];
                $wo_create_person = (new CenterInterface())->getUserByUserId($wo_detail['user_id']);
                $wo_assigned_person = $wo_detail['assigned_to'];
                switch ($extraData['wo_type']){
                    case 0:
                        $wo_type = Yii::t('base', 'create');
                        break;
                    case 1:
                        $wo_type = Yii::t('base', 'confirm');
                        break;
                    case 2:
                        $wo_type = Yii::t('base', 'solve');
                        break;
                    case 3:
                        $wo_type = Yii::t('base', 'close');
                        break;
                    case 4:
                        $wo_type = Yii::t('base', 'activate');
                        break;
                }
                $titleKey = 'collaboration_message_title';
                $msgContentKey = ($approvalResult == 1) ? 'wo_agree_content' : 'wo_refuse_content';
                $msgContentParams = [
                    $userName,
                    $wo_type,
                    ELN_URL . '?route=collaboration&collaboration_id=' . $approval['business_id'],
                    $approval['business_id'],
                    $wo_title,
                    CommonServer::displayUserName($wo_create_person),
                    $wo_assigned_person,
                    $approval_route_str,
                    $remark
                ];
                $emailContentKey = ($approvalResult == 1) ? 'wo_agree_content' : 'wo_refuse_content';
                $emailContentParams = [
                    $userName,
                    $wo_type,
                    ELN_URL . '?route=collaboration&collaboration_id=' . $approval['business_id'],
                    $approval['business_id'],
                    $wo_title,
                    CommonServer::displayUserName($wo_create_person),
                    $wo_assigned_person,
                    $approval_route_str,
                    $remark
                ];
                break;

        }

        // 发送平台消息
        $elnMsgSer = new ElnMessageServer();
        $msgTitle = \Yii::t('msg', $titleKey, [], $userLang);
        $msgContent = \Yii::t('msg', $msgContentKey, $msgContentParams, $userLang);
        $elnMsgSer->addMessage($userId, $toUserId, $msgTitle, $msgContent, 1, false, \Yii::$app->view->params['curr_company_id']);

        // 发送邮件 提醒复核人
//        $extraData = json_decode($approval['extra_data'], true);
//        if (!isset($extraData['send_email']) || !empty($extraData['send_email'])) {
//            $emailTitle = \Yii::t('msg', $titleKey, [], $userLang);
//            $emailContent = \Yii::t('msg', $emailContentKey, $emailContentParams, $userLang);
//            $sendRes = $elnMsgSer->addMail($userId, $userId, [], $emailTitle, $emailContent);
//        }
        // 邮件提醒被复核人实验的复核结果
        if (!isset($extraData['remind_result_with_email']) || !empty($extraData['remind_result_with_email'])) {
            $emailTitle = \Yii::t('msg', $titleKey, [], $userLang);
            $emailContent = \Yii::t('msg', $emailContentKey, $emailContentParams, $userLang);
            $sendRes = $elnMsgSer->addMail($userId, $toUserId, [], $emailTitle, $emailContent, 1, $userLang);
        }

        //记录本换群给群主和记录本所属人员发消息
        if($node['node_status'] == 2 && $approval['type'] == \Yii::$app->params['approval_type']['change_book_group'] && $node['next_node_id'] == ''){
            //发送系统消息和邮件(给记录本创建者)
            $extraData = json_decode($approval['extra_data'], true);
            $bookName = sprintf('<a href="%1$s" target="_blank">%2$s</a>',
                sprintf('%1$s?route=approval&type=5', ELN_URL),
                $extraData['book_name']
            );
            $userDetailCreate = (new CenterInterface())->getUserByUserId($extraData['user_id']);
            $title = \Yii::t('msg', 'notebook_msg');
            $message_content = \Yii::t('msg', 'notebook_msg_content', [
                CommonServer::displayUserName($userInfoArr[$toUserId]) ,
                $bookName,
                ELN_URL.'?book_id='.$extraData['book_id'],
                $extraData['book_code'],
                $extraData['group_name'],
                $extraData['to_group_name'],
                $extraData['operate_desc'],
            ]);

            $type = \Yii::$app->params['messageType']['move_notebook_group'];

            (new ElnMessageServer())->addMessage(\Yii::$app->view->params['curr_user_id'], $userDetailCreate['user_id'], $title, $message_content, $type, false );

            $elnMsgSer = new ElnMessageServer();
            $emailTitle =         $title = \Yii::t('msg', 'notebook_msg');
            $emailContent = $message_content;
            $elnMsgSer->addMail(\Yii::$app->view->params['curr_user_id'], [$userDetailCreate['user_id']], [], $emailTitle, $emailContent, 1, $userLang);

            //发送系统消息和邮件(给目标群主)
            $userDetailMaster = (new CenterInterface())->getUserByUserId($extraData['master_id']);
            $title = \Yii::t('msg', 'notebook_msg');
            $type = \Yii::$app->params['messageType']['move_notebook_group'];


            $elnMsgSer = new ElnMessageServer();
            $emailTitle = \Yii::t('msg', 'notebook_msg');

            $message_content = \Yii::t('msg', 'notebook_msg_content_for_master', [
                CommonServer::displayUserName($userInfoArr[$toUserId]) ,
                CommonServer::displayUserName($userDetailCreate),
                $bookName,
                ELN_URL.'?book_id='.$extraData['book_id'],
                $extraData['book_code'],
                $extraData['group_name'],
                $extraData['to_group_name'],
                $extraData['operate_desc'],
            ]);
            (new ElnMessageServer())->addMessage(\Yii::$app->view->params['curr_user_id'], $userDetailMaster['user_id'], $title, $message_content, $type, false );

            $emailContent = $message_content;
            $elnMsgSer->addMail(\Yii::$app->view->params['curr_user_id'], [$userDetailMaster['user_id']], [], $emailTitle, $message_content, 1, $userLang);
        }

        // 当前节点审批完成
        if($node['node_status'] == 2) {
            // 提醒下一节点的审批人
            $this->sendApprovalRemindMsg($node['approval_id']);
        }
    }

    /**
     * Notes: 根据审批节点生成审批路径, 内嵌对审批流程的 check, 创建审批流程时 (createApproval) 也会调用此方法, 保持同步
     * 输出 [
         'approval_nodes' => $checkResData['approval_nodes'],// 审批流程节点数组
         'approver_names' => $checkResData['approval_route'], // 此名称使用原来的 审批流程显示 - array
         'approval_route' => $approvalRoute2Show, // 审批流程显示 - str
     * ]
     * Author: zhu huajun
     * Date: 2019/10/14 17:56
     * @param $approvalNodes // 审批节点
     * @param array $approvalNodesSource // 审批设置源 按角色设置审批流程时有
     * @param int $groupId // 审批流程所在的群, 用于提示语显示
     * @return array
     */
    public function getApprovalRoute($approvalNodes, $approvalNodesSource = [], $groupId = 0, $postUserId = 0) {
        // 处理代办审核
        if (!empty($groupId)) {
            // 获取鹰群下的代办审核设置
            $dfaArr = (new GroupSettingServer())->getDfaSetting($groupId);
            if (!empty($dfaArr)) {
                // 遍历审批节点
                foreach ($approvalNodes as $key => &$node) {
                    // 统一处理审批人和合批人字段
                    foreach (['approval_user_ids', 'coapproval_user_ids'] as $field) {
                        if (!empty($node[$field])) {
                            if (!is_array($node[$field])) {
                                $node[$field] = explode(',', $node[$field]);
                            }

                            // 当前节点的原始审批人或合批人
                            $nodeUsers = $node[$field];

                            // 遍历待办审核设置
                            foreach ($dfaArr as $originalId => $delegationIds) {
                                // 如果当前节点的原始审批人或合批人中包含被代办的用户，则将代办用户加入到审批人或合批人中
                                // 注意“原始“二字，防止代办关系的传递
                                if (array_intersect($nodeUsers, $delegationIds)) { // 这里不能使用$node[$field]
                                    // 使用数组合并避免重复
                                    $node[$field] = array_unique(
                                        array_merge($node[$field], [$originalId])
                                    );
                                }
                            }
                        }
                    }
                }
                unset($node); // 销毁引用
            }
        }

        $checkRes = $this->checkApprovalNodes($groupId, $postUserId, $approvalNodes, $approvalNodesSource);
        $checkResData = $checkRes['data'];
        $approvalNodes = $checkResData['approval_nodes'];
        $approvalRoute = $checkResData['approval_route'];
        $approvalRouteList = $checkResData['approval_route_list'];

        // 过滤掉后审批流程依旧有效, 则直接返回
        if ($checkRes['status'] == 1) {
            $approvalRoute2Show = $this->approvalRoute2Show($approvalRoute);
            return $this->success([
                'approval_nodes' => $approvalNodes,
                'approver_names' => $approvalRoute, // 此名称使用原来的
                'approval_route' => $approvalRoute2Show, // 审批流程在一行显示
            ]);
        }

        // 审批流程无效后, 进行合并
        $approvalRouteNew = CompanyAuthServer::mergeApprovalRouteAndSource($approvalRouteList, $approvalNodesSource);
        $approvalRoute2Show = $this->approvalRoute2Show($approvalRouteNew);
        $approvalRouteShowTip = sprintf('%s: %s', Yii::t('base', 'approval_route'), $approvalRoute2Show);
        return $this->fail(
            $checkRes['info'] . '<br>' . $approvalRouteShowTip,
            [
                'approval_nodes' => $approvalNodes,
                'approver_names' => $approvalRouteNew, // 此名称使用原来的
                'approval_route' => $approvalRoute2Show, // 审批流程在一行显示
            ]
        );
    }

    /**
     * Note: 根据审批流程节点数组获取审批流程显示
     * @param $approvalNodes
     * @param $approvalNodesSource
     * @param string $withStr
     * @return string
     * <AUTHOR>
     * @copyright 2022/8/12
     */
    public function getApprovalRouteShowName($approvalNodes, $approvalNodesSource = [], $postGroupId = 0, $postUserId = 0, $withStr = ' -> ') {
        // 这里不是弹框, 不需要传 group_id
        // 复核时需要传 userId, 判断禁止复核自己的实验
        $approvalRouteRes = $this->getApprovalRoute($approvalNodes, $approvalNodesSource, 0, $postUserId);
        // 无论是否通过了 checkApproval, 都会有审批节点名称数组供显示
        // 1 == $approvalRouteRes['status'] &&
        $resData = @getVar($approvalRouteRes['data'], []);
        $approvalNode = @getVar($resData['approver_names'], []);
        return $this->approvalRoute2Show($approvalNode, $withStr);
    }

    /**
     * @Notes: 审批流程数组转字符串
     * 输入: [[A], [B], [C]]
     * 输出: A -> B -> C
     * @param $approvalNode
     * @param $withStr
     * @return string
     * @author: tianyang
     * @Time: 2023/3/8 14:55
     */
    public function approvalRoute2Show($approvalNode, $withStr = ' -> ')
    {
        return !empty($approvalNode) ? implode($withStr, $approvalNode) : '';
    }

    /**
     * Note: 验证审批流程是否有效, 过滤掉不符合的用户, 此方法 eln 5.3.2 改为 private, 内嵌在  getApprovalRoute 中
     * 若过滤后的用户满足审批流程的创建, 则返回审批节点和其显示 --- 正常用户, 用户禁止登录
     * 若不满足审批流程的创建, 则检查是否各个节点都有用户, 若有则返回 --- 用户已注销
     * 若存在节点只是占位 --- 找不到鹰群, 找不到部门, 找不到对应人 (getApprovalRoute 中合并)
     *
     * 输出 : [
         'approval_nodes' => $newApprovalNodes, // 审批流程节点数组
         'approval_route' => $approvalRoute // 审批流程显示 - array
     * ]
     * @param $approvalNodes
     * @param $postGroupId
     * @return array
     * <AUTHOR>
     * @date 2022/7/20 16:56
     */
    private function checkApprovalNodes($postGroupId = 0, $postUserId = 0, $approvalNodes = [], $approvalNodesSource = []) {
        $tipMap = [
            'account_closed' => sprintf('<span style="color: red">(%s)</span>', Yii::t('base', 'account_closed')),
            'not_activate_eln' => sprintf('<span style="color: red">(%s)</span>', Yii::t('base', 'not_activate') . ' ELN'),
            'not_witness_own_exp' => sprintf('<span style="color: red">(%s)</span>', Yii::t('base', 'forbidden_own_exp')),
        ];

        // 拿到审批人合批人所有信息, 状态为 0, 1, 2
        $allUserIds = [];
        foreach ($approvalNodes as $node) {
            if (!empty($node['approval_user_ids'])) {
                $allUserIds = array_merge($allUserIds, $node['approval_user_ids']);
            }
            if (!empty($node['coapproval_user_ids'])) {
                $allUserIds = array_merge($allUserIds, $node['coapproval_user_ids']);
            }
        }

        // 处理联系人(联系人用于在审批流程无效时提醒用户，大部分情况下审批流程应该是有效的，这一步可以考虑放到后面，出现了无效情况再处理)
        $contactUserId = 0;
        if ($postGroupId > 0) {
            // 联系群主, 返回的是数组, 一个群只有一个群主
            $masterUserIds = CompanyAuthServer::getUserIdsByRoleIds([2], $postGroupId);
            if (!empty($masterUserIds)) {
                $contactUserId = $masterUserIds[0];
                $allUserIds[] = $contactUserId;
            }
        } elseif (-1 == $postGroupId) {
            // 联系系统管理员, 系统管理员会有多个
            $contactUserId = -1;
        }

        $allUserIds = array_unique($allUserIds);
        $userDetailArr = [];
        if (!empty($allUserIds)) {
            $usersRes = (new CenterInterface())->userDetailsByUserIds($allUserIds);
            $userDetailArr = yii\helpers\ArrayHelper::index($usersRes, 'id');
        }

        $isApprovalValid = true;
        $newApprovalNodes = [];
        $approvalRoute = [];
        $approvalRouteList = [];
        foreach ($approvalNodes as $node) {
            $approval_user_ids = @getVar($node['approval_user_ids'], []);
            $coapproval_user_ids = @getVar($node['coapproval_user_ids'], []);

            $newNode = [];
            $newNode['approval_user_ids'] = [];
            $newNode['coapproval_user_ids'] = [];

            // 获取审批人及其显示
            $nodeApprovalNameArr = [];
            foreach ($approval_user_ids as $uid) {
                $user = @getVar($userDetailArr[$uid], null);
                $userName = $user ? CommonServer::displayUserName($user) : '';
                $userRemark = '';

                if ($user) {
                    if ($user['status'] == 0) {
                        // 用户是否注销
                        $userRemark = $tipMap['account_closed'];
                    } elseif (!in_array(2, $user['apply'])) {
                        // 用户是否开通 ELN 应用
                        $userRemark = $tipMap['not_activate_eln'];
                    } elseif ($postUserId && $uid == $postUserId) {
                        // 不能复核自己的实验
                        $userRemark = $tipMap['not_witness_own_exp'];
                    } else {
                        // 用户正常或禁止登录, 收集  in_array($user['status'], [1, 2])
                        $newNode['approval_user_ids'][] = $uid;
                    }
                }
                $nodeApprovalNameArr[] = $userName . $userRemark;
            }

            // 获取合批人及其显示
            $nodeCoapprovalNameArr = [];
            foreach ($coapproval_user_ids as $uid) {
                $user = @getVar($userDetailArr[$uid], null);
                $userName = $user ? CommonServer::displayUserName($user) : '';
                $userRemark = '';

                if ($user) {
                    if ($user['status'] == 0) {
                        // 用户是否注销
                        $userRemark = $tipMap['account_closed'];
                    } elseif (!in_array(2, $user['apply'])) {
                        // 用户是否开通 ELN 应用
                        $userRemark = $tipMap['not_activate_eln'];
                    } elseif ($postUserId && $uid == $postUserId) {
                        // 不能复核自己的实验
                        $userRemark = $tipMap['not_witness_own_exp'];
                    } else {
                        // 用户正常或禁止登录, 收集  in_array($user['status'], [1, 2])
                        $newNode['coapproval_user_ids'][] = $uid;
                    }
                }
                $nodeCoapprovalNameArr[] = $userName . $userRemark;
            }

            /**
             * 判断是否审批流程有效
             * 1 无审批人 && 无合批人
             * 2 有审批人 -> 无审批人
             * 3 有合批人 -> 无合批人
             */
            $approvalNodeInvalid = empty($approval_user_ids) && empty($coapproval_user_ids);
            $approvalUserInvalid = !empty($approval_user_ids) && empty($newNode['approval_user_ids']);
            $coapprovalUserInvalid = !empty($coapproval_user_ids) && empty($newNode['coapproval_user_ids']);
            if ($approvalNodeInvalid || $approvalUserInvalid || $coapprovalUserInvalid) {
                $isApprovalValid = false;
            }


            // 存入新的节点数组
            $newApprovalNodes[] = $newNode;

            // 审批和合批直接是否连接 A & B 形式
            $nodeApprovalNameStr = implode(',', $nodeApprovalNameArr);
            $nodeCoapprovalNameStr = implode(',', $nodeCoapprovalNameArr);
            $withStr = '';
            if (!empty($nodeApprovalNameStr) && !empty($nodeCoapprovalNameStr)) {
                $withStr = ' & ';
            }

            // 存入审批流程显示数组
            $approvalRoute[] = $nodeApprovalNameStr . $withStr . $nodeCoapprovalNameStr;
            $approvalRouteList[] = [
                'approval_name_show' => $nodeApprovalNameStr,
                'coapproval_name_show' => $nodeCoapprovalNameStr,
            ];
        }

        if (empty($approvalNodes) && !empty($approvalNodesSource)) {
            $isApprovalValid = false;
        }

        // 审批流程有效
        if ($isApprovalValid) {
            return $this->success([
                'approval_nodes' => $newApprovalNodes,
                'approval_route' => $approvalRoute,
                'approval_route_list' => $approvalRouteList,
            ]);
        }

        // 审批流程无效, 构建联系人
        $failInfo = '';
        if ($contactUserId > 0) {
            // 联系群主
            $groupMaster = @getVar($userDetailArr[$contactUserId], null);
            $groupMasterName =  $groupMaster ? CommonServer::displayUserName($groupMaster) : '';
            $failInfo = sprintf('%s(%s)', \Yii::t('base', 'invalid_approval'), $groupMasterName);
        } elseif (-1 == $contactUserId) {
            // 联系系统管理员
            $failInfo = \Yii::t('base', 'invalid_approval_contact_sys_admin');
        }

        return $this->fail(
            $failInfo,
            [
                'approval_nodes' => $newApprovalNodes,
                'approval_route' => $approvalRoute,
                'approval_route_list' => $approvalRouteList,
            ]
        );
    }

    /**
     * @Notes: 获取审批详情
     * @param $approvalId
     * @return array|ApprovalModel|ActiveRecord
     * @author: tianyang
     * @Time: 2023/3/9 15:00
     */
    public function  getApprovalInfo($approvalId) {
        return ApprovalModel::find()
            ->where(['id' => $approvalId])->asArray()->one();
    }

    /**
     * @Notes: 获取审批流程详情
     * @param $approvalIds
     * @return array|ApprovalNodeModel[]|ActiveRecord[]
     * @author: tianyang
     * @Time: 2023/3/10 10:22
     */
    public function getApprovalNodesById($approvalIds) {
        return ApprovalNodeModel::find()
            ->where(['approval_id' => $approvalIds])
            ->orderBy('id asc')->asArray()->all();
    }


    /**
     * @Notes: 构造审批日志记录, 按时间顺序
     * @param $approvalNodes
     * @return array
     * @author: tianyang
     * @Time: 2023/3/9 18:16
     */
    public function approvalNodes2Log($approvalNodes) {
        $approvalLog = [];

        if (empty($approvalNodes)) {
            return $approvalLog;
        }

        foreach ($approvalNodes as $node) {
            // 当前节点审批状态（0->未开始；1->进行中；2->通过；3->拒绝）
            if (!in_array($node['node_status'], [2, 3])) {
                continue;
            }

            if (!empty($node['approver_id'])) {
                $approvalLog[] = [
                    'node_id'             => $node['id'],
                    'log_approver_id'     => $node['approver_id'],
                    'log_approval_time'   => $node['approval_time'],
                    'log_approval_remark' => $node['approval_remark'],
                    'log_approval_status' => $node['approval_status'],
                ];
            }

            if (!empty($node['coapprover_id'])) {
                $approvalLog[] = [
                    'node_id'             => $node['id'],
                    'log_approver_id'     => $node['coapprover_id'],
                    'log_approval_time'   => $node['coapproval_time'],
                    'log_approval_remark' => $node['coapproval_remark'],
                    'log_approval_status' => $node['coapproval_status'],
                ];
            }
        }

        if (!empty($approvalLog)) {
            array_multisort(array_column($approvalLog, 'log_approval_time'), SORT_DESC, $approvalLog);
        }

        return $approvalLog;
    }

    /**
     * @param
     * @param string $curr_node_id
     * @param bool[] $set_params
     * @return array
     * @auther: dx
     * @date:2023/1/5
     */
    /** 根据审批ID获取审批路线arr
     * @param string $approval_id 审批id
     * @param bool[] $set_params 构建设置审批路线的参数 [
     *  'show_node_no'=是否在路线后展示节点序号（从1开始）,
     *  'display_username'=在显示用户real_name后，是否显示用户name
     * ]
     * @return array
     * @auther: dx
     * @date:2023/1/5
     */
    public function getApprovalRouteByApprovalId(
        $approval_id,
        $set_params = ['display_username' => true]
    ) {
        $display_username = $set_params['display_username'];//是否展示用户

        $approval_nodes = ApprovalNodeModel::find()
            ->where([
                'approval_id' => $approval_id,
                'status' => 1,
            ])->orderBy(['id' => SORT_ASC])->asArray()->all();
        if (empty($approval_nodes)) {
            return $this->fail('no approval node found by provided approval ID!!!');
        }

        // 读取当前审批节点, 申领流程是否完结 null 未完结 0 拒绝, 1 通过
        $curr_node_id = null;
        $approval_status = -1;
        foreach ($approval_nodes as $approval_node) {
            if (1 == $approval_node['node_status']) {
                $curr_node_id = $approval_node['id'];
            }
            $approval_status = $approval_node['approval_status'];
        }


        /* 获取所有用户信息，并将审批用户id字段由string改为array */
        $all_uids = [];
        array_walk($approval_nodes, function (&$approval_node) use (&$all_uids) {
            $approver_str = $approval_node['approver_ids'];
            $co_approver_str = $approval_node['coapprover_ids'];

            $approval_node['approver_ids_arr'] = [];
            $approval_node['coapprover_ids_arr'] = [];
            if (!empty($approver_str)) {
                $approvers = explode(',', $approver_str);
                $approval_node['approver_ids_arr'] = $approvers;//审批人id数组形式
                $all_uids = array_merge($all_uids, $approvers);
            }
            if (!empty($co_approver_str)) {
                $co_approvers = explode(',', $co_approver_str);
                $approval_node['coapprover_ids_arr'] = $co_approvers;//合批人id数组形式
                $all_uids = array_merge($all_uids, $co_approvers);
            }
        });
        $all_uids = array_unique($all_uids);
        $all_userInfos = (new CenterInterface())->userDetailsByUserIds($all_uids);
        // $user_lang = CommonServer::getUserLang($all_userInfos[0]);//将第一个用户的语言视作语言设置
        $userInfo_map = yii\helpers\ArrayHelper::index($all_userInfos, 'user_id');
        /*-end 获取所有用户信息 */

        /* 遍历审批节点，构造审批人和合批人姓名 列表 */
        $approve_user_names = [];
        foreach ($approval_nodes as $idx => $approval_node) {
            $node_approver_names = [];//每个节点审批人姓名列表
            $node_co_approver_names = [];//每个节点合批人姓名列表
            if (!empty($approval_node['approver_ids_arr'])) {
                array_walk($approval_node['approver_ids_arr'], function ($approver_id) use (&$node_approver_names, $userInfo_map, $display_username) {
                    $userInfo1 = $userInfo_map[$approver_id];
                    $user_name1 = $display_username ? CommonServer::displayUserName($userInfo1) : CommonServer::displayRealName($userInfo1);
                    $node_approver_names[] = $user_name1;
                });
            }

            if (!empty($approval_node['coapprover_ids_arr'])) {
                array_walk($approval_node['coapprover_ids_arr'], function ($co_approver_id) use (&$node_co_approver_names, $userInfo_map, $display_username) {
                    $userInfo2 = $userInfo_map[$co_approver_id];
                    $user_name2 = $display_username ? CommonServer::displayUserName($userInfo2) : CommonServer::displayRealName($userInfo2);
                    $node_co_approver_names[] = $user_name2;
                });
            }

            $node_usernames_str = join(',', $node_approver_names);
            if (!empty($node_co_approver_names)) {
                $node_usernames_str .= sprintf(' & %1$s', join(',', $node_co_approver_names));
            }

            /*需要修改节点字符串的样式*/
            /*如果当前审批节点，修改字体样式*/
            if (!empty($curr_node_id) && $curr_node_id == $approval_node['id']) {//当前节点是正在审批的节点
                $node_usernames_str = sprintf(
                    '<span style="font-weight: bolder;color: #1388ff">%1$s</span>',
                    $node_usernames_str
                );
            }

            $approve_user_names[] = $node_usernames_str;
        }
        unset($approval_node);
        /*-end 遍历审批节点 */

        return $this->success([
            'approval_status'    => $approval_status,
            'approver_names_arr' => $approve_user_names,
            'nodes_arr'          => $approval_nodes,
        ]);
    }

    /** 通过审批id构建审批路径字符串
     * @param $approval_id
     * @return array
     * @auther: dx
     * @date:2023/1/5
     */
    public function buildApprovalRouteByApproveId($approval_id) {
        $approve_res = $this->getApprovalRouteByApprovalId($approval_id);
        $nodes_arr = [];
        $approve_route_str = '';
        if (empty($approve_res['status'])) {
            yii::info('no approval route got by provided approval id');
            return [
                'nodes_arr' => $nodes_arr,
                'approve_route_str' => $approve_route_str,
            ];
            //throw (new yii\base\Exception('no approval route got by provided approval id'));
        }
        $approve_res = $approve_res['data'];

        $approve_route = $approve_res['approver_names_arr'];//各节点审批人列表
        $nodes_arr = $approve_res['nodes_arr'];//审批节点数据
        $approve_route_str = join(' -> ', $approve_route);

        return [
            'nodes_arr' => $nodes_arr,
            'approve_route_str' => $approve_route_str,
        ];
    }

    /**
     * Note: 根据代办审核设置，将代办人加入审批流程
     * Author: zhhj
     * Date: 2025/3/13 16:47
     * @param int $groupId
     * @param array $originalApproverIds 原审批人ID集合
     * @param array $delegationUserIds 代理人ID集合
     * @return void
     */
    public function applyDelegation($groupId, $originalApproverIds, $delegationUserIds)
    {
        // 获取进行中的审批流程
        $approvalList = ApprovalModel::find()
            ->where([
                'approval_status' => null,
                'status' => 1
            ])->asArray()->all();

        // 遍历
        foreach ($approvalList as $approval) {
            // 如果审批关联了鹰群
            if ($this->_isApprovalRelatedToGroup($approval, $groupId)) {
                $this->_mergeApproversWithDelegation($approval['id'], $originalApproverIds, $delegationUserIds);
            }
        }
    }

    /**
     * Note: 判断审批流程是否和指定鹰群相关
     * Author: zhhj
     * Date: 2025/3/14 16:35
     * @param array $approval 审批流程数据
     * @param int $groupId 鹰群ID
     * @return bool
     */
    private function _isApprovalRelatedToGroup($approval, $groupId)
    {
        // 审批中直接记录了鹰群ID
        if (!empty($approval['group_id'])) {
            return $approval['group_id'] == $groupId;
        }

        // 处理实验的分享审批
        if ($approval['type'] == \Yii::$app->params['approval_type']['share']) {
            $extraData = json_decode($approval['extra_data'], true);
                if (!empty($extraData['exp_id_arr'])) {
                    // 通过第1个实验ID获取实验所属鹰群ID
                    $expBaseData = (new ExperimentServer())->getExpBaseData($extraData['exp_id_arr'][0]);
                    return $expBaseData['group_id'] == $groupId;
                }
        }

        return false;
    }

    /**
     * Note: 合并代理人到审批流程
     * Author: zhhj
     * Date: 2025/3/14 17:03
     * @param int $approvalId 审批流程ID
     * @param array $originalApproverIds 原审批人ID集合
     * @param array $delegationUserIds 代理人ID集合
     * @return void
     */
    private function _mergeApproversWithDelegation($approvalId, $originalApproverIds, $delegationUserIds)
    {
        // 获取未开始和进行中的审批节点
        $approvalNodes = ApprovalNodeModel::find()
            ->where([
                'approval_id' => $approvalId,
                'node_status' => [0, 1], // 未开始和进行中的节点
                'status' => 1,
            ])->orderBy(['id' => SORT_ASC])->all();

        // 遍历节点
        foreach ($approvalNodes as $node) {
            // 标记节点是否需要更新
            $nodeNeedsUpdate = false;

            // 处理审批人和合批人
            $fieldsToProcess = ['approver_ids', 'coapprover_ids'];
            foreach ($fieldsToProcess as $field) {
                if (!empty($node[$field])) {
                    // 当前审批人
                    $currentIds = explode(',', $node[$field]);

                    // 包含原审批人时，追加代理人
                    if (array_intersect($currentIds, $originalApproverIds)) {
                        // 合并代理人并去重
                        $currentIds = array_unique(array_merge(
                            $currentIds,
                            $delegationUserIds
                        ));

                        // 更新审批人
                        $node[$field] = implode(',', $currentIds);
                        $nodeNeedsUpdate = true;
                    }
                }
            }

            // 如果节点需要更新，则保存
            if ($nodeNeedsUpdate) {
                $node->save();
            }
        }
    }
}
