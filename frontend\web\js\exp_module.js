define(function(require) {
    function saveExperiment(success) {
        require(['save_exp'], function (save_exp) {
            save_exp.saveExperiment({
                autoSave: true,
                success: success,
                error: function () {
                    $.showAlert('Save failed.');
                }
            });
        });
    }

    //用户模板
    var module = {
        // open: function(dom){
        //
        //     module.expTemplateList();
        //
        // },

        open: function (dom) {
            var type = dom.attr('data-type');

            if (type === 'module') {
                module.expTemplateList();
            }
            if (type === 'manage_module') {
                module.manageTemplateList();
            }

        },

        //获取组件信息 打开模板
        getModuleAssembly: function(id, temp_type){
            if (temp_type == 2) {// 方法模板
                require(['tool'], function (expTool) {
                    expTool.viewsonmodule(id);
                });
                return;
            }

            var temp_type = temp_type || '1';
            if(temp_type == 1 || temp_type == 3) { // 全文模板和系统模板
                var url = '/?r=template/view-temp&id=' + id + '&temp_type='+temp_type;
            } else {
                var url = '/?r=template/get-temp&id=' + id + '&temp_type=' + temp_type;
            }

            var currentTab = $('.my_exp_detial.module[data-id="' + id + '"]');
            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            $.ajaxFn({
                type: 'GET',
                url: url,
                success: function(data){
                    if(data.status == 1){

                        //子模板
                        if(temp_type == 2){
                            var html = '<div class="sub_temp_pop">';
                            html += '<div class="clear"><div class="fl">'+mainLang('sub_name')+'：</div><div class="sub_temp_con">' + data.data.name + '</div></div>';
                            html += '<div class="clear"><div class="fl">'+mainLang('sub_desc')+'：</div><div class="sub_temp_con">' + data.data.descript + '</div></div>';
                            html += '<div class="clear"><div class="fl">'+mainLang('sub_content')+'：</div><div class="sub_temp_con">' + data.data.content + '</div></div>';
                            html += '</div>';
                            //require(['exp_data', 'exp_add']); //stechiometry
                            var html = _.template(require('text!popup/sub_temp.html'),{variable: 'data'})({'html': html});
                            $('body').append(html);
                            $('.sub_temp_modal').modal('show');
                            return;
                        }

                        if(temp_type == 4){
                            var html = '<div class="sub_temp_pop">'
                            html += '<div class="clear"><div class="fl">'+mainLang('sub_name')+'：</div><div class="sub_temp_con">' + data.data.name + '</div></div>';
                            html += '<div class="clear"><div class="fl">'+mainLang('sub_desc')+'：</div><div class="sub_temp_con">' + data.data.title + '</div></div>';
                            html += '<div class="clear"><div class="fl">'+mainLang('sub_content')+'：</div><div class="sub_temp_con"><img src="'+data.data.img+'" ></div></div>';
                            html += '</div>';
                            //require(['exp_data', 'exp_add']); //stechiometry
                            var html = _.template(require('text!popup/sub_temp.html'),{variable: 'data'})({'html': html});
                            $('body').append(html);
                            $('.sub_temp_modal').modal('show');
                            return;
                        }

                        $('.my_exp_list').removeClass('on');
                        var data =data.data;
                        $('.layout_right_box .exp_conetnt').html(data.expFile);
                        var name = data.moduleName;

                        /*处理标题*/
                        $('.my_exp_list, .my_exp_detial').removeClass('on');
                        var currentTab = $('.my_exp_detial.module[data-id="'+id+'"]');
                        if(currentTab.length > 0){
                            currentTab.addClass('on');
                        }else{
                            $('.exp_title').append('<a class="iblock on my_exp_detial module" type="module" data-id="'+ id +'" title="'+ name +'"><span class="name">'+ name +'</span><span class="close"></span></a>');
                        }
                        handleExpTitle();
                        $('.exp_main').show();
                        $('.bg_white').empty();

                        $('.click_module_tool').attr('data-id', id);
                        require(['exp_detial'], function(exp_detial){
                            exp_detial();
                        });
                        //$(window).scrollTop(0);
                    }
                }
            })
        },

        setSystemTemp: function(){
            var id = this.getId();
            if(id.length == 0){
                $.showAlert(mainLang('select_temp_first'));
                return;
            }
            $.ajaxFn({
                url: ELN_URL+'?r=template/set-system-temp',
                data: {
                    id: id
                },
                success: function(data){
                    if(data.status == 1){
                        $.showAlert(mainLang('set_normal_temp_success'));
                    }
                }
            })
        },

        expTemplateList: function(page){
            var that = this;

            // add by hkk 2020/6/23 初次打开走新的标签路径
            require('get_html').genExpTempListPage('exp_module.pageFnCallBack'); // 传入pageFn回调函数
        },

        manageTemplateList: function(page){
            var that = this;

            // add by hkk 2020/6/23 初次打开走新的标签路径
            require('get_html').genManageTempListPage('exp_module.pageFnManageCallBack'); // 传入pageFn回调函数
        },

        recycleTemplateList: function(page){
            var that = this;

            // add by hkk 2020/6/23 初次打开走新的标签路径
            require('get_html').genRecycleTempListPage('exp_module.pageFnRecycleCallBack'); // 传入pageFn回调函数
        },

        templateApprovalConfig: function(page){
            var that = this;

            // add by hkk 2020/6/23 初次打开走新的标签路径
            require('get_html').genTemplateApprovalConfigPage('exp_module.pageFnApprovalConfigCallBack'); // 传入pageFn回调函数
        },

        shareModule: function(obj){
//            var id = this.getId();
//            if(id.length == 0){
//                $.showAlert('请选择模板');
//            }
        },

        copyModule: function(obj){
            var id = this.getId();
            if(id.length == 0){
                $.showAlert(mainLang('select_temp'));
            }
        },

        delModule: function(obj){
            var id = [obj.attr('temp-id')];
                //this.getId();
//            if(id.length == 0){
//                $.showAlert('请选择模板');
//                return;
//            }
            $.showContent('warning', mainLang('move_recycle_tips'), mainLang('confirm_move_recycle'), function(){
                $.ajaxFn({
                    url: ELN_URL+'?r=template/del-temp',
                    data: {
                        id: id
                    },
                    success: function(data){
                        if(data.status == 1){
                            for(var i=0; i<id.length; i++){
                                var pageBox = $('.exp_conetnt.active .page_box');
                                pageBox.trigger('currentPage');
                            }
                            data.data && $.showAlert(data.data);
                            $.closeModal();
                            // $('.exp_conetnt.active .eln_setting_btn.changeManageTab.on').trigger('click');
                            $('.exp_conetnt.active .search-manage-template-list').trigger('click'); // 修改为点击搜索按钮更新审批管理页面 bug#33637 mod dx
                            require(['tab'], function (tab) {
                               var tagId = tab.getTag('getExpTempContent', [id + '']);
                               if (tagId) {
                                   tab.closeThisTag(tagId);
                               }
                            });
                        }
                    }
                });
            });
        },
        //批量移入回收站
        delModuleBatch: function(obj){
            var id = this.getId();
            if(id.length == 0){
                $.showAlert(mainLang('select_temp_first'));
                return;
            }

            $.showContent('warning', mainLang('move_recycle_tips'), mainLang('confirm_move_recycle'), function(){
                $.ajaxFn({
                    url: ELN_URL+'?r=template/del-temp',
                    data: {
                        id: id
                    },
                    success: function(data){
                        if(data.status == 1){
                            for(var i=0; i<id.length; i++){
                                $('.user_module_list tr[data-id="'+ id[i] +'"]').remove();
                            }
                            data.data && $.showAlert(data.data);
                            $.closeModal();
                            // $('.exp_conetnt.active .eln_setting_btn.changeManageTab.on').trigger('click');
                            $('.exp_conetnt.active .search-manage-template-list').trigger('click');// 修改为点击搜索按钮更新审批管理页面 bug#33637 mod dx
                            require(['tab'], function (tab) {
                                var tagId = tab.getTag('getExpTempContent', [id + '']);
                                if (tagId) {
                                    tab.closeThisTag(tagId);
                                }
                            });
                        }
                    }
                });
            });
        },

        //还原
        restoreTemplate: function(obj){
            var id = [obj.attr('temp-id')];
            $.showContent('warning', mainLang('recycle_module_tips'),  mainLang('recycle_module_confirm'), function(){
                $.ajaxFn({
                    url: ELN_URL+'?r=recycle/restore-template',
                    data: {
                        temp_id: obj.data('parentid')
                    },
                    success: function(data){
                        if(data.status == 1){
                            for(var i=0; i<id.length; i++){
                                $('.recycle_module_list tr[data-id="'+ id[i] +'"]').remove();
                            }
                            data.data && $.showAlert( mainLang('recycle_success'));
                            $.closeModal();
                            $('.search_recycle_template').click();
                        }
                    }
                })
            });
        },
        //批量还原
        restoreTemplateBatch: function(obj){
            var id = [];
            $('.recycle_module_list .template-checkbox:checked').each(function(){
                id.push($(this).val());
            });
            if(id.length === 0){
                $.showAlert(mainLang('select_temp_first'));
                return;
            }

            $.showContent('warning', mainLang('recycle_module_tips'), mainLang('recycle_module_confirm'), function(){
                $.ajaxFn({
                    url: ELN_URL+'?r=recycle/restore-template',
                    data: {
                        temp_id: id
                    },
                    success: function(data){
                        if(data.status == 1){
                            for(var i=0; i<id.length; i++){
                                $('.recycle_module_list tr[data-id="'+ id[i] +'"]').remove();
                            }
                            data.data && $.showAlert(mainLang('recycle_success'));
                            $.closeModal();
                            $('.search_recycle_template').click();
                        }
                    }
                })
            });
        },



        refreshTable: function () {
            var data = {
                page: $('.exp_conetnt.active .current.page-btn').text(),
                limit: $('.exp_conetnt.active .page_box').data('limit'),
                needUpdateAllPage: 0,
                module_keywords: $('.exp_conetnt.active #module_keywords').val(),
                module_type: $('.exp_conetnt.active #module_type').val(),
                module_from: $('.exp_conetnt.active #module_from').val()
            };

            $.ajaxFn({
                url: '/?r=template/template-list',
                data: data,
                success: function (res) {
                    if (res.status === 1) {
                        $('.exp_conetnt.active .templates_table').html(res.data.listData)
                        module.pageFn();
                    }
                }
            });
        },

        setCompanyModule: function (obj) {
            var id = obj.attr('temp-id');
            var status = obj.attr('data-val');
            var title = mainLang('set_is_company_tips');
            var content = mainLang('confirm_set_is_company');
            var icon_title = mainLang('set_is_company');

            if(status == 1) {
                title = mainLang('cancel_is_company_tips');
                content = mainLang('confirm_cancel_is_company');
                icon_title = mainLang('set_is_company');
                status = 0;
            } else {
                 status = 1;
                 icon_title = mainLang('cancel_is_company');
            }

            $.ajaxFn({
                url: ELN_URL + '?r=template/get-template-approval-setting',
                data: {
                    operate: 'create',
                    template_id: Array(id),
                    to_company_status: status,
                }
            }).then(function (ret) {
                // console.log(ret);
                if (ret.status == 1) {
                    var needApproval = ret.data.need_approval;
                    var approval_route = ret.data.approval_route;
                    if (needApproval && approval_route.length > 0) {    //需要审批，弹窗添加审批人提醒
                        content += '<br>' + mainLang('approver') + ': ' + approval_route.join(' -> ');
                    }
                    // bug#34467 plug：设置企业模板需要审批。英文状态下，将模板设置为企业模板或者取消企业模板的提示语弹框内字体比较大
                    $.showContent('warning', title, '<div style="font-size:13px;">'+content+'</div>', function () {
                        that.setCompanyTemp({
                            temp_id: Array(id), to_company_status: status
                        }, obj);
                    });
                }
            });


        },

        /**发送请求，修改模板企业状态
         * @param post_params object {temp_id: id, to_company_status: status, needApproval: needApproval}
         */
        setCompanyTemp: function (post_params, obj) {
            if (typeof post_params.temp_id === 'undefined' ||
                typeof !post_params.to_company_status === 'undefined'
            ) {
                throw 'Invalid params passed to send set company!!!';
            }
            $.ajaxFn({
                url: ELN_URL + '?r=template/set-company-temp',
                data: {
                    id: post_params.temp_id,
                    status: post_params.to_company_status,
                },
                success: function (data) {
                    if (data.status == 1) {
                        //如果不需要审批，直接返回
                        if (!data.data.need_approval) {
                            $.showAlert(mainLang('success'));
                            obj.attr('data-val', post_params.to_company_status);
                            obj.attr('title', mainLang('cancel_company_temp_audit'));
                            $(obj).toggleClass("company-ico").toggleClass("cancel-company-ico");
                            $.closeModal();
                            return;
                        }
                        // 需要审批，增加取消审批按钮
                        $.showAlert(mainLang('audit_submitted'));
                        // 增加取消审批按钮
                        var cancel_btn = `
<a href="javascript:void(0)" class="cancel-temp-audit table-ico" 
    temp-id="${data.data.need_approval_temp_arr}" title="${mainLang('cancel_company_temp_audit')}">                     
</a>`;
                        obj.siblings('.del_module_temp').after(cancel_btn); //添加取消审批按钮
                        obj.hide(); //隐藏设为企业模板按钮
                        $.closeModal();
                    }
                }
            });
        },


        setCompanyModuleBatch: function(obj){
            var id = this.getId();
            if(id.length == 0){
                $.showAlert(mainLang('select_temp_first'));
                return;
            }

            var status = obj.attr('data-val');
            var title = mainLang('set_is_company_tips');
            var content = mainLang('confirm_set_is_company');
            var icon_title = mainLang('set_is_company');


            if(status == 1) {
                title = mainLang('cancel_is_company_tips');
                content = mainLang('confirm_cancel_is_company');
                icon_title = mainLang('set_is_company');
                status = 0;
            } else {
                status = 1;
                icon_title = mainLang('cancel_is_company');
            }

            // bug#34467 plug：设置企业模板需要审批。英文状态下，将模板设置为企业模板或者取消企业模板的提示语弹框内字体比较大
            $.showContent('warning', title, '<div style="font-size:13px;">'+content+'</div>', function(){
                $.ajaxFn({
                    url: ELN_URL+'?r=template/set-company-temp',
                    data: {
                        id: id,
                        status: status
                    },
                    success: function(data){
                        if(data.status == 1){
                            $.showAlert(mainLang('success'));
                            $.closeModal();
                            $('.exp_conetnt.active .current.page-btn').trigger('click')
                        }
                    }
                })
            });

        },

        showTransferModal:function (dom)
        {
            $('.follow_tip').remove();
            if ($('.transfer_module_modal').length == 0) {
                var temp = _.template(require('text!module/transfer_module.html'), {
                    variable: 'data',
                })($(dom).attr('data-owner_id'));
                // 向html中添加这个div区块
                $('body').append(temp);
            }
            $('.transfer_module_modal').modal('show');
        },

        transferModuleBatch:function (obj){
            var id = this.getId();
            if(id.length == 0){
                $.showAlert(mainLang('select_temp_first'));
                return;
            }
            that.showTransferModal($(this));
            //将此模板的id绑定到确定按钮上
            $('.transfer_module_modal .transfer_submit').attr('data-id',id);
        },

        hideShowTemp: function(selector) {
            var tempId = selector.attr('data-id');
            var isHide = selector.attr('data-hide');
            var title, content;

            if (isHide === '1') {
                title = mainLang('show_temp');
                content = mainLang('show_temp_content');
            } else {
                title = mainLang('hide_temp');
                content = mainLang('hide_temp_content');
            }

            var data = {
                tempId: tempId,
                isHide: isHide,
            }
            $.showContent('warning', title, content, function(){
                $.ajaxFn({
                    url: '/?r=template/hide-or-show-template',
                    data: data,
                    success: function (res) {
                        if (res.status === 1) {
                            $.closeModal();
                            $.showAlert(mainLang('success'));
                            if (isHide === '1') {
                                selector.removeClass('temp_hide');
                                selector.addClass('temp_show');
                                selector.attr('data-hide', 0);
                                selector.attr('title', mainLang('hide_temp'));
                            } else {
                                selector.removeClass('temp_show');
                                selector.addClass('temp_hide');
                                selector.attr('data-hide', 1);
                                selector.attr('title', mainLang('show_temp'));
                            }
                        }
                    }
                })
            });
        },

        getId: function(){
            var id = [];
            $('.exp_conetnt.active .user_module_list .module_check:checked').each(function(){
                id.push($(this).val());
            });
            return id;
        },
        //ajax分页插件
        pageFn: function(page) {
            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;
            var refreshFun = function (page_index, jq, isRefreshPagination) {
                that.limit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                
                // 基本分页数据
                var newData = {
                    page: page_index + 1,
                    limit: that.limit,
                    needUpdateAllPage: 0,
                    type: 'shareMeTemp'
                };
                
                // 使用collectTemplateSearchParams函数收集所有查询条件
                var searchParams = module.collectTemplateSearchParams();
                
                // 合并查询条件到请求数据
                $.extend(newData, searchParams);
                
                // 确保module_from参数也被包含
                if ($('.exp_conetnt.active #module_from').length > 0) {
                    newData.module_from = $('.exp_conetnt.active #module_from').val();
                }
                
                $.ajaxFn({
                    url: ELN_URL + '?r=template/template-list',
                    data: newData,
                    type: 'post',
                    success: function (data) {
                        if (data.status == 1) {
                            $('.exp_conetnt.active').removeClass('search');
                            $('.exp_conetnt.active .templates_table').html(data.data.listData);
                            that.pageData = page_index + 1;
                            module.pageFn();
                            var pageBox = $('.exp_conetnt.active .page_box');
                            pageBox.attr('data-num', pageBox.attr('data-num') - 1);
                            // 是否刷新Pagination
                            if(isRefreshPagination) {
                                pageBox.pagination(pageBox.attr('data-num'), opt);
                            }
                        }
                    }
                }, null, true);
            };
            var opt = {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function (page_index, jq) {
                    refreshFun(page_index, jq, false);
                },
                items_per_page: that.limit || pageBox.attr('max-num') || 15
            };
            pageBox.pagination(pageBox.attr('data-num'), opt);
            // 定义 'currentPage' 自定义事件用来刷新页面数据 bug 36392
            pageBox.on('currentPage', function () {
                refreshFun(page - 1, null, true);
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            });
        },

        //ajax分页插件
        pageFnManage: function(page) {
            var that = this; // 保存module对象的引用
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;
            
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function(page_index, jq) {
                    // 获取每页显示数量
                    that.limit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                    
                    // 基本分页数据
                    var newData = {
                        page: page_index + 1,
                        limit: that.limit,
                        needUpdateAllPage: 0,
                        type: 'manage_module' // 确保包含类型信息
                    };
                    
                    // 使用模块的方法收集查询条件
                    // 注意：这里使用module而不是that来调用方法，避免this指向问题
                    var searchParams = module.collectTemplateSearchParams();
                    
                    // 合并查询条件到请求数据
                    $.extend(newData, searchParams);
                    
                    // 发送AJAX请求
                    $.ajaxFn({
                        url: ELN_URL + '?r=template/manage-template-list',
                        data: newData,
                        type: 'post',
                        success: function(data) {
                            if (data.status == 1) {
                                $('.exp_conetnt.active').removeClass('search');
                                $('.exp_conetnt.active .templates_table').html(data.data.listData);
                                that.pageData = page_index + 1; // 更新当前页码
                                
                                // 重新初始化分页，但不触发回调
                                module.pageFnManage();
                            }
                        }
                    }, null, true); // 添加loading效果
                    
                    return false; // 阻止默认行为
                },
                items_per_page: that.limit || pageBox.attr('max-num') || 15
            });
            
            // 对IE的修改
            $(".page_box a, .page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            });
        },

        //ajax分页插件
        pageFnRecycle: function(page) {
            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function(page_index, jq) {
                    that.limit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                    // that.expTemplateList((page_index + 1));

                    var newData = {
                        page: page_index +1,
                        limit:that.limit ,
                        needUpdateAllPage:0,
                        type : 'recycle_module',
                        module_keywords:$('.exp_conetnt.active #module_keywords').val().trim(),
                        module_type:$('.exp_conetnt.active #module_type').val(),
                        module_owner:$('.exp_conetnt.active .owner_select_user_box input').attr('idbox'),
                        module_remover:$('.exp_conetnt.active .remover_select_user_box input').attr('idbox')
                    };

                    $.ajaxFn({
                        url: ELN_URL+'?r=template/recycle-template-list',
                        data: newData,
                        type: 'post',
                        success: function(data){
                            if (data.status == 1) {
                                $('.exp_conetnt.active').removeClass('search');
                                $('.exp_conetnt.active .templates_table').html(data.data.listData);
                                that.pageData = page_index + 1;
                                module.pageFnRecycle();
                            }
                        }
                    }, null, true)

                },
                items_per_page: that.limit || pageBox.attr('max-num')|| 15
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        //ajax分页插件
        pageFnApprovalConfig: function(page) {
            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function(page_index, jq) {
                    that.limit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                    // that.expTemplateList((page_index + 1));

                    var newData = {
                        page: page_index +1,
                        limit:that.limit ,
                        needUpdateAllPage:0,
                        type : 'approval_module',
                        module_keywords:$('.exp_conetnt.active #module_keywords').val(),
                        module_type:$('.exp_conetnt.active #module_type').val(),
                    };

                    $.ajaxFn({
                        url: ELN_URL+'?r=template/manage-template-approval',
                        data: newData,
                        type: 'post',
                        success: function(data){
                            if (data.status == 1) {
                                $('.exp_conetnt.active').removeClass('search');
                                $('.exp_conetnt.active .templates_table').html(data.data.listData);
                                that.pageData = page_index + 1;
                                module.pageFnApprovalConfig();
                            }
                        }
                    }, null, true)

                },
                items_per_page: that.limit || pageBox.attr('max-num')|| 15
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        pageFnCallBack:function(){
            that.pageData = 1;
            module.pageFn()
        },

        pageFnManageCallBack:function(){
            that.pageData = 1;
            module.pageFnManage();
        },

        pageFnRecycleCallBack:function(){
            that.pageData = 1;
            module.pageFnRecycle()
        },

        pageFnApprovalConfigCallBack:function(){
            that.pageData = 1;
            module.pageFnApprovalConfig()
        },

        // add by hkk 2020/6/30 分享模板页面做分页
        shareTempPageFn: function(shareType) {

            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box');
            var page = that.pageData || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function(page_index, jq) {
                    that.limit = $(".exp_conetnt.active .page_box:visible .pager-select").val();
                    //url = ELN_URL + '?r=share-temple/share-temple-to-me';

                    var url;
                    if(shareType === "shareMeTemp"){
                        url = ELN_URL + '?r=share-temple/share-temple-to-me';
                        that.shareToMeLimit = $(".page_box:visible .pager-select").val();
                    } else{
                        url = ELN_URL + '?r=share-temple/share-temple-by-me';
                        that.shareByMeLimit = $(".page_box:visible .pager-select").val();
                    }


                    var newData = {
                        page: page_index +1,
                        limit:that.limit ,
                        needUpdateAllPage:0,
                        type : shareType,
                        module_keywords:$('.exp_conetnt.active #module_keywords').val(),
                        module_type:$('.exp_conetnt.active #module_type').val(),
                    };

                    $.ajaxFn({
                        url: url,
                        data: newData,
                        type: 'post',
                        success: function(data){
                            if (data.status == 1) {


                                $('.exp_conetnt.active .templates_table').html(data.data.listData);
                                that.pageData = page_index + 1;
                                that.shareTempPageFn(shareType);
                            }
                        }
                    }, null, true)



                },
                items_per_page: that.limit || pageBox.attr('max-num')|| 15
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function(event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        // add by hkk 2020/6/29 把getExp里的分享模板页面移到此处
        shareTemp: function (dom) {
            if (dom.attr('data-type') === 'shareMeTemp') {
                require('get_html').genShareMeTempListPage();
            } else if (dom.attr('data-type') === 'myShareTemp') {
                require('get_html').genMyShareTempListPage();
            }

        }
    };

    // 确保在模块定义时就添加这个方法
    var module = module || {};

    // 使用更安全的方式定义函数，避免this指向问题
    module.collectTemplateSearchParams = function() {
        // 使用局部变量，不依赖this
        var searchParams = {};
        
        // 获取模板名称关键字
        searchParams.module_keywords = $('.exp_conetnt.active #module_keywords').val().trim();
        
        // 获取企业模板选择状态
        searchParams.is_company = $('.exp_conetnt.active #is_company').val();
        
        // 获取模板拥有者信息
        var moduleOwnerInput = $('.exp_conetnt.active #select_module_owner').find('input[type="hidden"]');
        if (moduleOwnerInput.length > 0) {
            searchParams.module_owner = moduleOwnerInput.val();
        }
        
        // 获取模板类型数据
        var templateTypeContainer = $('.exp_conetnt.active .template-type-tree-select');
        if (templateTypeContainer.length > 0) {
            // 获取选中的主类型
            var selectedMainTypes = [];
            templateTypeContainer.find('.main-type-checkbox:checked').each(function() {
                selectedMainTypes.push($(this).val());
            });
            searchParams.template_main_types = selectedMainTypes.join(',');
            
            // 获取选中的子类型及其对应的主类型
            var subtypeData = {};
            templateTypeContainer.find('.subtype-checkbox:checked').each(function() {
                var subtypeId = $(this).val();
                var mainType = $(this).data('main-type');
                
                if (!subtypeData[mainType]) {
                    subtypeData[mainType] = [];
                }
                subtypeData[mainType].push(subtypeId);
            });
            
            searchParams.template_subtypes_json = JSON.stringify(subtypeData);
        }
        
        return searchParams;
    };

    var that = module;
    //取实验模板

    //删除模板
    $('body').on('click', '.del_module_temp', function(){
        that.delModule($(this));
    });

    //批量删除模板
    $('body').on('click','.del_module_temp_batch',function (){
        that.delModuleBatch($(this));
    })

    //还原模板
    $('body').on('click', '.restore_temp', function(){
        that.restoreTemplate($(this));
    });

    $('body').on('click', '.restore_temp_batch', function(){
        that.restoreTemplateBatch($(this));
    });



    //设置为企业模板
    $('body').on('click', '.set_company_temp', function(){
        that.setCompanyModule($(this));
    });

    //设置为企业模板
    $('body').on('click', '.set_company_temp_batch', function(){
        that.setCompanyModuleBatch($(this));
    });

    //复制模板
    $('body').on('click', '.copy_module', function(){
        that.copyModule($(this));
    });

    //全选
    $('body').on('click', '#module_all', function(){
        $('.module_check').prop('checked', $(this).prop('checked'));
    });

    //设为经典模板
    $('body').on('click', '.set_classic_module', function(){
        that.setSystemTemp($(this));
    });

    //批量转让
    $('body').on('click', '.transfer_module_batch', function(){
        that.transferModuleBatch($(this));
    });

    //打开分享
    $('body').on('click', '.share_module', function(){
        var idarr = [$(this).attr('temp-id')];
        require(['share_exp'], function(share){
            share(idarr, null, 'module');
        });
    });

    //查看
    $('body').on('click', '.opts_btn', function(){
        var temp_type = $(this).attr('temp-type');
        var temp_id = $(this).attr('temp-id');

        if (temp_type == 2) {
            //that.getModuleAssembly(temp_id, temp_type);
            require('get_html').genSubTempPage(temp_id); // add by hkk 2020/6/28 走新标签路径

        }else if (temp_type == 4) {
            //that.getModuleAssembly(temp_id, temp_type);
            require('get_html').genIntableTempPage(temp_id); // add by hkk 2020/6/28 走新标签路径

        }else{
            require('get_html').genExpTempPage(temp_id); // add by hkk 2020/6/28 走新标签路径

        }


    });

    //显示或隐藏模板
    $('body').on('click', '.temp_hide, .temp_show', function () {
        that.hideShowTemp($(this));
    });

	//added by xieyuxiang 2022.8.23 增加模板转让触发事件
    $('body').on('click','.transfer_module',function (){
        that.showTransferModal($(this));
        //将此模板的id绑定到确定按钮上
        $('.transfer_module_modal .transfer_submit').attr('data-id',$(this).attr('data-id'));
    });

    $('body').on('click','.transfer_submit',function (){
        var transfer_to=$('.transfer_module_body .visible-user-selector-box input').attr('idbox');
        var transfer_to_value=$('.transfer_module_body .visible-user-selector-box input').attr('value');
        var temp_ids=$(this).attr('data-id');//可能单个转让也可能批量转让
        var data={
            transferTo:transfer_to,
            tempIds:temp_ids,
            transferToValue:transfer_to_value
        }
        $.ajaxFn({
            url:'/?r=template/transfer',
            data:data,
            success:function (res){
                if(res.status===1){
                    $('.transfer_module_modal').modal('hide');
                    $.showAlert(mainLang('success'));
                    $('.exp_conetnt.active .search-template-list').trigger('click');
                }
            }
        })
    });

    //查看模板页面
    $('body').on('click', '.view_my_temp', function () {
        require('get_html').genExpTempListPage();
    });

    //查看分享给我的页面
    $('body').on('click', '.view_temp_share_to_me', function () {
        require('get_html').genShareMeTempListPage();
    });

    //标签显示单个模板
    $('body').on('click', '.my_exp_detial.module', function(event){
        if($('.new_module.on').length > 0){
            $.showAlert(mainLang('save_temp_first'));
            return;
        }
        if($('.new_exp.on').length > 0){
            $.showAlert(mainLang('save_exp_first'));
            return;
        }

        var event = event || window.event;
        var obj = event.srcElement ? event.srcElement : event.target;
        if($(obj).hasClass('close') || $(obj).hasClass('close-menu')){
            return
        }
        that.getModuleAssembly($(this).attr('data-id'));
    });

    //模板列表 标签显示
    $('body').on('click', '.my_exp_detial.listmodule[type=module]', function(event){
        saveExperiment(function () {
            var obj = event.srcElement ? event.srcElement : event.target;
            if($(obj).hasClass('close')){
                return;
            }
            that.expTemplateList();
        });
    });

    //绑定搜索
    $("body").on('click', '.search-template-list', function (event) {

        // add by hkk 2020/6/23 搜索只更新table即可，类似分页
        var page = page || 1;
        var data = {
                page: page
            };
        var ajaxType = 'post';
        // added by xyx2023.11.11 bug196 搜索后应该重置页码
        that.pageData = 1;
       // data.module_keywords=$('.exp_conetnt.active #module_keywords').val().trim();
     //   data.module_type=$('.exp_conetnt.active #module_type').val();
        data.module_from=$('.exp_conetnt.active #module_from').val();
        data.needUpdateAllPage = 0; // add by hkk 2019/11/19
        data.limit = that.limit;

        // 使用安全的方式调用函数
        var searchParams = module.collectTemplateSearchParams();
        
        // 合并查询条件
        $.extend(data, searchParams);

        $.ajaxFn({
            url: ELN_URL + '?r=template/template-list',
            data: data,
            type: ajaxType,
            success: function (data) {
                if (data.status == 1) {
                    $('.exp_conetnt.active').removeClass('search');
                    $('.exp_conetnt.active .templates_table').html(data.data.listData);
                    that.pageFn();
                }
            }
        });
    });

    // 绑定搜索 - 模板管理列表搜索事件
    $("body").on('click', '.search-manage-template-list', function (event) {
        // 保存module的引用，确保在回调中能正确访问
        var that = module;
        var page = page || 1;
        
        // 重置页码
        that.pageData = 1;
        
        // 基本数据
        var data = {
            page: page,
            needUpdateAllPage: 0,
            limit: that.limit
        };
        
        // 使用安全的方式调用函数
        var searchParams = module.collectTemplateSearchParams();
        
        // 合并查询条件
        $.extend(data, searchParams);
        
        // 发送AJAX请求
        $.ajaxFn({
            url: ELN_URL + '?r=template/manage-template-list',
            data: data,
            type: 'post',
            success: function (data) {
                if (data.status == 1) {
                    $('.exp_conetnt.active').removeClass('search');
                    $('.exp_conetnt.active .templates_table').html(data.data.listData);
                    that.pageFnManage();
                }
            }
        });
    });

    //模板回收站搜索
    $("body").on('click', '.search_recycle_template', function (event) {

        var page = page || 1;
        var data = {
            page: page
        };
        var ajaxType = 'post';
        data.module_keywords=$('.exp_conetnt.active #module_keywords').val().trim();
        data.module_type=$('.exp_conetnt.active #module_type').val();
        data.module_owner=$('.exp_conetnt.active .owner_select_user_box input').attr('idbox');
        data.module_remover=$('.exp_conetnt.active .remover_select_user_box input').attr('idbox');
        data.needUpdateAllPage = 0; // add by hkk 2019/11/19
        data.limit = that.limit;
        $.ajaxFn({
            url: ELN_URL + '?r=template/recycle-template-list',
            data: data,
            type: ajaxType,
            success: function (data) {
                if (data.status == 1) {
                    $('.exp_conetnt.active').removeClass('search');
                    $('.exp_conetnt.active .templates_table').html(data.data.listData);
                    that.pageFnRecycle();
                }
            }
        });

    });

    //绑定分享模板页面的搜索add by hkk 2019/11/20
    $("body").on('click', '.search-share-template-list', function (event) {

        var currentTab = $('.my_exp_detial.listmodule[type=module]');
        if (!checkTopTab(currentTab)) {
            return;
        }

        var page = page || 1;

        var url;
        var shareType = $(this).attr('data-type');
        if(shareType === "shareMeTemp"){
            url = ELN_URL + '?r=share-temple/share-temple-to-me';
        } else{
            url = ELN_URL + '?r=share-temple/share-temple-by-me';
        }

        var data = {
            page: page
        };

        //data.module_keywords = $('.exp_conetnt.active #module_keywords').val();
        data.module_type = $('.exp_conetnt.active #module_type').val();
        data.needUpdateAllPage = 0; // add by hkk 2019/11/19
        data.limit = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;

        // 使用安全的方式调用函数
        var searchParams = module.collectTemplateSearchParams();
        
        // 合并查询条件
        $.extend(data, searchParams);
        
        // 发送AJAX请求
        $.ajaxFn({
            url: url,
            data: data,
            type: 'post',
            success: function (data) {
                if (data.status == 1) {
                    $('.exp_conetnt.active .templates_table').html(data.data.listData); // add by hkk 2019/11/19 更新部分页面
                    that.shareTempPageFn(shareType);
                }
            }
        });
    });

    //子标签点击事件
    $("body").on('click', '.exp_conetnt.active .changeManageTab', function (event) {
        var typeID = $(this).attr('data-id');
        var tagId = $('.exp_title .tag.on').attr('data-id');
        
        // 移除当前选中标签的"on"类
        $('.changeManageTab.on').removeClass('on');
        
        // 为点击的标签添加"on"类
        $(this).addClass('on');
        
        // 根据标签ID加载不同的内容
        if (typeID == 1) {
            //! 保留分页设置加载模板管理页面 bug#33637 mod dx
            require('get_html').getManageTempListContent()
                .then(function (res) {
                    require('tab').switchTag(tagId, res.data.contentHtml);

                    delete module.limit;
                    require('exp_module')['pageFnManageCallBack'].apply(null, []);
                });
        }
        if (typeID == 2) {
            require('get_html').getTempListApprovalConfigContent(data).then(function (res) {
                require('tab').switchTag(tagId, res.data.contentHtml);
                //module.pageFnSysExpOpenLogCallBack,
                require('exp_module')['pageFnApprovalConfigCallBack'].apply(null, []);
            });
        }
        if (typeID == 3) {
            // 新增的设置标签页
            require('get_html').getTemplateSettingsContent().then(function (res) {
                require('tab').switchTag(tagId, res.data.contentHtml);
                
                // 确保在DOM更新后再挂载Vue组件
                setTimeout(function() {
                    var container = $('#template-settings');
                    if (container.length === 0) {
                        console.error('找不到挂载容器 #template-settings');
                        return;
                    }
                    container.show();
                    
                    // 加载Vue组件
                    renderComponent('/vue-ui/dist/templateGeneralSettings.js', '#template-settings', {
                        close: function() {
                            unrenderComponent('#template-settings');
                            container.hide();
                            console.log('模板设置组件已关闭');
                        },
                        // 添加数据更新后的回调
                        onDataUpdated: function() {
                            // 更新当前标签页内容
                            require('tab').reloadTag($('.exp_title .tag.on').attr('data-id'));
                        }
                    });
                }, 100); // 短暂延迟确保DOM已更新
            });
        }
        if (typeID == 4) {
            // 原来的第三个标签页，现在是第四个
            require('get_html').getRecycleTempListContent().then(function (res) {
                require('tab').switchTag(tagId, res.data.contentHtml);
                require('exp_module')['pageFnRecycleCallBack'].apply(null, []);
            });
        }
    });

    //添加模板审批组
    $("body").on('click', '#add_template_approval', function (event) {
        var add_index = parseInt($(".templates_table .approval_group").length) + 1;
        
        // 克隆demo内容
        var $cloneDiv = $("#demo").clone(true);
        $cloneDiv.removeClass("hide");
        $cloneDiv.attr("id", "approval_group_" + add_index);
        $cloneDiv.attr("add_index", add_index);
        $cloneDiv.addClass("approval_group");
        
        // 使用类选择器更新复选框ID和类名 
        $cloneDiv.find('.full-template-checkbox_demo').each(function() {
            var typeId = $(this).val();
            var newId = 'full_' + add_index + '_' + typeId;
            $(this).attr('id', newId);
            $(this).removeClass('full-template-checkbox_demo').addClass('full-template-checkbox_' + add_index);
            $(this).next('label').attr('for', newId);
        });
        
        $cloneDiv.find('.intext-template-checkbox_demo').each(function() {
            var typeId = $(this).val();
            var newId = 'intext_' + add_index + '_' + typeId;
            $(this).attr('id', newId);
            $(this).removeClass('intext-template-checkbox_demo').addClass('intext-template-checkbox_' + add_index);
            $(this).next('label').attr('for', newId);
        });
        
        $cloneDiv.find('.intable-template-checkbox_demo').each(function() {
            var typeId = $(this).val();
            var newId = 'intable_' + add_index + '_' + typeId;
            $(this).attr('id', newId);
            $(this).removeClass('intable-template-checkbox_demo').addClass('intable-template-checkbox_' + add_index);
            $(this).next('label').attr('for', newId);
        });
        
        // 更新其他ID
        $cloneDiv.find('#create_template').attr('id', 'create_template_' + add_index);
        $cloneDiv.find('#share_template').attr('id', 'share_template_' + add_index);
        $cloneDiv.find('#set_company_template').attr('id', 'set_company_template_' + add_index);
        $cloneDiv.find('#send_remind_email').attr('id', 'send_remind_email_' + add_index);
        
        // 更新label的for属性
        $cloneDiv.find('label[for="create_template"]').attr('for', 'create_template_' + add_index);
        $cloneDiv.find('label[for="share_template"]').attr('for', 'share_template_' + add_index);
        $cloneDiv.find('label[for="set_company_template"]').attr('for', 'set_company_template_' + add_index);
        $cloneDiv.find('label[for="send_remind_email"]').attr('for', 'send_remind_email_' + add_index);
        
        // 为审批配置区域添加唯一类名，用于后续获取审批节点数据
        $cloneDiv.find('.approval_config').addClass('approvalData_' + add_index);
        
        // 添加到页面
        $(".templates_table").append($cloneDiv);
        
        // 初始化审批节点 - 使用现有的approval模块
        require(['approval'], function(module) {
            // 直接调用现有的初始化方法
            module.init();
        });
        
        // 自动滚动到页面底部，使新添加的审批组可见
        document.documentElement.scrollTop = $('.templates_table').height() > 400 ? document.body.scrollHeight : 0;
    });

    //删除组
    $("body").on('click', '.del_approval_line', function (event) {
        var btn = $(this);
        $.showContent('delete_approval', mainLang('delete_approval'), mainLang('delete_approval_tips'), function () {

            btn.closest(".approval_group").remove();
            $('.delete_approval').parent().siblings('.modal-footer').find('.cancel').trigger('click'); //关闭模态框

        })

    });

    // 保存信息
    $("body").on('click', '.save-template-approval-setting', function (event) {
        var approvalList = $(".exp_conetnt.active .templates_table").find(".approval_group");
        
        // 从按钮的data属性中获取模板生效模式
        var templateEffectMode = $(this).data('templateEffectMode');
        console.log('Saving with Template Effect Mode:', templateEffectMode);

        //表头不用，所以i从1开始
        var approvalSettingList = [];//数组
        for (var i = 0; i < approvalList.length; i++) {
            var dataArr = approvalList.eq(i);
            var add_index = dataArr.attr('add_index');
            var approval_id = dataArr.attr('approval_id');

            var user_type = dataArr.find(".user_type").val();
            var group_ids = dataArr.find("[name='group_ids']").attr('idBox');
            var department_ids = dataArr.find("[name='department_ids']").attr('data');
            var create_template = $('#create_template_'+add_index).prop('checked') ? 1 : 0;
            var share_template = $('#share_template_'+add_index).prop('checked') ? 1 : 0;
            var publish = $('#publish_template_'+add_index).prop('checked') ? 1 : 0;            
            var set_company_template = $('#set_company_template_'+add_index).prop('checked') ? 1 : 0;
            

            // 根据模板生效模式检查必要的选项
            if(templateEffectMode == 1) {
                // 模式1：创建/修改需要审核
                if(create_template == 0 && share_template == 0 && set_company_template == 0) {
                    $.showAlert(mainLang('operate_type_tips'));
                    return;
                }
            } else {
                // 模式2：发布需要审核
                if(share_template == 0 && publish == 0 && set_company_template == 0) {
                    $.showAlert(mainLang('operate_type_tips'));
                    return;
                }
                create_template = publish;
            }

            //保存full[],intext[],intable[]
            var full = [];
            var intext = [];
            var intable = [];
            //打印原始checkbox勾选情况
            //console.log('full template checkbox: ', dataArr.find('.full-template-checkbox'));            
            dataArr.find('.full-template-checkbox_'+add_index).each(function() {
                //重复的不添加，避免重复添加到full[]中，导致full[]中有重复的id值，导致保存时报错。
                if ($(this).prop('checked') && full.indexOf($(this).val())==-1) { //重复的不添加，避免重复添加到full[]中，导致full[]中有重复的id值，导致保存时报错。
                    full.push({id: $(this).val()});
                }
            });
            dataArr.find('.intext-template-checkbox_'+add_index).each(function() {
                if ($(this).prop('checked') && intext.indexOf($(this).val())==-1) { //重复的不添加，避免重复添加到intext[]中，导致intext[]中有重复的id值，导致保存时报错。
                    intext.push({id: $(this).val()});
                }
            });
            dataArr.find('.intable-template-checkbox_'+add_index).each(function() {
                if ($(this).prop('checked') && intable.indexOf($(this).val())==-1) { //重复的不添加，避免重复添加到intable[]中，导致intable[]中有重复的id值，导致保存时报错。
                    intable.push({id: $(this).val()});
                }
            });            
            var send_remind_email = $('#send_remind_email_'+add_index).prop('checked') ? 1 : 0;
            var approval_nodes=require('approval').getNodes($('.approvalData_'+add_index), '', 0)

            if(full.length==0 && intext.length==0 && intable.length==0){
                $.showAlert(mainLang('template_type_tips'));
                return;
            }

            if (approval_nodes.length == 0) {
                $.showAlert(mainLang('pls_set_approval_node'));
                return false;
            }


            var data = {
                approval_id: approval_id,
                user_type: user_type,
                group_ids: group_ids,
                department_ids: department_ids,
                create_template: create_template,
                share_template: share_template,
                publish: publish,
                set_company_template: set_company_template,                 
                full: full,
                intext: intext,
                intable: intable,
                send_remind_email:send_remind_email,
                approval_nodes:approval_nodes,
            };
            //console.log('data: ', data);

            approvalSettingList.push(data);

        }

        approvalSettingList=JSON.stringify(approvalSettingList);

        $.ajaxFn({
            url: ELN_URL + '?r=template/set-approval-setting-submit',
            type: 'post',
            data: {
                approvalSetting: approvalSettingList,
            },
            success: function (data) {
                if (data.status == 1) {
                    $.showAlert(jsLang['success']);
                    $.closeModal();

                }
            }
        })
    });

    //切换鹰群 / 部门
    $("body").on('change', '.user_type', function (event) {

        if ($(this).val() == 1) {
            $(this).closest(".share-part").find('.group_list').show();
            $(this).closest(".share-part").find('.department_list').hide();
        }
        if ($(this).val() == 2) {
            $(this).closest(".share-part").find('.group_list').hide();
            $(this).closest(".share-part").find('.department_list').show();
        }

    });

    return {
        open: module.open,
        shareTemp: module.shareTemp,// add by hkk 2020/6/29
        shareTempPageFn: module.shareTempPageFn,// add by hkk 2020/6/29
        getModuleAssembly: module.getModuleAssembly,
        pageFnCallBack:module.pageFnCallBack, // add by hkk 2020/6/23 暴露出去用于加载完页面后的回调加载分页
        pageFnManageCallBack:module.pageFnManageCallBack, // add by lcy 2022/8/19 暴露出去用于加载完页面后的回调加载分页
        pageFnRecycleCallBack:module.pageFnRecycleCallBack, // add by lcy 2022/8/19 暴露出去用于加载完页面后的回调加载分页
        pageFnApprovalConfigCallBack:module.pageFnApprovalConfigCallBack, // add by lcy 2022/8/19 暴露出去用于加载完页面后的回调加载分页
    };
})
