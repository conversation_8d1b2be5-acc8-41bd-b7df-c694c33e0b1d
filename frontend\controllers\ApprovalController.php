<?php
namespace frontend\controllers;

use frontend\interfaces\CenterInterface;
use frontend\interfaces\PMInterface;
use frontend\services\ApprovalServer;
use frontend\services\TemplateSubtypeServer;
use frontend\services\CompanyServer;
use yii;

/**
 * Class CheckController
 * @package frontend\controllers
 */
class ApprovalController extends MyController {


    /**
     * @Notes: 我提交的审批
     * @return \common\controllers\json
     * @author: tianyang
     * @Time: 2023/2/27 13:03
     */
    public function actionListSubmitted() {
        $postData = \Yii::$app->request->post();
        $filter = \Yii::$app->request->post('filter', []);
        $type = !empty($postData['type']) ? $postData['type'] : 1;
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];

        // 获取我提交的审批 各审批分类的数量
        $cntRes = (new ApprovalServer())->getMyApprovalCnt($this->userinfo->id);
        // 渲染 标签页
        $tabHtml = $this->renderAjax('/approval/tab', [
            'dataType' => 'approval_submitted',
            'type' => $type,
            'approvalCnt' => $cntRes
        ]);

        // 获取我提交的审批 列表
        $res = (new ApprovalServer())->listMyApprovalByType($this->userinfo->id, $type, $filter, $page, $limit);
        $approvalList = $res['list'];
        $total = $res['total'];

        //查询该企业下所有用户,和所有鹰群，用于用户 名称显示
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userRes = (new CenterInterface())->getUserListByCompanyId($company_id, 1);
        $userList = yii\helpers\ArrayHelper::index($userRes['list'], 'user_id');
        $groupRes = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', 1);
        $groupList = yii\helpers\ArrayHelper::index($groupRes, 'id');

        // 渲染 列表页
        if($type == \Yii::$app->params['approval_type']['create_book']) {
            // 记录本的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/book_list_submitted', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'groupList' => $groupList,
                'filter' => $filter,
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['instrument_check']) {
            // 仪器审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/instrument_check_list_submitted', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'groupList' => $groupList,
                'filter' => $filter,
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['template']) {
            // 模板审批

            $templateList = array_column($approvalList, null, 'id');
            $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList(); //模板类型
            $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];        
            $listHtml = $this->renderAjax('/approval/template_list_submitted', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'templateList' => $templateList,
                'templateSubtypeList' => $templateSubtypeList['data'], //模板类型
                'templateEffectMode' => $templateEffectMode,                
                'groupList' => $groupList,
                'filter' => $filter,
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['share']) {
            //实验分享的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/share_list_submitted', [
                'type' => $type,
                'list' => $approvalList,
                'userList' => $userList,
                'groupList' => $groupList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'filter' => $filter,
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['work_order']) {
            // 工单审批
            $approvalWithWoDetail = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type, $filter);
            $workOrderList = array_column($approvalWithWoDetail, null, 'business_id');
            $listHtml = $this->renderAjax('/approval/work_order_submitted', [
                'type' => $type,
                'list' => $approvalWithWoDetail,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'workOrderList' => $workOrderList,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                        'project_tree' => []
                    ],
                    'filter' => $filter
                ]
            ]);
        }
        else {
            // 针对实验的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $projectList = (new PMInterface())->getUserProjects($this->userinfo);

            $listHtml = $this->renderAjax('/approval/exp_list_submitted', [
                'type' => $type,
                'list' => $approvalList,
                'userList' => $userList,
                'projectList' => $projectList,
                'tool' => 'approval',
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'filter' => $filter,
            ]);
        }

        return $this->success([
            'contentHtml' => $tabHtml . $listHtml,
            'tabName' => \Yii::t('views/left', 'my_approval_submitted'),
            'tabTitle' => \Yii::t('views/left', 'my_approval_submitted'),
            'title' => '1',
            'showData' => 0,
            'cnt' => $total,
            'total' => $total,
            'page' => $page,
            'approvalCnt' => $cntRes
        ]);
    }


    /**
     * Notes: 待审批实验列表
     *
     * Author: zhu huajun
     * Date: 2019/3/7 20:33
     * @throws yii\db\Exception
     */
    public function actionList() {
        $postData = \Yii::$app->request->post();
        $filter = \Yii::$app->request->post('filter', []);
        $type = !empty($postData['type']) ? $postData['type'] : 1;
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];

        // 统计待审批总数
        $cntRes = (new ApprovalServer())->getApprovalCnt($this->userinfo->id);
        
        $res = (new ApprovalServer())->listApprovalByType($this->userinfo->id, $type, $filter, $page, $limit);
        $approvalList = $res['list'];
        $total = $res['total'];

        $tabHtml = $this->renderAjax('/approval/tab', [
            'dataType' => 'approval',
            'type' => $type,
            'approvalCnt' => $cntRes
        ]);

        //查询该企业下所有用户,和所有鹰群，用于用户 名称显示
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userRes = (new CenterInterface())->getUserListByCompanyId($company_id, 1);
        $userList = yii\helpers\ArrayHelper::index($userRes['list'], 'id');
        $groupRes = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', 1);
        $groupList = yii\helpers\ArrayHelper::index($groupRes, 'id');

        if($type == \Yii::$app->params['approval_type']['create_book']) {
            // 记录本的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/book_list', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter' => $filter
                ]
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['instrument_check']) {
            // 仪器审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/instrument_check_list', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter' => $filter
                ]
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['template']) {
            // 模板审批
            $templateList = array_column($approvalList, null, 'id');
            $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList(); //模板类型
            $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];        
            $listHtml = $this->renderAjax('/approval/template_list', [                
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'templateList' => $templateList,
                'templateSubtypeList' => $templateSubtypeList['data'], //模板类型
                'templateEffectMode' => $templateEffectMode,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter' => $filter
                ]
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['share']) {

            //实验分享的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/share_list', [
                'type' => $type,
                'list' => $approvalList,
                'userList' => $userList,
                'groupList' => $groupList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                        'project_tree' => []
                    ],
                    'filter' => $filter
                ]
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['work_order']) {
            // 工单审批
            $approvalWithWoDetail = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type, $filter);

            $workOrderList = array_column($approvalWithWoDetail, null, 'business_id');
            $listHtml = $this->renderAjax('/approval/work_order', [
                'type' => $type,
                'list' => $approvalWithWoDetail,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'workOrderList' => $workOrderList,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                        'project_tree' => []
                    ],
                    'filter' => $filter
                ]
            ]);
        }
        else {
            // 针对实验的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $projectList = (new PMInterface())->getUserProjects($this->userinfo);

            $listHtml = $this->renderAjax('/approval/exp_list', [
                'type' => $type,
                'list' => $approvalList,
                'userList' => $userList,
                'group_list' => $groupList,
                'projectList' => $projectList,
                'tool' => 'approval',
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                    ],
                    'filter' => $filter
                ]
            ]);
        }

        return $this->success([
            'contentHtml' => $tabHtml . $listHtml,
            'tabName' => \Yii::t('views/left', 'for_approval'),
            'tabTitle' => \Yii::t('views/left', 'for_approval'),
            'title' => '1',
            'showData' => 0,
            'cnt' => $total,
            'total' => $total,
            'page' => $page,
            'approvalCnt' => $cntRes,
        ]);
    }

    /**
     * Notes: 审核历史
     * /approval/history_book_list
     * /approval/history_template_list
     * /approval/history_instrument_check_list
     * /approval/history_share_list
     * /approval/history_exp_list
     * Author: zhu huajun
     * Date: 2019/9/25 15:58
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionHistoryList() {
        $postData = \Yii::$app->request->post();
        $type = !empty($postData['type']) ? $postData['type'] : 1;
        $filter = !empty($postData['filter']) ? $postData['filter'] : [];
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];

        // 统计历史审批总数
        $cntRes = (new ApprovalServer())->getHistoryApprovalCnt($this->userinfo->id);

        $res = (new ApprovalServer())->listHistoryApprovalByType($this->userinfo->id, $type, $filter, $page, $limit);
        $approvalList = $res['list'];
        $total = $res['total'];

        $tabHtml = $this->renderAjax('/approval/tab', [
            'dataType' => 'approval_history',
            'type' => $type,
            'approvalCnt' => $cntRes
        ]);

        //查询该企业下所有用户,和所有鹰群，用于用户 名称显示
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userRes = (new CenterInterface())->getUserListByCompanyId($company_id, 1);
        $userList = yii\helpers\ArrayHelper::index($userRes['list'], 'id');
        $groupRes = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', 1);
        $groupList = yii\helpers\ArrayHelper::index($groupRes, 'id');

        if($type == \Yii::$app->params['approval_type']['create_book']) {
            // 记录本的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/history_book_list', [
                'type' => $type,
                'list' => $approvalList,
                'tool' => 'approval',
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'groupList' => $groupList,
                'filter' => $filter,
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['template']) {
            // 模板审批

            $templateList = array_column($approvalList, null, 'id');
            $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList(); //模板类型
            $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];        
            $listHtml = $this->renderAjax('/approval/history_template_list', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'templateList' => $templateList,
                'templateSubtypeList' => $templateSubtypeList['data'], //模板类型
                'templateEffectMode' => $templateEffectMode,                
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter' => $filter
                ]
            ]);
        }
        else if ($type == \Yii::$app->params['approval_type']['instrument_check']) {
            // 仪器审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/history_instrument_check_list', [
                'type' => $type,
                'list' => $approvalList,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter' => $filter
                ]
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['share']) {
            // 实验分享的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $listHtml = $this->renderAjax('/approval/history_share_list', [
                'type' => $type,
                'list' => $approvalList,
                'userList' => $userList,
                'groupList' => $groupList,
                'tool' => 'approval',
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                        'project_tree' => []
                    ],
                    'filter' => $filter
                ]
            ]);
        }
        else if($type == \Yii::$app->params['approval_type']['work_order']) {
            // 工单审批
            $approvalWithWoDetail = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type, $filter);

            $workOrderList = array_column($approvalWithWoDetail, null, 'business_id');
            $listHtml = $this->renderAjax('/approval/history_work_order_list', [
                'type' => $type,
                'list' => $approvalWithWoDetail,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'userList' => $userList,
                'workOrderList' => $workOrderList,
                'groupList' => $groupList,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                        'project_tree' => []
                    ],
                    'filter' => $filter
                ]
            ]);
        }
        else {
            // 针对实验的审批
            $approvalList = (new ApprovalServer()) -> dealApprovalViewData($approvalList, $type);

            $projectList = (new PMInterface())->getUserProjects($this->userinfo);

            $listHtml = $this->renderAjax('/approval/history_exp_list', [
                'type' => $type,
                'list' => $approvalList,
                'user_list' => $userList,
                'group_list' => $groupList,
                'projectList' => $projectList,
                'tool' => 'approval',
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'filter' => $filter,
                'params' => [
                    'filter_list' => [
                        'user_list' => $userList,
                        'group_list' => $groupList,
                    ],
                    'filter' => $filter
                ]
            ]);
        }

        return $this->success([
            'contentHtml' => $tabHtml . $listHtml,
            'tabName' => \Yii::t('base', 'approval_history'),
            'tabTitle' => \Yii::t('base', 'approval_history'),
            'title' => '1',
            'showData' => 0,
            'cnt' => $total,
            'total' => $total,
            'page' => $page,
            'approvalCnt' => $cntRes
        ]);
    }

    /**
     * Notes: 审批通过
     *
     * Author: zhu huajun
     * Date: 2019/9/24 19:54
     */
    public function actionAgree() {
        $postData = \Yii::$app->getRequest()->post();

        // 验证密码
        if (empty($postData['password'])) {
            return $this->fail(\Yii::t('sign', 'pass_must'));
        }
        $isRight = (new CenterInterface())->checkPwd($this->userinfo->id, $postData['password'], $this->userinfo->name);
        if (!$isRight) {
            return $this->fail(\Yii::t('sign', 'pass_error'));
        }

        $nodeIds = \Yii::$app->request->post('approval_id_arr');
        $businessType = \Yii::$app->request->post('business_type');
        $businessId = \Yii::$app->request->post('business_id');
        if (empty($nodeIds)) {
            if (empty($businessType) || empty($businessId)) {
                return $this->fail(\Yii::t('exp', 'select_exp'));
            }

            $approvalNodeRes = (new ApprovalServer())->getApprovalByBusiness($businessType, $businessId, $this->userinfo->id);
            $nodeIds = @getVar($approvalNodeRes['data']['node_ids'], []);
            if (empty($nodeIds)) {
	            return $this->fail(\Yii::t('base', 'invalid_approval_used'));
            }
        }

        $remark = \Yii::$app->request->post('remark', '');

        $result = (new ApprovalServer())->agree($this->userinfo, $nodeIds, $remark);

        if($result['status'] != 1) {
            return $this->fail($result['info']);
        } else if($result['status'] == 1 && count($result['data']) > 0 && count($result['data']) == count($nodeIds)){
            return $this->success('No need deal'); // add by hkk 2022/8/9  无需处理提示
        }
        return $this->success(\Yii::t('base', 'success'));
    }

    /**
     * Notes: 审批拒绝
     *
     * Author: zhu huajun
     * Date: 2019/9/24 19:54
     */
    public function actionRefuse() {
        $postData = \Yii::$app->getRequest()->post();

        // 验证密码
        if (empty($postData['password'])) {
            return $this->fail(\Yii::t('sign', 'pass_must'));
        }
        $isRight = (new CenterInterface())->checkPwd($this->userinfo->id, $postData['password'], $this->userinfo->name);
        if (!$isRight) {
            return $this->fail(\Yii::t('sign', 'pass_error'));
        }

        $nodeIds = \Yii::$app->request->post('approval_id_arr');
        $businessType = \Yii::$app->request->post('business_type');
        $businessId = \Yii::$app->request->post('business_id');
        if (empty($nodeIds)) {
            if (empty($businessType) || empty($businessId)) {
                return $this->fail(\Yii::t('exp', 'select_exp'));
            }

            $approvalNodeRes = (new ApprovalServer())->getApprovalByBusiness($businessType, $businessId, $this->userinfo->id);
            $nodeIds = @getVar($approvalNodeRes['data']['node_ids'], []);
            if (empty($nodeIds)) {
                return $this->fail(\Yii::t('exp', 'select_exp'));
            }
        }

        $remark = \Yii::$app->request->post('remark', '');

        $result = (new ApprovalServer())->refuse($this->userinfo, $nodeIds, $remark);

        if($result['status'] != 1) {
            return $this->fail($result['info']);
        } else if($result['status'] == 1 && count($result['data']) > 0 && count($result['data']) == count($nodeIds)){
            return $this->success('No need deal'); // add by hkk 2022/8/9  无需处理提示
        }

        return $this->success(\Yii::t('base', 'success'));
    }

    /**
     * @Notes: 查看审批流程详情
     * @return \common\controllers\json
     * @author: tianyang
     * @Time: 2023/3/9 10:06
     */
    public function actionView() {
        $postData = \Yii::$app->getRequest()->post();
        $approvalId = @getVar($postData['approval_id'], 0);

        if (empty($approvalId)) {
            return $this->fail('base', 'lost_params');
        }

        $approvalServer = new ApprovalServer();
        $approvalInfo   = $approvalServer->getApprovalInfo($approvalId);
        $approvalNodes  = $approvalServer->getApprovalNodesById($approvalId);
        $approvalLog    = $approvalServer->approvalNodes2Log($approvalNodes);

        if (empty($approvalInfo)) {
            return $this->fail('base', 'lost_params');
        }

        $file = $this->renderAjax(
            '/popup/approval_detail', [
                'approval_info' => $approvalInfo,
                'approval_nodes' => $approvalNodes,
                'approval_log' => $approvalLog
            ]
        );

        return $this->success(['file' => $file]);
    }
}
