<?php
namespace frontend\controllers;

use frontend\models\TemplateHistoryNewModel;
use frontend\services\CompanyAuthServer;
use frontend\services\CompanyServer;
use frontend\services\ExperimentServer;
use yii;
use common\controllers\Common;
use yii\helpers\VarDumper;
use yii\helpers\ArrayHelper;
use frontend\services\HistoryServer;
use frontend\services\TempleServer;
use frontend\interfaces\CenterInterface;
use frontend\services\GroupSettingServer;

/**
 *
 *历史
 * <AUTHOR> @copyright 2016-3-8
 */
class TemplateHistoryController extends MyController {
	/**
	 * 获取具体实验的痕迹列表
	 * <AUTHOR> @param integer $experimentid 实验id
	 * @copyright 2016-3-8
	 */
	public function actionHistoryList($expid){
        $list = (new HistoryServer())->historyList($expid);

        if (!empty($list['data'])) {
            $list = $list['data'];
        }

        //获取是否有查看详情的权限
        $showDetailTrace = false;
        $detailTraceAuth = (new CompanyServer())->getCompanySetting(
            'GENERAL_SETTING_TRACE_DETAIL'
        );
        if (@getVar($detailTraceAuth['data']['GENERAL_SETTING_TRACE_DETAIL']['value']) == 1) {
            $showDetailTrace = true;
        }

        // 判断实验能否通过痕迹版本进行恢复
        $showRevertBtn = (new ExperimentServer())->checkRecoverAuth($expid);

        $file = $this->renderAjax('/popup/exp_history', [
            'tool' => 'detail',
            'list' => isset($list['list']) ? $list['list'] : [],
            'userList' => isset($list['userList']) ? $list['userList'] : [],
            'baseHis' => isset($list['baseHis']) ? $list['baseHis'] : [],
            'expId' => $expid, // add by hkk 2019/12/3
            'showDetailTrace' => $showDetailTrace, // add by hkk 2019/12/3,
            'showRevertBtn' => $showRevertBtn
        ]);
        echo $file;
	}

    /**
     * Notes: 根据模板id获取模板操作日志
     * Author: Zheyu Qin
     * Date: 2025/06/05
     *  /?r=template-history/action-logs
     * @param int $tempId 模板id
     * @return \common\controllers\json
     */
    public function actionActionLogs() {
        $tempId = \Yii::$app->request->get('temp_id');
        if (empty($tempId)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }
        $res = (new TempleServer())->getActionLogs($tempId);
        $actionLogs = $res['data'];
        $userIds = array_column($actionLogs, 'create_by');
        $userList = (new CenterInterface())->userDetailsByUserIds($userIds);
        $userList = yii\helpers\ArrayHelper::index($userList, 'id');

        return $this->success([
            'actionLogs' => $actionLogs,
            'userList' => $userList,
        ]);
    }

    /**
     * Notes: 获取模板版本列表
     * Author: Zheyu Qin
     * Date: 2025/06/11
     *  /?r=template-history/version-list
     * @param int $tempId 模板id
     * @return \common\controllers\json
     */
    public function actionVersionList() {
        $tempId = \Yii::$app->request->get('temp_id');
        if (empty($tempId)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }
        $res = (new TempleServer())->getVersionList($tempId);
        $userIds = array_column($res['data'], 'create_by');
        $userList = (new CenterInterface())->userDetailsByUserIds($userIds);
        $userList = yii\helpers\ArrayHelper::index($userList, 'id');        
                
        return $this->success([
            'data' => $res['data'],
            'userList' => $userList
        ]);
    }

    /**
     * Notes: 修改历史记录中的版本号和生效日期
     * Author: Zheyu Qin
     * Date: 2025/06/11
     *  /?r=template-history/update-history
     * @param int $historyId 历史记录id
     * @param string $userVersion 新的版本号
     * @param string $effectDate 生效日期
     * @return \common\controllers\json
     */
    public function actionUpdateHistory()
    {
        // 获取请求参数
        $request = \Yii::$app->request;
        $historyId = $request->post('history_id');
        $userVersion = $request->post('user_version');
        $effectDate = $request->post('effect_date');
        
        // 验证必要参数
        if (!$historyId) {
            return $this->fail(\Yii::t('temp', 'missing_required_params'));
        }
        
        try {
            // 查找历史记录
            $history = TemplateHistoryNewModel::findOne($historyId);
            if (!$history) {
                return $this->fail(\Yii::t('temp', 'no_history'));
            }

            if(!isset($userVersion)&& empty(trim($userVersion)) && !isset($effectDate) && empty(trim($effectDate))) {
                return $this->fail(\Yii::t('temp', 'missing_required_params'));
            }
            
            //使用tempserver中的方法
            $res = (new TempleServer())->updateHistory($historyId, null,$userVersion, $effectDate);
            if ($res['status'] == 0) {
                return $this->fail($res['info']);
            }
            
            if ($res['status'] == 1) { // 保存成功，返回更新后的历史记录数据
                return $this->success([
                    'id' => $history->id,
                    'user_version' => $history->user_version,
                    'effect_date' => $history->effect_date
                ]);
            } else {
                return $this->fail(\Yii::t('temp', 'save_failed') . ': ' . implode(', ', $history->getErrorSummary(true)));
            }
        } catch (\Exception $e) {
            return $this->fail(\Yii::t('temp', 'operation_failed') . ': ' . $e->getMessage());
        }
    }

    /**
     * Notes: 取消发布
     * Author: Zheyu Qin
     * Date: 2025/06/11
     *  /?r=template-history/cancel-publish
     * @param int $versionId 版本id
     * @return \common\controllers\json
     */
    public function actionCancelPublish() {
        $history_id = \Yii::$app->request->post('history_id');
        if (empty($history_id)) {
            return $this->fail(\Yii::t('temp', 'no_version'));
        }

        $tempServer = new TempleServer();
        $res = $tempServer->cancelPublish($history_id);
        return $this->success(['data' => $res['data']]);
    }

	/**
	 * Notes: 查看痕迹
	 * Author: zhu huajun
	 * Date: 2019/2/27 16:14
	 * @return \common\controllers\json
	 */
	public function actionViewHistory() {
		$id = \Yii::$app->request->get('id', 0);
		if (empty($id)) {
			return $this->fail(\Yii::t('exp', 'select_history'));
		}

		// 获取数据
		$result = (new HistoryServer())->getHistoryById($id);
		if($result['status'] == 0) {
			return $this->fail($result['info']);
		}
		$data = $result['data'];

		// 获取盐型列表 add by zhj hkk 2019/3/14
		$saltList = (new ExperimentServer())->saltList();
		$data['saltList'] = $saltList['data'];

		\Yii::$app->view->params['edit'] = 2;
		$html = $this->renderAjax('/experiment/experiment.php', [
			'experimentData' => $data,
			'projectList' => [],
			'projectInfo' => null,
			'tool' => 'detail',
			'type' => 'save_exp',
			'history' => 'history',
			'edit' => 2,
			'tmpPower' => 0
		]);

		return $this->success([
			'contentHtml' => $html,
			'history' => true,
            'tagName' =>  \Yii::t('base', 'history').":".$data['base_data']['exp_all_code']."-".$data['base_data']['version'],
            'tagTitle' =>  \Yii::t('base', 'history').":".$data['base_data']['exp_all_code']."-".$data['base_data']['version'],
		]);
	}

    /**
     * Notes: 保存实验操作痕迹详情
     * Author: hkk
     * Date: 2019/12/3 9:37
     * @return \common\controllers\json
     * @throws yii\db\Exception
     */
    public function actionSaveExperimentTraceDetail() {

        $expData = \Yii::$app->request->post();

        $expsTraceRecordList = \Yii::$app->request->post('exps_trace_record_list', []);
        $modulesTraceRecordList = \Yii::$app->request->post('modules_trace_record_list', []);

        $curUserId = \Yii::$app->session['userinfo']['id'];

        $expData['cur_user_id'] = $curUserId;
        $expData['exps_trace_record_list'] = $expsTraceRecordList;
        $expData['modules_trace_record_list'] = $modulesTraceRecordList;

        $saveResult = (new HistoryServer())->saveExperimentTraceDetail($expData);

        if (empty($saveResult['status'])) {
            return $this->fail($saveResult['info']);
        }

        return $this->success($saveResult['data']);
    }


    public function actionGetTraceDetailPage() {

        $postData = \Yii::$app->getRequest()->post();


        // 获取查询条件
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', 50);
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', "yes");
        $where['exp_id'] = \Yii::$app->request->post('exp_id', '');
        $where['trace_detail'] = \Yii::$app->request->post('trace_detail', '');
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['compare_start_time'] = \Yii::$app->request->post('compare_start_time', '');
        $where['compare_end_time'] = \Yii::$app->request->post('compare_end_time', '');

        // 获取查询的数据
        $traceDetailData = (new HistoryServer())->listTraceDetail($where, $limit, $page);

        $data['traceDetailList'] = $traceDetailData['trace_detail_list'];
        $data['totalCount'] = $traceDetailData['totalCount'];
        $data['limit'] = $limit;


        if ($needUpdateAllPage == "yes") {
            $file = $this->renderAjax('/experiment/trace_detail.php', $data);
            return $this->success(['file' => $file]);
        }else{
            $file = $this->renderAjax('/experiment/trace_detail_page.php', $data);
            return $this->success(['file' => $file]);
        }
    }


}
