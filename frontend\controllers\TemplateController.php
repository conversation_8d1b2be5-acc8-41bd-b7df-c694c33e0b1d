<?php
namespace frontend\controllers;

use Faker\Provider\Company;
use frontend\controllers\MyController;
use frontend\models\ChemModel;
use frontend\models\TemplateConfig;
use frontend\models\TemplateHistoryModel;
use frontend\models\TemplateRuleIndraw;
use frontend\services\ApprovalServer;
use frontend\services\CompanyServer;
use frontend\services\ExperimentServer;
use frontend\services\ModuleServer;
use service\models\ineln\ExperimentRelayModel;
use yii;
use service\models\ineln\TemplateModel;
use service\models\ineln\TemplateRelayModel;
use yii\helpers\Html;
use frontend\interfaces\CenterInterface;
use frontend\services\TempleServer;
use frontend\models\TemplateForGroupModel;
use frontend\services\GroupSettingServer;
use frontend\services\TemplateSubtypeServer;

use frontend\models\DefineTableKeyModel;
use frontend\services\CompanyAuthServer;
use yii\helpers\ArrayHelper;
use frontend\models\TemplateActionLogModel;
use frontend\models\TemplateHistoryNewModel;


class TemplateController extends MyController {
    private static $comeFrom = [
        'save' => 1,
        'new' => 2,
        'copy' => 3
    ];
    private static $tempType = [
        'all' => 1,
        'sub' => 2,
        'xs' => 4
    ]; 

    /**
     * 渲染 新建模板页面
     */
    public function actionNewTemp() {
        // 获取我所在的鹰群列表
        $interFace = new GroupSettingServer();

        $companyId = 0;
        if(isset($this->userinfo->current_company_id)){
            $companyId = $this->userinfo->current_company_id;
        }

        //$result = $interFace->listTempGroup($this->userinfo->id,$companyId);
        //$groups = empty( $result['status'] )?[]:$result['data'];
        $tempId=0;
        $groups = (new TempleServer())->listTempForGroup($tempId, $this->userinfo->id);

        $file = $this->renderAjax('/experiment/newExp', [
            'type' => 'module',
            'tool' => 'module',
            'come_from' => self::$comeFrom['new'],
            'user_id' => $this->userinfo->id,
            'groupList' => $groups,
            'pageTools' => ['add_module', 'save_template'],
            'display_weather' => 1,
        ]);
        return $this->success($file);
    }

    /**
     * 保存模板
     *
     * @return template 主键
     * @param number $id 实验ID
     * <AUTHOR>
     *
     */
    public function actionSaveAsTemp() {
        $data = \Yii::$app->getRequest()->post();
        if (empty($data['id'])) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        if (empty($data['insertData']['name'])) {
            return $this->fail(\Yii::t('temp', 'must_temp_name'));
        }

        $data['insertData']['tfrom'] = self::$comeFrom['save'];
        $data['insertData']['user_id'] = $this->userinfo->id;
        $data['insertData']['edit_user_id'] = $this->userinfo->id;
        $data['insertData']['email'] = !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone;
        $data['insertData']['type'] = self::$tempType['all'];
        $data['insertData']['title'] = !empty($data['base_data']['title']) ? $data['base_data']['title'] : '';
        $data['insertData']['keywords'] = !empty($data['base_data']['keywords']) ? $data['base_data']['keywords'] : '';
        $data['insertData']['project_id'] = !empty($data['base_data']['project_id']) ? $data['base_data']['project_id'] : '';

        $chemData = $this->chemData($data);

        unset($data['substrates_data']);
        unset($data['catalysts_data']);
        unset($data['solvent_data']);
        unset($data['product_data']);

        $data = array_merge($data, $chemData);

        $res = (new TempleServer())->addTempByCopy($data);
        //$res = (new TempleServer())->addTemp($data);

        if (isset($res['code']) && (1 != $res['code'])) {
            $info = '';
            if(is_array($res['message'])){
            	foreach ($res['message'] as $msg){
            	    $info .= $msg;
            	}
            }

            if(!is_array($res['message'])){
                $info = $res['message'];
            }

            return $this->fail($info);
        }

        return $this->success($res);
    }

    /**
     * 新增模板
     *
     * @param number $id
     */
    public function actionAddTemp() {
        $postData = \Yii::$app->request->post();

        $postData = ModuleServer::decodeModuleData($postData);

        $saveResult = (new TempleServer())->saveTemp($postData, 'add');

        if (empty($saveResult['status'])) {
            return $this->fail($saveResult['info']);
        }
        return $this->success($saveResult['data']);


    }

    /**
     * 编辑模板
     *
     * <AUTHOR> @copyright 2016-4-18
     * @return \common\controllers\json ?r=template/update-temp
     */
    public function actionUpdateTemp() {
        $postData = \Yii::$app->request->post();

        $postData = ModuleServer::decodeModuleData($postData);
        
        // 确保 subtype_id 被正确传递
        // if (isset($postData['insertData']['subtype_id'])) {
        //     $postData['base_data']['subtype_id'] = $postData['insertData']['subtype_id'];
        // }

        $saveResult = (new TempleServer())->saveTemp($postData, 'update');

        // 不记录痕迹，统一在server中记录 

        if (empty($saveResult['status'])) {
            return $this->fail($saveResult['info']);
        }
        return $this->success($saveResult['data']);
    }

    /**
     * 设为经典模板
     *
     * <AUTHOR> @copyright 2016-3-30
     * @return \common\controllers\json ?r=template/set-system-temp
     */
    public function actionSetSystemTemp() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        $id = $postData['id'];

        if (!in_array($this->userinfo->email, self::$adminUserArr)) {
            return $this->fail(\Yii::t('temp', 'no_rights'));
        }

        $res = (new TempleServer())->setTemp($postData);
        if(empty($res['status'])){
        	return $this->fail($res['info']);
        }

        //$this->elnService('ineln.STemple.setTemp', $postData, 'POST');

       /*  if (isset($res['code']) && (1 != $res['code'])) {
            $info = '';
            if(is_array($res['message'])){
            	foreach ($res['message'] as $msg){
            	    $info .= $msg;
            	}
            }

            if(!is_array($res['message'])){
                $info = $res['message'];
            }

            return $this->fail($info);
        } */

        return $this->fail(\Yii::t('base', 'success'));
    }

    /**
     * 复制模板
     *
     * <AUTHOR> @copyright 2016-3-30
     * @return \common\controllers\json
     * ?r=template/duplic-temp
     */
    public function actionDuplicTemp() {
        $postData = \Yii::$app->getRequest()->post();
        
        // 正确处理数组格式的表单字段
        $idArr = isset($postData['temp_id']) ? $postData['temp_id'] : [];
        
        // 如果是单个值，转换为数组
        if (!is_array($idArr)) {
            $idArr = [$idArr];
        }

        if (empty($idArr)) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        if (empty($postData['name'])) {
            return $this->fail(\Yii::t('temp', 'must_temp_name'));
        }

        $insertData['name'] = $postData['name'];
        $insertData['type'] = self::$tempType['all'];
        $insertData['tfrom'] = self::$comeFrom['copy'];
        $insertData['user_id'] = $this->userinfo->id;
        $insertData['email'] = !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone;
        $insertData['edit_user_id'] = $this->userinfo->id;
        $insertData['descript'] = isset($postData['descript']) ? $postData['descript'] : '';
        $insertData['subtype_id'] = isset($postData['subtype_id']) ? $postData['subtype_id'] : 0;



        $res = (new TempleServer())->duplicTemp([
            'idArr' => $idArr,
            'insertData' => $insertData,
            'user_id' => $this->userinfo->id
        ]);

        if (empty($res['status'])) {

            return $this->fail($res['info']);
        }

        return $this->success($res['data']);
    }


    /**
     * 复制文本方法模板
     * 
     * <AUTHOR> @copyright 2023
     * @return \common\controllers\json
     */
    public function actionDuplicIntextTemp() { 
        $postData = \Yii::$app->getRequest()->post();
        
        if (!isset($postData['temp_id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        
        $old_temp_id = $postData['temp_id'];
        
        $insertData = [];
        $insertData['name'] = $postData['name'];
        $insertData['type'] = self::$tempType['sub']; // 文本方法模板 值为2
        $insertData['tfrom'] = self::$comeFrom['copy'];
        $insertData['user_id'] = $this->userinfo->id;
        $insertData['email'] = !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone;
        $insertData['edit_user_id'] = $this->userinfo->id;
        $insertData['subtype_id'] = isset($postData['subtype_id']) ? $postData['subtype_id'] : 0;
        
        $res = (new TempleServer())->duplicIntextTemp(
            $old_temp_id, $insertData, $this->userinfo->id
        );
        
        if (empty($res['status'])) {
            return $this->fail($res['info']);
        }
        
        return $this->success($res['data']);
    }


    /**
     * 复制表内模板
     * 
     * <AUTHOR> @copyright 2023
     * @return \common\controllers\json
     */
    public function actionDuplicIntableTemp() { 
        $postData = \Yii::$app->getRequest()->post();
        
        if (!isset($postData['temp_id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        
        $old_temp_id = $postData['temp_id'];
        
        $insertData = [];
        $insertData['name'] = $postData['name'];
        $insertData['type'] = self::$tempType['xs']; // 表格方法模板 值为4
        $insertData['tfrom'] = self::$comeFrom['copy'];
        $insertData['user_id'] = $this->userinfo->id;
        $insertData['email'] = !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone;
        $insertData['edit_user_id'] = $this->userinfo->id;
        $insertData['subtype_id'] = isset($postData['subtype_id']) ? $postData['subtype_id'] : 0;
        
        $res = (new TempleServer())->duplicIntableTemp(
            $old_temp_id, $insertData, $this->userinfo->id
        );
        
        if (empty($res['status'])) {
            return $this->fail($res['info']);
        }
        
        return $this->success($res['data']);
    }


    /**
     * 删除模板
     *
     * @return template 主键
     * @param number $tempid
     * <AUTHOR>
     *
     */
    public function actionDelTemp() {
        $idArr = \Yii::$app->getRequest()->post('id');
        if (empty($idArr)) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        $tServer = new TempleServer();
        $res = $tServer->delTemp([
            'idArr' => $idArr,
            'user_id' => $this->userinfo->id
        ]);

        if(empty($res['status'])){
            return $this->fail(\Yii::t('base', 'failed'));
        }

        return $this->success($res['data']);
    }

    /**
     * 获取模板数据
     *
     * <AUTHOR> @copyright 2016-4-8
     * @param int $id
     * @return \common\controllers\json ?r=template/get-temp&id=1
     */
    public function actionViewTemp() {
        $tempId = \Yii::$app->request->get('id', 0);
        if (empty($tempId)) {
            return $this->fail(\Yii::t('exp', 'select_temp'));
        }

        // 获取实验数据
        $tempResult = (new TempleServer())->getTempById($tempId);
        if($tempResult['status'] == 0) {
            return $this->fail($tempResult['info']);
        }
        $tempData = $tempResult['data'];
        //print_r($tempData);exit;
        $userId = $this->userinfo->id;
        // 判断是否有打开权限
        if (!((new TempleServer())->checkTemplateReadable($userId, $tempId))) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }


         $groupList = (new TempleServer())->listTempForGroup($tempId, $userId);
         $defaultGroupList=$groupList;
         foreach($defaultGroupList as $key=>$value){
             if($value['is_check']==0){
                 unset($defaultGroupList[$key]);
             }
         }

        $groupsWithTemp = (new GroupSettingServer())->listTempGroup($userId);//获取用户拥有模板权限的鹰群
        $groupIdsWithTemp = array_column($groupsWithTemp['data'], 'id');


        $groupIdsWithTemp2 = (new TempleServer())->getGroupIdsRelayTemp($tempId);//获取创建模板时所选的鹰群

        if (!empty(array_intersect($groupIdsWithTemp, $groupIdsWithTemp2))) {   //如果创建模板时所选鹰群和有模板权限的鹰群有交集，视为有模板权限
            $editTempPower = 1;
        } else {
            $editTempPower = 0;
        }

        if ($tempData['base_data']['user_id'] == $this->userinfo->id) {
            $groupIds = $groupIdsWithTemp;
        } else {
            $groupIds = array_unique(array_merge($groupIdsWithTemp, $groupIdsWithTemp2));
        }
        $groupList = (new CenterInterface())->groupByGroupIds($groupIds);

        $postData = [
            'data' => $tempData,
            'type' => 'module',
            'temp_type' => 1,
            'user_id' => $this->userinfo->id ,
            'groupList' => $groupList,
            'default_group_ids' => $groupIdsWithTemp2,
            'isCheck' => true
        ];
        $templateConfig = new TemplateConfig();
        $base_data_rule = $templateConfig->getRequireData($tempId, 0);
        $postData['base_data_rule'] = $base_data_rule;

        // 获取盐型列表 add by zhj hkk 2019/3/14
        $saltList = (new ExperimentServer())->saltList();
        $postData['saltList'] = $saltList['data'];

        $userDetail = (new CenterInterface())->getUserAllInfoByCompanyId([$userId]);
        $userId2Detail = array_column($userDetail, null, 'user_id');

        

        //检查模板生效模式  1为模板保存立即生效，2为需要发布才生效
        $settingRes = (new CompanyServer())->getCompanySetting([
            'TEMPLATE_EFFECT_MODE',
        ]);

        $setting = @getVar($settingRes['data'], []);
        $postData['templateEffectMode'] = isset($setting['TEMPLATE_EFFECT_MODE']['value']) ? $setting['TEMPLATE_EFFECT_MODE']['value'] : 1;       
        
        $needPublishApproval = false;
        $needApproval = false;
        $actionArr = ['copy']; // 可以对模板进行的操作,copy功能什么情况下都有

        //如果是发布管控模式,对于全文模板
        if($postData['templateEffectMode'] == 2 && isset($tempData['base_data']['type']) && $tempData['base_data']['type'] == 1){
            $needPublishApproval = (new TempleServer())->checkTemplateApproval(null, 'publish', $tempData['base_data']['type'], $tempData['base_data']['subtype_id'], $userId2Detail[$userId],1,[]);  
        }else{
            
            $needApproval = (new TempleServer())->checkTemplateApproval(null, 'create_template', $tempData['base_data']['type'], $tempData['base_data']['subtype_id'], $userId2Detail[$userId],1,[]);
        }


        \Yii::$app->view->params['edit'] = 2;
        

        // 对于自己的模板 || 有模板管理权限的用户
        // 草稿状态 or 通用设置为不需要审核 -> 可以进行编辑(添加模块，保存，设置必填项，设置结构化数据，设置结构化数据，审阅模式)
        // 审核中  -> 可以进行撤销
        // 审核通过 and 通用设置为需要审核  -> 可以启动编辑

        //判断是否拥有企业及权限“修改模板管理”

        //获取通用设置的权限，鉴权
        $settingRes = (new CompanyServer())->getCompanySetting([
            'EMPOWER_BY_AUTH',
        ]);
        $setting = @getVar($settingRes['data'], []);
        if(isset($setting['EMPOWER_BY_AUTH']) && $setting['EMPOWER_BY_AUTH']['value']==1){
            //获取企业级权限
            $companyAuth=CompanyAuthServer::getCompanyAuthByUserId($this->userinfo->id);
            $companyFeature=$companyAuth['company_feature'];
        }

        if ($tempData['base_data']['user_id'] == $this->userinfo->id||(isset($companyFeature)&&$companyFeature['edit_template_manage'])) {
            $editTempPower = 1;
            if(($tempData['base_data']['step'] == 1 || $tempData['base_data']['step'] == 4) && $needApproval){
                $actionArr[] = 'submit_audit';//添加提交审批按钮权限
            }
            if($tempData['base_data']['step'] == 1 || $tempData['base_data']['step'] == 4 || !$needApproval) {
                \Yii::$app->view->params['edit'] = 1;
                $actionArr[] = 'save';
                $actionArr[] = 'add_module';
                $actionArr[] = 'set_require';
                $actionArr[] = 'set_struct_data';
                $actionArr[] = 'set_revision';

            } else if ($tempData['base_data']['step'] == 2) {
                $actionArr[] = 'cancel_audit';
            }  else if ($tempData['base_data']['step'] == 3 && $needApproval) {
                $actionArr[] = 'edit';

            }

            if($needPublishApproval){
                $actionArr[] = 'submit_publish_audit';
            }else if($postData['templateEffectMode'] == 2){
                $actionArr[] = 'publish';
            }            
        }

        $postData['tempPower'] = $editTempPower;

        if (empty($editTempPower)) {    //如果用户没有模板权限，将view对象的edit属性设为0, 并删除可以操作模板的权限
            \Yii::$app->view->params['edit'] = 0;
            $actionArr = array_diff($actionArr, [/*'copy', deleted by xieyuxiang 2022.10.21 copy功能什么情况下都有*/'save', 'add_module', 'set_require', 'set_struct_data', 'set_revision']);
        }

        /*如果模板正在审批,去除编辑模板的权限 bug#32622，mod dx*/
        // 发布管控模式下，模板正在审批时，可以编辑，即时生效模式下，不可以编辑
        $tempIsApproving = ($tempData['base_data']['step'] == 2);
        if ($tempIsApproving && $postData['templateEffectMode'] == 1) {
            \Yii::$app->view->params['edit'] = 0;
            $actionArr = array_diff($actionArr, ['save', 'add_module', 'set_require', 'set_struct_data', 'set_revision']);
        }

        // 审核人显示审核通过和审核拒绝
        $res = (new ApprovalServer())->listApprovalByType($userId, \Yii::$app->params['approval_type']['template'], [
            'business_id' => $tempId
        ]);
        if(@getVar($res['total'], 0) > 0) {
            $actionArr[] = 'audit';
        }
        $postData['actionArr'] = $actionArr;

        // 当前用户信息，用于xsheet jiangdm 2022/7/29
        $currentUserInfo = [
            'user_id' => $this->userinfo->id,
            'email' => $this->userinfo->email,
            'name' => $this->userinfo->name,
            'real_name' => $this->userinfo->real_name,
            'nick_name' => $this->userinfo->nick_name,
            'phone' => $this->userinfo->phone,
        ];
        $postData['currentUserInfo'] = $currentUserInfo;

        //获取模板子类型信息 main_type = 1
        $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList($main_type = 1);
        $postData['templateSubtypeList'] = $templateSubtypeList['data'];

        //获取显示版本的信息
        $displayVersion = (new TempleServer())->getDisplayVesionDetail($tempId);
        $postData['displayVersion'] = $displayVersion['data'];

        //获取模板的最大内部版本号
        $maxInnerVersion = (new TempleServer())->getMaxInnerVersion($tempId);
        $postData['maxInnerVersion'] = $maxInnerVersion;

        $tempFile = $this->renderAjax('/experiment/template.php', $postData);


        return $this->success([
            'contentHtml' => $tempFile,
            'moduleName' => $tempData['base_data']['temp_name'],
            'tagName' => $tempData['base_data']['temp_name'],
        ]);
    }

    /**
     * 查看模板历史版本，无法编辑或保存，暂时不支持恢复
     * @param int $history_id 历史版本ID
     * @return \common\controllers\json
     */
    public function actionViewTempHistory(){
        $historyId = \Yii::$app->request->get('history_id', 0);
        if (empty($historyId)) {
            return $this->fail(\Yii::t('temp', 'select_history'));
        }

        // 获取历史版本数据
        $historyResult = TemplateHistoryNewModel::find()->where(['id' => $historyId])->asArray()->one();
        if(empty($historyResult)) {
            return $this->fail(\Yii::t('temp', 'no_history'));
        }
        $historyData = $historyResult;
        $tempId = $historyData['template_id'];
        
        $userId = $this->userinfo->id;
        // 判断是否有模板的查看权限
        if (!((new TempleServer())->checkTemplateReadable($userId, $tempId))) {
            return $this->fail(\Yii::t('base', 'no_auth').$tempId);
        }
        
        // 获取模板基本信息
        $tempResult = (new TempleServer())->getBasicById($tempId);
        if($tempResult['status'] == 0) {
            return $this->fail($tempResult['info']);
        }
        $tempData = $tempResult['data'];
        
        // 获取模板内容数据（包含模块数据）
        $tempContentResult = (new TempleServer())->getTempHistoryById($historyId);
        if($tempContentResult['status'] == 0) {
            return $this->fail($tempContentResult['info']);
        }
        $data = $tempContentResult['data'];
        
        // 获取模板子类型列表
        $templateSubtypeList = [];
        if (!empty($tempData['type'])) {
            $templateSubtypeResult = (new TemplateSubtypeServer())->getTemplateTypeList($tempData['type']);
            if ($templateSubtypeResult['status'] == 1) {
                $templateSubtypeList = $templateSubtypeResult['data'];
            }
        }
        
        // 获取分组列表
        $groupList = [];
        
        // 设置为只读模式
        \Yii::$app->view->params['edit'] = 0;
        
        $postData = [
            'historyData' => $historyData,
            'tempData' => $tempData,
            'baseData' => $tempData, // 添加baseData，与template.php保持一致
            'data' => $data,
            'type' => isset($tempData['type']) ? $tempData['type'] : '',
            'groupList' => $groupList,
            'templateSubtypeList' => $templateSubtypeList,
            'tempPower' => 0, // 历史版本没有编辑权限
            'isHistory' => true,
            'pageTools' => []
        ];
        
        $tempFile = $this->renderAjax('/experiment/template_history.php', $postData);
        
        return $this->success([
            'contentHtml' => $tempFile,
            'moduleName' => $tempData['name'] . '_' . $historyData['user_version'],
            'tagName' => $tempData['name'] . '_' . $historyData['user_version'],
        ]);
    }   

    /**
     * 存为子模板
     *
     * <AUTHOR> @copyright 2016-4-18
     *  $data['insertData'] = [
            'name' => $data['insertData']['name'],
            'content' => $data['insertData']['content'],
            'descript' => $data['insertData']['descript']
        ];

        $data['tempReal'] = [
            [
                'component_id' => 2 ,
                'name' => '组件的name',
                'descript' => '组件的描述'
            ]
        ];
     * ?r=template/add-sub-temp
     */
    public function actionAddSubTemp() {
        $data = \Yii::$app->getRequest()->post();
        $insertData = $data['insertData'];

        if (empty($insertData['name'])) {
            return $this->fail(\Yii::t('temp', 'must_temp_name'));
        }

        if (empty($data['tempReal'])) {
            return $this->fail(\Yii::t('temp', 'son_must_module'));
        }
//        $needApproval = TempleServer::needApproval($this->userinfo->id);
        $currUserDetail = CompanyServer::getCurrUserDetail($this->userinfo->id); //模板审批逻辑修改，先弃用旧的审批方式
        $subtype_id = isset($insertData['subtype_id']) ? $insertData['subtype_id'] : 0;
        $needApproval = (new TempleServer())->checkTemplateApproval(null, 'create_template', 2,$subtype_id, $currUserDetail,0,[]);

        $postData = [
            'is_sub' => TRUE,
            'insertData' => [
                'name' => $insertData['name'],
                'tfrom' => self::$comeFrom['new'], //tfrom一项更改为$comeFrom['new']，值为2，来源为自建，新建单个方法模板的来源不应该是实验
                'user_id' => $this->userinfo->id,
                'edit_user_id' => $this->userinfo->id,
                'email' => !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone,
                'type' => self::$tempType['sub'],
                'subtype_id' => $subtype_id,
                'descript' => isset($insertData['descript']) ? $insertData['descript'] : '',
                'content' => @getVar($insertData['content'], ''),
                'step' => $needApproval ? 1 : 3,
            ],
            'temp_real' => [$data['tempReal']]
        ];

        $res = (new TempleServer())->addSubTemp($postData);

        if (empty($res['status'])) {
            return $this->fail($res['info']);
        }
        return $this->success([
            'id' => $res['data'],
            'message' => \Yii::t('base', 'save_success'),
            'need_approval' => $needApproval,
            ]);
    }

    //查看方法模板
    public function actionViewSubTemp() {
        $id = \Yii::$app->request->get('id', 0);

        $res = (new TempleServer())->getTemp($id, FALSE, 2);
        $tempData = @getVar($res['data'], []);
        if (empty($tempData)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }
        $userId = $this->userinfo->id;

        // 判断是否有打开权限
        if (!((new TempleServer())->checkTemplateReadable($userId, $id))) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        $currUserDetail = CompanyServer::getCurrUserDetail($userId);
        $needApproval = (new TempleServer())->checkTemplateApproval(null, 'create_template', 2, $tempData['subtype_id'], $currUserDetail);
        $tempData['need_approval'] = $needApproval;
        //获取用户模板管理权限
        $companySettings = CompanyAuthServer::getCompanyAuthByUserId($this->userinfo->id);
        $tempPower = !empty($companySettings['company_feature']['edit_template_manage']) ? 1 : 0;
        // 审核人显示审核通过和审核拒绝
        $res = (new ApprovalServer())->listApprovalByType($this->userinfo->id, \Yii::$app->params['approval_type']['template'], [
            'business_id' => $id
        ]);
        if(@getVar($res['total'], 0) > 0) {
            $tempData['approverIsMe'] = true;
        }

        //获取模板子类型信息 main_type = 2
        $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList($main_type = 2);
        $tempData['templateSubtypeList'] = $templateSubtypeList['data'];

        $html = $this->renderAjax('/module/son_temp', [
            'data' => $tempData,
            'tempPower' => $tempPower,
        ]);

        return $this->success([
            'name' => @getVar($tempData['name']),
            'content' => @getVar($tempData['content']),
            'contentHtml' => $html, // modified by hkk 2020/6/28
        ]);
    }

    public function actionViewIntableTemp() {
        $id = \Yii::$app->request->get('id', 0);

        $res = (new TempleServer())->getTemp($id, FALSE, 4);
        $tempData = @getVar($res['data'], []);
        if (empty($tempData)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }
        $userId = $this->userinfo->id;

        // 判断是否有打开权限
        if (!((new TempleServer())->checkTemplateReadable($userId, $id))) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        $currUserDetail = CompanyServer::getCurrUserDetail($userId);
        $needApproval = (new TempleServer())->checkTemplateApproval(null, 'create_template', 4, $tempData['subtype_id'], $currUserDetail);
        $tempData['need_approval'] = $needApproval;
        //获取企业编辑模板权限设置
        $companySettings = CompanyAuthServer::getCompanyAuthByUserId($this->userinfo->id);  //
        $tempPower = !empty($companySettings['company_feature']['edit_template_manage']) ? 1 : 0;
        // 审核人显示审核通过和审核拒绝
        $res = (new ApprovalServer())->listApprovalByType($this->userinfo->id, \Yii::$app->params['approval_type']['template'], [
            'business_id' => $id
        ]);
        if(@getVar($res['total'], 0) > 0) {
            $tempData['approverIsMe'] = true;
        }
        \Yii::$app->view->params['edit'] = 2;
        if ($tempData['step'] == 1 || !$needApproval) {
            \Yii::$app->view->params['edit'] = 1;
        }

        //获取模板子类型信息 main_type = 4
        $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList($main_type = 4);
        $tempData['templateSubtypeList'] = $templateSubtypeList['data'];

        $html = $this->renderAjax('/module/intable_temp', [
            'data' => $tempData,
            'tempPower' => $tempPower,
        ]);

        return $this->success([
            'name' => @getVar($tempData['name']),
            'content' => @getVar($tempData['content']),
            'contentHtml' => $html, // modified by hkk 2020/6/28
        ]);
    }

    /**保存方法模板
     * @return \common\controllers\json
     * @auther: dx
     * @date:2022/9/16
     */
    public function actionSaveSubTemp() {
        $id = \Yii::$app->request->post('id', 0);
        $name = \Yii::$app->request->post('name', '');
        if(empty($name)) {
            $this->fail(\Yii::t('temp', 'must_temp_name'));
        }
        $descript = \Yii::$app->request->post('descript', '');
        $img = \Yii::$app->request->post('img', '');
        $zipContent = \Yii::$app->request->post('zip_content');
        if (!empty($zipContent)) {
            $content = gzdecode(base64_decode($zipContent));
            $content = json_decode($content, true);
            $content = $this->purify($content);

        } else {
            $content = \Yii::$app->request->post('content', '');
        }

        $currUserDetail = CompanyServer::getCurrUserDetail($this->userinfo->id);
        $templateServer = new TempleServer();
        $tempData = TemplateModel::findOne($id);
        $subtype_id = \Yii::$app->request->post('subtype_id', 0);
        $needApproval = $templateServer->checkTemplateApproval(null, 'create_template', $tempData['type'], $subtype_id, $currUserDetail, 0,[]);

        $res = (new TempleServer())->saveSubTemp($id, [
            'name' => $name,
            'content' => $content,
            'descript' => $descript,
            'img' => $img,
            'subtype_id' => $subtype_id,
            'step' => $needApproval ? 1 : 3,
        ]);

        if($res['status'] == 1) {
            return $this->success([
                'message' => \Yii::t('base', 'save_success'),
                'need_approval' => $needApproval,
            ]);
        }

        return $this->fail(\Yii::t('exp', 'save_failed'));
    }

    /**
     * 模板列表
     *
     * @param number $id
     */
    public function actionTemplateList() {


        // 企业模板权限控制 jiangdm 2022/7/13
        $set_conmany_template_write=1;
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['set_conmany_template_write'] != 1) {
            $set_conmany_template_write = 0;
        }
        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23

        $where['module_type'] = \Yii::$app->request->post('module_type', 0);
        //template_main_types 来自于template_main_types，是“，”分隔的字符串，为空时传入空数组
        $where['template_main_types'] = \Yii::$app->request->post('template_main_types', '');
        if(empty($where['template_main_types'])) {
            $where['template_main_types'] = [];
        } else {
            $where['template_main_types'] = explode(',', $where['template_main_types']);//转化为数组，才能和数据库中的值比较。
        }
        //template_sub_types来自于template_subtypes_json，是json格式的字符串，需要转化为字符串数组，才能和数据库中的值比较，为空时传入空数组。
        $where['template_sub_types'] = \Yii::$app->request->post('template_subtypes_json', '');
        if(empty($where['template_sub_types'])) {
            $where['template_sub_types'] = [];
        } else {
            $where['template_sub_types'] = json_decode($where['template_sub_types'], true);
        }

        $where['module_keywords'] = \Yii::$app->request->post('module_keywords', '');
        $where['module_from'] = \Yii::$app->request->post('module_from', 0);//added by xieyuxiang 2022.8.23

        //查询该企业下所有用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        // 获取用户id和企业_id
        $where['user_id'] = $this->userinfo->id;
        $where['company_id'] = $company_id;

        $tempData = (new TempleServer())->listTempLateNew($where, $limit, $page, $orderBy, $orderType);
        $groupSettingServer = new GroupSettingServer();
        $result = $groupSettingServer->listTempGroup($this->userinfo->id);
        $groups = empty($result['status']) ? [] : $result['data'];

        // 获取用户信息
        $userIds = array_column($tempData['temp_list'], 'user_id');
        $userIds = array_merge($userIds,[$this->userinfo->id]);
		//added by xieyuxiang 2022.8.23 转让模板里需要存储上一任模板拥有者的信息
        $previousIds=array_column($tempData['temp_list'], 'previous_id');
        $previousIds = array_filter($previousIds);
        $userIds = array_merge($userIds,$previousIds);
        $userIds = array_unique($userIds);
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId($userIds);
        $userList = yii\helpers\ArrayHelper::index($userList, 'id');
        $userDetail = $userList[$this->userinfo->id];
        foreach($tempData['temp_list'] as $key=>$template){
            if ($template['type'] == 3) {  // 默认模板 || 表格模板 不需要审核
                $needApproval = false;
            } else {
                $templateServer=new TempleServer();
                $needApproval = $templateServer->checkTemplateApproval($template['id'],'create_template',$template['type'],$template['subtype_id'],$userDetail);
            }

            $tempData['temp_list'][$key]['needApproval'] = $needApproval;
        }


        $data['tempLateData'] = $tempData['temp_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $tempData['totalCount'];
        $data['params'] = $where;
        $data['set_conmany_template_write'] = $set_conmany_template_write;
        $data['type'] = 'list';
        $data['groups'] =  $groups?:[];
        $data['userList'] = $userList;

        //获取模板子类型列表数据
        $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList();
        $data['templateSubtypeList'] = $templateSubtypeList['data'];

        $data['templateEffectMode'] = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];

        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/module/moduleList_new.php', $data);
            return $this->success([
                //'listData' => $file,
                'tool' => null,
                'contentHtml' => $file,
            ]);
        }

        // 只更新表格数据
        $file = $this->renderAjax('/module/moduleList_page_new.php', $data);
        return $this->success([
            'listData' => $file,
            'tool' => null,
        ]);
    }

    /**
     * Note: 创建实验时可选的模板列表
     * @return \common\controllers\json
     * <AUTHOR>
     * @date 2023/3/9 19:53
     */
    public function actionListForExp() {
        $userId = $this->userinfo->id;
        $params['user_id'] = $userId;
        $tempServer = new TempleServer();
        $groupList = $tempServer->listUserGroup([
            'user_id' => $userId
        ], $this->userinfo->current_company_id);
        if (empty($groupList)) {
            $params['group_ids'] = NULL;
        } else {
            $params['group_ids'] = array_column($groupList, 'group_id');
        }
        $params['company_id'] = $this->userinfo->current_company_id;
        $params['step'] = ['step' => 3];

        // 获取模板列表数据，并翻译模板名称
        $templateList = $tempServer->tempForExp($params);
        $templateList = $templateList['data'];
        foreach ($templateList as $key => $temp) {
            $templateList[$key]['name'] = $tempServer->translateName($temp['name']);
        }

        // 获取模板子类型列表数据
        $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList(1);
        $data['templateSubtypeList'] = $templateSubtypeList['data'];
        
        $data['templateList'] = $templateList;        
        return $this->success($data);
    }

    /**
     * 获取子模板列表
     *
     * <AUTHOR> @copyright 2016-4-18
     * ?r=template/list-sub-temp
     * @return ['id','name','descript', 'content']
     */
    public function actionListSubTemp() {
        $postData = \Yii::$app->getRequest()->post();
        $postData['component_id'] = 2;

        $editComponent = [
            \Yii::$app->params['component']['operation'],
            \Yii::$app->params['component']['discuss'],
            \Yii::$app->params['component']['diy']
        ];

        if (empty($postData['component_id']) || !in_array($postData['component_id'], $editComponent)) {
            return $this->fail(\Yii::t('temp', 'select_one_module'));
        }

        $groupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id,$this->userinfo-> current_company_id);

        $groupIdArr = array_column($groupList, 'group_id');

        $tempLateData = (new TempleServer())->subListTemp([
            'user_id' => $this->userinfo->id,
            'company_id' => $this->userinfo->current_company_id,
            'component_id' => $postData['component_id'],
            'group_id' =>  $groupIdArr
        ]);
        /* $this->elnService('ineln.STemple.subListTemp', [
            'user_id' => $this->userinfo->id,
            'component_id' => $postData['component_id'],
            'group_id' =>  $groupIdArr
        ], 'POST'); */



        $tempLateData = $tempLateData['data'];
        foreach($tempLateData as $key=>$value){
            unset($value['content']);
            $tempLateData[$key] =$value;
        }
        //print_r($tempLateData);exit;
        return $this->success($tempLateData);

    }

    /**
     * 存为子模板
     *
     * <AUTHOR> @copyright 2016-4-18
     *  $data['insertData'] = [
            'name' => $data['insertData']['name'],
            'content' => $data['insertData']['content'],
            'descript' => $data['insertData']['descript']
        ];

        $data['tempReal'] = [
            [
                'component_id' => 2 ,
                'name' => '组件的name',
                'descript' => '组件的描述'
            ]
        ];
     * ?r=template/add-sub-temp
     */
    public function actionSetRequire() {

        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['temp_id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        //获取模块信息
        $temp = (new TemplateRelayModel())->getTempRelay($postData['temp_id']);
        $templateConfig = new TemplateConfig();
        foreach($temp as $key=>$value)
        {
            if($value['component_id']==1)
            {
                $find = TemplateRuleIndraw::findOne(['relay_id' => $value['id']]);
                $indrawRequiredFields = $find ? $find->getAttributes(['reactant', 'solvent', 'condition', 'product', 'details']) :
                    ['reactant' => null, 'solvent' => null, 'condition' => null, 'product' => null, 'details' => null];
                $chem = ChemModel::findOne(['parent_id' => $value['id'], 'type' => 2]);
                $config = json_decode($chem['material_column_config'], true);
                $reactantAndReagent = $config['substratesConfig'];
                $solvent = $config['solventConfig'];
                $condition = $config['conditionConfig'];
                $product = $config['productConfig'];
                $detail = $config['detailConfig'];

                $temp[$key]['indraw_reactant']=$reactantAndReagent;
                $temp[$key]['indraw_product']=$product;
                $temp[$key]['indraw_reagent']=$reactantAndReagent;
                $temp[$key]['indraw_solvent']=$solvent;
                $temp[$key]['indraw_condition']=$condition;
                $temp[$key]['indraw_details']=$detail;
                $temp[$key]['required_fields'] = $indrawRequiredFields;
            }

            if (in_array($value['component_id'], [2, 3, 4, 7, 8, 11, 12])) {
                $temp[$key]['is_require'] = isset($templateConfig->getRequireData($postData['temp_id'], $value['id'])['is_require'])
                    ? $templateConfig->getRequireData($postData['temp_id'], $value['id'])['is_require'] : 0;
            }

            if($value['component_id']==13)
            {
                $result = $templateConfig->getRequireData($postData['temp_id'], $value['id']);
                $DefineTableKeyModel = new DefineTableKeyModel();
                $define_key = $DefineTableKeyModel->defineDataByRelayId($value['id'], 2);

                $temp[$key]['define']=$result;
                $temp[$key]['define_key']=isset($define_key['field_key'])?$define_key['field_key']:'';

            }

            if($value['component_id']==16)
            {
                $tlc = $templateConfig->getRequireData($postData['temp_id'], $value['id']);
                $temp[$key]['tlc']=$tlc;
            }
        }

        array_multisort(array_column($temp,'class'),SORT_ASC,$temp);

        //获取基础数据信息
        $base_data = $templateConfig->getRequireData($postData['temp_id'], 0);

        //获取自定义项
        $template_data = (new TemplateModel())->getTemp($postData['temp_id']);

        $define_item = $template_data['define_item'];


        $file = $this->renderPartial('/experiment/set_require.php', [
            'relay_list' => $temp,
            'base_data' => $base_data,
            'define_item' => json_decode($define_item,true),
            'temp_id' => $postData['temp_id'],
        ]);

        return $this->success($file);
    }

    /**
     * 保存必填项信息
     *
     * <AUTHOR>
     * @copyright 2016-4-18
     *  $data['insertData'] = [
            'name' => $data['insertData']['name'],
            'content' => $data['insertData']['content'],
            'descript' => $data['insertData']['descript']
        ];

        $data['tempReal'] = [
            [
                'component_id' => 2 ,
                'name' => '组件的name',
                'descript' => '组件的描述'
            ]
        ];
     * ?r=template/add-sub-temp
     */
    public function actionSetRequireAction() {

        $postData = \Yii::$app->getRequest()->post();

        $temp_id =$postData['temp_id'];

        if (empty($postData['temp_id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        $chk_value = empty($postData['chk_value']) ? [] : $postData['chk_value'];
        foreach($chk_value as $key=>$value2)
        {
            if($value2=='on')
            {
                unset($chk_value[$key]);
            }
        }
        //组装数据
        $require_data=[];
        for($x=0; $x<=17; $x++) {
             $require_data[$x]=[];
            foreach($chk_value as $key=>$value3)
            {
                $value_arr= explode('|', $value3);
                if($value_arr[0]==$x)
                {
                    if(isset($value_arr[1])){
                        $require_data[$x][]=$value_arr[1];
                    }

                }

            }
        }

        $templateConfig = new TemplateConfig();
        // 基础数据
        $base_vals = [];
        // 自定义表格
        $define_table_vals = [];
        $base_data = (new TemplateRelayModel())->relayByExperimentId($temp_id,13);
        $relay_ids = array_column($base_data, 'id');
        foreach ($relay_ids as $relay_id) {
            $define_table_vals[$relay_id] = [];
        }
        // tlc
        $tlc_vals = [];
        $base_data = (new TemplateRelayModel())->relayByExperimentId($temp_id,16);
        if (isset(array_column($base_data, 'id')[0])) {
            $tlc_relay_id = array_column($base_data, 'id')[0];
        }


        //处理数据
        foreach($require_data as $key=>$value)
        {
            //处理基础数据
            if($key==0){
                foreach ($value as $val) {
                    $base_vals[] = $val;
                }
            }

            //indraw
            if ($key == 1) {
                $base_data = (new TemplateRelayModel())->relayByExperimentId($temp_id, 1);

                if ($base_data) {
                    //查看有无老数据
                    $relay_id = $base_data[0]['id'];

                    $indrawRequiredFields = TemplateRuleIndraw::findOne(['relay_id' => $relay_id]);
                    if (!$indrawRequiredFields) {
                        $indrawRequiredFields = new TemplateRuleIndraw();
                        $indrawRequiredFields->setAttribute('relay_id', $relay_id);
                        $indrawRequiredFields->save();
                    }

                    //更新数据
                    $reactant = [];
                    $product = [];
                    $reagent = [];
                    $solvent = [];
                    $condition = [];
                    $details = [];
                    foreach ($value as $val2) {
                        $val_arr = explode('#', $val2);
                        if ($val_arr[1] == 0) {
                            $reactant [] = $val_arr[0];
                        }
                        if ($val_arr[1] == 1) {
                            $product [] = $val_arr[0];
                        }
                        if ($val_arr[1] == 2) {
                            $reagent [] = $val_arr[0];
                        }
                        if ($val_arr[1] == 3) {
                            $solvent [] = $val_arr[0];
                        }
                        if ($val_arr[1] == 4) {
                            $condition [] = $val_arr[0];
                        }
                        if ($val_arr[1] == 5) {
                            $details [] = $val_arr[0];
                        }
                    }
                    $indrawRequiredFields->setAttributes([
                        'reactant' => json_encode($reactant),
                        'reagent' => json_encode($reactant),
                        'product' => json_encode($product),
                        'solvent' => json_encode($solvent),
                        'condition' => json_encode($condition),
                        'details' => json_encode($details),
                    ]);
                    $indrawRequiredFields->save();
                }
            }
            //文本等
            if($key==2 || $key==3 || $key==4 || $key==7 || $key==8 || $key==11 || $key==12){
                //如果有 则修改数据库 如果没有则清除信息
                //清除所有
                $allResult = TemplateRelayModel::find()
                    ->where(['template_id'=>$temp_id, 'component_id'=>$key, 'status'=>1])
                    ->select('id')
                    ->asArray()->all();
                $allRelay = array_column($allResult, 'id');
                // 先清空
                foreach ($allRelay as $val) {
                    $templateConfig->setRequireData($temp_id, $val, TemplateConfig::$components['other'], ['is_require' => 0]);
                }
                // 再设置
                foreach ($value as $val) {
                    $templateConfig->setRequireData($temp_id, $val, TemplateConfig::$components['other'], ['is_require' => 1]);
                }
            }

            //处理自定义表格
            if($key==13){
               foreach($value as $val)
               {
                   $current = explode('_', $val);
                   $define_field = str_replace('define', 'define_field', $current[0]);
                   $relay_id = $current[1];
                   $define_table_vals[$relay_id][] = $define_field;
               }
            }

            //处理自定义项
            if($key==14){
                //先找到模板里的数据
                $require_arr = [];
                foreach($value as $val){
                    $val_arr = explode('_',$val);
                    $require_arr[] =$val_arr[1];
                }

                //获取自定义项
                $template_data = (new TemplateModel())->getTemp($temp_id);

                $define_item = json_decode($template_data['define_item'],true);
                if (empty($define_item)) { // add by hkk 2020/7/31
                    $define_item = [];
                }
                foreach($define_item as $key=>$val){
                    if(in_array($key,$require_arr)){
                        $define_item[$key]['is_require'] = 1;
                    }
                    else
                    {
                        $define_item[$key]['is_require'] = 0;
                    }
                }
                $define_item_str = json_encode($define_item);
                TemplateModel::updateAll(['define_item'=>$define_item_str], ['id'=>$temp_id]);
                //exit;

            }

            //处理参考文献
            if($key==16){
                foreach($value as $val)
                {
                    $tlc_vals[] = $val;
                }
            }
        }
        $templateConfig->setRequireData($temp_id, 0, TemplateConfig::$components['base'], $base_vals);
        foreach ($define_table_vals as $relay_id => $define_table_val) {
            $templateConfig->setRequireData($temp_id, $relay_id, TemplateConfig::$components['define_table'], $define_table_val);
        }
        if (!empty($tlc_vals)) {
            $templateConfig->setRequireData($temp_id, $tlc_relay_id, TemplateConfig::$components['tlc'], $tlc_vals);
        }

        $data['define_item'] = $define_item;
        return $this->success($data);
    }

    /**
     * 存为子模板
     *
     * <AUTHOR> @copyright 2016-4-18
     *  $data['insertData'] = [
            'name' => $data['insertData']['name'],
            'content' => $data['insertData']['content'],
            'descript' => $data['insertData']['descript']
        ];

        $data['tempReal'] = [
            [
                'component_id' => 2 ,
                'name' => '组件的name',
                'descript' => '组件的描述'
            ]
        ];
     * ?r=template/add-sub-temp
     */
    public function actionSetStructData() {

        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['temp_id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        //获取模块信息
        $temp = (new TemplateRelayModel())->getTempRelay($postData['temp_id']);
        $templateConfig = new TemplateConfig();
        foreach($temp as $key=>$value)
        {
            if($value['component_id']==1)
            {
                $result = $templateConfig->getStructData($postData['temp_id'], $value['id'], 'relay');
                $temp[$key]['indraw_reactant'] = isset($result['reactant']) ? $result['reactant'] : [];
                $temp[$key]['indraw_product'] = isset($result['product']) ? $result['product'] : [];
                $temp[$key]['indraw_reagent'] = isset($result['reagent']) ? $result['reagent'] : [];
                $temp[$key]['indraw_solvent'] = isset($result['solvent']) ? $result['solvent'] : [];
                $temp[$key]['indraw_condition'] = isset($result['condition']) ? $result['condition'] : [];
                $temp[$key]['indraw_details'] = isset($result['details']) ? $result['details'] : [];
            }

            if($value['component_id']==13)
            {
                $result = $templateConfig->getStructData($postData['temp_id'], $value['id'], 'relay');
                $DefineTableKeyModel = new DefineTableKeyModel();
                $define_key = $DefineTableKeyModel->defineDataByRelayId($value['id'], 2);

                $temp[$key]['define']=$result;
                $temp[$key]['define_key']=isset($define_key['field_key'])?$define_key['field_key']:'';

            }
        }

        array_multisort(array_column($temp,'class'),SORT_ASC,$temp);

        //获取基础数据信息
        $base_data = $templateConfig->getStructData($postData['temp_id'], 0, 'relay');

        //获取自定义项
        $template_data = (new TemplateModel())->getTemp($postData['temp_id']);
        $define_item = $template_data['define_item'];

        $file = $this->renderPartial('/experiment/set_struct_data.php', [
            'relay_list' => $temp,
            'base_data' => $base_data,
            'define_item' => json_decode($define_item,true),
            'temp_id' => $postData['temp_id'],
        ]);

        return $this->success($file);
    }

    /**
     * 保存必填项信息
     *
     * <AUTHOR>
     * @copyright 2016-4-18
     *  $data['insertData'] = [
            'name' => $data['insertData']['name'],
            'content' => $data['insertData']['content'],
            'descript' => $data['insertData']['descript']
        ];

        $data['tempReal'] = [
            [
                'component_id' => 2 ,
                'name' => '组件的name',
                'descript' => '组件的描述'
            ]
        ];
     * ?r=template/add-sub-temp
     */
    public function actionSetStructdataAction() {
        $postData = \Yii::$app->getRequest()->post();
        $temp_id =$postData['temp_id'];
        if (empty($postData['temp_id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        $chk_value = isset($postData['chk_value']) ? $postData['chk_value'] : [];

        foreach($chk_value as $key=>$value)
        {
            if($value=='on' || strstr($value,'|')==false)
            {
                unset($chk_value[$key]);
            }
        }
        //组装数据
        $require_data=[];
        for($x=0; $x<=17; $x++) {
             $require_data[$x]=[];
            foreach($chk_value as $key=>$value)
            {
                $value_arr= explode('|', $value);
                if($value_arr[0]==$x)
                {
                    $require_data[$x][]=$value_arr[1];
                }

            }
        }
        $templateConfig = new TemplateConfig();
        // 基础数据
        $base_vals = [];
        // indraw
        $indrawRelayId = 0;
        $indraw_reactant_vals = [];
        $indraw_product_vals = [];
        $indraw_reagent_vals = [];
        $indraw_solvent_vals = [];
        $indraw_condition_vals = [];
        $indraw_details_vals = [];
        // 自定义表格
        $define_table_vals = [];
        $base_data = (new TemplateRelayModel())->relayByExperimentId($temp_id,13);
        $relay_ids = array_column($base_data, 'id');
        foreach ($relay_ids as $relay_id) {
            $define_table_vals[$relay_id] = [];
        }

        //处理数据
        foreach($require_data as $key=>$value)
        {
            //处理基础数据
            if($key==0 || $key==14){
                foreach ($value as $val) {
                    $base_vals[] = $val;
                }
            }

            //indraw
            if($key==1){
                $base_data = (new TemplateRelayModel())->relayByExperimentId($temp_id,1);
                if($base_data && !empty($base_data)) {
                    $indrawRelayId = $base_data[0]['id'];
                    foreach ($value as $val) {
                        $val_arr= explode('#', $val);
                        switch ((int)$val_arr[1]) {
                            case 0:
                                $indraw_reactant_vals[] = $val_arr[0];
                                break;
                            case 1:
                                $indraw_product_vals[] = $val_arr[0];
                                break;
                            case 2:
                                $indraw_reagent_vals[] = $val_arr[0];
                                break;
                            case 3:
                                $indraw_solvent_vals[] = $val_arr[0];
                                break;
                            case 4:
                                $indraw_condition_vals[] = $val_arr[0];
                                break;
                            case 5:
                                $indraw_details_vals[] = $val_arr[0];
                                break;
                        }
                    }
               }
            }

            // 处理自定义表格
            if($key==13){
               foreach($value as $key => $val)
               {
                   $current = explode('_', $val);
                   $define_field = str_replace('define', 'define_field', $current[0]);
                   $relay_id = $current[1];
                   $define_table_vals[$relay_id][] = $define_field;
               }
            }

            //处理自定义项
            if($key==14){
                //echo $key;exit;
                //先找到模板里的数据
                $require_arr = [];
                foreach($value as $val){
                    $val_arr = explode('_',$val);
                    $require_arr[] =$val_arr[1];
                }
                //print_r($require_arr);exit;

                //获取自定义项
                $template_data = (new TemplateModel())->getTemp($temp_id);
                $define_item = json_decode($template_data['define_item'],true);

                if (empty($define_item)) { // add by hkk 2020/7/31
                    $define_item = [];
                }
                foreach($define_item as $key=>$val){

                    if(in_array($key,$require_arr)){
                        $define_item[$key]['is_struct'] = 1;
                    }
                    else
                    {
                        $define_item[$key]['is_struct'] = 0;
                    }
                }
                $define_item_str = json_encode($define_item);
                //print_r($define_item);
                TemplateModel::updateAll(['define_item'=>$define_item_str], ['id'=>$temp_id]);
                //exit;

            }

        }

        $templateConfig->setStructData($temp_id, 0, TemplateConfig::$components['base'], $base_vals);
        if ($indrawRelayId != 0) {
            $templateConfig->setStructData($temp_id, $indrawRelayId, TemplateConfig::$components['indraw'], [
                'reactant'  =>  $indraw_reactant_vals,
                'product'   =>  $indraw_product_vals,
                'reagent'   =>  $indraw_reagent_vals,
                'solvent'   =>  $indraw_solvent_vals,
                'condition' =>  $indraw_condition_vals,
                'details'   =>  $indraw_details_vals
            ]);
        }
        foreach ($define_table_vals as $relay_id => $define_table_val) {
            $templateConfig->setStructData($temp_id, $relay_id, TemplateConfig::$components['define_table'], $define_table_val);
        }

        $data['define_item'] = $define_item;
        return $this->success($data);
    }

    /**
     * 设置企业模板
     *
     * @return \common\controllers\json template 主键 [data=template_id,info,status]
     * @param number $tempid
     * <AUTHOR>
     *
     */
    public function actionSetCompanyTemp() {
        // 权限控制 jiangdm 2022/7/13
        // 设置之前先更新权限
        (new \frontend\services\CompanyServer)->updateRoles($this->userinfo);

        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['set_conmany_template_write'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }
        $idArr = \Yii::$app->getRequest()->post('id');
        if (empty($idArr)) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        $needApproval = false;
        $needApprovalArr = [];
        $toCompanyStatus = \Yii::$app->getRequest()->post('status');
        foreach ($idArr as $tempId) {
            $res = (new TempleServer())->setCompanyTemp([
                'idArr' => $tempId,
                'user_id' => $this->userinfo->id,
                'status' => $toCompanyStatus,
                'company_id' => $this->userinfo->current_company_id,
                ]);
            if (empty($res['status'])) {
                $resData = @getVar($res['data'], []);
                $failInfo = !empty($res['info']) ? $res['info'] : \Yii::t('base', 'failed');
                return $this->fail($failInfo, 'JSON', [
                    'tipType' => @getVar($resData['tipType'], '')
                ]);
            }

            if (@getVar($res['data']['need_approval']) && $toCompanyStatus) {
                $needApprovalArr[] = $res['data']['temp_id'];
                $needApproval = true;
            }
        }
        return $this->success([
            'need_approval_temp_arr' => $needApprovalArr,
            'need_approval' => $needApproval
        ]);
    }

    /**创建子模板  貌似是废弃的方法 先注释掉
     * by Zheyu Qin 2025/05/29
     * @return \common\controllers\json
     * @date:2022/11/4
     */
    // public function actionAddSonTemp() {
    //     $tempName = \Yii::$app->request->post('temp_name', '');

    //     $postData = [
    //         'is_sub' => TRUE,
    //         'insertData' => [
    //             'name' => $insertData['name'],
    //             'tfrom' => self::$comeFrom['save'],
    //             'user_id' => $this->userinfo->id,
    //             'edit_user_id' => $this->userinfo->id,
    //             'email' => !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone,
    //             'type' => self::$tempType['sub'],
    //             'descript' => isset($insertData['descript']) ? $insertData['descript'] : '',
    //             'content' => $insertData['content']
    //         ],
    //         'temp_real' => [$data['tempReal']]
    //     ];

    //     $res = (new TempleServer())->addSubTemp($postData);

    //     if (empty($res['status'])) {
    //         return $this->fail($res['info']);
    //     }

    //     return $this->success($res['data']);
    // }

    /*批量导入子模板页面*/
    public function actionBatchAddSonTemp() {

      //print_r($useStaticConfig);
        $html = $this->renderAjax('/module/batch_add_son_temp.php', []);

        return $this->success([
            'file' => $html
        ]);
    }

    /*批量导入子模板页面*/
    public function actionBatchAddSonTempSubmit() {
        $postData = \Yii::$app->getRequest()->post();

        $filename = \Yii::getAlias('@filepath') . DS . $postData['dep_path'] . DS . $postData['save_name'];
        $arr = pathinfo($filename);
        $ext = $arr['extension'];
        //echo $ext;exit;
        //需要手工加载这个类
        require_once dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'Excel' . DIRECTORY_SEPARATOR . 'PHPExcel.php';
        $objPHPExcel = new \PHPExcel();
        //读取excel文件
        if (in_array($ext, array('xls', 'xlsx'))) {
            /*$excelFile = Yii::getAlias('@web/www/uploads'.$filename.'');*///获取文件名
            $fileType = \PHPExcel_IOFactory::identify($filename); //文件名自动判断文件类型
            $excelReader = \PHPExcel_IOFactory::createReader($fileType);

            $phpexcel = $excelReader->load($filename)->getSheet(0);//载入文件并获取第一个sheet
            $total_line = $phpexcel->getHighestRow();//总行数
            $total_column = $phpexcel->getHighestColumn();//总列数 $highestColumn = $sheet->getHighestColumn();
            ++$total_column;

            //从第二行开始 第一列为标题 第二列为内容
            for ($row = 2; $row <= $total_line + 1; $row++) {
                $name = $phpexcel->getCell('A' . $row)->getValue();
                $content = $phpexcel->getCell('B' . $row)->getValue();
                if ($content instanceof PHPExcel_RichText) { //富文本转换字符串
                    $content = $content->__toString();
                }

                if (emptyExclude0($name) || emptyExclude0($content) ) {
                    continue;
                }

                $currUserDetail = CompanyServer::getCurrUserDetail($this->userinfo->id);
                $needApproval = (new TempleServer())->checkTemplateApproval(null, 'create_template', 2,0,$currUserDetail,0 );


                $postData = [
                    'is_sub' => TRUE,
                    'insertData' => [
                        'name' => (string)$name,
                        'tfrom' => self::$comeFrom['save'],
                        'user_id' => $this->userinfo->id,
                        'edit_user_id' => $this->userinfo->id,
                        'email' => !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone,
                        'type' => 2,
                        'descript' => '',
                        'content' => (string)$content,
                        'step' => $needApproval ? 1: 3,
                    ],
                    'temp_real' => [['component_id' => 2]]
                ];
                $template = new TempleServer();
                $template->addSubTemp($postData);
            }
            return $this->success([]);
        } else {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

    }

    /**
     * Notes: 获取模板的修订设置
     *
     * Author: zhu huajun
     * Date: 2019/12/4 15:23
     * @return \common\controllers\json
     */
    public function actionGetRevisionSetting() {
        $templateId = \Yii::$app->getRequest()->get('template_id');
        if (empty($templateId)) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        // 获取模板的修订设置
        $res = (new TempleServer())->getRevisionSetting($templateId);
        $modules = @getVar($res['data'], []);

        $html = $this->renderAjax('/template/revision_setting', [
            'modules' => $modules
        ]);

        return $this->success([
            'html' => $html
        ]);
    }

    /**
     * Notes: 保存模板的修订设置
     * Author: zhu huajun
     * Date: 2019/12/4 15:32
     * @return \common\controllers\json
     */
    public function actionSaveRevisionSetting() {
        $settings = \Yii::$app->getRequest()->post('settings', []);

        // 保存模板的修订设置
        $res = (new TempleServer())->saveRevisionSetting($settings);

        return $this->success([]);
    }

    /**
     * 存为表格模板
     *
     * <AUTHOR> @copyright 2016-4-18
     *  $data['insertData'] = [
            'name' => $data['insertData']['name'],
            'content' => $data['insertData']['content'],
            'descript' => $data['insertData']['descript']
        ];

        $data['tempReal'] = [
            [
                'component_id' => 2 ,
                'name' => '组件的name',
                'descript' => '组件的描述'
            ]
        ];
     * ?r=template/add-sub-temp
     */
    public function actionAddXsTemp() {
        echo 1;exit;
        header("Access-Control-Allow-Origin:*");
        $data = \Yii::$app->getRequest()->post();

        //print_r($data);exit;
        $insertData = $data['insertData'];



        if (empty($insertData['name'])) {
            return $this->fail(\Yii::t('temp', 'must_temp_name'));
        }

        if (empty($insertData['content'])) {
            return $this->fail(\Yii::t('temp', 'son_must_content'));
        }

        if (empty($data['tempReal'])) {
            return $this->fail(\Yii::t('temp', 'son_must_module'));
        }

        $postData = [
            'is_sub' => TRUE,
            'insertData' => [
                'name' => $insertData['name'],
                'tfrom' => self::$comeFrom['save'],
                'user_id' => $this->userinfo->id,
                'edit_user_id' => $this->userinfo->id,
                'email' => !empty($this->userinfo->email) ? $this->userinfo->email : $this->userinfo->phone,
                'type' => self::$tempType['xs'],
                'descript' => isset($insertData['descript']) ? $insertData['descript'] : '',
                'content' => $insertData['content']
            ],
            'temp_real' => [$data['tempReal']]
        ];

        $res = (new TempleServer())->addSubTemp($postData);
//         $this->elnService('ineln.STemple.addSubTemp', $postData, 'POST');

        if (empty($res['status'])) {
            return $this->fail($res['info']);
        }

        return $this->success($res['data']);
    }

    //弹窗批量上传
    public function actionSyncWms(){
        $data = \Yii::$app->request->post();


        $syncData = (new TempleServer())->getSyncWms($data['relay_id']);

        $field = [];
        if($data['component_id'] == 10 ){
            $tempUrl = '/setting/sync_wms.php';
        }
        else
        {
            $res = (new TempleServer())->getDefineKeyByRelayId($data['relay_id']);
            $fieldAll = $res['data']['field_key'];
            unset($fieldAll['id']);
            unset($fieldAll['parent_id']);
            unset($fieldAll['type']);
            unset($fieldAll['experiment_id']);
            unset($fieldAll['upload_file_title']);
            unset($fieldAll['status']);


            $fieldAll['field1'] = empty($fieldAll['field1']) ? \Yii::t('define', 'name'): $fieldAll['field1'];
            $fieldAll['field2'] = empty($fieldAll['field2']) ? \Yii::t('define', 'name2'): $fieldAll['field2'];
            $fieldAll['field3'] = empty($fieldAll['field3']) ? \Yii::t('define', 'name3'): $fieldAll['field3'];
            $fieldAll['field4'] = empty($fieldAll['field4']) ? \Yii::t('define', 'name4'): $fieldAll['field4'];
            $fieldAll['field5'] = empty($fieldAll['field5']) ? \Yii::t('define', 'name5'): $fieldAll['field5'];
            $fieldAll['field6'] = empty($fieldAll['field6']) ? \Yii::t('define', 'name6'): $fieldAll['field6'];

            if(empty($fieldAll['field7'])){
               unset($fieldAll['field7']);
            }

            if(empty($fieldAll['field8'])){
               unset($fieldAll['field8']);
            }
            if(empty($fieldAll['field9'])){
               unset($fieldAll['field9']);
            }
            if(empty($fieldAll['field10'])){
               unset($fieldAll['field10']);
            }
            if(empty($fieldAll['field11'])){
               unset($fieldAll['field11']);
            }
            if(empty($fieldAll['field12'])){
               unset($fieldAll['field12']);
            }
            if(empty($fieldAll['field13'])){
               unset($fieldAll['field13']);
            }
            if(empty($fieldAll['field14'])){
               unset($fieldAll['field14']);
            }
            if(empty($fieldAll['field15'])){
               unset($fieldAll['field15']);
            }
            if(empty($fieldAll['field16'])){
               unset($fieldAll['field16']);
            }
            if(empty($fieldAll['field17'])){
               unset($fieldAll['field17']);
            }
            if(empty($fieldAll['field18'])){
               unset($fieldAll['field18']);
            }
            if(empty($fieldAll['field19'])){
               unset($fieldAll['field19']);
            }
            if(empty($fieldAll['field20'])){
               unset($fieldAll['field20']);
            }
            $field=$fieldAll;
            $tempUrl = '/setting/sync_wms.php';
        }

        $html = $this->renderAjax($tempUrl, [
            'component_id'=>$data['component_id'],
            'relay_id'=>$data['relay_id'],
            'field'=>$field,
            'sync_data'=>$syncData
        ]);
        return $this->success([
            'file' => $html
        ]);
    }

    //弹窗批量上传
    public function actionSubmitSyncWms(){
        $data = \Yii::$app->request->post();

            $relayId = $data['relay_id'];
            unset($data['component_id']);
            unset($data['relay_id']);
            $wmsSyncData = $data;
            $res = (new TempleServer())->saveSyncWms($relayId,$wmsSyncData);
            //print_r($res['data']);  exit();
            return $this->success($res['data']);



    }

    /**
     * Notes: 提交审核界面
     *
     * Author: zhu huajun
     * Date: 2020/4/27 9:35
     * submit-audit-view
     * @return \common\controllers\json
     */
    public function actionSubmitAuditView() {
        // 获取公司所有人员
        $tempId = \Yii::$app->request->get('temp_id', 0);
        // 获取通用设置，判断是否需要审核
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[\Yii::$app->view->params['curr_user_id']],1, 1);
        $userDetail = $userList[0]; //获取用户信息 根据用户信息获取审批信息

        $tempResult = TemplateModel::find()->where(['id'=>$tempId])->asArray()->one();


        $templateServer=new TempleServer();
        $setting = $templateServer->checkTemplateApproval($tempId,'create_template',$tempResult['type'],$tempResult['subtype_id'],$userDetail,1);
        
        // 获取上次选择的审批人
        $lastChoosedApprovalNodes = [];
        // 这里可以添加获取上次选择的审批人的逻辑
        
        // 审批路径
        $approvalRouteRes = (new ApprovalServer())->getApprovalRoute(@getVar($setting['approval_nodes'], []), [], -1);
        if ($approvalRouteRes['status'] != 1) {
            return $this->fail($approvalRouteRes['info'], 'JSON', ['type' => 'popContent']);
        }
        $approverNames = $approvalRouteRes['data']['approver_names'];

        $approvalNodes = $approvalRouteRes['data']['approval_nodes'];
        $approvalUsers = [];
        if (!empty($approvalNodes)) {
            $approvalUserIds = [];
            foreach ($approvalNodes as $node) {
                if (!empty($node['approval_user_ids'])) {
                    $approvalUserIds = array_merge($approvalUserIds, $node['approval_user_ids']);
                }
                if (!empty($node['coapproval_user_ids'])) {
                    $approvalUserIds = array_merge($approvalUserIds, $node['coapproval_user_ids']);
                }
            }
            $approvalUserIds = array_unique($approvalUserIds);

            $usersRes = (new CenterInterface())->userDetailsByUserIds($approvalUserIds);
            $approvalUsers = ArrayHelper::index($usersRes, 'id');
        }

        $file = $this->renderAjax('/module/audit_confirm', [
            'tempId' => $tempId,
            'approverNames' => $approverNames,
            'setting' => $setting,
            'lastChoosedApprovalNodes' => $lastChoosedApprovalNodes,
            'approvalNodes' => $approvalNodes,
            'approvalUsers' => $approvalUsers,
            'publish' => 0,
        ]);
        return $this->success($file);
    }

    /**
     * Notes: 提交发布审核界面
     *
     */
    public function actionSubmitPublishAuditView() {
        // 获取公司所有人员
        $tempId = \Yii::$app->request->get('temp_id', 0);
        // 获取通用设置，判断是否需要审核
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[\Yii::$app->view->params['curr_user_id']],1, 1);
        $userDetail = $userList[0]; //获取用户信息 根据用户信息获取审批信息

        $tempResult = TemplateModel::find()->where(['id'=>$tempId])->asArray()->one();


        $templateServer=new TempleServer();
        $setting = $templateServer->checkTemplateApproval($tempId,'publish',$tempResult['type'],$tempResult['subtype_id'],$userDetail,1);
        
        // 获取上次选择的审批人
        $lastChoosedApprovalNodes = [];
        // 这里可以添加获取上次选择的审批人的逻辑
        
        // 审批路径
        $approvalRouteRes = (new ApprovalServer())->getApprovalRoute(@getVar($setting['approval_nodes'], []), [], -1);
        if ($approvalRouteRes['status'] != 1) {
            return $this->fail($approvalRouteRes['info'], 'JSON', ['type' => 'popContent']);
        }
        $approverNames = $approvalRouteRes['data']['approver_names'];

        $approvalNodes = $approvalRouteRes['data']['approval_nodes'];
        $approvalUsers = [];
        if (!empty($approvalNodes)) {
            $approvalUserIds = [];
            foreach ($approvalNodes as $node) {
                if (!empty($node['approval_user_ids'])) {
                    $approvalUserIds = array_merge($approvalUserIds, $node['approval_user_ids']);
                }
                if (!empty($node['coapproval_user_ids'])) {
                    $approvalUserIds = array_merge($approvalUserIds, $node['coapproval_user_ids']);
                }
            }
            $approvalUserIds = array_unique($approvalUserIds);

            $usersRes = (new CenterInterface())->userDetailsByUserIds($approvalUserIds);
            $approvalUsers = ArrayHelper::index($usersRes, 'id');
        }

        //获取显示版本的信息
        $maxInnerVersion = (new TempleServer())->getMaxInnerVersion($tempId);

        $file = $this->renderAjax('/module/audit_confirm', [
            'tempId' => $tempId,
            'approverNames' => $approverNames,
            'setting' => $setting,
            'lastChoosedApprovalNodes' => $lastChoosedApprovalNodes,
            'approvalNodes' => $approvalNodes,
            'approvalUsers' => $approvalUsers,
            'publish' => 1,
            'maxInnerVersion' => $maxInnerVersion,
        ]);
        return $this->success($file);
    }

    /**
     * Notes: 提交审批
     *
     * Author: zhu huajun
     * Date: 2020/4/26 18:01
     */
    public function actionSubmitAudit() {
        $tempId = \Yii::$app->request->post('temp_id', 0);
        // 获取用户选择的审批节点
        $userSelectedNodes = \Yii::$app->request->post('approval_nodes', []);

        // 获取通用设置，判断是否需要审核
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[\Yii::$app->view->params['curr_user_id']],1);
        $userDetail = $userList[0]; //获取用户信息 根据用户信息获取审批信息

        $tempResult = TemplateModel::find()->where(['id'=>$tempId])->asArray()->one();

        $templateServer=new TempleServer();
        $setting = $templateServer->checkTemplateApproval($tempId,'create_template',$tempResult['type'],$tempResult['subtype_id'],$userDetail,1,[]);
        $needApproval = (!empty($setting['create_template']));

        $step = 3;
        if ($needApproval) {
            $step = 2;
            
            // 使用用户选择的审批节点，如果为空则使用默认节点
            $approvalNodes = !empty($userSelectedNodes) ? $userSelectedNodes : $setting['approval_nodes'];
            
            
            // 生成审批
            $createApprovalRes = (new ApprovalServer())->createApproval(\Yii::$app->params['approval_type']['template'], $tempId, 0, json_encode([
                'template_id' => $tempId,
                'send_email' => !empty($setting['send_remind_email']) ? 1 : 0,
                'remind_result_with_email' => !empty($setting['send_remind_email']) ? 1 : 0
            ]), $approvalNodes, @getVar($setting['approval_nodes_source'], []));

            if (empty($createApprovalRes['status'])) {
                return $this->fail($createApprovalRes['info'], 'JSON',['tipType' => 'popContent']);
            }
        }

        $tServer = new TempleServer();

        // 更新模板状态
        $tServer->updateStep($tempId, $step);

        return $this->success([
            'step' => $step
        ]);
    }

    /**
     * Notes: 撤销审批
     *
     * Author: zhu huajun
     * Date: 2020/4/26 18:01
     */
    public function actionCancelSubmit() {
        $tempId = \Yii::$app->request->post('temp_id', 0);

        // 撤销审批
        $clApv = (new ApprovalServer())->cancelApproval(\Yii::$app->params['approval_type']['template'], $tempId);
        if (empty($clApv['data'])) {
            return $this->fail('no approval to cancel!!!');
        }
        $extra_data = json_decode($clApv['data']['approval']['extra_data'], true);

        $tServer = new TempleServer();
        $basicRes = $tServer->getBasicById($tempId);
        $step = @getVar($basicRes['data']['step'], '');
        if (isset($extra_data['to_company_status'])) {   // 取消/设置 模板为企业模板，设置模板step为3=已通过
            $step = 3;
            $tServer->updateStep($tempId, $step);
        }
        else if ($step == 2) {
            // 更新模板状态
            $step = 1;
            $tServer->updateStep($tempId, $step);
        }



        return $this->success([
            'step' => $step
        ]);
    }

    /**
     * Notes: 查看痕迹
     *
     * Author: zhu huajun
     * Date: 2020/4/29 17:33
     * @return \common\controllers\json
     */
    public function actionListHistory() {
        $tempId = \Yii::$app->request->get('temp_id', 0);
        $res = (new TempleServer())->listHistory($tempId);
        $historyList = $res['data'];

        $userIds = array_column($historyList, 'create_by');
        $userList = (new CenterInterface())->userDetailsByUserIds($userIds);
        $userList = yii\helpers\ArrayHelper::index($userList, 'id');

        $file = $this->renderAjax('history_list', [
            'historyList' => $historyList,
            'userList' => $userList,
        ]);
        return $this->success($file);
    }

    public function actionViewHistory() {
        $hisId = \Yii::$app->request->get('his_id', 0);
        $tServer = new TempleServer();

        $res = $tServer->getHistory($hisId);
        $history = $res['data'];

        $tempRes = $tServer->getBasicById($history['template_id']);
        return $this->success([
            'template' => $tempRes['data'],
            'history' => $history,
            'tagName' => \Yii::t('base', 'history') . ':' . $tempRes['data']['name'] . '(version:' . $history['version'] . ')',
            'contentHtml' => $history['content'] ,
        ]);
    }

    /**
     * Notes: 启动编辑
     *
     * Author: zhu huajun
     * Date: 2020/4/28 16:50
     * @throws yii\db\Exception
     */
    public function actionStartEdit() {
        $tempId = \Yii::$app->request->post('temp_id', 0);
        /*
        //  // 获取通用设置，判断是否需要审核
        //  $settingRes = (new CompanyServer())->getCompanySetting(\Yii::$app->view->params['curr_company_id'], 'CREATE_EDIT_TEMPLATE');
        //  $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        //  $setting = json_decode($setting, true);
        //  $needApproval = !empty($setting['require_approval']);
        */

        $userInfo = json_decode(json_encode($this->userinfo), true);
        //获取模板是否需要审批
        $templateServer = new TempleServer();

        $approvalRes = $templateServer->getIfTempNeedApproval([$tempId], $userInfo['id'], 'create_template');
        if (empty($approvalRes['status'])) {
            return $this->fail('no template provided!!!');
        }
        $tempId2TempDetail = $approvalRes['data'];

        // 更新模板状态
        $step = $tempId2TempDetail[$tempId]['need_approval'] ? 1 : 3;
        (new TempleServer())->updateStep($tempId, $step);

        // 记录日志
        (new TempleServer())->addActionLog($tempId, TemplateActionLogModel::RE_EDIT, $this->userinfo->id);

        return $this->success([$step]);
    }

    /**
     * Notes: 记录痕迹
     *
     * Author: zhu huajun
     * Date: 2020/4/29 16:43
     * @param $tempId
     * @param $type
     */
    private function _addHistory($tempId, $type,$transferToValue='') {
        // 获取数据

        $tempResult = (new TempleServer())->getTempById($tempId, -1);
        $tempHtml = $this->renderAjax('history_detail', $tempResult['data']);
        (new TempleServer())->addHistory($tempId, $type, $tempHtml,'',$transferToValue);
    }

    public function actionGetTemplateById() {
        $id = \Yii::$app->request->post('id', 0);

        $res = (new TempleServer())->getSubTemp($id, FALSE, 2);
        $tempData = @getVar($res['data'], []);
        if (empty($tempData)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }

        return $this->success([
            'name' => @getVar($tempData['name']),
            'content' => @getVar($tempData['content']),
        ]);
    }

    /**
     * Notes: 显示或隐藏模板
     * Author: szq
     * Date: 2020/7/23 15:44
     */
    public function actionHideOrShowTemplate() {
        $postData = Yii::$app->request->post();
        $isHide = $postData['isHide'];
        $tempId = $postData['tempId'];
        $result = (new TempleServer())->hideOrShowTemp($isHide, $tempId, $this->userinfo->id);

        if (empty($result['status'])) {
            return $this->fail($result['data']);
        }
        return $this->success([]);
    }
    /**
     * 模板列表
     *
     * @param number $id
     */
    public function actionManageTemplateList() {
        $postData = \Yii::$app->request->post();// add by hkk 2019/11/19
        $group_id = array_column($this->userinfo->groups, 'id');
        /*
        //  // 验证是否是系统管理员
        //  $userDetails = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]);
        //  if (isset($userDetails[0]['role'])) {
        //      $isAdmin = in_array(1, $userDetails[0]['role']) ? 1 : 0;
        //  } else {
        //      $isAdmin = 0;
        //  }
        */

        // 企业模板权限控制 jiangdm 2022/7/13
        $set_conmany_template_write = 0;
        if (\Yii::$app->view->params['is_system_admin'] || \Yii::$app->view->params['set_conmany_template_write'] == 1) {
            $set_conmany_template_write = 1;
        }
        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23

        $where['module_type'] = \Yii::$app->request->post('module_type', 0);
        //template_main_types 来自于template_main_types，是“，”分隔的字符串，为空时传入空数组
        $where['template_main_types'] = \Yii::$app->request->post('template_main_types', '');
        if(empty($where['template_main_types'])) {
            $where['template_main_types'] = [];
        } else {
            $where['template_main_types'] = explode(',', $where['template_main_types']);//转化为数组，才能和数据库中的值比较。
        }
        //template_sub_types来自于template_subtypes_json，是json格式的字符串，需要转化为字符串数组，才能和数据库中的值比较，为空时传入空数组。
        $where['template_sub_types'] = \Yii::$app->request->post('template_subtypes_json', '');
        if(empty($where['template_sub_types'])) {
            $where['template_sub_types'] = [];
        } else {
            $where['template_sub_types'] = json_decode($where['template_sub_types'], true);
        }
        
        $where['module_keywords'] = \Yii::$app->request->post('module_keywords', '');
        $where['module_owner']= \Yii::$app->request->post('module_owner',0);
        $where['is_company']=\Yii::$app->request->post('is_company',0);
        //查询该企业下所有用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        // 获取用户id和企业_id
        $where['user_id'] = $this->userinfo->id;
        $where['company_id'] = $company_id;

        $companySettings = CompanyAuthServer::getCompanyAuthByUserId($this->userinfo->id);

        //! 判断是否有 查看全公司模板权限 (查看模板管理（公司所有模板）)
        $where['company_template_manage'] = yii\helpers\ArrayHelper::getValue($companySettings,
            ['company_feature', 'company_template_manage'], '0');

        //! 判断是否有 查看可见人员模板权限 (查看模板管理（可见人员的模板）)
        $visibleUserTemplateManage = yii\helpers\ArrayHelper::getValue($companySettings,
            ['company_feature', 'visible_user_template_manage'], '0');
        $where['visible_user_template_manage'] = $visibleUserTemplateManage;

        //! 如果有查看可见人员模板权限
        if (!empty($visibleUserTemplateManage)) {   //设置了只能查看可见人员模板
            $userList = (new CenterInterface())->getVisibleUsers($this->userinfo->user_id);
            $visUserIds = array_column($userList, 'user_id');
            $visUserIds = array_merge($visUserIds,[0]);

            //如果可见用户只有一个人（即本用户）,就不使用数组形式，防止yii2构建sql语句时报错
            if (count($visUserIds) > 1) {
                $where['user_id'] = $visUserIds;
            }
        }

        $tempData = (new TempleServer())->listTempLateNew($where, $limit, $page, $orderBy, $orderType);
        //$interFace = new GroupSettingServer();
        //$result = $interFace->listTempGroup($this->userinfo->id);
        //$groups = empty($result['status']) ? [] : $result['data'];
        // 获取用户信息
        $userIds = array_column($tempData['temp_list'], 'user_id');
        $userIds = array_merge($userIds,[$this->userinfo->id]);
        //added by xieyuxiang 2022.8.23 转让模板里需要存储上一任模板拥有者的信息
        $previousIds=array_column($tempData['temp_list'], 'previous_id');
        $previousIds = array_filter($previousIds);
        $userIds = array_merge($userIds,$previousIds);
        $userIds = array_unique($userIds);
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId($userIds);
        $userList = yii\helpers\ArrayHelper::index($userList, 'id');
        $userDetail = $userList[$this->userinfo->id];

        //获取当前用户的所有可见用户
        $visUsers = (new CenterInterface())->getVisibleUsers($this->userinfo->id);
        $visUsers = array_column($visUsers, null, 'user_id');
        //获取企业模板审批设置
        $cmpyTempSettings = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $cmpyTempSettings = @getVar($cmpyTempSettings['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $cmpyTempSettings = json_decode($cmpyTempSettings, true);

        $templateServer = new TempleServer();
        foreach($tempData['temp_list'] as $key=>$template){

            /*
            //            if ($template['type'] != 1 && $template['type'] != 2) {  //默认模板 和 表格模板 不需要审核
            //                $temp_creator_appro = false;
            //            } else {    //bug#29178,是否有模板审核权限 修改为根据模板创建人权限，mod dx
            //                $tempUserDetail = $visUsers[$template['user_id']];
            //                $temp_creator_appro = $templateServer->checkTemplateApproval(
            //                    null,'create_template', $template_type, $tempUserDetail, null, $cmpyTempSettings
            //                );
            //            }
            */
            // 只有模板创建人属于用户的可见人员时，才视作需要审核
            $temp_creator_appro = false;
            if ($template['type'] == 1 || $template['type'] ==0) {//全文模板 和 方法模板 需要审核
                if (!empty($visUsers[$template['user_id']])) {
                    $tempUserDetail = $visUsers[$template['user_id']];
                    $temp_creator_appro = $templateServer->checkTemplateApproval(
                        null,'create_template', $template['type'], $template['subtype_id'], $tempUserDetail, null, $cmpyTempSettings
                    );
                }
            }
            $tempData['temp_list'][$key]['temp_creator_appro'] = $temp_creator_appro;
        }

        $data['tempLateData'] = $tempData['temp_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $tempData['totalCount'];
        $data['params'] = $where;
        $data['set_conmany_template_write'] = $set_conmany_template_write;
        $data['type'] = 'list';
        //$data['groups'] =  $groups?:[];
        $data['userList'] = $userList;
        $data['edit_template_manage'] = !empty($companySettings['company_feature']['edit_template_manage']) ? 1 : 0;

        //获取模板子类型列表数据
        $templateSubtypeList = (new TemplateSubtypeServer())->getTemplateTypeList();
        $data['templateSubtypeList'] = $templateSubtypeList['data'];
        $data['templateEffectMode'] = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];

        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/module/moduleList_manage.php', $data);
            return $this->success([
                //'listData' => $file,
                'tool' => null,
                'contentHtml' => $file,
                'template_sub_types' => $where['template_sub_types'],
                'template_main_types' => $where['template_main_types'],
            ]);
        }

        // 只更新表格数据
        $file = $this->renderAjax('/module/moduleList_page_manage.php', $data);
        return $this->success([
            'listData' => $file,
            'template_sub_types' => isset($where['template_sub_types']) ? json_encode($where['template_sub_types'], JSON_UNESCAPED_UNICODE) : '',
            'template_main_types' => isset($where['template_main_types']) ? implode(',', $where['template_main_types']) : '',
            'tool' => null
        ]);
    }

    /**
     * 模板列表
     *
     * @param number $id
     */
    public function actionRecycleTemplateList() {
        $postData = \Yii::$app->request->post();// add by hkk 2019/11/19
        $group_id = array_column($this->userinfo->groups, 'id');
        // 验证是否是系统管理员
        $userDetails = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]);
        if (isset($userDetails[0]['role'])) {
            $isAdmin = in_array(1, $userDetails[0]['role']) ? 1 : 0;
        } else {
            $isAdmin = 0;
        }

        // 企业模板权限控制 jiangdm 2022/7/13
        $set_conmany_template_write=1;
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['set_conmany_template_write'] != 1) {
            $set_conmany_template_write = 0;
        }
        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23

        $where['module_type'] = \Yii::$app->request->post('module_type', 0);
        $where['module_keywords'] = \Yii::$app->request->post('module_keywords', '');
        $where['owner_id'] = \Yii::$app->request->post('module_owner', '');
        $where['operator_id'] = \Yii::$app->request->post('module_remover', '');

        //查询该企业下所有用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        // 获取用户id和企业_id
        $where['user_id'] = $this->userinfo->id;
        $where['company_id'] = $company_id;

        $tempData = (new TempleServer())->listRecycle($where, $limit, $page, $orderBy, $orderType,0);
        $interFace = new GroupSettingServer();
        $result = $interFace->listTempGroup($this->userinfo->id);
        $groups = empty($result['status']) ? [] : $result['data'];

        // 获取用户信息

        $userListData = (new CenterInterface())->getUserListByCompanyId(1,true,false);
        $userList = $userListData['list'];
        $userList = yii\helpers\ArrayHelper::index($userList, 'id');

        $data['tempLateData'] = $tempData['temp_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $tempData['totalCount'];
        $data['params'] = $where;
        $data['set_conmany_template_write'] = $set_conmany_template_write;
        $data['type'] = 'list';
        $data['groups'] =  $groups?:[];
        $data['userList'] = $userList;

        // 获取通用设置，判断是否需要审核
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $setting = json_decode($setting, true);
        $needApproval = !empty($setting['require_approval']);
        $data['needApproval'] = $needApproval;

        // 获取模板生效模式
         //获取模板生效模式设置
        $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE');
        $data['templateEffectMode'] = $templateEffectMode['data']['TEMPLATE_EFFECT_MODE']['value'];

        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/module/moduleList_recycle.php', $data);
            return $this->success([
                //'listData' => $file,
                'tool' => null,
                'contentHtml' => $file,
            ]);
        }

        // 只更新表格数据
        $file = $this->renderAjax('/module/moduleList_page_recycle.php', $data);
        return $this->success([
            'listData' => $file,
            'tool' => null,
        ]);
    }

    /**
     * 获取模板审批设置数据 
     *
     */
    public function actionManageTemplateApproval() {
        // 获取通用设置，判断是否需要审核
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $approvalList = json_decode($setting, true);

        //获取我的可见部门
        $visableDepartmentList = (new CenterInterface()) -> getVisibleDepartments($this->userinfo->id);
        $data['visable_department_list'] = $visableDepartmentList;
        $data['visable_department_ids'] = array_column($visableDepartmentList,'id');
        //获取我的可见鹰群
        $visableGroupList = (new CenterInterface()) -> getVisibleGroups($this->userinfo->id);

        $data['visable_group_list'] = $visableGroupList;
        $data['visable_group_ids'] = array_column($visableGroupList,'id');

        //获取所有模板子类型
        $templateTypeList = (new TemplateSubtypeServer())->getTemplateTypeList();
        if(!empty($templateTypeList['data'])){
            $templateTypeList = $templateTypeList['data'];
        }
        //根据main_type分类
        $fullSubtypes = array_filter($templateTypeList, function($item) {
            return $item['main_type'] == 1;
        });
        $inTextSubtypes = array_filter($templateTypeList, function($item) {
            return $item['main_type'] == 2;
        });
        $inTableSubtypes = array_filter($templateTypeList, function($item) {
            return $item['main_type'] == 4;
        });

        //fullSubtypes inTextSubtypes inTableSubtypes 分别添加 id 为0 的数据，表示未分类,如果已经存在则不添加
        $fullSubtypes = array_values(array_filter($fullSubtypes, function($item) {
            return $item['id'] != 0;
        }));
        $inTextSubtypes = array_values(array_filter($inTextSubtypes, function($item) {
            return $item['id'] != 0;
        }));
        $inTableSubtypes = array_values(array_filter($inTableSubtypes, function($item) {
            return $item['id'] != 0;
        }));

        array_unshift($fullSubtypes, ['id' => 0, 'name' => '未分类']);
        array_unshift($inTextSubtypes, ['id' => 0, 'name' => '未分类']);
        array_unshift($inTableSubtypes, ['id' => 0, 'name' => '未分类']);

        $data['fullSubtypes'] = $fullSubtypes;
        $data['inTextSubtypes'] = $inTextSubtypes;
        $data['inTableSubtypes'] = $inTableSubtypes;

         //获取模板生效模式设置
        $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE');
        $data['templateEffectMode'] = $templateEffectMode['data']['TEMPLATE_EFFECT_MODE']['value'];

        // 确保所有必要的字段都存在
        if (!empty($approvalList)) {
            //老数据处理，normal_full字段为0  且 normal_function字段为0,且不存在full,intext,intable的老数据需要从数组中移除
            $approvalList = array_filter($approvalList, function($item) {
                return !((!isset($item['normal_full']) || $item['normal_full'] == 0) && (!isset($item['normal_function']) || $item['normal_function'] == 0) && (!isset($item['full']) && !isset($item['intext']) && !isset($item['intable'])));
            });
            
            foreach ($approvalList as &$approval) {
                // 确保set_company_template字段存在
                if (!isset($approval['set_company_template'])) {
                    $approval['set_company_template'] = 0;
                }

                //确保publish字段存在
                if (!isset($approval['publish'])) {
                    $approval['publish'] = 0;
                    if ($data['templateEffectMode'] == 1) {
                    //    $approval['publish'] = $approval['create_template'];
                    }
                }

                //确保create_template字段存在
                if (!isset($approval['create_template'])) {
                    $approval['create_template'] = 0;
                    if ($data['templateEffectMode'] == 2) {
                   //     $approval['create_template'] = $approval['publish'];
                    }
                }
                
                if (!isset($approval['full'])) {
                    $approval['full'] = [];
                    //未分类的情况根据normal_full来判断，如果normal_full为1，则添加'id'=>0
                    if (isset($approval['normal_full']) && $approval['normal_full'] == 1) {
                        $approval['full'][0] = ['id' => 0];
                    }
                }
                
                //添加"intext":[]字段
                if (!isset($approval['intext'])) {
                    $approval['intext'] = [];
                    if (isset($approval['normal_function']) && $approval['normal_function'] == 1) {
                        $approval['intext'][0] = ['id' => 0];
                    }
                }
                
                //添加"intable":[]字段
                if (!isset($approval['intable'])) {
                    $approval['intable'] = [];
                    if (isset($approval['normal_function']) && $approval['normal_function'] == 1) {
                        $approval['intable'][0] = ['id' => 0];
                    }
                }
            }
        }

        $data['approvalList'] = $approvalList;

        //获取模板生效模式设置
        $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE');
        $data['templateEffectMode'] = $templateEffectMode['data']['TEMPLATE_EFFECT_MODE']['value'];

        // 第一次渲染，更新整个页面
        $file = $this->renderAjax('/module/moduleList_approval.php', $data);
        return $this->success([
            'contentHtml' => $file,
        ]);
    }

    /**
     * Notes: 模板转让
     * Author: xie yuxiang
     * Date : 2022/8/23 13:47
     * @param tempIds 模板id数组
     * @param transferTo 转让对象的id
     * @param transferToValue 转让对象的用户名和真实姓名（用于保存转让历史）
     * @return \common\controllers\json
     */
    public function actionTransfer()
    {
        $tempIds = \Yii::$app->request->post('tempIds', 0);
        $tempIds = explode(",",$tempIds);
        $transferTo=\Yii::$app->request->post('transferTo', 0);
        $transferToValue = \Yii::$app->request->post('transferToValue', "");
        if($transferTo==0)
        {
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $templateServer=new TempleServer();

        foreach ($tempIds as $tempId)
        {
            $template=$templateServer->getTempById($tempId);
            $templateServer->updateTransferTemp($tempId,$transferTo,$this->userinfo->id,$template['data']['base_data']['user_id']);        }
        return $this->success([]);
    }

    /**
     * 模板列表
     *
     * @param number $id
     */
    public function actionSetApprovalSettingSubmit() {
        $approvalSetting = \Yii::$app->request->post('approvalSetting');
        // 保存通用设置，关于模板审批流程的设置组
        (new CompanyServer())->SetCompanySetting(\Yii::$app->view->params['curr_company_id'], 'CREATE_EDIT_TEMPLATE',$approvalSetting);
        return $this->success([]);
    }

    /**获修改模板是否为企业级模板的审批设置
     * @return \common\controllers\json
     * @throws yii\base\Exception
     * @auther: dx
     * @date:2022/11/4
     */
    public function actionGetTemplateApprovalSetting() {
        $operate = \Yii::$app->request->post('operate');    //操作类型 creat || share
        $temp_id = \Yii::$app->request->post('template_id', '');
        $to_company_status = \Yii::$app->request->post('to_company_status');    //将模板修改为的企业级模板状态= 0 || 1

        if (!isset($operate) || ('create' != $operate && 'share' != $operate)) {
            throw (new yii\base\Exception('Illegal operate type provided!!!'));
        }
        if (empty($temp_id)) {
            throw (new yii\base\Exception('no template ID provided!!!'));
        }
        //如果用户的企业权限没有设置企业模板权限，报错
        if (\Yii::$app->view->params['set_conmany_template_write'] != 1) {
            $this->fail('no company template authority!!!');
        }
        // 企业模板修改为普通模板，取消设置
        if (empty($to_company_status)) {
            return $this->success([
                'need_approval' => false,
                'approval_route' => [],
            ]);
        }

        $oldTemp = TemplateModel::findOne(['id' => $temp_id]);
        if (empty($oldTemp)) {
            throw (new yii\base\Exception('no template found by provided ID!!!'));
        }

        $operate_type = sprintf('%1$s_template', $operate); //拼装后的操作类型 create_template || share_template
        $template_company_type = !empty($to_company_status) ? 'company' : 'normal';
        switch ($oldTemp['type']) {
            case 1:
                $old_temp_type = 'full'; break;
            case 2:
            case 4:
                $old_temp_type = 'function'; break;
            default:
                $old_temp_type = 'others';
        }
        $template_type = sprintf('%1$s_%2$s', $template_company_type, $old_temp_type);//模板类型

        //根据操作类型 & 模板类型 获取审批设置
        $currUid = $this->userinfo->id;
        $userDetail = CompanyServer::getCurrUserDetail($currUid);
        $approvalSetting = (new TempleServer())->checkTemplateApproval(
            null,$operate_type, $oldTemp['type'],$oldTemp['subtype_id'],$userDetail,1,[]);

        $needApproval = false;
        $approvalRoute = [];
        $message = '';
        //如果需要审批，获取审批路线
        if (!empty($approvalSetting[$operate_type]) && !empty($approvalSetting[$template_type])) {
            $needApproval = true;
            $approvalNodes = $approvalSetting['approval_nodes'];
            $approvalRouteRes = (new ApprovalServer())->getApprovalRoute($approvalNodes);
            if (empty($approvalRouteRes['status'])) {
                $message = $approvalRouteRes['info'];
                return $this->fail($approvalRouteRes['info'], 'JSON', ['type' => 'popContent']);
            }
            $approvalRoute = $approvalRouteRes['data']['approver_names'];
        }

        return $this->success([
            'need_approval' => $needApproval,
            'approval_route' => $approvalRoute,
        ], null, $message);

    }

    /**
     * 获取模板保存后是否立即生效的企业级设置
     * 访问链接：   ?r=template/get-template-effect-setting
     */
    public function actionGetTemplateEffectSetting() {
        $settingRes = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE');
        $setting = @getVar($settingRes['data']['TEMPLATE_EFFECT_MODE']['value'], '');
        //模板生效模式开关项：1为模板保存立即生效，2为需要发布才生效,如果为空默认取1
        $template_effect_mode = !empty($setting) ? $setting : 1; //如果为空默认取1，即模板保存立即生效
        return $this->success([
            'template_effect_mode' => $template_effect_mode,
        ]);
    }

    /**
     * 保存模板保存后是否立即生效的企业级设置
     * 访问链接：   ?r=template/save-template-effect-setting
     */
    public function actionSaveTemplateEffectSetting() {
        $template_effect_mode = \Yii::$app->request->post('template_effect_mode', 1);
        //模板生效模式开关项：1为模板保存立即生效，2为需要发布才生效
        if (!in_array($template_effect_mode, [1, 2])) {
            return $this->fail('Invalid template effect mode provided!!!');
        }
        $result = (new CompanyServer())->setCompanySetting(\Yii::$app->view->params['curr_company_id'], 'TEMPLATE_EFFECT_MODE', (string)$template_effect_mode);
        return $this->success([
            'result' => $result
        ]);
    }



/**
 * 获取模板设置页面内容
 */
public function actionTemplateSettingsContent() {
    // 获取设置数据
    $settings = [
        'defaultVisibility' => 'private',
        // 其他设置项...
    ];
    
    // 渲染视图
    $contentHtml = $this->renderAjax('/module/moduleList_settings', [
        'settings' => $settings,
        // 其他需要传递给视图的参数...
    ]);
    
    return $this->success([
        'contentHtml' => $contentHtml
        // 其他需要返回的数据...
    ]);
}

    /**
     * 保存模块名称
     * @return \yii\web\Response
     */
    public function actionSaveModuleName()
    {
        // 获取参数
        $groupHtml = \Yii::$app->request->get('groupHtml', '');
        $title = \Yii::$app->request->get('title', '');
        $submitId = \Yii::$app->request->get('submitId', '');
        $main_type = \Yii::$app->request->get('main_type', 1);
        
        // 如果没有提供组HTML，则获取组数据
        if (empty($groupHtml)) {
            $groupServer = new \frontend\services\GroupSettingServer();
            $companyId = isset($this->userinfo->current_company_id) ? $this->userinfo->current_company_id : 0;
            $result = $groupServer->listTempGroup($this->userinfo->id, $companyId);
            $groupData = !empty($result['status']) ? $result['data'] : [];
            $groupHtml = $this->getGroupHtml($groupData);
        }
        
        // 获取模板子类型列表
        $templateSubtypeServer = new \frontend\services\TemplateSubtypeServer();
        $result = $templateSubtypeServer->getTemplateTypeList($main_type);
        $templateSubtypeList = isset($result['data']) ? $result['data'] : [];
        
        // 渲染视图
        $html = $this->renderAjax('/popup/save_module_name', [
            'groupHtml' => $groupHtml,
            'title' => $title,
            'submitId' => $submitId,
            'templateSubtypeList' => $templateSubtypeList,
        ]);
        
        // 返回JSON响应，包含HTML内容
        return $this->success([
            'html' => $html
        ]);
    }
    
    /**
     * 生成组HTML
     * @param array $data 组数据
     * @return string HTML字符串
     */
    private function getGroupHtml($data)
    {
        if (empty($data)) {
            return '<div class="no-group-wrap">' . \Yii::t('app', 'no group') . '</div>';
        }
        
        $groupHtmlArr = [];
        foreach ($data as $v) {
            $groupHtmlArr[] = '<div class="check">' .
                '    <input class="middle mr5" type="checkbox" check_id="' . $v['group_id'] . '" check_name="' . $v['group_name'] . '">' .
                '    <span class="check-name check_name">' . $v['group_name'] . '</span>' .
                '</div>';
        }
        
        return implode('', $groupHtmlArr);
    }

    /**
     * 发布模板
     * @return \yii\web\Response
     */
    public function actionPublish() {
        $tempId = \Yii::$app->request->post('temp_id', 0);
        $version = \Yii::$app->request->post('version', '');
        $effectDate = \Yii::$app->request->post('effect_date', null);
        $effectDateType = \Yii::$app->request->post('effect_date_type', 'immediate'); // immediate: 立即生效，custom: 自定义生效日期
        
        if (empty($tempId)) {
            return $this->fail(\Yii::t('temp', 'template_id_empty'));
        }
        
        if (empty($version)) {
            return $this->fail(\Yii::t('temp', 'pls_input_version'));
        }

        //检查版本号是否重复
        $templateServer = new TempleServer();
        if (!$templateServer->checkVersionValid($tempId, $version)) {
            return $this->fail(\Yii::t('temp', 'version_exists'));
        }
        
        if ($effectDateType === 'custom' && empty($effectDate)) {
            return $this->fail(\Yii::t('temp', 'pls_select_effect_date'));
        }

        //检查是否要走发布审批,提醒用户刷新页面重试
        $tempData = TemplateModel::findOne(['id' => $tempId]);
        $needApproval = (new TempleServer())->checkTemplateApproval($tempId, 'publish', $tempData['type'], $tempData['subtype_id'], $this->userinfo, 1, []);
        if ($needApproval) {
            return $this->fail(\Yii::t('temp', 'need_publish_approval'));
        }
        
        //执行发布操作
        $extraData = [
            'version' => $version,
            'effect_date' => $effectDate,
        ];
        $result = $templateServer->addActionLog($tempId, TemplateActionLogModel::PUBLISH, $this->userinfo->id, 
        json_encode($extraData), $version, TemplateHistoryNewModel::$status['is_agreed'], $effectDate);
        
        if ($result['status'] == 1) {
            return $this->success([
                'message' => \Yii::t('temp', 'publish_success'),
            ]);
        }
        
        return $this->fail($result['info']);
    }

     /**
     * 提交模板发布审核
     * submit-publish-audit
     */
    public function actionSubmitPublishAudit() {
        $tempId = \Yii::$app->request->post('temp_id', 0);
        $version = \Yii::$app->request->post('version', '');
        $effectDate = \Yii::$app->request->post('effect_date', null);
        $effectDateType = \Yii::$app->request->post('effect_date_type', 'immediate'); // immediate: 立即生效，custom: 自定义生效日期
        $userSelectedNodes = \Yii::$app->request->post('approval_nodes', []);
        
        if (empty($tempId)) {
            return $this->fail(\Yii::t('temp', 'template_id_empty'));
        }
        
        if (empty($version)) {
            return $this->fail(\Yii::t('temp', 'pls_input_version'));
        }

        //检查版本号是否重复
        $templateServer = new TempleServer();
        if (!$templateServer->checkVersionValid($tempId, $version)) {
            return $this->fail(\Yii::t('temp', 'version_exists'));
        }
        
        if ($effectDateType === 'custom' && empty($effectDate)) {
            return $this->fail(\Yii::t('temp', 'pls_select_effect_date'));
        }
        
        // 获取通用设置，判断是否需要审核
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[\Yii::$app->view->params['curr_user_id']],1);
        $userDetail = $userList[0]; //获取用户信息 根据用户信息获取审批信息
        
        $tempResult = TemplateModel::find()->where(['id'=>$tempId])->asArray()->one();
        
        $setting = $templateServer->checkTemplateApproval($tempId, 'publish', $tempResult['type'], $tempResult['subtype_id'], $userDetail, 1, []);
        $needApproval = (!empty($setting['publish']));
        $needApproval = true;
        // 首先创建历史记录，获取历史记录ID作为审批的business_id
        if ($effectDateType === 'custom') {
            $effectDateValue = $effectDate;
        } else {
            $effectDateValue = null; // 立即生效
        }
        $extraData = [
            'version' => $version,
            'effect_date' => $effectDateValue,
        ];
        
        // 添加操作日志和历史记录
        $historyResult = $templateServer->addActionLog(
            $tempId, 
            TemplateActionLogModel::SUBMIT_PUBLISH_AUDIT, 
            $this->userinfo->id, 
            json_encode($extraData), 
            $version, 
            TemplateHistoryNewModel::$status['is_approving'], 
            $effectDateValue
        );
        
        if (empty($historyResult['status'])) {
            return $this->fail($historyResult['info']);
        }

        $historyId = $historyResult['data']['history_id'];
        
        if ($needApproval) {
            // 使用用户选择的审批节点，如果为空则使用默认节点
            $approvalNodes = !empty($userSelectedNodes) ? $userSelectedNodes : $setting['approval_nodes'];
            
            // 生成审批，注意这里的business_id是历史记录ID
            $createApprovalRes = (new ApprovalServer())->createApproval(
                \Yii::$app->params['approval_type']['publish_template'], 
                $historyId, 
                0, 
                json_encode([
                    'template_id' => $tempId,
                    'history_id' => $historyId,
                    'version' => $version,
                    'effect_date' => $effectDateValue,
                    'send_email' => !empty($setting['send_remind_email']) ? 1 : 0,
                    'remind_result_with_email' => !empty($setting['send_remind_email']) ? 1 : 0
                ]), 
                $approvalNodes, 
                @getVar($setting['approval_nodes_source'], [])
            );
            
            if (empty($createApprovalRes['status'])) {
                return $this->fail($createApprovalRes['info'], 'JSON', ['tipType' => 'popContent']);
            }
        } else {
            // 如果不需要审批，提醒用户刷新页面重试
            return $this->fail(\Yii::t('temp', 'no_need_publish_approval'));
            
        }
        
        return $this->success([
            'message' => \Yii::t('temp', 'publish_submit_success'),
            'need_approval' => $needApproval,
            'history_id' => $historyId,
            'step' => $needApproval ? 2 : 3
        ]);
    }
}
