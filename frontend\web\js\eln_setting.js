define(function (require) {
    function saveExperiment(success) {
        require(['save_exp'], function (save_exp) {
            save_exp.saveExperiment({
                autoSave: true,
                success: success,
                error: function () {
                    $.showAlert('Save failed.');
                }
            });
        });
    }

    /*整合我的词条相关功能。

     *author:ls
     *date:20161018
     */

    var module = {
        // 事件委托
        init: function () {
            var obj = this;
            $('body').on('click', '.mui-switch-mini', function (e) {
                if ($(this).hasClass('group_master') || $(this).hasClass('disabled')) {
                    return;
                }
                var method = $(this).attr('method');
                obj[method] ? obj[method]($(this)) : '';
            });
            $('body').on('click', '.opt-more span', function (e) {
                if ($(this).hasClass('disabled')) {
                    return;
                }
                var cMethod = $(this).attr('cMethod');
                obj[cMethod] ? obj[cMethod]($(this)) : '';
            });
            $('body').on('click', '.small-btn,td .btns-area .set-module-ico,.authCompare,.mySelector', function (e) {
                var cMethod = $(this).attr('cMethod');
                obj[cMethod] ? obj[cMethod]($(this)) : '';
            });
            $('body').on('mouseover', '.together-tools .eln_setting_btn', function (e) {
                var hMethod = $(this).attr('hMethod');
                obj[hMethod] ? obj[hMethod]($(this)) : '';
            });
            $('body').on('mouseover', '.page_setting_button', function (e) {
                var hMethod = $(this).attr('hMethod');
                obj[hMethod] ? obj[hMethod]($(this)) : '';
            });

            $('body').on('mouseover', '.together-tools .wo_batch_setting_btn', function (e) {
                var cMethod = $(this).attr('cMethod');
                obj[cMethod] ? obj[cMethod]($(this)) : '';
            });
            $('body').on('mouseover', '.print_page_set .down-box', function (e) {
                var hmethod = $(this).attr('hmethod');
                obj[hmethod] ? obj[hmethod]($(this)) : '';
            });
            //全选
            $('body').on('click', '#checkAll', function () {

                var all_checkbox;
                if ($(this).parents('.thead-fixed').length > 0) { // add by hkk 2022/6/23 固定表头类全选
                    all_checkbox = $(this).parents('.fixHeaderOuter').find("input[type='checkbox']");
                    all_checkbox.prop('checked', $(this).prop('checked'));
                } else  {
                    all_checkbox = $('.exp_conetnt.active .ineln_title').find("input[type='checkbox']:not(.no_check_all)");
                    all_checkbox.prop('checked', $(this).prop('checked'));

                    var all_checkbox1 = $('.exp_conetnt.active #checkAll').parents("table").find("input[type='checkbox']:not(.no_check_all)");
                    all_checkbox1.prop('checked', $(this).prop('checked'));

                }
            });

  /*          $('body').on('click', '#checkAll', function () {
                var all_checkbox = $('.exp_conetnt.active .ineln_title').find("input[type='checkbox']");
                all_checkbox.prop('checked', $(this).prop('checked'));
                console.log(222);
            });*/
            //点别的checkbox
            $('body').on('click', '.checkboxBtn:not(#checkAll)', function () {
                var checkboxes = $(this).closest('table').find('.checkboxBtn:not(#checkAll)');
                $('.exp_conetnt.active #checkAll').prop('checked', checkboxes.length == checkboxes.filter(':checked').length);
            });
            $('body').on('click', '.modal .opts-btn', function (e) {
                var tools_method = $(this).attr('tools_method');
                obj[tools_method] ? obj[tools_method]($(this)) : '';
            });
            $('body').on('click', '#dshare-user-list a', function (e) {
                var delete_method = $(this).attr('delete_method');
                obj[delete_method] ? obj[delete_method]($(this)) : '';
            });
            $('body').on('click', '.modal-content .btn-primary', function (event) {
                var submit_method = $(this).attr('submit_method');
                obj[submit_method] ? obj[submit_method]($(this)) : '';
            });
            $('body').on('click', '.ineln_title .btn', function (event) {
                var title_method = $(this).attr('title_method');
                obj[title_method] ? obj[title_method]($(this)) : '';
            });

            $('body').on('change', '#group_name', function (e) {
                obj.changeGroup()
            });

            // 分享设置(允许分享到本群 与 允许分享到所加入的鹰群 联动)
            $('body').on('click', '#share_to_this_group, #share_to_my_group', function (event) {
                var targetId = $(this).attr('id');
                if(targetId == 'share_to_this_group') {
                    if(!$(this).is(':checked')) {
                        $('#share_to_my_group').prop('checked', false).removeClass('some');
                    }
                } else {
                    if($(this).is(':checked')) {
                        $('#share_to_this_group').prop('checked', 'checked').removeClass('some');
                    }
                }
            });

            // 分享设置(允许选择群内人员进行整本合著 与 允许对本群的所有记录本设置整本合著 联动)
            $('body').on('click', '#coauthor_book, #coauthor_group_book', function (event) {
                var targetId = $(this).attr('id');
                if (targetId === 'coauthor_book') {
                    if (!$(this).is(':checked')) {
                        $('#coauthor_group_book').prop('checked', false).removeClass('some');
                    }
                } else {
                    if ($(this).is(':checked')) {
                        $('#coauthor_book').prop('checked', 'checked').removeClass('some');
                    }
                }
            });

            //打印设置相关功能
            $('body').on('click', '.print-set-btn', function (event) {
                var method = $(this).attr('method');
                obj[method] ? obj[method]($(this)) : '';
            });
            $('body').on('click', '.resign-side li', function (event) {
                var select_method = $(this).attr('select_method');
                obj[select_method] ? obj[select_method]($(this)) : '';
            });
            $('body').on('click', '.resign-middle-side img', function (event) {
                var judge_method = $(this).attr('judge_method');
                obj[judge_method] ? obj[judge_method]($(this)) : '';
            });
            $('body').on('click', '.choose-area button', function (event) {
                var search_method = $(this).attr('search_method');
                obj[search_method] ? obj[search_method]($(this)) : '';
            });

            function checkBatchDisabled() {
                //等待结果，因为可能是全选。要用他们的事件逻辑结束之后的状态来判断。
                setTimeout(function () {
                    var table = $(".exp_conetnt.active .eln_setting_table");
                    var inputLen = $('.exp_conetnt.active .eln_setting_table input:not(#checkbox_all):checked').length;
                    // 如果选择的大于一个。给该按钮加上disabled。
                    if (0 == inputLen) {
                        $(".exp_conetnt.active .batch_check_btn").addClass('disabled');
                    } else {
                        $(".exp_conetnt.active .batch_check_btn").removeClass('disabled');
                    }
                }, 0)
            }

            //给实验列表里所有的checkbox添加的事件。
            //1.给tool-btn中只能单个生效的加disabled类。判断依据single_check_btn
            $('body').on('click', '.eln_setting_table input:checkbox', function (event) {
                checkBatchDisabled();
            });

            //实验创建多少天后。不能超过3位。
            $("body").on('input', '.sign-setting .plan_day', function (event) {
                var val = $('.sign-setting .plan_day').val();
                if (val.length > 2) {
                    $('.sign-setting .plan_day').val(val.substring(0, 2));
                }
            });

            /*$('body').on('focus input', '.selector-input', function() {
             /!*var id = $(this).attr('id');
             $ul = $('[for-input='+id+']');*!/
             var val = $.trim($(this).val());
             var $ul = $(this).next();
             $ul.find('li').each(function() {
             if($(this).attr('data-user_account').indexOf(val) != -1) {
             $(this).removeClass('hide');
             } else {
             $(this).addClass('hide');
             }
             });
             $ul.show();
             });

             $('body').on('blur', '.selector-input', function(e) {
             var $ul = $(this).next();
             setTimeout(function() {
             $ul.hide();
             }, 150);
             });

             $('body').on('click', '.selector-list li', function() {
             var $ul = $(this).parent();
             var $input = $ul.prev();
             $input.attr('data-user_id', $(this).attr('data-user_id'));
             $input.val($(this).attr('data-user_account'));
             });*/

            // 预审设置界面相关事件/*, #all_exp_user, #indraw_user, #project_user*/
            $('body').on('click', '#all_exp_need_pretrial', function () {
                if ($(this).is(':checked')) {
                    $('#all_exp_user').removeClass('disabled');
                } else {
                    $('#all_exp_user').addClass('disabled');
                }
            });

            $('body').on('click', '#indraw_need_pretrial', function () {
                if ($(this).is(':checked')) {
                    $('.danger-item').prop('checked', 'checked');
                    $('#indraw_user').removeClass('disabled');
                } else {
                    $('.danger-item').prop('checked', false);
                    $('#indraw_user').addClass('disabled');
                }
            });

            $('body').on('click', '.danger-item', function () {
                if ($(this).is(':checked')) {
                    $('#indraw_need_pretrial').prop('checked', 'checked');
                    $('#indraw_user').removeClass('disabled');
                } else {
                    var hasOne = false;
                    $('.danger-item').each(function () {
                        if ($(this).is(':checked')) {
                            hasOne = true;
                        }
                    });
                    if (!hasOne) {
                        $('#indraw_need_pretrial').prop('checked', false);
                        $('#indraw_user').addClass('disabled');
                    }
                }
            });

            $('body').on('click', '#project_need_pretrial', function () {
                if ($(this).is(':checked')) {
                    $('.project-setting-line').removeClass('disabled');
                } else {
                    $('.project-setting-line').addClass('disabled');
                }
            });

            $('body').on('click', '.add-project-setting', function () {
                $('.project-setting').append($('#project-setting-line-temp').html());

                $('.selector-input').each(function () {
                    if ($(this).parents('#project-setting-line-temp').length == 0) {
                        $(this).fSelect({
                            placeholder: '请选择项目',
                            numDisplayed: 3,
                            overflowText: '{n} selected',
                            noResultsText: '搜索无结果',
                            searchText: '搜索',
                            showSearch: true
                        });
                    }
                });
            });

            $('body').on('click', '.del-project-setting', function () {
                $(this).parent().remove();
            });

            // 重开设置界面事件处理 begin
            $('body').on('click', '#un_allow_edit', function () {
                if ($(this).is(':checked')) {
                    $('#reopen_checker_container').addClass('disabled');
                    $('#reopen_checker').prop('checked', false);
                } else {
                    $('#reopen_checker_container').removeClass('disabled');
                }
            });

            $('body').on('click', '#reopen_checker', function () {
                if ($(this).is(':checked')) {
                    $('#reopen_checker_selector_container').removeClass('disabled');
                } else {
                    $('#reopen_checker_selector_container').addClass('disabled');
                }
            });
            // 重开设置界面事件处理 end

            $('body').on('click', '#re_edit_approval_user_ids', function () {
                if ($(this).is(':checked')) {
                    $('.re-edit-setting .multi_user_select').removeClass('disabled');
                } else {
                    $('.re-edit-setting .multi_user_select').addClass('disabled');
                }
            });

            $('body').on('change', '.select-user,.select-group,.select-role', function () {
                var user_id = $(".exp_conetnt.active .select-user").val() || 0;
                var group_id = $(".exp_conetnt.active .select-group").val();
                var role_id = $(".exp_conetnt.active .select-role").val();

                $(".exp_conetnt.active .eln_setting_table").find("tr").each(function (i) {
                    if (i > 0) {
                        $(this).hide();
                    }
                });

                $(".exp_conetnt.active .eln_setting_table").find("tr").each(function (i) {
                    if (i > 0) {
                        //第一 三个条件全有
                        if (user_id > 0 && group_id > 0 && role_id > 0) {
                            if(role_id == 1) {
                                if ($(this).attr('data-user-id') == user_id && $(this).attr('data-group-id') == group_id && $(this).attr('data-system-master') == role_id) {
                                    $(this).show();
                                }
                            }
                            else {
                                if ($(this).attr('data-user-id') == user_id && $(this).attr('data-group-id') == group_id && $(this).attr('data-role-id') == role_id) {
                                    $(this).show();
                                }
                            }

                        }
                        //三个条件 满足其二满足
                        if (user_id > 0 && group_id > 0 && role_id == 0) {
                            if ($(this).attr('data-user-id') == user_id && $(this).attr('data-group-id') == group_id) {
                                $(this).show();
                            }
                        }

                        if (user_id > 0 && group_id == 0 && role_id > 0) {

                            if(role_id == 1){
                                if ($(this).attr('data-user-id') == user_id && $(this).attr('data-system-master') == role_id) {
                                    $(this).show();
                                }
                            }
                            else {
                                if ($(this).attr('data-user-id') == user_id && $(this).attr('data-role-id') == role_id) {
                                    $(this).show();
                                }
                            }
                        }

                        if (user_id == 0 && group_id > 0 && role_id > 0) {

                            if(role_id == 1){
                                if ($(this).attr('data-group-id') == group_id && $(this).attr('data-system-master') == role_id) {
                                    $(this).show();
                                }
                            }
                            else {
                                if ($(this).attr('data-group-id') == group_id && $(this).attr('data-role-id') == role_id) {
                                    $(this).show();
                                }
                            }

                        }
                        //三个条件满足其一
                        if (user_id > 0 && group_id == 0 && role_id == 0) {
                            if ($(this).attr('data-user-id') == user_id) {
                                $(this).show();
                            }
                        }
                        if (user_id == 0 && group_id > 0 && role_id == 0) {
                            if ($(this).attr('data-group-id') == group_id) {
                                $(this).show();
                            }
                        }
                        if (user_id == 0 && group_id == 0 && role_id > 0) {


                            if(role_id == 1){
                                if ($(this).attr('data-system-master') == role_id) {
                                    $(this).show();
                                }
                            }
                            else {
                                if ($(this).attr('data-role-id') == role_id) {
                                    $(this).show();
                                }
                            }
                        }
                        //三个条件全不满足
                        if (user_id == 0 && group_id == 0 && role_id == 0) {
                            $(this).show();
                        }
                    }
                });
            });

            $('body').on('click', '#require_approval', function () {
                if ($(this).is(':checked')) {
                    $('.send-email-box, .approval-node-box').removeClass('disabled');
                    $('.send_remind_email').prop('checked', false);
                } else {
                    $('.send-email-box, .approval-node-box').addClass('disabled');
                }
            });

            $('body').on('click', 'input[type=radio][name=witness-type]', function () {
                var approvalType = $('input[type=radio][name=witness-type]:checked').val();
                if(approvalType == 'multiple') {
                    $('.approval-node-box').removeClass('disabled');
                } else {
                    $('.approval-node-box').addClass('disabled');
                }
            });

            // add by hkk 2019/10/28 仪器库管理 新增 批量新增 编辑 查看 报修仪器 查看历史 预约界面 提醒界面 设置界面
            $('body').on('click', '.operateInstrument', function () {
                var type = $(this).attr('data-type');  // add batchAdd view edit repair history book
                var tr = $(this).parents('tr')
                var instrumentId = (type !=="add" || type !=="batchAdd") ? tr.attr('data-id'): ''; // 除了添加仪器操作都有id

                instrumentId = tr.attr('data-instrumentId') ? tr.attr('data-instrumentId') : instrumentId; // add by hkk 2021/4/20

                var instrumentName =  tr.attr('data-name'); // add by hkk 2021/4/20
            

                if(type === 'book') {

                    // 将 string 解析成 arr/obj
                    function parseUntilArray(jsonString) {
                        let result = jsonString;
                        
                        while (typeof result === 'string') {
                            try {
                                result = JSON.parse(result);
                            } catch (e) {
                                // 无法继续解析，跳出循环
                                break;
                            }
                        }
                        
                        return result;
                    }

                    // 获取30分钟之后的时间
                    function getTimeAfter30Minutes(plusTime) {
                        // 获取当前时间
                        const now = new Date();

                        // 添加30分钟
                        const after30Minutes = new Date(now.getTime() + plusTime * 60 * 1000);

                        // 格式化日期时间
                        const year = after30Minutes.getFullYear();
                        const month = String(after30Minutes.getMonth() + 1).padStart(2, '0');
                        const day = String(after30Minutes.getDate()).padStart(2, '0');
                        const hours = String(after30Minutes.getHours()).padStart(2, '0');
                        const minutes = String(after30Minutes.getMinutes()).padStart(2, '0');
                        const seconds = String(after30Minutes.getSeconds()).padStart(2, '0');

                        // 返回格式化后的时间字符串
                        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    }
                    
                    var instrumentAvailableSlots = parseUntilArray(tr.attr('data-availableSlots'));
                    var instrumentMaxAdvanceDay =  tr.attr('data-maxAdvanceDay');
                    var instrumentMinAdvance =  parseUntilArray(tr.attr('data-minAdvance'));
                    var instrumentMaxBookingDuration =  parseUntilArray(tr.attr('data-maxBookingDuration'));
                    renderComponent('/vue-ui/dist/InstrumentBookingCreateDialog.js', '#instrumentBookCreate', {
                        oldStatus: 0,
                        oldItem: {
                            id: instrumentId,
                            name: instrumentName,
                            time: [ getTimeAfter30Minutes(30),getTimeAfter30Minutes(60)],
                            available_slots: instrumentAvailableSlots,
                            max_advance_day: instrumentMaxAdvanceDay,
                            min_advance: instrumentMinAdvance,
                            max_booking_duration: instrumentMaxBookingDuration
                        },
                        closeBookCreate: function() {
                            // 使用unmountDynamicApp销毁应用
                            unrenderComponent('#instrumentBookCreate');
                            $('#instrumentBookCreate').hide();
                        }
                    })
                    $('#instrumentBookCreate').show();
                } else {
                    obj.getInstrumentPage(instrumentId, type);
                }

                return false // 防止冒泡
            });

            // add by ysj 2023/08/28 记录本管理 自定义编号 只支持字母，数字，特定特殊字符
            var chineseInput = false
            function replaceNumber() {
                var regex = /^[^\\/:*?"<>|]+$/;
                var newVal = $('.exp_conetnt.active .book_number_manage .book_prefix').val().replace(/./g, (match) => {
                    if (regex.test(match)) {
                        return match;
                    } else {
                        return "";
                    }
                });
                // 存在非法字符
                if (newVal.length != $('.exp_conetnt.active .book_number_manage .book_prefix').val().length) {
                    $.showAlert(mainLang('book_number_rule_tip'));
                    $('.exp_conetnt.active .book_number_manage .book_prefix').val(newVal)
                }
            }
            $('body').on('input', '.exp_conetnt.active .book_number_manage .book_prefix', function() {
                if (!chineseInput) {
                    replaceNumber()
                }
            })

            $('body').on('compositionstart', '.exp_conetnt.active .book_number_manage .book_prefix', function (e) {
                chineseInput = true
            })
            $('body').on('compositionend', '.exp_conetnt.active .book_number_manage .book_prefix', function (e) {
                chineseInput = false
                replaceNumber()
            })

            $('body').on('change', '.addInstrumentHtml .instrument_data_type', function (e) {
                // 获取选择的值
                var selectedValue = $(this).val();

                // 如果选择的值是1，则动态添加一个类似的<li>元素
                if (selectedValue == 1) {
                    // 移除新的<li>元素中 numerical_instrument_type_box 的 hidden 类
                    $('.addInstrumentHtml .numerical_instrument_type').removeClass('hidden');
                    $('.addInstrumentHtml .numerical_instrument_separator').removeClass('hidden');
                    $('.addInstrumentHtml .instrument_data_type').width('103px');

                }else{
                    $('.addInstrumentHtml .numerical_instrument_type').addClass('hidden');
                    $('.addInstrumentHtml .numerical_instrument_separator').addClass('hidden');
                    $('.addInstrumentHtml .instrument_data_type').width('236px');
                }
            })

            // add by hkk 2020/4/26 弹框内弹框新增校验页面
            $('body').on('click', '.instrument_page .addInstrumentCheck', function () {
                var type = $(this).attr('data-type'); // addCheck
                var instrumentId = $(this).attr('data-id');

                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/get-page',
                    data: {
                        instrumentId:instrumentId,
                        type: type,
                    },
                    success: function (data) {
                        if (data.status == 1) {

                            $(".instrument_check_page").remove();
                            $("body").append(data.data.file);
                            $(".instrument_check_page").modal('show');

                            //预约界面调用日历插件

                            if(type === "addCheck"){
                                $('.instrument_check_page .datetimepicker').datetimepicker({
                                    format: 'yyyy-mm-dd',
                                    autoclose: true,
                                    minView: 2,
                                    clearBtn: true,
                                });
                            }

                        }
                    }
                });

                return false // 防止冒泡
            });

            // add by hkk 2019/10/29 仪器库提交 新增 编辑 查看 报修仪器 预约 编辑预约 提醒
            $('body').on('click', '.instrument_conform_btn,.instrument_conform_popup_btn', function () {

                var type = $(this).attr('data-type');
                var instrumentData = {};

                if(type === "repair") { // repair
                    instrumentData.id = $(this).attr('data-id');
                    instrumentData.remark = $('.instrument_repair_page #repair_instrument').val();
                    instrumentData.email_cc_ids = $('.instrument_repair_page .input').attr('idbox');
                    instrumentData.approval_status = $('.instrument_repair_page .approval_route').data('id') == 3 ? '1' : '0'; //3是维修中
                }
                else if(type === "book"){ // book
                    instrumentData.instrument_id = $(this).attr('data-id');
                    instrumentData.start_time  = $('.instrument_book_page .instrument_book [name="start_time"]').val();
                    instrumentData.end_time  = $('.instrument_book_page .instrument_book [name="end_time"]').val();
                    instrumentData.remark  = $('.instrument_book_page #book_instrument_remark').val();
                    instrumentData.related_experiment = $('.instrument_book_page .related_experiments').val();
                    if(instrumentData.start_time ==="" || instrumentData.end_time ==="" ){
                        $.showAlert(mainLang('instrument_book_tip1'));
                        return
                    }
                }
                else if(type === "reminder"){ // 提醒
                    instrumentData.id = $(this).attr('data-id');
                    instrumentData.remind_setting = [];
                    var ifEnd = false;
                    $('.instrument_reminder_setting .add-instrument-reminder:visible').each(function (index,value){
                        var reminder_setting = [];
                        if ($(this).find('.period_reminder').prop('checked')) { // 周期提醒 默认

                            var reminder_period = $(this).find('.reminder_period_input').val();
                            var reminder_start_time = $(this).find('.reminder_start_time').val();

                            if (reminder_period === '') {
                                $.showAlert(mainLang('instrument_reminder_tip1')); // 请填写周期天数
                                ifEnd = true;
                                return
                            } else if (!(/(^[1-9]\d*$)/.test(reminder_period))) {
                                $.showAlert(mainLang('please_fill_in_period_correctly'));
                                ifEnd = true;
                                return
                            } else if (reminder_start_time === '') {
                                $.showAlert(mainLang('instrument_reminder_tip5')); // 请填写开始时间
                                ifEnd = true;
                                return
                            }
                            else {
                                reminder_setting.reminder_start_time = reminder_start_time;
                                reminder_setting.reminder_period = reminder_period;
                                reminder_setting.reminder_type = 'period';
                            }
                        }
                        else if ($(this).find('.time_point_reminder').prop('checked')) { // 时间点提醒
                            var timePoints = [];
                            $(this).find('.tp-box input.time_point').each(function () {
                                var tpValue = $(this).val();
                                if (tpValue !== '') {
                                    timePoints.push(tpValue);
                                }
                            });
                            if (timePoints.length === 0) {
                                $.showAlert(mainLang('instrument_reminder_tip2')); // 请填写时间点
                                ifEnd = true;
                                return
                            } else {
                                reminder_setting.time_points = timePoints;
                                reminder_setting.reminder_type = 'timePoints';
                            }
                            reminder_setting.addToSchedule = $(this).find('.add_to_schedule_checkbox').prop('checked') ? 1 : 0;
                        } else { // 都未选中，此时相当于取消此前设置过的提醒
                            reminder_setting.reminder_type = 'cancel';
                        }

                        if(reminder_setting.reminder_type !== 'cancel'){
                            var userIds = $(this).find('[name="user_ids"]').attr('idbox');
                            if(userIds) {
                                reminder_setting.reminder_users = userIds.split(',');
                            }else{
                                $.showAlert(mainLang('instrument_reminder_tip3'));// 请选择提醒人员
                                ifEnd = true;
                                return
                            }

                            var reminder_content = $(this).find('.reminder_content').val();
                            if(reminder_content === ''){
                                $.showAlert(mainLang('instrument_reminder_tip4'));// 请填写提醒内容
                                ifEnd = true;
                                return
                            } else {
                                reminder_setting.reminder_content = reminder_content;
                            }
                        }
                        instrumentData.remind_setting[index] = {...reminder_setting};
                        // console.log(instrumentData.setting);
                    })
                        instrumentData.remind_setting = {...instrumentData.remind_setting};
                    if(Object.keys(instrumentData.remind_setting).length === 0 || ifEnd){
                        return;
                    }

                }
                else if(type === "record_setting"){ // 仪器记录复核和时间重复设置
                    instrumentData.id = $(this).attr('data-id');
                    var setting = {};

                    // 运行记录复核设置
                    if ($('.instrument_record_setting #operate_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.operate_check_ids').attr('idbox');
                        if (userIds) {
                            setting.operate_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_operate_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 维保记录复核设置
                    if ($('.instrument_record_setting #repair_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.repair_check_ids').attr('idbox');
                        if (userIds) {
                            setting.repair_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_repair_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 校验记录复核设置
                    if ($('.instrument_record_setting #check_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.check_check_ids').attr('idbox');
                        if (userIds) {
                            setting.check_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_check_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 报废需要审核
                    if ($('.instrument_record_setting #scrap_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.scrap_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.scrap_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_status_adjust_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 停用需要审核
                    if ($('.instrument_record_setting #suspend_use_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.suspend_use_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.suspend_use_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_status_adjust_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 删除需要审核
                    if ($('.instrument_record_setting #delete_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.delete_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.delete_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_status_adjust_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 报修需要审核
                    if ($('.instrument_record_setting #apply_repair_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.apply_repair_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.apply_repair_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_status_adjust_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 时间冲突设置
                    setting.operate_repeat = $('.instrument_record_setting #operate_time').prop('checked');
                    setting.repair_repeat = $('.instrument_record_setting #repair_time').prop('checked');
                    setting.check_repeat = $('.instrument_record_setting #check_time').prop('checked');
                    setting.time_operate = $('.instrument_record_setting #time_operate').prop('checked');// add by hkk 2022/5/27

                    instrumentData.setting = setting;

                }
                else if(type === "record_setting_batch"){ // 仪器记录复核和时间重复设置 // add by hkk 2022/5/27

                    // 获取选中的仪器记录id
                    var chooseIds = [];
                    $('.instruments_table input:checkbox.checkboxBtn:checked').each(function() {
                        chooseIds.push($(this).parents('tr').attr('data-id'));
                    });

                    instrumentData.ids = chooseIds;
                    var setting = {};

                    // 运行记录复核设置
                    if ($('.instrument_record_setting #operate_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.operate_check_ids').attr('idbox');
                        if (userIds) {
                            setting.operate_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_operate_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 维修记录复核设置
                    if ($('.instrument_record_setting #repair_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.repair_check_ids').attr('idbox');
                        if (userIds) {
                            setting.repair_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_repair_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 校验记录复核设置
                    if ($('.instrument_record_setting #check_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.check_check_ids').attr('idbox');
                        if (userIds) {
                            setting.check_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_check_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 修改状态审核
                    if ($('.instrument_record_setting #scrap_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.scrap_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.scrap_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_operate_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 修改状态审核
                    if ($('.instrument_record_setting #suspend_use_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.suspend_use_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.suspend_use_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_repair_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 修改状态审核
                    if ($('.instrument_record_setting #delete_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.delete_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.delete_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_check_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 修改状态审核
                    if ($('.instrument_record_setting #apply_repair_must_check').prop('checked')) {
                        var userIds = $('.instrument_record_setting input.apply_repair_must_check_ids').attr('idbox');
                        if (userIds) {
                            setting.apply_repair_must_check_users = userIds.split(',');
                        } else {
                            $.showAlert(mainLang('please_select_check_record_check_user'));// 请选择提醒人员
                            return
                        }
                    }

                    // 时间冲突设置
                    setting.operate_repeat = $('.instrument_record_setting #operate_time').prop('checked');
                    setting.repair_repeat = $('.instrument_record_setting #repair_time').prop('checked');
                    setting.check_repeat = $('.instrument_record_setting #check_time').prop('checked');
                    setting.time_operate = $('.instrument_record_setting #time_operate').prop('checked');// add by hkk 2022/5/27

                    instrumentData.setting = setting;

                }
                else if(type === "editBook"){ // editBook
                    instrumentData.book_id = $(this).attr('data-id');
                    instrumentData.instrument_id = $(this).attr('data-instrumentId');
                    instrumentData.start_time  = $('.instrument_book_page .instrument_book [name="start_time"]').val();
                    instrumentData.end_time  = $('.instrument_book_page .instrument_book [name="end_time"]').val();
                    instrumentData.remark  = $('.instrument_book_page #book_instrument_remark').val();
                    instrumentData.related_experiment = $('.instrument_book_page .related_experiments').val();

                    if(instrumentData.start_time ==="" || instrumentData.end_time ==="" ){
                        $.showAlert(mainLang('instrument_book_tip1'));
                        return
                    }

                }
                else if(type === "addCheck"){ // 校验页面弹框新增校验  疑似废弃，现在新增校验不走这边了

                    instrumentData.instrument_id = $(this).attr('data-id');
                    instrumentData.uncertainty = $('.instrument_check_page input.uncertainty').val();
                    instrumentData.credibility = $('.instrument_check_page input.credibility').val();
                    instrumentData.k_value = $('.instrument_check_page input.k_value').val();
                    instrumentData.corrected_value = $('.instrument_check_page input.corrected_value').val();
                    instrumentData.check_personnel = $('.instrument_check_page input.check_personnel').val();
                    instrumentData.check_institution = $('.instrument_check_page input.check_institution').val();
                    instrumentData.exp_code = $('.instrument_check_page input.exp_code').val();
                    instrumentData.remark = $('.instrument_check_page input.remark').val();

                    if(instrumentData.check_personnel ===""  ){
                        $.showAlert(mainLang('instrument_check_tip2'));
                        return
                    }
                    // 有效期
                    var start_time  = $('.instrument_check_page input#start_time').val();
                    var end_time  = $('.instrument_check_page input#end_time').val();
                    if (start_time === "" || end_time === "") {
                        $.showAlert(mainLang('instrument_check_tip1'));
                        return
                    } else {
                        instrumentData.check_start_time = start_time ;
                        instrumentData.check_end_time = end_time;
                    }

                    // 根据当前时间计算是否在校验有效期内
                    var isCheckValid =false;
                    if( new Date() >=  new Date(start_time)  && new Date() <=  new Date(end_time) ){
                        isCheckValid = true;
                    }
                    console.log(isCheckValid)
                    instrumentData.isCheckValid = isCheckValid;


                    // 文件上传
                    var checkFilesArray = [];
                    $(this).parents('.instrument_check_page').find('.single_detail_file').each(function(){
                        var allFileData = $.formSerializeFn($(this));
                        var fileData = {
                            dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                            save_name: allFileData.save_name ? allFileData.save_name : '',
                            real_name: allFileData.file_name ? allFileData.file_name : ''
                        };
                        checkFilesArray.push(fileData)
                    });
                    instrumentData.files = JSON.stringify(checkFilesArray);
                    console.log(instrumentData)

                } else if(type === "fromBiology") { // add by hkk 2021/6/8 去掉实验里插入仪器draw.js重复执行此函数
                    return
                }
                else { //  add view edit
                    instrumentData.id = $(this).attr('data-id');
                    instrumentData.name = $('.addInstrumentHtml input.name').val();
                    instrumentData.batch_number = $('.addInstrumentHtml input.batch_number').val();
                    instrumentData.specification = $('.addInstrumentHtml input.specification').val();
                    instrumentData.model = $('.addInstrumentHtml input.model').val();
                    instrumentData.manufacturer = $('.addInstrumentHtml input.manufacturer').val();
                    instrumentData.position = $('.addInstrumentHtml input.position').val();
                    instrumentData.responsible_person = $('.addInstrumentHtml input.responsible_person').attr('idbox');
                    instrumentData.in_charge_person = $('.addInstrumentHtml input.in_charge_person').attr('idbox');
                    instrumentData.maintenance_person = $('.addInstrumentHtml input.maintenance_person').attr('idbox');
                    instrumentData.groupIds = $('.addInstrumentHtml input#group_ids').attr('idbox'); // 2020/4/23
                    instrumentData.departmentIds = $('.addInstrumentHtml input.dept_select').attr('data'); // add by hkk 2022/6/27
                    instrumentData.supplier = $('.addInstrumentHtml input.supplier').val();
                    instrumentData.remark = $('.addInstrumentHtml input.remark').val();
                    instrumentData.files = $('.addInstrumentHtml input.files').val();
                    instrumentData.pictures = $('.addInstrumentHtml input.pictures').val();
                    instrumentData.status = $('.addInstrumentHtml .instrument_status').val();
                    instrumentData.ispopup = $(this).hasClass('instrument_conform_popup_btn') ? 1 : 0;//提醒选择的鹰群或部门是否是这个账号所在的鹰群或部门的弹窗 add by zwm 2023/5/10

                    // add by hkk 2021/4/28 仪器分类
                    instrumentData.instrument_type = $('.addInstrumentHtml .instrument_type').val();
                    //  数据对接类型
                    instrumentData.data_type = $('.addInstrumentHtml .instrument_data_type').val() || '';
                    instrumentData.numerical_instrument_type = $('.addInstrumentHtml .numerical_instrument_type').val() || 0;

                    // add by hkk 2021/4/28 仪器自定义项
                    var instrumentDefineList = $('.addInstrumentHtml .defineData');
                    instrumentDefineList.each(function (index,item) {
                        instrumentData['data' + $(item).attr('data-field')] = $(item).val()
                    });

                    if (instrumentData.name === "") {
                        $.showAlert(mainLang('instrument_input'));
                        return
                    }

                    // 更改校验逻辑
                    var needCheck = $('.addInstrumentHtml .instrument_situation').val();
                    if (type === 'add') {
                        instrumentData.check_situation = needCheck === '0' ? 0 : 2; // 新增只能是0或2(未校验)
                    } else if (type === 'edit') {
                        instrumentData.check_status = needCheck; // 编辑的话传过去根据原来数据确定状态是0 1还是2
                    }

                    // modified by hkk 2019/10/4/14   新增保存文件上传
                    var filesArray = [];
                    $(this).parents('.instrument_page').find('.single_detail_file').each(function(){
                        var allFileData = $.formSerializeFn($(this));
                        var fileData = {
                            dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                            save_name: allFileData.save_name ? allFileData.save_name : '',
                            real_name: allFileData.file_name ? allFileData.file_name : ''
                        };
                        filesArray.push(fileData)
                    });
                    instrumentData.files = JSON.stringify(filesArray);

                    // modified by zwm 2023/4/17   新增保存图片上传
                    var picturesArray = [];
                    $(this).parents('.instrument_page').find('.single_detail_picture').each(function(){
                        var allPictureData = $.formSerializeFn($(this));
                        var pictureData = {
                            dep_path: allPictureData.dep_path ? allPictureData.dep_path : '',
                            save_name: allPictureData.save_name ? allPictureData.save_name : '',
                            real_name: allPictureData.file_name ? allPictureData.file_name : ''
                        };
                        picturesArray.push(pictureData);
                    });
                    instrumentData.pictures = JSON.stringify(picturesArray);

                    var statusApprovalId = $('[name="instrument_status"].instrument_status option:selected').attr('data-id');
                }

                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/submit-page',
                    data: {
                        instrumentData:instrumentData,
                        type: type,
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            if (type === 'addCheck') {
                                $(".instrument_check_page").remove();
                                $('body').removeClass('modal-open')
                            }else if(data.data.isPopup == 1){
                                $("body").append(data.data.file);
                                $(".instrument_edit_popup").modal('show');
                            } else if (data.data.needApproval == 1) {
                                $("body").append(data.data.file);
                                $(".instrument_status_change_approval").modal('show');
                            } else {
                                $(".instrument_page").remove();
                                $(".instrument_edit_popup").remove();
                                $('body').removeClass('modal-open')
                            }

                            switch (type) {
                                case 'add':
                                case 'edit':
                                    // bug#34072 仪器库管理，里面筛选了一个条件后搜索，再编辑仪器后，页面重置
                                    $('.exp_conetnt.active .search-instrument-manage[data-type = "instruments_manage"]').click();
                                    // module.instrumentsManage(false, parseInt($('.exp_conetnt.active .page-btn.current').text(),10));  // add by hkk 2020/8/3 传入当前页码数，编辑完成后直接跳转
                                    break;
                                case 'repair':
                                    module.instrumentsManage(false);
                                    $.showAlert(mainLang('repair_successful'));
                                    break;
                                case 'reminder': // add by hkk 2020/4/24
                                    $.showAlert(mainLang('reminder_successful'));
                                    break;
                                case 'record_setting': // add by hkk 2020/4/24
                                    $.showAlert(mainLang('setting_successful'));
                                    break;
                                case 'book':
                                    $.showAlert(mainLang('book_successful'));
                                    break;
                                case 'editBook':
                                    $.showAlert(mainLang('modify_book_successful'));
                                    module.upData.instrument_name = $.formSerializeFn($(".exp_conetnt.active #search-my-instrument-book")).instrument_name;// 带上条件
                                    module.myInstrumentsBook(false); // // 从新渲染我的预约前端页面

                                    //跳到原页面
                                    break;
                                case 'addCheck': // 更新当前弹框校验表

                                    // 构造文件列html
                                    var fileHtml = '<div class="detail_file_add">';
                                    for (var i = 0; i < checkFilesArray.length; i++) {
                                        var path = checkFilesArray[i].dep_path;
                                        var name = checkFilesArray[i].real_name;
                                        var saveName = checkFilesArray[i].save_name;
                                        fileHtml += `<div class="single_detail_file">
                                                        <span class="ref_file_part file_up_box">
                                                        <span class="file_name" style="width: 80px;max-width: 100px" title="${name}">${name}</span>
                                                        <a class="download_ico" target="_blank" title="下载" href=?r=download/file&path=${path}&name=${saveName}&file_name=${encodeURI(name)} ></a>
                                                     </div>`
                                    }
                                    fileHtml += '</div>';

                                    // 获取当前时间 2019-02-28 15:23:12
                                    function currentDate() {
                                        var now = new Date();
                                        var year = now.getFullYear(); //得到年份
                                        var month = now.getMonth();//得到月份
                                        var date = now.getDate();//得到日期
                                        var hour = now.getHours();//得到小时
                                        var min = now.getMinutes();//得到分钟
                                        var sec = now.getSeconds();//得到分钟
                                        month = month + 1;
                                        date = date < 10 ? ("0" + date) : date;
                                        month = month < 10 ? ("0" + month) : month;
                                        return year + "-" + month + "-" + date + " " + hour + ":" + min + ":" + sec
                                    }

                                    // 获取文件，写入第一行表格
                                    var firstTr = $('.instrument_check_table tr:eq(0)');
                                    var addHtml = `<tr class="addTr">
                                                     <td class="order">1</td>
                                                     <td>${currentDate()}</td>
                                                     <td>${instrumentData.uncertainty}</td>
                                                     <td>${instrumentData.credibility}</td>
                                                     <td>${instrumentData.k_value}</td>
                                                     <td>${instrumentData.corrected_value}</td>
                                                     <td>${instrumentData.check_start_time} -- ${instrumentData.check_end_time}</td>
                                                     <td>${instrumentData.check_institution}</td>
                                                     <td>${instrumentData.check_personnel}</td>
                                                     <td>${instrumentData.exp_code}</td>
                                                     <td>${instrumentData.remark}</td>
                                                     <td>${fileHtml}</td>
                                                    </tr>`;
                                    firstTr.after(addHtml);

                                    // 获取当前隐藏列信息并设置新增列隐藏
                                    var listCols = localStorage.getItem('instrument_check_history_cols_index');
                                    if(!listCols) {
                                        listCols = '3,4,5,6,10,12';
                                    }
                                    listCols = listCols.split(',');
                                    listCols.forEach(function(ele) {
                                        $('#instrument_check_history .addTr').find(`td:nth-child(${ele})`).hide();
                                    });

                                    // 遍历第一列更新序号
                                    $('#instrument_check_history td.order').each(function(index, item){
                                       item.innerText = index +1; // 更新第一列的order
                                    });

                                    // 更改当前仪器库列表的前端校验状态
                                    if (instrumentData.isCheckValid) {
                                        $(`.instruments_table tr[data-id=${instrumentData.instrument_id}] td.check-situation`)[0].innerText = mainLang('already_checked');
                                        $(`.instruments_table tr[data-id=${instrumentData.instrument_id}] td.check-situation`).attr('data-checkSituation', '1')
                                    }else{
                                        $(`.instruments_table tr[data-id=${instrumentData.instrument_id}] td.check-situation`)[0].innerText = mainLang('unchecked');
                                        $(`.instruments_table tr[data-id=${instrumentData.instrument_id}] td.check-situation`).attr('data-checkSituation', '2')
                                    }

                                    $.showAlert(mainLang('add_check_successful'));


                                    break;
                                default:
                                    break;
                            }

                        }
                    },

                })
            });

            // add by hkk 2019/10/28 仪器库管理删除仪器
            $('body').on('click', '.deleteInstrument', function () {

                var type = $(this).attr('data-type');
                var instrumentIds =  [$(this).attr('data-id')];

                    $.ajaxFn({
                        url: ELN_URL + '?r=instrument/delete',
                        data: {
                            instrumentIds:instrumentIds,
                            type: type,
                        },
                        success: function (data) {
                            if (data.status === 1) {
                                $(".instrument_delete").remove();
                                $("body").append(data.data.file);
                                $(".instrument_delete").modal('show');
                            }
                        }
                    })


                return false // 防止冒泡

            });

            // add by hkk 2019/10/30 仪器库管理更多筛选
            $('body').on('click', '.instrument_need_more_filter', function () {
               if($('.exp_conetnt.active .instrumentMoreFilter').hasClass('hide')){
                   $('.exp_conetnt.active .instrumentMoreFilter').removeClass('hide')
               }else{
                   $('.exp_conetnt.active .instrumentMoreFilter').addClass('hide')
               }
                var tableHeight = setTabHeight();
                $('.collaborationList .outFixedHead').css('height', tableHeight + 'px');
            });

            // add by hkk 2019/11/5  我的仪器库 编辑预约
            $('body').on('click', '.editInstrumentBook', function () {

                var bookId = $(this).attr('data-id'); // 除了新增都有id
                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/get-page',
                    data: {
                        bookId:bookId,
                        type: 'editBook',
                    },
                    success: function (data) {
                        if (data.status == 1) {

                            $(".instrument_page").remove();
                            $("body").append(data.data.file);
                            $(".instrument_page").modal('show');

                            $('.instrument_book_page .modal-dialog').addClass('modal-dialog-840');// 调整弹出框的大小


                            //预约界面调用日历插件
                            var dateOpts = {
                                format: 'yyyy-mm-dd hh:ii',
                                autoclose: true,
                                minView: 0, // 0精确到分钟 2精确到小时
                                clearBtn: true,
                            };

                            if( $.fn.datetimepicker){
                                $('.instrument_book .datetimepicker').datetimepicker(dateOpts).on('click', function () {

                                    if ($('.instrument_book [name="end_time"]').val() != '') {
                                        var startTimer = $('.instrument_book [name="end_time"]').val() ;
                                        $('.instrument_book [name="start_time"]').datetimepicker('setEndDate', startTimer);
                                    }

                                    if ($('.instrument_book [name="start_time"]').val() != '') {
                                        var endTimer = $('.instrument_book [name="start_time"]').val();
                                        $('.instrument_book [name="end_time"]').datetimepicker('setStartDate', endTimer);
                                    }
                                })
                            }



                        }
                    }
                });

                return false // 防止冒泡
            });

            // add by hkk 2019/11/5  我的仪器库 删除预约
            $('body').on('click', '.deleteInstrumentBook', function () {

                var type = $(this).attr('data-type');
                var bookIds =  [$(this).attr('data-id')];

                $.showContent(mainLang('delete_instrument_book'), mainLang('delete_instrument_book'), mainLang('delete_instrument_book_tip'), function () {

                    $.ajaxFn({
                        url: ELN_URL + '?r=instrument/delete-book',
                        data: {
                            bookIds:bookIds,
                            type: type,
                        },
                        success: function (data) {
                            if (data.status === 1) {
                                $.closeModal();
                                module.upData.instrument_name = $.formSerializeFn($(".exp_conetnt.active #search-my-instrument-book")).instrument_name;// 带上条件
                                module.myInstrumentsBook(false); // // 从新渲染我的预约前端页面
                                $.showAlert(mainLang('delete_successful'));

                            }
                        }
                    })

                })

                return false // 防止冒泡
            });

            // add by hkk 2019/7/1  编辑查看仪器可以上传文件 2020/4/26 增加校验文件
            $('body').on('click', '.instrument_page .detail_upload_file,.instrument_check_page .detail_upload_file', function() {
                var input = $(this);
                var addFileWidth = "width: 150px;max-width:200px";
                input.ajaxfileupload({
                    action: ELN_URL + '?r=upload/upload-file',
                    params: {
                        type: '2'
                    },

                    validate_extensions: false,
                    onComplete: function(res) {
                        $.closeLoading();
                        //edge浏览器下，即使下面的onstart return false了。依然会走到这里来。
                        //会影响start中的判断提示。
                        if (!res) {
                            return;
                        }
                        if (res.status !== 1) {
                            input[0].outerHTML = input[0].outerHTML;
                            $.showAlert(res.info || mainLang('upload_file_error'));
                            return;
                        }
                        window.noLoadTip = false;

                        var fileNameString = '';
                        for(var i=0; i<res.data.length; i++) {
                            var fileData = res.data[i];
                            if (/\.xlsx/.test(fileData.file_name) || /\.xls/.test(fileData.file_name)) { // excel用intable预览
                                var fileHtml = '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                                    '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                                    '<input type="hidden" name="file_name" value="' + fileData.file_name + '" />' +
                                    '<a class="preview-excel">' + fileData.file_name + '</a><br>';
                            } else {
                                var fileHtml = '<div class="img_bottom">' +
                                    '<a class="preview_file" target="_blank" > ' + fileData.file_name + ' </a><br>' +
                                    '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                                    '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                                    '<input type="hidden" name="file_name" value="' + fileData.file_name + '" />' +
                                    '</div>';
                            }
                            var html = '<div style="text-align: left;" class="single_detail_file"> <span class="ref_file_part file_up_box">' + fileHtml +
                                '<a style="margin-left:8px;"  class="_btn del_file_with_reason del_ico" title="' + mainLang('del') + '"></a>' +
                                '<a style="margin-left:5px;" class="download_ico" target="_blank" title="' + mainLang('download') + '" href=?r=download/file&path=' + fileData.dep_path + '&name=' + fileData.save_name + '&file_name=' + encodeURIComponent(fileData.file_name) + '&exp_id=' + expId + '></a>' +
                                '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                                '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                                '<input type="hidden" name="file_name" value="' + fileData.file_name + '" /></span></div>';
                            input.parents('.upload_file').next().append(html)

                            fileNameString += fileData.file_name + ',';
                        }

                        // 仪器上传文件记录痕迹 todo
                        if(input.parents('.addInstrumentHtml').length === 1 && input.parents('.addInstrumentHtml').attr("data-id") !== ''){

                            console.log('上传痕迹')
                            var part = input.parents('.addInstrumentHtml');
                            var instrumentId = part.attr('data-id');
                            var filesArray = []; // 获取现有的文件，用于数据库覆盖
                            part.find('.single_detail_file').each(function(){
                                var allFileData = $.formSerializeFn($(this));
                                var fileData = {
                                    dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                                    save_name: allFileData.save_name ? allFileData.save_name : '',
                                    real_name: allFileData.file_name ? allFileData.file_name : ''
                                };
                                filesArray.push(fileData)
                            });

                            $.ajaxFn({
                                url: ELN_URL + '?r=instrument/upload-instrument-file',
                                data: {
                                    instrumentId: instrumentId, // 仪器id
                                    currentFiles: JSON.stringify(filesArray),
                                    uploadFiles: fileNameString, // 上传的的文件名
                                },
                                success: function (data) {
                                    if (data.status === 1) {

                                    }
                                },
                            })

                        }
                    },
                    onStart: function(a) {

                        var dom = this;

                        if (dom[0].files && dom[0].files[0]) {
                            if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                                $.showAlert(mainLang('file_too_big'));
                                setTimeout(function() {
                                    $.closeLoading();
                                }, 100);
                                return false;
                            }
                        }
                        $.loading();
                    },
                    onCancel: function() {
                        $.closeLoading();
                        console.log('onCancel');
                    }
                });
            });

            // add by zwm 编辑查看仪器可以上传图片
            $('body').on('click', '.instrument_page .detail_upload_picture ', function() {
                var input = $(this);
                input.ajaxfileupload({
                    action: ELN_URL + '?r=upload/upload-file',
                    params: {
                        type: '1'
                    },

                    validate_extensions: false,
                    onComplete: function(res) {
                        $.closeLoading();
                        //edge浏览器下，即使下面的onstart return false了。依然会走到这里来。
                        //会影响start中的判断提示。
                        if (!res) {
                            return;
                        }
                        if (res.status !== 1) {
                            input[0].outerHTML = input[0].outerHTML;
                            $.showAlert(res.info || mainLang('upload_file_error'));
                            return;
                        }
                        window.noLoadTip = false;

                        var fileNameString = '';
                        for(var i=0; i<res.data.length; i++) {
                            var fileData = res.data[i];
                            var html = '<div style="text-align: left;margin: 4px;" class="single_detail_picture"> <span class="ref_file_part file_up_box"><img src="'+res.data[i].img_url+'" class="file_name">' +
                                '<a style="margin-left:5px"  class="_btn del_file_with_reason del_ico" title="' + mainLang('del') + '"></a>' +
                                '<a class="download_ico" target="_blank" title="' + mainLang('download') + '" href=?r=download/file&path=' + fileData.dep_path + '&name=' + fileData.save_name + '&file_name=' + encodeURIComponent(fileData.file_name) + '></a>' +
                                '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                                '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                                '<input type="hidden" name="file_name" value="' + fileData.file_name + '" /></img></div>';
                            input.parents('.upload_file').next().append(html)

                            fileNameString += fileData.file_name + ',';
                        }

                        // 仪器上传文件记录痕迹 todo
                        if(input.parents('.addInstrumentHtml').length === 1 && input.parents('.addInstrumentHtml').attr("data-id") !== ''){

                            console.log('上传痕迹')
                            var part = input.parents('.addInstrumentHtml');
                            var instrumentId = part.attr('data-id');
                            var filesArray = []; // 获取现有的文件，用于数据库覆盖
                            part.find('.single_detail_picture').each(function(){
                                var allFileData = $.formSerializeFn($(this));
                                var fileData = {
                                    dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                                    save_name: allFileData.save_name ? allFileData.save_name : '',
                                    real_name: allFileData.file_name ? allFileData.file_name : ''
                                };
                                filesArray.push(fileData)
                            });

                            $.ajaxFn({
                                url: ELN_URL + '?r=instrument/upload-instrument-picture',
                                data: {
                                    instrumentId: instrumentId, // 仪器id
                                    currentFiles: JSON.stringify(filesArray),
                                    uploadFiles: fileNameString, // 上传的的文件名
                                },
                                success: function (data) {
                                    if (data.status === 1) {

                                    }
                                },
                            })

                        }
                    },
                    onStart: function(a) {

                        var dom = this;

                        if (dom[0].files && dom[0].files[0]) {
                            if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                                $.showAlert(mainLang('file_too_big'));
                                setTimeout(function() {
                                    $.closeLoading();
                                }, 100);
                                return false;
                            }
                        }
                        $.loading();
                    },
                    onCancel: function() {
                        $.closeLoading();
                        console.log('onCancel');
                    }
                });
            });


            // add by hkk 2019/7/1   2020/4/26 增加校验文件删除
            $('body').on('click', '.instrument_check_page .single_detail_file .del_file_with_reason', function() {
                var box = $(this);
                $.showContent('warning', mainLang('del_tips'), mainLang('del_file'), function() {
                    var part = box.parents('.single_detail_file');
                    part.remove();
                    $('.pop_modal').modal('hide');
                    window.noLoadTip = false;
                });
            });

            // add by hkk 2020/4/28 仪器删除文件要记录痕迹和删除原因
            $('body').on('click', '.instrument_page .single_detail_file .del_file_with_reason', function() {
                var box = $(this);

                if(box.parents('.addInstrumentHtml').attr("data-id") && box.parents('.addInstrumentHtml').attr("data-id") !== ''){
                    var instrumentFiles = $(this).parents('.instrument_file');
                    var html = `<label>${mainLang('del_fil_reason')}：</label><textarea class="instrument_delete_content" ></textarea>`
                    $.showContent('warning', mainLang('del_fil_warning'), html, function() {

                        var deleteReason = $('.instrument_delete_content').val();
                        if (deleteReason === '') {
                            $.showAlert(mainLang('input_del_fil_reason'));
                            return
                        }

                        // 发送请求给后端存储删除文件的痕迹
                        var part = box.parents('.single_detail_file');
                        var instrumentId = part.parents('.addInstrumentHtml').attr('data-id');
                        var deleteFile = {  // 获取删除的文件信息
                            dep_path: part.find('input[name=dep_path]').val(),
                            save_name: part.find('input[name=save_name]').val(),
                            real_name: part.find('input[name=file_name]').val()
                        };
                        part.remove(); // 删除前端
                        $('.pop_modal').modal('hide');
                        var filesArray = []; // 获取删除后剩余的文件，用于数据库覆盖
                        instrumentFiles.find('.single_detail_file').each(function(){
                            var allFileData = $.formSerializeFn($(this));
                            var fileData = {
                                dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                                save_name: allFileData.save_name ? allFileData.save_name : '',
                                real_name: allFileData.file_name ? allFileData.file_name : ''
                            };
                            filesArray.push(fileData)
                        });


                        window.noLoadTip = false;


                        $.ajaxFn({
                            url: ELN_URL + '?r=instrument/delete-instrument-file',
                            data: {
                                deleteReason: deleteReason,//  删除原因
                                instrumentId: instrumentId, // 仪器id
                                currentFiles: JSON.stringify(filesArray), //删除的文件
                                deleteFile: deleteFile, //删除的文件
                            },
                            success: function (data) {
                                if (data.status === 1) {
                                    $.showAlert(mainLang('del_success'));
                                }
                            },
                        })
                    });

                }else{
                    $.showContent('warning', mainLang('del_tips'), mainLang('del_file'), function() {
                        var part = box.parents('.single_detail_file');
                        part.remove();
                        $('.pop_modal').modal('hide');
                        window.noLoadTip = false;
                    });
                }
            });

            // add by zwm 2023/4/21 仪器删除图片要记录痕迹和删除原因
            $('body').on('click', '.instrument_page .single_detail_picture .del_file_with_reason', function() {
                var box = $(this);

                if(box.parents('.addInstrumentHtml').attr("data-id") && box.parents('.addInstrumentHtml').attr("data-id") !== ''){
                    var instrumentFiles = $(this).parents('.instrument_file');
                    var html = `<label>${mainLang('del_fil_reason')}：</label><textarea class="instrument_delete_content" ></textarea>`
                    $.showContent('warning', mainLang('del_fil_warning'), html, function() {

                        var deleteReason = $('.instrument_delete_content').val();
                        if (deleteReason === '') {
                            $.showAlert(mainLang('input_del_fil_reason'));
                            return
                        }

                        // 发送请求给后端存储删除文件的痕迹
                        var part = box.parents('.single_detail_picture');
                        var instrumentId = part.parents('.addInstrumentHtml').attr('data-id');
                        var deleteFile = {  // 获取删除的文件信息
                            dep_path: part.find('input[name=dep_path]').val(),
                            save_name: part.find('input[name=save_name]').val(),
                            real_name: part.find('input[name=file_name]').val()
                        };
                        part.remove(); // 删除前端
                        $('.pop_modal').modal('hide');
                        var filesArray = []; // 获取删除后剩余的文件，用于数据库覆盖
                        instrumentFiles.find('.single_detail_picture').each(function(){
                            var allFileData = $.formSerializeFn($(this));
                            var fileData = {
                                dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                                save_name: allFileData.save_name ? allFileData.save_name : '',
                                real_name: allFileData.file_name ? allFileData.file_name : ''
                            };
                            filesArray.push(fileData)
                        });


                        window.noLoadTip = false;


                        $.ajaxFn({
                            url: ELN_URL + '?r=instrument/delete-instrument-picture',
                            data: {
                                deleteReason: deleteReason,//  删除原因
                                instrumentId: instrumentId, // 仪器id
                                currentFiles: JSON.stringify(filesArray), //删除的文件
                                deleteFile: deleteFile, //删除的文件
                            },
                            success: function (data) {
                                if (data.status === 1) {
                                    $.showAlert(mainLang('del_success'));
                                }
                            },
                        })
                    });

                }else{
                    $.showContent('warning', mainLang('del_tips'), mainLang('del_file'), function() {
                        var part = box.parents('.single_detail_picture');
                        part.remove();
                        $('.pop_modal').modal('hide');
                        window.noLoadTip = false;
                    });
                }
            });

            // add by hkk 2019/11/11 批量上传提交
            $("body").on('click', '.submit-instrument-batchAdd', function (event) {
                var data = $.formSerializeFn($(".upload_form_for_instrument_batchAdd"));
                if( !data['file_name']){
                    $.showAlert(mainLang('please_upload_file'));
                    return
                }
                $.loading();
                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/submit-batch-add',
                    data: data,
                    success: function (data) {
                        if (1 == data.status) {


                            $.closeLoading();
                            $(".instrument_page").remove();
                            $('body').removeClass('modal-open');
                            module.instrumentsManage(false);

                            // add by hkk 2021/7/28 更改导入成功提示
                            var popHtml = '<div style="margin-left: 10px;"><div>'+ mainLang('batch_add_tip') + ":" + data.data.successNumber + '</div>';
                            popHtml += '<div>'+ mainLang('batch_add_tip3') + ":"+ Object.keys(data.data.info).length + '</div>';
                            popHtml += '<div style="display: inline-block">'+ mainLang('batch_add_tip2') + ":" + '</div>';
                            popHtml += '<div style="display: inline-block;vertical-align: top">';
                            if (data.data && Object.keys(data.data.info).length > 0) {
                                Object.keys(data.data.info).forEach(function (index, item) {
                                    popHtml += '<p> Row ' + index + ' ' + data.data.info[index] + '</p>';
                                })
                            }
                            popHtml += '</div></div>';
                            $.popContent(popHtml, mainLang('batch_add_title'), function () {
                                $.closeModal();
                            });




                        }
                    }
                });
            });

            // add by hkk 2020/4/24 仪器提醒设置 -> 添加时间点
            $('body').on('click', '.instrument_reminder_setting .add-tp', function(){
                var tpHtml = '<span class="relative iblock fl mr20 tp-box">'+
                    '<input type="text" class="datetimepicker time_point new" style="width: 150px">'+
                    '<i class="date-picker-ico font-ico"></i>'+
                    '<span class="del-tp"></span>'+
                    '</span>';
                $(tpHtml).insertBefore($(this));
                $('.datetimepicker.new').datetimepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    minView: 2,
                    clearBtn: true,
                });
                $('.datetimepicker.new').removeClass('new');
            });

            // add by hkk 2020/4/24 仪器提醒设置 -> 删除时间点
            $('body').on('click', '.instrument_reminder_setting .del-tp', function(){
                $(this).parent().remove();
            });

            // add by hkk 2020/4/24 仪器提醒设置 -> 周期和时间点二选一
            $('body').on('click', '.instrument_reminder_setting .add-instrument-reminder:visible .beauty-checkbox-big', function(){
                var name =$(this).attr('name');
                if($(this).prop('checked')===true){
                    if($(this).hasClass('time_point_reminder')){
                        $('.period_reminder[name = '+name+']').prop('checked',false)
                    }else{
                        $('.time_point_reminder[name = '+name+']').prop('checked',false)
                    }
                }
            });

            //add by zwm 2023/4/21 仪器提醒设置，添加一组新的提醒
            $('body').on('click', '.instrument_reminder_setting .add-reminder', function(){
                var html = $('.instrument_reminder_setting .instrument-reminder-hide .add-instrument-reminder').clone();
                $('.instrument_reminder_setting .modal-body').append(html);
                var new_reminder = $('.add-instrument-reminder:visible:last-child');
                new_reminder.find('.datetimepicker.new').datetimepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    minView: 2,
                    clearBtn: true,
                });
                new_reminder.find('.datetimepicker.new').removeClass('new');
                new_reminder.find('.beauty-checkbox-big').attr('name','reminder_type'+$('.add-instrument-reminder').length);
                new_reminder.find('.period_reminder').attr('id','period_reminder'+$('.add-instrument-reminder').length);
                new_reminder.find('.period_reminder+label').attr('for','period_reminder'+$('.add-instrument-reminder').length);
                new_reminder.find('.time_point_reminder').attr('id','time_point_reminder'+$('.add-instrument-reminder').length);
                new_reminder.find('.time_point_reminder+label').attr('for','time_point_reminder'+$('.add-instrument-reminder').length);
                new_reminder.find('.add_to_schedule_checkbox').attr('id','add_to_schedule'+$('.add-instrument-reminder').length);
                new_reminder.find('.add_to_schedule_checkbox+label').attr('for','add_to_schedule'+$('.add-instrument-reminder').length);

            });

            //add by zwm 2023/4/21 仪器提醒设置，删除当前组的提醒
            $('body').on('click', '.instrument_reminder_setting .del-reminder', function(){
                $(this).closest('.add-instrument-reminder').remove();
            });

            $('body').on('click', '.collaboration_page .single_detail_file .del_file', function(event, params) {
                var box = $(this);
                $.showContent('warning', mainLang('del_tips'), mainLang('del_file'), function() {
                    var part = box.parents('.single_detail_file');
                    part.remove();
                    $('.pop_modal').modal('hide');
                    window.noLoadTip = false;
                });
            });

            $('body').on('click', '.send_time_box .add-node-ico', function () {
                var max = $('#max').val();
                var next = parseInt(max) + 1;
                $('.send_time_box').append('<div class="send_time">  <input type="text" class="angle_input time_input datetimepicker iblock send_time_x" style="width:150px"  autocomplete="off" name="send_time_' + next + '" id="send_time_' + next + '" maxlength="20" value=""  >                                        <i class="date-picker-ico font-ico"></i> <i class="font-ico del-node-ico"></i></div> ');
                $('#max').val(next);
                var dateOpts = {
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    minView: 2,
                    clearBtn: true,
                };

                $.fn.datetimepicker ? $('.send_time_box .datetimepicker').datetimepicker(dateOpts).on('click', function () {
                    $('.send_time_box .send_time_x').datetimepicker();
                }) : '';
            });

            // 企业通用设置勾选切换，包括搜索范围 痕迹设置等
            $("body").on('click', '.company_general_setting input.general_setting', function (event) {

                $.ajaxFn({
                    url: ELN_URL + '/?r=company-setting/set-general-setting',
                    data: {
                        key: $(this).attr('name'),
                        value: $(this).prop('checked') ? 1 : 0
                    },
                    success: function(res) {
                        if(res.status == 1) {
                            $.showAlert(jsLang['success']);
                        }
                    }
                });
            });

            // 企业通用设置勾选切换，允许创建者移入到回收站
            $("body").on('click', '.company_general_setting input.allow_creater_move_book_to_recycle', function (event) {

                $.ajaxFn({
                    url: ELN_URL + '/?r=company-setting/set-general-setting',
                    data: {
                        key: $(this).attr('name'),
                        value: $(this).prop('checked') ? 1 : 0
                    },
                    success: function(res) {
                        if(res.status == 1) {
                            $.showAlert(jsLang['success']);
                        }
                    }
                });
            });

            // 企业通用设置勾选切换，按权限分配
            $("body").on('click', '.company_general_setting input.empower_by_auth', function (event) {

                $.ajaxFn({
                    url: ELN_URL + '/?r=company-setting/set-general-setting',
                    data: {
                        key: $(this).attr('name'),
                        value: $(this).prop('checked') ? 1 : 0
                    },
                    success: function(res) {
                        if(res.status == 1) {
                            $.showAlert(jsLang['success']);
                        }
                    }
                });
            });

            // 企业通用设置勾选切换，按权限分配
            $("body").on('click', '.company_general_setting input.auth-setting', function (event) {
                $('#EMPOWER_BY_AUTH').trigger('click');
            });

            // 企业通用设置勾选切换，按权限分配
            $("body").on('click', '.company_general_setting input.empower_by_auth', function (event) {
                if($(this).prop('checked')){
                    $('#AUTH_SETTING_ALL').prop('checked',true);
                }
                else{
                    $('#AUTH_SETTING_ALL').prop('checked',false);
                }

                $.ajaxFn({
                    url: ELN_URL + '/?r=company-setting/set-general-setting',
                    data: {
                        key: $(this).attr('name'),
                        value: $(this).prop('checked') ? 1 : 0
                    },
                    success: function(res) {
                        if(res.status == 1) {
                            $.showAlert(jsLang['success']);
                        }
                    }
                });
            });

            //add by wy 2023/4/21 群内权限-鹰群下拉框点击事件
            $('body').on('click', '.ellipsisBox .fs-option', function (event){
                var choosedId = $(this).attr('data-value');
                var visLis = $('.outTopBox ul li:visible');
                var lastLiId = $(visLis).eq(showUlLength - 1).attr('data-id');
                $(lis).each(function(i , item) {
                    if ($(item).attr('data-id') == choosedId) {
                        module.changeSelectionGroup($(item), lastLiId, choosedId);
                    }
                })
            })

            // 企业通用设置勾选切换，允许领导查看下属实验
            $("body").on('click', '.company_general_setting input.allow_leader_view_exp', function (eve) {
                /* 由于input checkbox的点击后checked状态变化目前无法通过preventDefault()阻止，因此手动修改状态，保持在用户二次确认弹框后才改变checkbox的显示 */
                var nowState = $(this).prop('checked');   //按钮当前状态,nowState是被点击后的状态
                var originState = !$(this).prop('checked');   //originState按钮被点击之前的状态
                $(this).prop('checked', originState);  //让按钮先保持未点击前的状态

                var checkObj = $(this);
                var alertCnt = !originState ? mainLang('allow_leader_view_alert') : mainLang('cancel_leader_view_alert');

                var alertHtml = `<label style="padding-left: 30px;">${alertCnt}</label>`;
                //确认选项的弹框
                $.popContent(alertHtml,mainLang('tip'),
                    function () {   //点击ok执行的动作
                        var val = 0;
                        if(checkObj.val()==1){
                            val = !checkObj.prop('checked') ? 1 : 0
                            //$('#ALLOW_LEADER_VIEW_EXP_2').prop('checked')
                            $("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
                        }
                        if(checkObj.val()==2){
                            val = !checkObj.prop('checked') ? 2 : 0
                            $('#ALLOW_LEADER_VIEW_EXP_1').prop('checked',false)
                            //$("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
                        }
                        if(checkObj.val()==3){
                            val = !checkObj.prop('checked') ? 3 : 0
                            $('#ALLOW_LEADER_VIEW_EXP_1').prop('checked',false)
                            $("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
                            //$("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
                        }
                        if(checkObj.val()==4){
                            val = !checkObj.prop('checked') ? 4 : 0
                            $('#ALLOW_LEADER_VIEW_EXP_2').prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
                            $("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
                            //$("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
                        }
                        $.ajaxFn({
                            url: ELN_URL + '/?r=company-setting/set-general-setting',
                            data: {
                                //key: $(this).attr('name'),
                                key: 'ALLOW_LEADER_VIEW_EXP',
                                value: val
                            },
                            success: function(res) {
                                if(res.status == 1) {
                                    $.showAlert(jsLang['success']);
                                    checkObj.prop('checked', nowState);
                                    $('.pop_modal').modal('hide');
                                }
                            }
                        })
                    }
                    ,null,null);
            });


            // function () {   //点击ok执行的动作
            //
            //     var val = 0;
            //     if($(this).val()==1){
            //         val = $(this).prop('checked') ? 1 : 0
            //         //$('#ALLOW_LEADER_VIEW_EXP_2').prop('checked')
            //         $("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
            //     }
            //     if($(this).val()==2){
            //         val = $(this).prop('checked') ? 2 : 0
            //         $('#ALLOW_LEADER_VIEW_EXP_1').prop('checked',false)
            //         //$("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
            //     }
            //     if($(this).val()==3){
            //         val = $(this).prop('checked') ? 3 : 0
            //         $('#ALLOW_LEADER_VIEW_EXP_1').prop('checked',false)
            //         $("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
            //         //$("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
            //     }
            //     if($(this).val()==4){
            //         val = $(this).prop('checked') ? 4 : 0
            //         $('#ALLOW_LEADER_VIEW_EXP_2').prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_2").prop('checked',false);
            //         $("#ALLOW_LEADER_VIEW_EXP_3").prop('checked',false);
            //         //$("#ALLOW_LEADER_VIEW_EXP_4").prop('checked',false);
            //     }
            //     $.ajaxFn({
            //         url: ELN_URL + '/?r=company-setting/set-general-setting',
            //         data: {
            //             //key: $(this).attr('name'),
            //             key: 'ALLOW_LEADER_VIEW_EXP',
            //             value: val
            //         },
            //         success: function(res) {
            //             if(res.status == 1) {
            //                 $.showAlert(jsLang['success']);
            //
            //                 $('.pop_modal').modal('hide');
            //             }
            //         }
            //     });
            // }

            // 企业通用设置勾选切换，工单设置
            $("body").on('click', '.company_general_setting .collaboration_setting', function (event) {
                var create_by_me = $('.company_general_setting .create_by_me').prop('checked') ? 1 : 0;
                var send_to_me = $('.company_general_setting .send_to_me').prop('checked') ? 1 : 0
                var cc_to_me = $('.company_general_setting .cc_to_me').prop('checked') ? 1 : 0
                var my_group = $('.company_general_setting .my_group').prop('checked') ? 1 : 0
                var send_to_group_member = $('.company_general_setting .send_to_group_member').prop('checked') ? 1 : 0
                var send_to_department_member = $('.company_general_setting .send_to_department_member').prop('checked') ? 1 : 0

                $.ajaxFn({
                    url: ELN_URL + '/?r=company-setting/set-collaboration-setting',
                    data: {
                        create_by_me: create_by_me,
                        send_to_me: send_to_me,
                        cc_to_me: cc_to_me,
                        my_group: my_group,
                        send_to_group_member: send_to_group_member,
                        send_to_department_member:send_to_department_member,
                    },
                    success: function(res) {
                        if(res.status == 1) {
                            $.showAlert(jsLang['success']);
                        }
                    }
                });
            });

            // 群通用设置——审核设置事件处理 begin
            $('body').on('click', '.approval-setting-tabs .tab-item', function () {
                var $tab = $(this);
                if ($tab.hasClass('on')) {
                    return;
                }
                var $modal = $('.approval-setting-group');
                $('.tab-item', $modal).removeClass('on');
                $('.tab-content', $modal).addClass('hide');
                $tab.addClass('on');
                $('.' + $tab.data('type') + '-setting-box', $modal).removeClass('hide');
            });
            // 群通用设置——审核设置事件处理 end

            // 群成员权限——其他设置事件处理 begin
            $('body').on('click', '#allow_cbg', function () {
                var box =  $(this).closest('.other-setting');
                if ($(this).is(':checked')) {
                    box.find('.cbg-approval-container').removeClass('disabled');
                } else {
                    $('#cbg_need_approval').prop('checked',false);
                    box.find('.cbg-approval-container').addClass('disabled');
                }
            });
            $('body').on('click', '#cbg_need_approval', function () {
                if ($(this).is(':checked')) {
                    $('#approval_selector_container').removeClass('disabled');
                } else {
                    $('#approval_selector_container').addClass('disabled');
                }
            });
            $('body').on('click', '#allow_proxy', function () {
                var $box =  $(this).closest('.other-setting');
                if ($(this).is(':checked')) {
                    $box.find('.proxy-container').removeClass('disabled');
                } else {
                    $box.find('.proxy-container').addClass('disabled');
                }
            });
            $('body').on('click', '#allow_dfa', function () {
                var $box =  $(this).closest('.dfa-setting');
                if ($(this).is(':checked')) {
                    $box.find('.dfa-user-container').removeClass('disabled');
                } else {
                    $box.find('.dfa-user-container').addClass('disabled');
                }
            });
            // 群成员权限——其他设置事件处理 end

            require(['js/bin/jquery-image-cut/dist/cropper.js']);
        },

        //打开页面时调用。
        open: function (dom, group_id) {
            module.dom = dom;
            module.type = dom.attr('data-type') || 1;

            module.clearState();
            module.upData.group_id = group_id;
            module.default_page_size = null;
            if (module.type == 1) {
                //module.expTemplateList();
                require('get_html').genGroupUserAuthorityPage(); // modified by hkk 2020/6/30
            }
            if (module.type == 2) {
                //module.compmanySettingAuth();
                require('get_html').genGroupMasterAuthorityPage(); // modified by hkk 2020/6/30
            }
            if (module.type == 21) {
                module.compmanySettingDecimal();
            }
            if (module.type == 3) {
                module.companyDict();
            }

            // add by hkk 2019/9/23 记录本管理
            if (module.type == 22) {
                require('book_manage').genNoteBookManagePage();
            }

            if (module.type == 25) {
                module.addsonmodule();
            }

            // 仪器库管理 add by hkk 2019/10/28 我的仪器库 add by hkk 2019/11/1
            if (module.type === 'instruments_manage' || module.type === 'my_instruments') {
                require('instrument').instrumentsManage(module.type); // modified by hkk 2020/6/28
            }

            // 搜索范围设置
            if (module.type === 'search_range_setting') {
                var url = ELN_URL + '/?r=company-setting/search-range';
                $.ajaxFn({
                    url: url,
                    data: undefined,
                    success: function(res) {
                        $('.exp_conetnt').removeClass('search');
                        $('.my_exp_list').removeClass('on');
                        $('.my_exp_detial ').removeClass('on');
                        $('.eln_setting_href').removeClass('on');
                        $('.eln_setting_href[data-type=' + type + ']').addClass('on');

                        var $targetTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + module.type + ']');
                        if ($targetTab.length > 0) {
                            $targetTab.addClass('on');
                        } else {
                            var tabTitle = dom.text();
                            $('.exp_title').append('<a class="iblock on my_exp_detial listmodule eln_setting_detail" data-type="' + module.type + '" data-default="1"  title="' + tabTitle + '" ><span class="name">' + tabTitle + '</span><span class="close"></span></a>');
                        }

                        $('.exp_data_box').html(res.data.html);
                        $('.tool_data_box').html('');

                        handleExpTitle();
                    }
                });
            }

            // add by hkk 2019/12/4 企业级管理通用设置
            if (module.type === 'general_setting') {
                require('get_html').genGeneralSettingPage();
            }

        },

        // add by hkk 2019/10/23 仪器库管理和我的仪器库界面
        instrumentsManage: function (needUpdateAllPage,pageTo) {
            var upData = {};
            if ($('.exp_title .tag.on').attr('data-func') === 'getInstrumentListContent') { // 仪器库管理
                upData.type = 'instruments_manage'
            } else { // 我的仪器库
                upData.type = 'my_instruments'
            }

            var url = ELN_URL + '/?r=instrument/manage';

            if (needUpdateAllPage) { // add by hkk 2019/10/30 用于判断是否更新全页面
                upData.needUpdateAllPage = 1;
            } else {
                upData.needUpdateAllPage = 0;
            }

            var callback = function (data) {
                if (1 == data.status) {

                    if(needUpdateAllPage){// add by hkk 2019/10/30 用于判断是否更新全页面
                        $('.exp_conetnt.active .exp_data_box').html(data.data.file);
                    }else{
                        $('.exp_conetnt.active .instruments_table').html(data.data.file);
                    }


                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }

            }
            upData.page = pageTo || 1; // add by hkk 2020/8/3

            upData.limit = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: upData,
                success: callback
            });
        },

        getInstrumentPage: function (instrumentId, type) {
            $.ajaxFn({
                url: ELN_URL + '?r=instrument/get-page',
                data: {
                    instrumentId:instrumentId,
                    type: type,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".instrument_page").remove();
                        $("body").append(data.data.file);

                        // bug 6126 plug/马应龙：单个添加/编辑仪器时，如果不小心把鼠标移出弹框，添加的内容就会都消失，使用不方便
                        if (type === "add" || type === "edit") {
                            $(".instrument_page").modal({backdrop: false});
                        } else {
                            $(".instrument_page").modal('show');
                        }

                        //预约界面调用日历插件
                        if(type === "book"){
                            var dateOpts = {
                                format: 'yyyy-mm-dd hh:ii',
                                autoclose: true,
                                minView: 0, // 0精确到分钟 2精确到小时
                                clearBtn: true,
                            };

                            if( $.fn.datetimepicker){
                                $('.instrument_book .datetimepicker').datetimepicker(dateOpts).on('click', function () {

                                    if ($('.instrument_book [name="end_time"]').val() != '') {
                                        var startTimer = $('.instrument_book [name="end_time"]').val() ;
                                        $('.instrument_book [name="start_time"]').datetimepicker('setEndDate', startTimer);
                                    }

                                    if ($('.instrument_book [name="start_time"]').val() != '') {
                                        var endTimer = $('.instrument_book [name="start_time"]').val();
                                        $('.instrument_book [name="end_time"]').datetimepicker('setStartDate', endTimer);
                                    }
                                })
                            }

                        }

                        if(type === "reminder"){
                            $('.instrument_reminder_setting .datetimepicker').datetimepicker({
                                format: 'yyyy-mm-dd',
                                autoclose: true,
                                minView: 2,
                                clearBtn: true,
                            });
                        }

                        if(type === "book" || type === "history"){
                            $('.instrument_book_page .modal-dialog').addClass('modal-dialog-840');// 调整弹出框的大小
                            $('.instrument_history_page .modal-dialog').addClass('modal-dialog-1045');// 调整弹出框的大小
                        }

                    }
                }
            });
        },

        // add by hkk 2019/11/4  我的仪器库-我的预约界面
        myInstrumentsBook: function (needUpdateAllPage,pageTo) {
            var type = this.type;
            var that = this;

            var upData = {};

            this.upData.type = type;

            var url = ELN_URL + '/?r=instrument/my-book';

            if(needUpdateAllPage){ // add by hkk 2019/10/30 用于判断是否更新全页面
                that.upData.needUpdateAllPage = 1;
            }else{
                that.upData.needUpdateAllPage = 0;
            }


            var callback = function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');
                    $('.my_exp_list').removeClass('on');
                    $('.my_exp_detial ').removeClass('on');
                    $('.eln_setting_href').removeClass('on');
                    $('.eln_setting_href[data-type=' + type + ']').addClass('on');
                    if (needUpdateAllPage) {// add by hkk 2019/10/30 用于判断是否更新全页面
                        $('.exp_conetnt.active .exp_data_box').html(data.data.file);
                    } else {
                        $('.exp_conetnt.active .instruments_book_table').html(data.data.file);
                    }
                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
            }

            this.upData.limit = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        // add by hkk 2019/11/1 切换我的仪器库标签页
        changeMyInstruments: function (btn) {
            //当前tab，不修改。
            $(".exp_conetnt.active .top-group-tabs li").removeClass('on');
            var tab_id = btn.attr('data-id');
            btn.addClass('on');
            this.clearState();
            if (tab_id == 1) {
                require('instrument').myInstrumentsTab(1); // modified by hkk 2020/6/28
               // this.instrumentsManage(true);// 不用更新整个页面
            }
            if (tab_id == 2) {
                require('instrument').myInstrumentsTab(2); // modified by hkk 2020/6/28
                //this.myInstrumentsBook(true);
            }

            if(tab_id == 3) {
                require('get_html').instrumentManagerTab(1);
            }

            if(tab_id == 4) {
                require('get_html').instrumentManagerTab(2);
            }
        },

        // 切换 inscada仪器对接汇总标签页
        changeInscadaSummary: function (btn) {
            //当前tab，不修改。
            $(".exp_conetnt.active .top-group-tabs li").removeClass('on');
            var tab_id = btn.attr('data-id');
            btn.addClass('on');
            var dataType = '';
            if(btn.hasClass('my-instruments')){
                dataType = 'my-instruments';
            }
            this.clearState();
            if (tab_id == 1) {
                var numerical_instrument_type = btn.attr('data-numerical-type');
                require('instrument').inscadaSummaryTab(1, dataType, numerical_instrument_type);
            }
            if (tab_id == 2) {
                require('instrument').inscadaSummaryTab(2, dataType);
            }
        },

        // add by hkk 2019/9/23 记录本管理
        bookNumberManage: function () {
            require('book_manage').getBookNumberManageContent().then(function (res) {
                if (res.status === 1) {
                    $('.exp_conetnt.active').html(res.data.contentHtml);
                }
            });
        },

        // add by hkk 2019/9/23 记录本管理，第一次打开渲染
        bookFileManage: function () {
            require('book_manage').getBookFileManageContent().then(function (res) {
                if (res.status === 1) {
                    $('.exp_conetnt.active').html(res.data.contentHtml);
                }
            });
        },

        //add by hkk 2019/9/23 提交记录本管理前缀信息
        submit_book_prefix: function (btn) {
            // 选择系统默认编号时bookPrefix为空,不需要进行前缀判断。
            var bookPrefix;
            if($('.exp_conetnt.active input[name="book_number_rule"]:checked').val() === "default_number" ){
                bookPrefix = '';
            }else{
                bookPrefix = $('.exp_conetnt.active .book_number_manage .book_prefix').val()=="" ? "null": $('.exp_conetnt.active .book_number_manage .book_prefix').val();
                //add by wy 2023/4/7 判断输入内容是否只为数字或字母
                //fix by hsl 2024/6/7 正则表达式改为黑名单匹配，不允许输入windows操作系统下文件名不允许的字符
                var testReg = /^[^\\/:*?"<>|]+$/;
                if (!testReg.test(bookPrefix)) {
                    $.showAlert(mainLang('book_number_rule_tip'));
                    return;
                }
            }
            var data = {
                bookPrefix:bookPrefix
            };


            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/book-number-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            })
        },

        //add by hkk 2019/9/24 切换记录本管理标签页
        changeBookManage: function (btn) {
            //当前tab，不修改。
            $(".exp_conetnt.active .top-group-tabs li").removeClass('on');
            var tab_id = btn.attr('data-id');
            btn.addClass('on');
            this.clearState();
            if (tab_id == 1) {
                this.bookNumberManage();
            }
            if (tab_id == 2) {
                this.bookFileManage();
            }
        },

        //add by lcy 2020/07/15 记录本迁移弹窗
        moveNoteBook: function (btn) {

            var book_id =  btn.attr('data-id');
            var group_id =  btn.attr('data-group-id');
            var user_id =  btn.attr('data-user_id');
            var book_name =  btn.attr('data-book_name');

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/move-note-book',
                type: 'post',
                data: {
                    book_id:book_id,
                    group_id:group_id,
                    user_id:user_id,
                    book_name:book_name,
                },
                success: function (res) {
                    if (res.status == 1) {
                       $('body').append(res.data.html);
                       //$('.move_note_book .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips'));
                       $('.move_note_book').modal('show');
                    }
                }
            });
        },

        moveNotebookSubmit: function (btn) {

            var book_id =  btn.attr('data-id');
            var book_name =  btn.attr('data-book_name');
            var user_id =  btn.attr('data-user_id');    //记录本创建人员
            var to_group =  $('.move_note_book #group_id').val();
            var from_group =  btn.attr('data-group-id');
            var operate_desc =  $('.move_note_book #reason').val(); //原因
            var master_id =  $('.move_note_book #group_id').find("option:selected").attr("master_id");
            var to_group_name =  $('.move_note_book #group_id').find("option:selected").html();

            if(to_group == 0){
                $.showAlert(mainLang('select_group'));
                return;
            }

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/move-note-book-submit',
                type: 'post',
                data: {
                    book_id: book_id,
                    to_group: to_group,
                    from_group: from_group,
                    operate_desc: operate_desc,
                    master_id: master_id,   ///移入群群主id
                    user_id: user_id,
                    to_group_name: to_group_name,
                    book_name: book_name,
                },
                success: function (res) {
                    if (res.status == 1) {
                        $.showAlert(mainLang('success'));
                        var filter = require('book_manage').getFilter();
                        if ($('.exp_conetnt.active .book_file_manage').length > 0) {    //如果打开了记录本管理页面，才刷新，防止报错
                            require('book_manage').refreshTable($('.exp_conetnt.active .pager-select:visible').val() || 15, 1, filter.group_ids, filter.user_ids, filter.book_status);
                        }
                        $('.move_note_book').modal('hide');
                    }
                }
            });
        },

        //请求页面数据。
        expTemplateListOld: function () {
            var type = this.type;
            var that = this;

            var upData = {};

            var topTxt = jsLang['group_setting'];
            var url = ELN_URL + '/?r=group-setting/group-user-list';

            var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');
            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            var callback = function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');
                    /*处理标题*/

                    $('.eln_setting_href').removeClass('on');
                    $('.eln_setting_href[data-type=' + type + ']').addClass('on');

                    // var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');
                    $('.exp_title').find('.iblock').removeClass('on');
                    if (currentTab.length > 0) {
                        currentTab.addClass('on');
                    } else {
                        $('.exp_title').append('<a class="iblock on my_exp_detial listmodule eln_setting_detail" data-type="' + type + '"  title="' + topTxt + '" ><span class="name">' + topTxt + '</span><span class="close"></span></a>');
                    }


                    $('.exp_data_box').html(data.data.file);
                    $('.tool_data_box').html('');
                    handleExpTitle();


                    //只在第一次请求的时候设置default_page_size;
                    if (!that.default_page_size) {
                        that.default_page_size = $(".page_box").attr('data-limit');
                    }
                    that.pageFn();

                    //$(window).scrollTop(0);

                    //对placeholder的兼容处理。
                    $('input[placeholder]:visible').placeholder();
                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
                $.each($('.isAdmin'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                $.each($('.isOpenAc'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });

            }
            this.upData.limit = $('.pager-select:visible').val() || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        // add by hkk 2020/6/30 新请求页面数据--群成员权限。切换标签页发到此处
        expTemplateList: function (btn, lastLiId='', choosedId='') {
            var type = this.type;
            var that = this;
            var upData = {};
            var url = ELN_URL + '/?r=group-setting/group-user-list';

            var callback = function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt.active').html(data.data.contentHtml); // 替换内容不换标签

                    //只在第一次请求的时候设置default_page_size;
                    if (!that.default_page_size) {
                        that.default_page_size = $(".exp_conetnt.active .page_box").attr('data-limit');
                    }
                    that.pageFn();
                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
                $.each($('.isAdmin'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                $.each($('.isOpenAc'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                if (choosedId !== '') {
                    $('.outTopBox ul li:visible').eq(showUlLength - 1).css('display','none');
                    $(lis).each(function(i , item) {
                        if ($(item).attr('data-id') == choosedId) {
                            $(item).css('display','inline-block');
                        }
                    })
                    $(options).each(function (i, item) {
                        if ($(item).attr('data-value') == choosedId) {
                            $(item).css('display','none');
                        }
                        if ($(item).attr('data-value') == lastLiId) {
                            $(item).css('display','block');
                        }
                    })
                    if ($(lis).eq(showUlLength - 1).css('display') == 'none') {
                        $(options).eq(showUlLength - 1).css('display', 'block');
                    }
                }

                var selector = '.fs_select_group_name'
                $(selector).fSelect({
                    placeholder: mainLang('select'),
                    numDisplayed: 3,
                    overflowText: '{n} selected',
                    noResultsText: mainLang('no_search_result'),
                    searchText: mainLang('search'),
                    showSearch: true
                });

                //fs-wrap 调整样式
                $(selector).parents('.fs-wrap').css('width', '180px');
                $(selector).parents('.fs-wrap').css('line-height', '1.2');
                $(selector).parents('.fs-wrap').css('height', '28px');
                $(selector).parents('.fs-wrap').css('margin-top', '2px');
                $(selector).parents('.fs-wrap').css('vertical-align', 'top');
            }
            this.upData.limit = getPageLimit('getGroupUserAuthorityContent') || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        //请求页面数据。
        compmanySettingAuth: function () {
            var type = this.type;
            var that = this;

            var upData = {};

            var topTxt = jsLang['company_setting'];
            var url = ELN_URL + '/?r=company-setting/company-setting-auth';
            $('.exp_title iblock').removeClass('on');
            var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');


            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            var callback = function (data) {
                if (1 == data.status) {
                    $('.my_exp_list').removeClass('on');
                    $('.my_exp_detial ').removeClass('on');

                    $('.exp_conetnt').removeClass('search');
                    /*处理标题*/
                    $('.eln_setting_href').removeClass('on');
                    $('.eln_setting_href[data-type=' + type + ']').addClass('on');


                    // var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');

                    if (currentTab.length > 0) {
                        currentTab.addClass('on');
                    } else {
                        $('.exp_title').append('<a class="iblock on my_exp_detial listmodule eln_setting_detail" data-type="' + type + '"  title="' + topTxt + '" ><span class="name">' + topTxt + '</span><span class="close"></span></a>');
                    }


                    $('.exp_data_box').html(data.data.file);
                    $('.tool_data_box').html('');
                    handleExpTitle();


                    //只在第一次请求的时候设置default_page_size;
                    if (!that.default_page_size) {
                        that.default_page_size = $(".page_box").attr('data-limit');
                    }
                    that.pageFn();

                    //$(window).scrollTop(0);

                    //对placeholder的兼容处理。
                    $('input[placeholder]:visible').placeholder();

                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
                $.each($('.isAdmin'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                $.each($('.isOpenAc'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
            }
            this.upData.limit = $('.pager-select:visible').val() || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        //小数点设置。
        compmanySettingDecimal: function () {
            require('get_html').genDecimalSettingPage();
        },

        //使用统计。
        compmanySettingUseStatic: function () {
            var type = this.type;
            var that = this;

            var upData = {};

            var topTxt = jsLang['company_setting'];
            var url = ELN_URL + '/?r=company-setting/company-setting-auth';

            var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');
            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            var callback = function (data) {
                if (1 == data.status) {

                    $('.exp_conetnt').removeClass('search');
                    /*处理标题*/
                    $('.eln_setting_href').removeClass('on');
                    $('.eln_setting_href[data-type=' + type + ']').addClass('on');

                    // var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');

                    if (currentTab.length > 0) {
                        currentTab.addClass('on');
                    } else {
                        $('.exp_title').append('<a class="iblock on my_exp_detial listmodule eln_setting_detail" data-type="' + type + '"  title="' + topTxt + '" ><span class="name">' + topTxt + '</span><span class="close"></span></a>');
                    }


                    $('.exp_data_box').html(data.data.file);
                    $('.tool_data_box').html('');
                    handleExpTitle();


                    //只在第一次请求的时候设置default_page_size;
                    if (!that.default_page_size) {
                        that.default_page_size = $(".page_box").attr('data-limit');
                    }
                    that.pageFn();

                    //$(window).scrollTop(0);

                    //对placeholder的兼容处理。
                    $('input[placeholder]:visible').placeholder();

                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
                $.each($('.isAdmin'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                $.each($('.isOpenAc'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });

            }
            this.upData.limit = $('.pager-select:visible').val() || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        //登录统计。
        compmanySettingLoginStatic: function () {
            var type = this.type;
            var that = this;

            var upData = {};

            var topTxt = jsLang['company_setting'];
            var url = ELN_URL + '/?r=company-setting/company-setting-auth';

            var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');
            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            var callback = function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');
                    /*处理标题*/
                    $('.eln_setting_href').removeClass('on');
                    $('.eln_setting_href[data-type=' + type + ']').addClass('on');

                    // var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');

                    if (currentTab.length > 0) {
                        currentTab.addClass('on');
                    } else {
                        $('.exp_title').append('<a class="iblock on my_exp_detial listmodule eln_setting_detail" data-type="' + type + '"  title="' + topTxt + '" ><span class="name">' + topTxt + '</span><span class="close"></span></a>');
                    }


                    $('.exp_data_box').html(data.data.file);
                    $('.tool_data_box').html('');

                    handleExpTitle();


                    //只在第一次请求的时候设置default_page_size;
                    if (!that.default_page_size) {
                        that.default_page_size = $(".page_box").attr('data-limit');
                    }
                    that.pageFn();
                    //$(window).scrollTop(0);

                    //对placeholder的兼容处理。
                    $('input[placeholder]:visible').placeholder();
                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
                $.each($('.isAdmin'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                $.each($('.isOpenAc'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
            }
            this.upData.limit = $('.pager-select:visible').val() || this.default_page_size || undefined;

            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        structdata_setting_action: function (btn) {
            var postData = $("#structdata_setting").serialize();
            var temp_id = $('.exp_conetnt.active #temp_id').val();
            var chk_value = [];
            $.each($('input:checkbox'), function () {
                if (this.checked) {
                    chk_value.push($(this).val());
                }
            });

            $.ajaxFn({
                url: ELN_URL + '?r=template/set-structdata-action',
                data: {
                    temp_id: temp_id,
                    chk_value: chk_value,
                },
                success: function (data) {
                    if (data.status == 1) {
                        // 设置必填项后更新模板的必填项
                        $('.exp_conetnt.active .exp_info').find('.added_define_item_part').remove();

                        // 把设置的自定义项重新写到实验头部
                        data.data.define_item.forEach((item,index,array)=>{

                            var toAddHtml = '<div class="info_part iblock added_define_item_part">' +
                                '<div class="fl"><label class="define_item_label body_left"><span style="color:#999"></span>' + item.title + '：</label></div>'+
                                '<div class="fl">' +
                                '<input type="text" autocomplete="off" data-id="'+ item.dict_id +'" data-is_require="'+ item.is_require +'" data-is_struct="'+ item.is_struct +'" value="' + item.value + '" name="title" data-title="' + item.title + '" />' +
                                '</div>'+
                                '</div> '

                            $('.exp_conetnt.active .exp_info .set_define_item').parent().before(toAddHtml);
                        });
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            })
        },

        //设置实验核查人员
        add_input: function (btn) {
            console.log($(".exp-no-input-box").length);
            if ($(".exp_conetnt.active .exp-no-input-box").length > 0) {
                var html = '<div class="arrow-box fl ">-</div><div class="exp-no-input-box fl "><input autocomplete="off" type="text" name="exp_num" class="exp-no-input" placeholder="' + jsLang['input_exp_num'] + '"/><span class="eln_setting_btn del prev-del" method="del_input"></span></div> ';
            }
            else {
                var html = '<div class="exp-no-input-box fl "><input autocomplete="off" type="text" name="exp_num" class="exp-no-input" placeholder="' + jsLang['input_exp_num'] + '"/><span class="eln_setting_btn del prev-del" method="del_input"></span></div> ';
            }

            $(".exp_conetnt.active .exp_num").append(html);
        },

         // 新的导出结构化数据项 add by hkk 2021/3/12
        export_struct_data_csv: function (btn) {


            // 获取标题数据
            var titleArray = [];
            $('.exp_conetnt.active .structure_data_table th').each(function (index, item) {
                if ($(item).is(':visible')) {
                    titleArray.push($(item).text().trim())
                }
            });

            // 获取内容数据
            var dataArray = [];
            $('.exp_conetnt.active .structure_data_table tr.structure-data').each(function (index, dom) {
                var tr = [];
                $(dom).find('td').each(function (index2, dom2) {
                    if ($(dom2).is(':visible')) {
                        tr.push($(dom2).text().trim());
                    }
                });
                dataArray.push(tr);
            });

            $.ajaxFn({
                url: '?r=group-setting/export-structure-data-csv',
                data: {
                    titleArray: titleArray,
                    dataArray: dataArray,
                },
                success: function (data) {
                    if (1 === data.status) {
                        window.location.href = ELN_URL + "?r=download/file&path=&name=structureData.xlsx&file_name=structureData.xlsx";
                    }
                }
            })
        },

        //生成统计数据列表
        remove_struct_data_filed: function (btn) {
            //获取ID
            var id = $(btn).attr('data-id');
            //根据ID判断是否选中
            var is_show = $('.exp_conetnt.active #' + id).prop('checked') ? 1 : 0;
            if (is_show == 1) {
                $('.exp_conetnt.active .' + id).show();
            } else {
                $('.exp_conetnt.active .' + id).hide();
            }
        },

        del_input: function (btn) {
            if (btn.parent().prev().hasClass('arrow-box')) {
                btn.parent().prev().remove();
            }
            btn.parent().remove();
        },

        //请求页面数据。
        companyDict: function () {
            require('get_html').genEnterpriseThesaurusPage();
        },

        // 新建模板 走tool.js 里
        addsonmodule: function () {
            var type = 25;
            var that = this;

            var upData = {};

            var topTxt = jsLang['add_son_module'];
            var url = ELN_URL + '/?r=template/add-son-temp';

            var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');
            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            var callback = function (data) {
                if (1 == data.status) {
                    $('.exp_conetnt').removeClass('search');
                    /*处理标题*/
                    $('.my_exp_list').removeClass('on');
                    $('.my_exp_detial ').removeClass('on');
                    $('.eln_setting_href').removeClass('on');
                    $('.eln_setting_href[data-type=' + type + ']').addClass('on');

                    // var currentTab = $('.my_exp_detial.listmodule.eln_setting_detail[data-type=' + type + ']');

                    if (currentTab.length > 0) {
                        currentTab.addClass('on');
                    } else {
                        $('.exp_title').append('<a class="iblock on my_exp_detial listmodule eln_setting_detail" data-type="' + type + '"  title="' + topTxt + '" ><span class="name">' + topTxt + '</span><span class="close"></span></a>');
                    }

                    $('.exp_data_box').html(data.data.file);
                    $('.tool_data_box').html('');


                    // add by hkk 2019/11/19 实例化方法模板编辑器
                    var ue_add_son_temp = UE.getEditor('add_son_temp');
                    $('#add_son_temp .edui-editor-iframeholder').height(500);
                    ue_add_son_temp.addListener('focus', function () {
                        var parentWindow = window.parent.window;
                        if(parentWindow.$('.modul_line.chendraw').length == 1) {
                            parentWindow.materielFn.showMateriel()
                        }
                        if(parentWindow.$('.modul_line.tlc').length == 1) {
                            parentWindow.tlcUp2Down()
                        }
                    });
                    ue_add_son_temp.addListener('beforefullscreenchange',function(event,isFullScreen){
                        if(isFullScreen){
                            $('.top_nav').removeClass("fixed");
                            $('.layout_left').removeClass("fixed");
                            $('.tool_data_box').css({"z-index":"1","position":"absolute"});
                            //$('.create_exp_btn').css({"z-index":"1","position":"absolute"});
                            $('.tool_nav').css("z-index:1");
                            // $('.exp_title').css({"z-index":"1","position":"absolute"});
                            $('footer').css({"display":"none"});
                            $('.modul_head').css({"display":"none"});
                            $('.layout_left').hide();
                            $('.border_all').css({"border":"0px solid #eaebee"});
                            $('#layout_left_bar').hide();
                        } else {
                            $('.top_nav').addClass("fixed");
                            $('.layout_left').addClass("fixed");
                            $('.tool_data_box').css({"z-index":"91","position":"fixed"});
                            //$('.create_exp_btn').css({"z-index":"101","position":"fixed"});
                            $('.tool_nav').css("z-index:101");
                            // $('.exp_title').css({"z-index":"91","position":"fixed"});
                            $('footer').css({"display":""});
                            $('.modul_head').css({"display":""});
                            $('.border_all').css({"border":"1px solid #eaebee"});
                            $('.layout_left').show();
                            $('#layout_left_bar').show();
                            //$('.exp_title').addClass("fixed");
                        }
                    });
                    ue_add_son_temp.addListener("ready", function () {
                        // editor准备好之后才可以使用
                        window.noLoadTip = false;
                        var old_contene_add_son_temp = ue_add_son_temp.getContent();
                        ue_add_son_temp.addListener("contentChange",function(){
                            var new_contene_add_son_temp = ue_add_son_temp.getContent();
                            if(old_contene_add_son_temp != new_contene_add_son_temp) {
                                window.noLoadTip = false;
                                console.log('修改');
                            }
                        });
                    });


                    //父窗口拉伸窗口的事件处理。
                    ue_add_son_temp.addListener('mousemove',function(event){
                        console.log(1);
                        parent.$(parent.window.document).trigger('mousemove.longEditor',event);
                    });
                    ue_add_son_temp.addListener('mouseup',function(event){
                        parent.$(parent.window.document).trigger('mouseup.longEditor',event);
                    });

                    handleExpTitle();

                    //$(window).scrollTop(0);
                    //对placeholder的兼容处理。
                    $('input[placeholder]:visible').placeholder();


                } else {
                    if (data.data == "no_rights") {
                        window.location.reload();
                    }
                }
                $.each($('.isAdmin'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
                $.each($('.isOpenAc'), function (index, val) {
                    if ($(this).hasClass('group_master')) {
                        $(this).addClass('disabled');
                    }
                });
            }



            $.ajaxFn({
                url: url,
                data: this.upData,
                success: callback
            });
        },

        //事件绑定
        bindEvent: function () {
            var that = this;
            var obj = this;
            //事件委托
            $('body').on('click', '.eln_setting_btn', function () {
                var method = $(this).attr('method');
                obj[method] ? obj[method]($(this)) : '';
            });

            $('body').on('click', '.eln_setting_btn_switch', function () {
                var method = $(this).attr('method');
                obj[method] ? obj[method]($(this)) : '';
            });

            //模板列表 标签显示
            $('body').on('click', '.my_exp_detial.listmodule.eln_setting_detail', function (event) {
                var theDom = $(this);

                saveExperiment(function () {
                    var obj = event.srcElement ? event.srcElement : event.target;
                    if ($(obj).hasClass('close')) {
                        return;
                    }
                    var groupId = that.getTabGroup();
                    that.open(theDom, groupId);
                });
            });

            //点击鹰群，重新加载页面
            $("body").on('click', '.print-group-tab:not(.on)', function (event) {
                var groupId = $(this).attr('data-id');
                that.open($(this), groupId);
            });

            //中间状态的checkbox。点一下，把它的状态改成选中。并去掉some。 ls20170922
            $("body").on('click', '.beauty-checkbox-big.some', function (event) {
                var self = $(this);
                setTimeout(function () {
                    self.prop('checked', true)
                        .removeClass('some');
                }, 0)
            });

        },
        //设置tab标签。将本页的groupId放到tab上。用于从别的便签跳回来的情况。
        setTabGroup: function () {
            var groupId = $(".print-group-tab.on").attr('data-id');
            $(".eln_setting_detail.on").attr('group_id', groupId);
        },
        //从tab标签中获取group_id
        getTabGroup: function () {
            var group_id = $(".eln_setting_detail[data-type=" + this.type + "]").attr('group_id') || '';
            return group_id;
        },
        // 是否开通应用
        openAc: function (btn) {
            var arr = [];
            var that = this;
            arr.push(btn.parents("td").find(".member_id").val());
            if (btn.hasClass('mui-active')) {
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/undispatch-app',
                    data: {
                        member_ids: arr
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            btn.removeClass('mui-active');

                            if (btn.parents("tr").find(".isAdmin.mui-active").length > 0) {
                                that.adminRole(btn.parents("tr").find(".isAdmin"), true);
                            }
                            setTimeout(function () {
                                that.expTemplateList();
                            }, 100)
                            if ($(".exp_conetnt.active #whoAmI").val() == btn.parents("td").find(".eln_userId").val()) {
                                if ($(".exp_conetnt.active .top-group-tabs li").length > 1) {
                                    setTimeout(function () {
                                        that.expTemplateList();
                                        $(".exp_conetnt.active .top-group-tabs li:first").trigger("click");
                                    }, 300)
                                } else {
                                    window.location.reload();
                                }
                            }
                        }
                    }
                })
            } else {
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/dispatch-app',
                    data: {
                        member_ids: arr
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            btn.addClass('mui-active');
                            setTimeout(function () {
                                that.expTemplateList();
                            }, 100)
                        }
                    }
                })
            }
        },
        // 是否设为管理员
        adminRole: function (btn) {
            var that = this;
            if (btn.hasClass('mui-active')) {
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/set-admin',
                    data: {
                        group_id: btn.parents("td").find(".eln_groupId").val(),
                        user_id: btn.parents("td").find(".eln_userId").val(),
                        type: 0,
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            $.showAlert(mainLang('success'));
                            btn.removeClass('mui-active');
                            that.expTemplateList();
                        } else {
                            if (data.data == "no_rights") {
                                window.location.reload();
                            }
                        }
                    }
                })
            } else {
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/set-admin',
                    data: {
                        group_id: btn.parents("td").find(".eln_groupId").val(),
                        user_id: btn.parents("td").find(".eln_userId").val(),
                        type: 1,
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            $.showAlert(mainLang('success'));
                            btn.addClass('mui-active');
                            setTimeout(function () {
                                that.expTemplateList();
                            }, 100)
                        }
                    }
                })
            }
        },

        openAllEln: function (btn) {
            var that = this;
            // var arr = []; // 所有的 member_id 数组
            // var arr2 = []; // 获取选中 的 0,1 数组
            // var arr3 = []; // 被选中的 memberid 数组
            // var arr4 = []; // 获取 被选中的 所在tr 的序列

            var type = btn.attr('data-type');
            var url = ELN_URL + '?r=group-setting/dispatch-app';
            if ('close' == type) {
                url = ELN_URL + '?r=group-setting/undispatch-app';
            }
            // arr2 = this.is_checked_now(".exp_data_box td");
            // $.each($(".exp_data_box .checkboxBtn"), function() {
            // 	arr.push($(this).prev("input").val());
            // })
            // $.each(arr2, function(index, val) {
            // 	if (arr2[index] == 1) {
            // 		arr3.push(arr[index])
            // 	}
            // });
            // for (var i = 0; i < arr2.length; i++) {
            // 	if (arr2[i] == 1) {
            // 		arr4.push(i + 1);
            // 	}
            // }

            var mIdArr = [];
            $.each($(".exp_conetnt.active .exp_data_box .checkboxBtn:checked"), function () {
                var $openBtn = $(this).closest('tr').find('[method=openAc]');
                //过滤掉不能操作的。
                if (!$openBtn.hasClass('disabled')) {
                    mIdArr.push($(this).prev("input").val());
                }
            })
            //为空，不操作。
            if (0 == mIdArr.length) {
                return;
            }
            $.ajaxFn({
                url: url,
                data: {
                    member_ids: mIdArr
                },
                success: function (data) {
                    if (data.status == 1) {
                        // $.each(arr4, function(index, val) {
                        // 	$(".exp_data_box tr").eq(arr4[index]).find(".isOpenAc").addClass('mui-active');
                        // });
                        setTimeout(function () {
                            that.expTemplateList();
                        }, 100)
                    }
                }
            })
        },

        share_setting_group: function (btn) {
            var user_id = this.getChoosedUserIds();
            if (user_id.length == 0) {
                $.showAlert(mainLang('select_user'));
                return;
            }

            if (user_id.length >1)
            $.ajaxFn({
                url: ELN_URL + "?r=group-setting/get-share-set-of-group",
                type: "get",
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    is_group: 1,
                    user_id: user_id
                },
                success: function (data) {
                    if (data.status == 1) {
                        // 确保没有弹出框了
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        $(".share-setting .search-btn-box button").attr("group_id", btn.attr("data-group"));

                        $(".share-setting h4 span").text(btn.parents("tr").find("td").eq(1).text());                        
                        $(".share-setting .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips_for_share'));

                        // delete by hkk 2019/10/11
                        // $(".share-setting .share-part:first label").eq(0).text(jsLang['Shared_authority_setting']);
                        // $(".share-setting .share-part:first .right-con label").eq(0).find("span").text(jsLang['Allow_share_to_member']);
                        // $(".share-setting .share-part:first .right-con label").eq(1).find("span").text(jsLang['Allow_share_to_joined_group']);
                        // $(".share-setting .share-part:first .right-con label").eq(2).find("span").text(jsLang['Allow_share_to_other_group']);
                        //
                        // $(".share-setting .share-part").eq(1).find("label").eq(0).text(jsLang['Default_sharing_group']);
                        // $(".share-setting .share-part").eq(2).find("label").eq(0).text(jsLang['Default_sharing_members']);
                    }
                }
            });
            else if (user_id.length == 1)
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-share-set',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    user_id: user_id[0],
                    is_group: 0
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);

                        $(".share-setting h4 span").attr("dataUserId", btn.attr("data-userid"));
                        $(".share-setting h4 span").text(btn.parents("tr").find("td").eq(1).text());
                        $(".share-setting .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips_for_share'));
                        $(".share-setting").modal('show');
                        $(".share-setting .search-btn-box button").attr("group_id", btn.attr("data-group"));
                    }
                }
            })
        },

        share_setting: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-share-set',
                data: {
                    group_id: btn.parents("td").attr("group_id"),
                    user_id: btn.attr("data-userid"),
                    is_group: 0
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);

                        $(".share-setting h4 span").attr("dataUserId", btn.attr("data-userid"));
                        $(".share-setting h4 span").text(btn.parents("tr").find("td").eq(1).text());
                        $(".share-setting .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips_for_share'));
                        $(".share-setting").modal('show');
                        $(".share-setting .search-btn-box button").attr("group_id", btn.attr("data-group"));
                        $('.view-checked-box').each(function () {
                            $.getCheckStatus($(this), $(this).find('.check_list_box'));
                        })
                    }
                }
            })
        },

        print_setting_group: function (btn) {
            var user_id = this.getChoosedUserIds();
            if (user_id.length == 0) {
                $.showAlert(mainLang('select_user'));
                return;
            }
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-print-set',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    is_group: 1,
                    user_id: user_id,
                },success: function (data) {
                    if (data.status == 1) {
                        if ($(".print-settings").length < 1) {
                            $("body").append(data.data);
                        }
                        $(".print-settings h4").html(jsLang['batch_export_settings'] + "<span>" + $(".exp_conetnt.active .top-group-tabs .on").text() + "</span>"); //这个设置会覆盖print_temp.php中的模态框
                        $(".print-settings .print_conform_btn").attr("is_group", btn.attr("data-group"))
                        $(".print-settings").modal('show');
                        $(".print-settings").addClass('group-print-setting');


                        $(".print-settings .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips_for_batch'));


                        // $(".print-settings .print_part").eq(0).find("p").text(jsLang['Print_type']);
                        // $(".print-settings .print_part").eq(0).find("label").eq(0).find("span").text(jsLang['Print_single']);
                        // $(".print-settings .print_part").eq(0).find("label").eq(1).find("span").text(jsLang['Print_all']);

                        // $(".print-settings .print_part").eq(1).find("label").eq(0).find("span").text(jsLang['Print_single']);
                        // $(".print-settings .print_part").eq(1).find("label").eq(1).find("span").text(jsLang['Print_all']);

                        // $(".print-settings .print_part").eq(2).find("p").text(jsLang['Print_display']);
                        // $(".print-settings .print_part").eq(2).find("label").eq(0).find("span").text(jsLang['More_page']);
                    }
                }
            })
        },

        print_setting: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-print-set',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    user_id: btn.attr("data-userid"),
                    is_group: 0
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".print-settings").remove();
                        // if ($(".print-settings").length < 1) {
                        $("body").append(data.data);
                        // }
                        $(".print-settings h4 span").attr({
                            "dataUserId": btn.attr("data-userid"),
                            "dataGroupId": btn.parents("td").attr("group_id")
                        })
                        $(".print-settings h4 span").text(btn.parents("tr").find("td").eq(1).text());
                        $(".print-settings .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips'));
                        $(".print-settings").modal('show');
                    }
                }
            })
        },

        require_setting: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=template/set-require',
                data: {
                    temp_id: $('.exp_conetnt.active #temp_id').val(),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".print-settings").remove();
                        // if ($(".print-settings").length < 1) {
                        $("body").append(data.data);
                        // }
                        $(".print-settings h4 span").attr({
                            "dataUserId": btn.attr("data-userid"),
                            "dataGroupId": btn.parents("td").attr("group_id")
                        })
                        $(".print-settings h4 span").text(btn.parents("tr").find("td").eq(1).text());

                        $(".print-settings").modal('show');
                        $(".print-settings .modal-dialog").width('900px');
                    }
                }
            })
        },

        require_setting_action: function (btn) {
            var postData = $("#require_setting").serialize();
            var temp_id = $('.exp_conetnt.active #temp_id').val();
            var chk_value = [];
            $.each($('input:checkbox'), function () {
                if (this.checked) {
                    chk_value.push($(this).val());
                }
            });

            $.ajaxFn({
                url: ELN_URL + '?r=template/set-require-action',
                data: {
                    temp_id: temp_id,
                    chk_value: chk_value,
                },
                success: function (data) {
                    if (data.status == 1) {

                        // 设置必填项后更新模板的必填项
                        $('.exp_conetnt.active .exp_info').find('.added_define_item_part').remove();

                        // 把设置的自定义项重新写到实验头部
                        data.data.define_item.forEach((item,index,array)=>{

                            var toAddHtml = '<div class="info_part iblock added_define_item_part">' +
                                '<div class="fl"><label class="define_item_label body_left"><span style="color:#999"></span>' + item.title + '：</label></div>'+
                                '<div class="fl">' +
                                '<input type="text" autocomplete="off" data-id="'+ item.dict_id +'" data-is_require="'+ item.is_require +'" data-is_struct="'+ item.is_struct +'" value="' + item.value + '" name="title" data-title="' + item.title + '" />' +
                                '</div>'+
                                '</div> '

                            $('.exp_conetnt.active .exp_info .set_define_item').parent().before(toAddHtml);
                        });

                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        $('.tag.on').dblclick()
                    }
                }
            })
        },

        re_setting_group: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-group-sign-set',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        if ($(".sign-setting").length < 1) {
                            $("body").append(data.data.file);
                            $(".sign-setting-group h4 span").text($(".exp_conetnt.active .top-group-tabs .on").text());
                            $(".sign-setting-group .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips'));
                            $(".sign-setting-group").modal('show');
                            $("body").on('input', '.sign_day_group', function (event) {
                                var val = $('.sign_day_group').val();
                                if (val.length > 2) {
                                    $('.sign_day_group').val(val.substring(0, 2));
                                }
                            })
                        }
                    }
                }
            });
        },

        group_require_setting: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-require-setting',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        //if ($('.sign-setting').length < 1) {
                            $('body').append(data.data.html);
                            $('.require-setting-group h4 span').text($('.exp_conetnt.active .top-group-tabs .on').text());
                            // $('.reopen-setting-group .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips'));
                            $('.require-setting-group').modal('show');
                        //}
                    }
                }
            });
        },

        group_require_setting_submit: function (btn) {
            var data = {
                group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                title: $('.modal-body #title').is(':checked') ? 1 : 0,
                keyword: $('.modal-body #keyword').is(':checked') ? 1 : 0,
                project: $('.modal-body #project').is(':checked') ? 1 : 0,
                task: $('.modal-body #task').is(':checked') ? 1 : 0,
                wo_cc: $('.modal-body #wo_cc').is(':checked') ? 1 : 0,
                wo_resolve_exp: $('.modal-body #wo_resolve_exp').is(':checked') ? 1 : 0,
            };


            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/set-require-setting',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            })
        },

        // 获取鹰群关于某个类型（复核后重开，创建记录本）的设置
        get_group_setting: function ($btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-general-setting-by-type',
                type: 'get',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    type: $btn.attr('data-type')
                },
                success: function (res) {
                    if (res.status == 1) {
                        $('.group-general-setting').remove();
                        $('body').append(res.data.html);
                        $('.group-general-setting h4 span').text($('.exp_conetnt.active .top-group-tabs .on').text());
                        $('.group-general-setting').modal('show');
                    }
                }
            });
        },

        save_group_setting: function ($btn) {
            var type = $btn.attr('data-type');
            var setting = {};

            switch (type) {
                case 'reopen':
                    setting.un_allow_edit = $('#un_allow_edit').is(':checked') ? 1 : 0;
                    if (!setting.un_allow_edit) {
                        if ($('#reopen_checker').is(':checked')) {
                            setting.approval_nodes = require('approval').getNodes();
                            if (setting.approval_nodes.length == 0) {
                                $.showAlert(mainLang('pls_set_approval_node'));
                                return false;
                            }
                        }
                    }
                    break;
                case 'create_book':
                    setting.require_approval = $('#require_approval').is(':checked') ? 1 : 0;
                    if (setting.require_approval) {
                        if ($('#send_remind_email').is(':checked')) {
                            setting.send_remind_email = 1;
                        }

                        setting.approval_nodes = require('approval').getNodes();
                        if (setting.approval_nodes.length == 0) {
                            $.showAlert(mainLang('pls_set_approval_node'));
                            return false;
                        }
                    }
                    break;
                case 'approval':
                    var $modal = $('.approval-setting-group');

                    // 复核设置项
                    var remindTrigger = '';
                    if ($('#remind_trigger_create', $modal).is(':checked')) {
                        remindTrigger = 'create';
                    } else if ($('#remind_trigger_update', $modal).is(':checked')) {
                        remindTrigger = 'update';
                    }
                    setting.witness = {
                        witness_required: $('#witness_required', $modal).is(':checked') ? 1 : 0,
                        remind_witness: $('#remind_witness', $modal).is(':checked') ? 1 : 0,
                        remind_result: $('#remind_witness_result', $modal).is(':checked') ? 1 : 0,
                        submit_reason_required: $('#witness_submit_reason_required', $modal).is(':checked') ? 1 : 0,
                        remind_trigger: remindTrigger,
                        remind_day_limit: $('.remind_day_limit', $modal).val(),
                        remind_with_email: $('#remind_with_email', $modal).is(':checked') ? 1 : 0,
                        remind_day_period: $('.remind_day_period', $modal).val(),
                    };

                    // 重开设置项
                    setting.reopen = {
                        un_allow_edit: $('#un_allow_edit', $modal).is(':checked') ? 1 : 0,
                        save_exp_check: $('#reopen_save_exp_check', $modal).is(':checked') ? 1 : 0,
                    };
                    if (!setting.reopen.un_allow_edit) {
                        if ($('#reopen_checker', $modal).is(':checked')) {
                            var $nodes = $('.reopen-setting-box .approval-node-box .approval-node', $modal);
                            setting.reopen.approval_nodes = require('approval').getNodes(undefined, $nodes);
                            if (setting.reopen.approval_nodes.length == 0) {
                                $.showAlert(mainLang('pls_set_approval_node_reopen'));
                                return false;
                            }
                        }
                    }

                    // 预审设置项
                    setting.pretrial = {
                        submit_reason_required: $('#pretrial_submit_reason_required', $modal).is(':checked') ? 1 : 0,
                        save_exp_check: $('#pretrial_save_exp_check', $modal).is(':checked') ? 1 : 0,
                    };

                    // 签字设置项
                    setting.signing = {
                        send_remind_email: $('#signing_send_remind_email', $modal).is(':checked') ? 1 : 0,
                        submit_reason_required: $('#signing_submit_reason_required', $modal).is(':checked') ? 1 : 0,
                        save_exp_check: $('#signing_save_exp_check', $modal).is(':checked') ? 1 : 0,
                    };

                    //工单设置项
                    var woSettingArr = [];
                    var wo_type = $("input[name='wo_type']");
                    if (wo_type.attr('idbox') != '') {
                        if ($(".tab-item[data-type= 'work-order']").hasClass('on')) {
                            var approvalNodes = require('approval').getNodes($('.work-order-box'));
                        } else {
                            var approvalNodes = require('approval').getNodes($('.work-order-box'), '', 0);
                        }
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('pls_set_approval_node_wo'));
                            return false;
                        }
                    }
                    setting.work_order = {
                        wo_type: wo_type.attr('idbox'),
                        wo_type_name: wo_type.val(),
                        approval_nodes: approvalNodes
                    };
                    break;
            }

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/save-general-setting-by-type',
                data: {
                    group_id: $('.exp_conetnt.active .top-group-tabs .on').attr('data-id'),
                    type: $btn.attr('data-type'),
                    setting: setting
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            })
        },

        // 获取鹰群关于某个成员针对某个类型（复核设置）的设置
        get_group_member_setting: function ($btn) {
            var userIds = [];
            var isBatch = true;
            if ($btn.attr('data-userid')) {
                userIds = [$btn.attr('data-userid')];
                isBatch = false;
            } else {
                userIds = this.getChoosedUserIds();
            }
            var type = $btn.attr('data-type');

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-member-setting-by-type',
                type: 'get',
                data: {
                    group_id:$(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    type: type,
                    user_ids: userIds
                },
                success: function (res) {
                    if (res.status == 1) {
                        switch (type) {
                            case 'witness': // 复核设置
                            case 'signing': // 签字设置
                            case 'other': // 其他设置
                                $('body').append(res.data.html);
                                $userBox = $('.group-member-setting h4 span');
                                if (!isBatch) { // 单个设置
                                    $userBox.text($btn.parents('tr').find('td').eq(1).text());
                                    $userBox.attr({
                                        'data-userid': userIds[0]
                                    });
                                    $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips'));
                                } else { // 批量设置
                                    $userBox.attr({
                                        'data-userid': userIds
                                    });
                                    $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips_for_batch'));
                                }

                                $('.group-member-setting').modal('show');
                                break;
                            case 'pretrial': // 预审设置
                                $('body').append(res.data.html);
                                $userBox = $('.group-member-setting h4 span');
                                $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips1'));
                                $('.group-member-setting .modal-footer .tips2').html( mainLang('setting_tips2'));
                                if (!isBatch) { // 单个设置
                                    $userBox.text($btn.parents('tr').find('td').eq(1).text());
                                    $userBox.attr({
                                        'data-userid': userIds[0]
                                    });
                                } else { // 批量设置
                                    $userBox.attr({
                                        'data-userid': userIds
                                    });
                                    $('.group-member-setting .modal-footer .tips2').append( mainLang('setting_tips3_batch'));
                                }

                                $('.group-member-setting').modal('show');
                                break;
                        }
                    }
                }
            });
        },

        save_group_member_setting: function ($btn) {
            var userIds = $('.group-member-setting h4 span').attr('data-userid').split(',');
            var type = $btn.attr('data-type');
            var setting = {};

            switch (type) {
                case 'witness': // 复核设置
                    var $modal = $('.group-member-setting.witness-setting');
                    var remindTrigger = $('#remind_trigger').val();
                    var remindWith = $('#remind_with').val();
                    var $userInput = $('input.visible-user-input', $modal);
                    var userIdStr = $userInput.attr('idbox');
                    $('#remind_others', $modal).is(':checked') ? (userIdStr = userIdStr) : (userIdStr="");
                    if(remindWith) {
                        for(var i =0 ; i<remindWith.length; i++) {
                            if (remindWith[i] == 'remind_with_message') {
                                var remind_with_message = 1
                            }
                            if (remindWith[i] == 'remind_with_email') {
                                var remind_with_email = 1
                            }
                        }
                    }

                    setting = {
                        remind_trigger: remindTrigger,
                        remind_day_limit: $('.remind_day_limit', $modal).val(),
                        remind_with_message: remind_with_message ? 1 : 0,
                        remind_with_email: remind_with_email ? 1 : 0,
                        remind_day_period: $('.remind_day_period', $modal).val(),
                        remind_creator: $('#remind_creator', $modal).is(':checked') ? 1 : 0,
                        remind_others: $('#remind_others', $modal).is(':checked') ? 1 : 0,
                        remind_other_users: (userIdStr !== '' ? userIdStr.split(',') : []),
                        approval_nodes: require('approval').getNodes()
                    };
                    break;
                case 'signing': // 签字设置
                    setting.approval_nodes = require('approval').getNodes();
                    setting.send_remind_email = $('#send_remind_email').is(':checked') ? 1 : 0;
                    setting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
                    setting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;
                    if ($('#re_edit_approval_user_ids').is(':checked')) {
                        var reEditApprovalUserIds = $('.re-edit-setting [name=user_ids]').attr('idbox');
                        if (!reEditApprovalUserIds) {
                            $.showAlert(mainLang('pls_set_reedit_approval'));
                            return false;
                        }
                        if (reEditApprovalUserIds) {
                            reEditApprovalUserIds = reEditApprovalUserIds.split(',');
                            setting.re_edit_approval_user_ids = reEditApprovalUserIds;
                        }
                    }
                    break;
                case 'pretrial': // 预审设置
                    // 针对所有实验的设置
                    var allSettingChecked = $('#all_exp_need_pretrial').is(':checked');
                    if (allSettingChecked) {
                        var approvalNodes = require('approval').getNodes($('#all_exp_user'));
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('set_approval_node_completely'));
                            return false;
                        }
                        setting.all_setting = {
                            approval_nodes: approvalNodes
                        };
                    }

                    // 针对InDraw的设置
                    var indrawCheckedArr = [];
                    $('.danger-item').each(function () {
                        if ($(this).is(':checked')) {
                            indrawCheckedArr.push($(this).attr('data-type'));
                        }
                    });
                    if (indrawCheckedArr.length > 0) {
                        var approvalNodes = require('approval').getNodes($('#indraw_user'));
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('set_approval_node_completely'));
                            return false;
                        }
                        setting.indraw_setting = {
                            checked_arr: indrawCheckedArr,
                            approval_nodes: approvalNodes
                        };
                    }

                    // 针对项目的设置
                    var _valid = 1;
                    var projectChecked = $('#project_need_pretrial').is(':checked');
                    if (projectChecked) {
                        var projectSettingArr = [];
                        $('.project-setting-line:visible').each(function () {
                            var projectIds = $(this).find('[name=project_ids]').attr('idbox');
                            if (projectIds) {
                                projectIds = projectIds.split(',');
                                var approvalNodes = require('approval').getNodes($(this));
                                if (approvalNodes.length == 0) {
                                    _valid = 0;
                                }
                                projectSettingArr.push({
                                    project_ids: projectIds,
                                    approval_nodes: approvalNodes
                                });
                            } else {
                                _valid = 0;
                            }
                        });
                        if (projectSettingArr.length > 0) {
                            setting.project_setting = projectSettingArr;
                        }
                    }
                    if (!_valid) {
                        $.showAlert(mainLang('set_approval_node_completely'));
                        return false;
                    }

                    setting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
                    setting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;
                    break;
                case 'other': // 其他设置
                    var $modal = $('.group-member-setting.other-setting');
                    setting.allow_cbg = $('#allow_cbg', $modal).is(':checked') ? 1 : 0;
                    var needApprovalChecked = $('#cbg_need_approval', $modal).is(':checked');
                    if (needApprovalChecked) {
                        var approvalNodes = require('approval').getNodes($('#approval_selector_container', $modal));
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('pls_set_approval_node'));
                            return false;
                        }
                        setting.cbg_approval_nodes = approvalNodes;
                    }
                    setting.allow_proxy = $('#allow_proxy', $modal).is(':checked') ? 1 : 0;
                    if (setting.allow_proxy) {
                        var proxyUserIds = $('.proxy-container [name=user_ids]', $modal).attr('idbox');
                        if (proxyUserIds) {
                            proxyUserIds = proxyUserIds.split(',');
                            setting.proxy_user_ids = proxyUserIds;
                        }
                        else {
                            $.showAlert(jsLang['please_select_proxy_user'])
                            return false;
                        }
                        var proxyExpireDate = $('.proxy-container .proxy_expire_date', $modal).val();
                        setting.proxy_expire_date = proxyExpireDate;
                    }
                    // 代办审核设置
                    setting.allow_dfa = $('#allow_dfa', $modal).is(':checked') ? 1 : 0;
                    if (setting.allow_dfa) {
                        var dfaUserIds = $('[name=dfa_user_ids]', $modal).attr('idbox');
                        if (dfaUserIds) {
                            dfaUserIds = dfaUserIds.split(',');
                            setting.dfa_user_ids = dfaUserIds;
                        }
                        else {
                            $.showAlert(jsLang['please_select_proxy_user'])
                            return false;
                        }
                    }

                    //创建全文模板权限 按钮为确认时 not_allow_create_full_temp = 0
                    setting.not_allow_create_full_temp = !$('#group_not_allow_create_full_temp', $modal).is(':checked') ? 1 : 0;
                    break;
            }

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/save-member-setting-by-type',
                data: {
                    group_id: $('.exp_conetnt.active .top-group-tabs .on').attr('data-id'),
                    type: type,
                    user_ids: userIds,
                    setting: setting
                },
                success: function (res) {
                    if (res.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            });
        },

        // 获取鹰群关于某个成员针对某个类型（复核设置）的设置
        get_group_role_setting: function ($btn) {
            var userId;
            var isBatch = true;
            if ($btn.attr('data-userid')) {
                userId = $btn.attr('data-userid');
                isBatch = false;
            } else {
                var userIds = this.getChoosedUserIds();
                if (userIds.length == 1) {
                    userId = userIds[0];
                }
            }
            var type = $btn.attr('data-type');

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-member-setting-by-type',
                type: 'get',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    type: type,
                    user_id: userId
                },
                success: function (res) {
                    if (res.status == 1) {
                        switch (type) {
                            case 'witness': // 复核设置
                            case 'signing': // 签字设置
                            case 'pretrial': // 预审设置
                                $('body').append(res.data.html);
                                $userBox = $('.group-member-setting h4 span');
                                if(!isBatch) { // 单个设置
                                    $userBox.text($btn.parents('tr').find('td').eq(1).text());
                                    $userBox.attr({
                                        'data-userid': userId
                                    });
                                    $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips'));
                                } else { // 批量设置
                                    $userBox.attr({
                                        'data-userid': userIds
                                    });
                                    $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] + mainLang('setting_tips_for_batch'));
                                }

                                $('.group-member-setting').modal('show');
                                break;
                        }
                    }
                }
            });
        },

        save_group_role_setting: function ($btn) {
            var userIds = $('.group-member-setting h4 span').attr('data-userid').split(',');
            var type = $btn.attr('data-type');
            var setting = {};

            switch (type) {
                case 'witness': // 复核设置
                    setting.day = $('.group-member-setting .day-num-input').val();
                    setting.emails = $('.group-member-setting .email_list').val().split(';');
                    setting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
                    setting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;
                    var approvalType = $('input[type=radio][name=witness-type]:checked').val();
                    if (approvalType) {
                        setting.approval_type = approvalType;
                        if (approvalType == 'single') { // 一级复核
                            var approvalUserIds = $('.single-witness-box [name=user_ids]').attr('idbox');
                            if (approvalUserIds) {
                                approvalUserIds = approvalUserIds.split(',');
                                setting.approval_user_ids = approvalUserIds;
                            }
                        } else if (approvalType == 'multiple') { // 多级复核
                            var approvalNodes = require('approval').getNodes();
                            if (approvalNodes.length == 0) {
                                $.showAlert(mainLang('pls_set_approval_node'));
                                return false;
                            }
                            setting.approval_nodes = approvalNodes;
                        }
                    }
                    break;
                case 'signing': // 签字设置
                    setting.approval_nodes = require('approval').getNodes();
                    setting.send_remind_email = $('#send_remind_email').is(':checked') ? 1 : 0;
                    setting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
                    setting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;
                    if ($('#re_edit_approval_user_ids').is(':checked')) {
                        var reEditApprovalUserIds = $('.re-edit-setting [name=user_ids]').attr('idbox');
                        if (!reEditApprovalUserIds) {
                            $.showAlert(mainLang('pls_set_reedit_approval'));
                            return false;
                        }
                        if (reEditApprovalUserIds) {
                            reEditApprovalUserIds = reEditApprovalUserIds.split(',');
                            setting.re_edit_approval_user_ids = reEditApprovalUserIds;
                        }
                    }
                    break;
                case 'pretrial': // 预审设置
                    // 针对所有实验的设置
                    var allSettingChecked = $('#all_exp_need_pretrial').is(':checked');
                    if (allSettingChecked) {
                        var approvalNodes = require('approval').getNodes($('#all_exp_user'));
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('pls_set_approval_node'));
                            return false;
                        }
                        setting.all_setting = {
                            approval_nodes: approvalNodes
                        };
                    }

                    // 针对InDraw的设置
                    var indrawCheckedArr = [];
                    $('.danger-item').each(function () {
                        if ($(this).is(':checked')) {
                            indrawCheckedArr.push($(this).attr('data-type'));
                        }
                    });
                    if (indrawCheckedArr.length > 0) {
                        var approvalNodes = require('approval').getNodes($('#indraw_user'));
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('pls_set_approval_node'));
                            return false;
                        }
                        setting.indraw_setting = {
                            checked_arr: indrawCheckedArr,
                            approval_nodes: approvalNodes
                        };
                    }

                    // 针对项目的设置
                    var _valid = 1;
                    var projectChecked = $('#project_need_pretrial').is(':checked');
                    if (projectChecked) {
                        var projectSettingArr = [];
                        $('.project-setting-line').each(function () {
                            var projectIds = $(this).find('[name=project_ids]').attr('idbox');
                            if (projectIds) {
                                projectIds = projectIds.split(',');
                                var approvalNodes = require('approval').getNodes($(this));
                                if (approvalNodes.length == 0) {
                                    $.showAlert(mainLang('pls_set_approval_node'));
                                    _valid = 0;
                                    return false;
                                }
                                projectSettingArr.push({
                                    project_ids: projectIds,
                                    approval_nodes: approvalNodes
                                });
                            }
                        });
                        if (projectSettingArr.length > 0) {
                            setting.project_setting = projectSettingArr;
                        }
                    }
                    if (!_valid) {
                        return false;
                    }

                    setting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
                    setting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;
                    break;
            }

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/save-member-setting-by-type',
                data: {
                    group_id: $('.exp_conetnt.active .top-group-tabs .on').attr('data-id'),
                    type: type,
                    user_ids: userIds,
                    setting: setting
                },
                success: function (res) {
                    if (res.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            });
        },

        // 保存修订设置
        saveRevisionSetting: function() {
            var settings = [];
            $('.revision_setting .editor_modules li').each(function() {
                $chechbox = $(this).find('input:checkbox');
                var revisionMode = ($chechbox.length == 1 && $chechbox.is(':checked')) ? 1 : 0;
                settings.push({
                    relay_id: $(this).attr('relay-id'),
                    revision_mode: revisionMode
                })
            });

            $.ajaxFn({
                url: ELN_URL + '?r=template/save-revision-setting',
                type: 'POST',
                data: {
                    settings: settings
                },
                success: function (res) {
                    if (res.status == 1) {
                        $.showAlert(mainLang('success'));
                        $.closeModal();
                    }
                }
            });
        },

        //模板设置弹窗
        module_setting: function (btn) {
            var that = this;
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-user-module-set',
                data: {
                    group_id: btn.parents("td").attr("group_id"),
                    user_ids: [btn.attr("data-userid")],
                },
                success: function (data) {
                    if (data.status == 1) {
                        if ($(".module-setting-temp").length < 1) {
                            $("body").append(data.data);
                            $(".module-setting-temp h4 span").text(btn.parents("tr").find("td").eq(1).text());
                            $(".module-setting-temp h4 span").attr({
                                "dataUserId": btn.attr("data-userid"),
                                "dataGroupId": btn.parents("td").attr("group_id"),
                                type: 1
                            })
                            $(".module-setting-temp .modal-footer .tips").html(jsLang['tip_'] + mainLang('setting_tips'));
                            $(".module-setting-temp").modal('show');
                        }
                    }
                }
            })
        },
        //批量模板设置弹窗
        module_setting_batch: function (btn) {
            var that = this;
            var groupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            var userIds = [];
            $('.exp_conetnt.active .eln_setting_table input:not(#checkAll):checked').each(function (index, el) {
                var userId = $.trim($(this).closest('tr').attr('data-user_id'));
                userIds.push(userId);
            });

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/get-user-module-set',
                data: {
                    group_id: groupId,
                    user_ids: userIds,
                    type: 2
                },
                success: function (data) {
                    if (data.status == 1) {
                        if ($(".module-setting-temp").length < 1) {
                            $("body").append(data.data);
                            $(".module-setting-temp h4").html(mainLang('template setting') + "<span>" + $(".exp_conetnt.active .top-group-tabs .on").text() + "</span>");
                            $(".module-setting-temp h4 span").attr({
                                "dataUserId": userIds.join(),
                                "dataGroupId": groupId,
                                type: 2
                            })

                            $(".module-setting-temp").modal('show');
                        }
                    }
                }
            })


        },
        //模板设置提交
        module_set: function () {
            var userIds = $(".module-setting-temp h4 span").attr("datauserid").split(',');
            var url = ELN_URL + '?r=group-setting/set-tmp-power';
            var activeGroupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            var $setInput = $(".module-set-input-wrap:visible input[name=module_set]");
            var set = 0;
            if ($setInput.prop('checked')) {
                set = 1;
            }

            $.ajaxFn({
                url: url,
                data: {
                    group_id: activeGroupId,
                    user_ids: userIds,
                    set: set,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            });
        },
        hoverToggle: function (btn) {
            btn.find(".absolute").show();
            btn.on("mouseout", function () {
                btn.find(".absolute").hide();
            })
        },
        // 添加默认分享用户
        addShareUser: function (btn) {
            var userId = $(".share-setting h4 span").attr("dataUserId");
            var url = ELN_URL + '?r=share/add-share-default';
            var val = $('#add_share_user').val();
            var is_group = $(".share-setting .search-btn-box button").attr("group_id");
            var activeGroupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            if (is_group == 1) {
                userId = this.getChoosedUserIds();
            }
            if ($.trim(val)) {
                $.ajaxFn({
                    url: url,
                    data: {
                        add_share_user: val,
                        group_id: activeGroupId,
                        user_id: userId,
                        is_group: is_group
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            // $("#dshare-user-list").append('<li><span class="email fl">' + val + '</span> <a class="blue opts-btn fr" delete_method="delShareUser">删除</a></li>');
                            // console.log(data.data);
                            var html = '';
                            if (is_group != 1) {
                                var result = data.data.user_list;
                                for (var key in result) {
                                    if (!result.hasOwnProperty(key)) {
                                        continue;
                                    }
                                    ;
                                    if (result[key].email) {
                                        html += '<li><span class="email fl">' + (result[key].email ? result[key].real_name + '(' + result[key].email + ')' : result[key].real_name) + '</span> <a class="blue opts-btn fr" delete_method="delShareUser" data-id=' + result[key].user_id + '>' + jsLang['del'] + '</a></li>';
                                    } else {
                                        html += '<li><span class="email fl">' + (result[key].phone ? result[key].real_name + '(' + result[key].phone + ')' : result[key].real_name) + '</span> <a class="blue opts-btn fr" delete_method="delShareUser" data-id=' + result[key].user_id + '>' + jsLang['del'] + '</a></li>';
                                    }
                                }
                            } else {
                                for (var i = 0; i < data.data.length; i++) {
                                    if (data.data[i].email) {
                                        html += '<li><span class="email fl">' + (data.data[i].email ? data.data[i].real_name + '(' + data.data[i].email + ')' : data.data[i].real_name) + '</span> <a class="blue opts-btn fr" delete_method="delShareUser" data-id=' + data.data[i].user_id + '>' + jsLang['del'] + '</a></li>';
                                    } else {
                                        html += '<li><span class="email fl">' + (data.data[i].phone ? data.data[i].real_name + '(' + data.data[i].phone + ')' : data.data[i].real_name) + '</span> <a class="blue opts-btn fr" delete_method="delShareUser" data-id=' + data.data[i].user_id + '>' + jsLang['del'] + '</a></li>';
                                    }
                                }
                            }
                            $("#dshare-user-list").append(html);
                            if (data.info) {
                                $.showAlert(data.info);
                            }
                        }
                    }
                })
            }
        },
        // 删除默认分享用户
        delShareUser: function (btn) {
            var userId = $(".share-setting h4 span").attr("datauserid");
            var url = ELN_URL + '?r=share/del-default-share';
            var is_group = $(".share-setting .search-btn-box button").attr("group_id");
            var activeGroupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            if (is_group == 1) {
                userId = this.getChoosedUserIds();
            }
            $.ajaxFn({
                url: url,
                data: {
                    group_id: activeGroupId,
                    to_user_id: btn.attr("data-id"),
                    user_id: userId,
                    is_group: is_group
                },
                success: function (data) {
                    if (data.status == 1) {

                        $.showAlert(jsLang['del_success']);
                        btn.parents("li").remove();
                    }
                }
            })
        },
        share_submit_btn: function (btn) {
            var userId = $(".share-setting h4 span").attr("dataUserId");
            var user_ids = $('#default-share-all #default-share-users #user_ids').attr('idbox');
            var url = ELN_URL + '?r=group-setting/set-share-set';
            var checkArr = this.is_checked_now(".share-setting .share-part");
            var is_group = $(".share-setting .search-btn-box button").attr("group_id");
            if (is_group && is_group != '0') {
                userId = this.getChoosedUserIds();
            }

            // add by hkk 2019/10/11 加上批量设置多个默认鹰群
            var group_ids = $('#default-share-all #group_ids').attr('idbox');

            var activeGroupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");

            var shareApprovalNodes = $('#share_with_approval').prop('checked') ? require('approval').getNodes($('.more_settings')) : []; // 审批节点，未勾选主动分享需要审批时为空
            if ($('#share_with_approval').prop('checked') && shareApprovalNodes == 0) {
                $.showAlert(mainLang('pls_set_approval_node'));
                return false;
            }

            var sendApprovalEmail = $('#send_approval_email').prop('checked') ? 1 : 0;

            var defaultShareByProject = $('#by-project-task').prop('checked') ? 1 : 0;

            //按项目/任务设置的默认分享鹰群、人员，
            var projectTaskSetting = {
                to_groups: [],
                to_users: []
            };
            $('#default-share-groups .def_share_item').each(function () { //分享到鹰群的每个设置
                var treeId = $(this).find('.tree_box').attr('id');
                var tree = $.fn.zTree.getZTreeObj(treeId);
                var nodes = tree.getCheckedNodes();
                var setting = {
                    projects: [],
                    tasks: [],
                    groups: []
                }
                nodes.forEach(function (node) {
                    if (node.type == 0) { //项目
                        setting.projects.push(node.id);
                    } else { //目标和任务
                        setting.tasks.push(node.id);
                    }
                });
                setting.projects.sort();
                setting.tasks.sort();
                var groupIdsStr = $(this).find('[name=group_ids]').attr('idbox');
                if (groupIdsStr) {
                    setting.groups = groupIdsStr.split(',').sort();
                }
                if (setting.projects.length + setting.tasks.length > 0 && setting.groups.length > 0) {
                    projectTaskSetting.to_groups.push(setting);
                }
            });
            $('#default-share-users .def_share_item').each(function () { //分享到人员的每个设置
                var treeId = $(this).find('.tree_box').attr('id');
                var tree = $.fn.zTree.getZTreeObj(treeId);
                var nodes = tree.getCheckedNodes();
                var setting = {
                    projects: [],
                    tasks: [],
                    users: []
                }
                nodes.forEach(function (node) {
                    if (node.type == 0) { //项目
                        setting.projects.push(node.id);
                    } else { //目标和任务
                        setting.tasks.push(node.id);
                    }
                });
                setting.projects.sort();
                setting.tasks.sort();
                var userIdsStr = $(this).find('[name=user_ids]').attr('idbox');
                if (userIdsStr) {
                    setting.users = userIdsStr.split(',').sort();
                }
                if (setting.projects.length + setting.tasks.length > 0 && setting.users.length > 0) {
                    projectTaskSetting.to_users.push(setting);
                }
            });
            $.ajaxFn({
                url: url,
                data: {
                    group_id: activeGroupId,
                    user_id: userId,
                    user_ids: user_ids,
                    is_group: 0,//批量也是0。鹰群设置才是1。
                    share_to_user: checkArr[0],
                    share_to_this_group: checkArr[1],
                    share_to_my_group: checkArr[2],
                    share_to_other_group: checkArr[3],
                    share_with_approval: this.getCheckStatus($('.share-setting #share_with_approval')),
                    // def_share_group: checkArr[3], // MODIFIED BY HKK 2019/10/11 此属性暂时不用
                    group_ids: group_ids, // ADD BY HKK 2019/10/11 原来只有上面一个默认鹰群，改为多个
                    approver: shareApprovalNodes,
                    send_approval_email: sendApprovalEmail,
                    by_project_task: defaultShareByProject,
                    project_task_setting: projectTaskSetting,
                    coauthor_book: this.getCheckStatus($('.share-setting #coauthor_book')),
                    coauthor_group_book: this.getCheckStatus($('.share-setting #coauthor_group_book')),
                },
                success: function (response) {
                    if (response.status == 1) {
                        $.showAlert(jsLang['setting_success']);
                        $.closeModal();
                    }
                }
            })
        },

        print_submit_btn: function (btn) {
            var groupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            var userId = $(".print-settings h4 span").attr("dataUserId") || '';
            var checkArr = this.is_checked_now(".print-settings");
            var is_group = btn.attr("is_group");
            if (is_group == 1) {
                userId = this.getChoosedUserIds();
            }

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/set-print-set',
                data: {
                    group_id: groupId,
                    user_id: userId,
                    is_group: is_group,
                    pdf_single: checkArr[1] ? 1 : checkArr[0],
                    pdf_book: checkArr[1],
                    word_single: checkArr[3] ? 1 : checkArr[2],
                    word_book: checkArr[3],
                    //more_page: checkArr[4],
                    print_history: checkArr[4],
                    print_revision_trace: checkArr[5],
                    print_comment: checkArr[6],
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            })
        },
        // 群复核设置调交
        groupReviewSubmit: function (btn) {
            var data = $.formSerializeFn($('.group-review-set'));
            data.group_id = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            var url = ELN_URL + '/eln/set-group-sign-set';
            $.ajaxFn({
                url: url,
                data: data,
                noLoad: true,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['set_ok']);
                        $.closeModal();
                    }
                }
            })
        },
        getCheckStatus: function ($checkBox) {
            if ($checkBox.hasClass('beauty-checkbox-big') && $checkBox.hasClass('some')) {
                return 2;
            }
            if ($checkBox.prop('checked')) {
                return 1;
            }
            return 0;
        },
        is_checked_now: function (something) {
            var dom = $(something);
            var arr = [];
            var checkedArr = dom.find("input[type='checkbox']");
            var $item;
            for (var i = 0; i < checkedArr.length; i++) {
                $item = checkedArr.eq(i);
                if ($item.hasClass('beauty-checkbox-big') && $item.hasClass('some')) {
                    arr.push(2);
                } else if ($item.prop('checked')) {
                    arr.push(1);
                } else {
                    arr.push(0);
                }
            }
            return arr;
        },
        // 新增成员
        addMemberInit: function (btn) {
            var that = this;
            var o = this;
            var html = '<div class="layer-member">' +
                '<p class="title">' + jsLang['add member tip'] + '</p>' +
                '<textarea class="textarea-member input" placeholder="' + jsLang['enter phone or email'] + '"></textarea>' +
                '</div>';

            function callback(type) {
                var memberList = $(".textarea-member");
                var val = $.trim(memberList.val());
                // 非空验证
                if (!val) return;
                // 分隔符验证
                if (/，/g.test(val)) {
                    $.showAlert(mainLang('Please use a comma to divide'));
                    return
                }
                var arr = val.split(";");
                // 循环验证邮箱
                for (var i = 0; i < arr.length; i++) {
                    var val = arr[i].trim();
                    arr[i] = val;
                    if (!val) {
                        continue;
                    }
                    if ($.checkVal(val, "email") || $.checkVal(val, "sj_")) {

                    } else {
                        $.showAlert('“&nbsp;' + val + '”&nbsp; ' + jsLang['wrong format']);
                        return;
                    }
                }
                // 发送请求
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/add-group-member',
                    type: 'POST',
                    noLoad: true,
                    data: {
                        group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                        account: arr
                    },
                    success: function (data) {
                        if (data.status == "1") {
                            $.closeModal();
                            setTimeout(function () {
                                $.showAlert(mainLang('jymed_add_success'));
                                that.expTemplateList();
                            }, 1000);
                        } else {
                            $.showAlert(data.info);
                        }
                    }
                });
            }

            $.popContent(html, jsLang['add member'], function () {
                callback("add");
            }, "smaller");
        },

        // add by hkk 2020/6/30 群成员权限 切换鹰群
        changeGroup: function () {
            //只有手动修改才触发
            var group_id = $('#group_name option:selected').attr('data-id');
            if(group_id == $("#group_name").attr('data-groupid')){
                return
            }
            this.clearState();
            this.upData.group_id = group_id;
            localStorage.setItem('group_auth_history_group_id',group_id);
            this.expTemplateList();
        },
        changeGroupById: function (btn) {
            var group_id = btn.attr('data-id');
            this.clearState();
            this.upData.group_id = group_id;
            localStorage.setItem('group_auth_history_group_id',group_id);
            this.expTemplateList();
        },

        //add by wy 2023/4/21 群内权限 下拉框选择鹰群切换
        changeSelectionGroup: function (btn, id, choosedId) {
            if ($(this).hasClass('on')) {
                return;
            }
            var group_id = btn.attr('data-id');
            this.clearState();
            this.upData.group_id = group_id;
            localStorage.setItem('group_auth_history_group_id',group_id);
            this.expTemplateList(btn, id, choosedId);
        },

        //切换鹰群
        changeCompanySetting: function (btn) {
            //当前tab，不修改。
            $(".exp_conetnt.active .top-group-tabs li").removeClass('on');
            var tab_id = btn.attr('data-id');
            btn.addClass('on');
            this.clearState();
            if (tab_id == 1) {
                this.compmanySettingAuth();
            }
            if (tab_id == 2) {
                this.compmanySettingDecimal();
            }
            if (tab_id == 3) {
                this.compmanySettingUseStatic();
            }
            if (tab_id == 4) {
                this.compmanySettingLoginStatic();
            }

        },

        //add by wy 2023/3/10 切换物料表设置页面
        changeMaterialSettingPage: function (btn) {
          $('.exp_conetnt.active .top-group-tabs li').removeClass('on');
          var tab_id = btn.attr('data-id');
          btn.addClass('on');
          this.clearState();
          if(tab_id == 1) {
              this.compmanySettingDecimalContent();
          }
          if(tab_id == 2) {
              this.compmanySettingShowIUPAC();
          }
          if(tab_id == 3) {
              this.exportMaterialsData();
          }
        },

        //add by wy 2023/3/10 物料设置-物料表小数点设置
        compmanySettingDecimalContent: function () {
            require('get_html').getDecimalSettingContent().then(function (res) {
                if(res.status === 1) {
                    $('.exp_conetnt.active').html(res.data.contentHtml);
                }
            })
        },

        //add by wy 2023/3/10 物料设置-中英文物料名称设置
        compmanySettingShowIUPAC: function () {
            require('get_html').getShowIUPACContent().then(function (res) {
                if(res.status === 1) {
                    $('.exp_conetnt.active').html(res.data.contentHtml);
                }
            })
        },

        //add by wy 2023/3/10 物料设置-导出物料数据
        exportMaterialsData: function () {
            require('get_html').getExportMaterialsContent().then(function (res) {
                if(res.status === 1) {
                    $('.exp_conetnt.active').html(res.data.contentHtml);
                }
            })
        },

        //搜索
        search: function (btn) {
            var input_value = btn.siblings('[name=search_value]').val().trim();
            this.upData.page = 1;
            this.upData.input_value = input_value;
            this.upData.group_id = $('#group_name option:selected').attr('data-id');
            this.expTemplateList();
        },



        //ajax分页插件
        pageFn: function () {
            var page = this.upData ? this.upData.page : 1;
            var that = this;
            var pageBox = $('.exp_conetnt.active .page_box:visible');

            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function (page_index, jq) {
                     if (that.type == 22) { // add by hkk 2019/9/29

                        that.upData = $.formSerializeFn($("#search-book-file-manage"));
                        that.upData.page = page_index + 1;

                        // 获取查询条件和 页数信息
                        var group_ids,user_ids,book_status;

                        if($("#search-book-file-manage #group_ids").attr('idbox')){
                            group_ids = $("#search-book-file-manage #group_ids").attr('idbox').split(',');
                        }else{
                            group_ids = []; // 为空相当于全选
                        }

                        if($("#search-book-file-manage #user_ids").attr('idbox')){
                            user_ids = $("#search-book-file-manage #user_ids").attr('idbox').split(',')
                        }else{
                            user_ids = []; // 为空相当于全选
                        }

                        if($("#search-book-file-manage #book_status").val() =='2'){
                            book_status = ['0','1'];
                        }else{
                            book_status = $("#search-book-file-manage #book_status").val();
                        }

                        that.upData.group_ids =  group_ids;
                        that.upData.user_ids =  user_ids;
                        that.upData.book_status =  book_status;
                        that.bookFileManage(true);

                    }
                    else if (that.type === 'instruments_manage' || that.type === 'my_instruments') { // add by hkk 2019/10/31 仪器库分页


                        var url,tableSelector;
                        if(that.type === 'my_instruments' && $('.top-group-tabs .on').attr('data-id') === "2"  ){// 我的仪器预约界面
                            url =  ELN_URL + '?r=instrument/my-book';
                            tableSelector =  '.instruments_book_table';
                        }else{
                            url =  ELN_URL + '?r=instrument/manage';
                            tableSelector =  '.instruments_table';
                        }

                        //获取查询条件和页数
                         var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-manage"));
                         data['page'] = page_index + 1;
                         data['limit'] = $('.pager-select:visible').val() || this.default_page_size || undefined;
                         data['needUpdateAllPage'] = 0;
                         data['type'] = that.type; // add by hkk 2019/11/1
                         that.upData.page = page_index + 1;

                        $.ajaxFn({
                            url: url,
                            data: data,
                            type: 'POST',
                            success: function (data) {
                                if (1 == data.status) {
                                    $('.exp_conetnt').removeClass('search');

                                    $(tableSelector).html(data.data.file);

                                    $('.tool_data_box').html('');

                                    //只在第一次请求的时候设置default_page_size;
                                    if (!that.default_page_size) {
                                        that.default_page_size = $(".page_box").attr('data-limit');
                                    }

                                    that.pageFn();
                                    $(window).scrollTop(0);

                                    //对placeholder的兼容处理。
                                    $('input[placeholder]:visible').placeholder();
                                }
                            }
                        });

                    }
                    else {
                        that.upData.page = page_index + 1;
                        // that.expTemplateList();

                        var callback = function (data) {
                            setLimitToLocal('getGroupUserAuthorityContent', that.upData.limit);
                            if (1 == data.status) {

                                $('.exp_conetnt.active').html(data.data.contentHtml); // 替换内容不换标签

                                //只在第一次请求的时候设置default_page_size;
                                if (!that.default_page_size) {
                                    that.default_page_size = $(".exp_conetnt.active .page_box").attr('data-limit');
                                }
                                that.pageFn();


                            } else {
                                if (data.data == "no_rights") {
                                    window.location.reload();
                                }
                            }
                            $.each($('.isAdmin'), function (index, val) {
                                if ($(this).hasClass('group_master')) {
                                    $(this).addClass('disabled');
                                }
                            });
                            $.each($('.isOpenAc'), function (index, val) {
                                if ($(this).hasClass('group_master')) {
                                    $(this).addClass('disabled');
                                }
                            });

                        }
                        that.upData.limit = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
                        $.ajaxFn({
                            url: ELN_URL + '/?r=group-setting/group-user-list',
                            data: that.upData,
                            success: callback
                        });
                    }

                },
                items_per_page: (that.updata && that.updata.limit) || pageBox.attr('data-limit'),
                default_page_size: that.default_page_size || pageBox.attr('data-limit')
            });

            //对ie的修改
            $(".page_box a,.page_box span").on("click", function (event) {
                event = event || window.event;
                event.preventDefault();
            })
        },

        clearState: function () {
            if (!this.upData) {
                this.upData = {};
            }
            // this.upData.limit = 1;
            this.upData.page = 1;
            delete this.upData.input_value;
            delete this.upData.only_apply;
            delete this.upData.only_history;
            this.upData.group_id = '';
        },
        re_selectLi: function (li) {
            if (li.attr("class") == "activeLi") {
                li.removeClass('activeLi');
            } else {
                li.addClass('activeLi');
            }
        },
        adminAddOrRemove: function (btn) {
            var arr = [],
                dataUserId;
            var dateIdArr = [];
            var addArr = [];
            var titleArr = [];
            var leftAC = $(".resign-left-side .activeLi");
            if (btn.index() == 0) {
                if (leftAC.length > 0) {
                    for (var i = 0; i < $(".resign-right-side li").length; i++) {
                        arr.push($(".resign-right-side li").eq(i).text());
                    }
                    for (var i = 0; i < leftAC.length; i++) {
                        if ($.inArray(leftAC.eq(i).text(), arr) == -1) {
                            dateIdArr.push(leftAC.eq(i).attr("data-userid"));
                            arr.push(leftAC.eq(i).text());
                            addArr.push(leftAC.eq(i).text());
                            titleArr.push(leftAC.eq(i).attr('title'));
                        }
                    }
                    for (var i = 0; i < dateIdArr.length; i++) {
                        $(".resign-right-side ul").prepend("<li title = " + titleArr[i] + " data-userid=" + dateIdArr[i] + " select_method='re_selectLi'>" + addArr[i] + "</li>");
                    }
                    $(".resign-left-side li").removeClass('activeLi');
                }
            } else {
                if ($(".resign-right-side .activeLi").length > 0) {
                    $.each($(".resign-right-side .activeLi"), function (index, val) {
                        var dataUserId = $(".resign-right-side .activeLi").eq(index).attr("data-userid");
                    });
                    $(".resign-right-side .activeLi").remove();
                    $(".resign-right-side li").removeClass('activeLi');
                }
            }
        },
        admin_resign: function (btn) {
            var sign_user_arr = [];
            var userIds = $(".sign-setting h4 span").attr("datauserid").split(',');
            var url = ELN_URL + '?r=group-setting/set-member-sign-set';
            var planDay = $('.sign-setting .plan_day').val();
            var activeGroupId = $(".exp_conetnt.active .top-group-tabs .on").attr("data-id");
            var emailArr = $(".sign-setting textarea").val();
            for (var i = 0; i < $(".resign-right-side li").length; i++) {
                sign_user_arr.push($(".resign-right-side li").eq(i).attr("data-userid"));
            }
            ;
            console.log(sign_user_arr);
            $.ajaxFn({
                url: url,
                data: {
                    group_id: activeGroupId,
                    user_ids: userIds,
                    emails: emailArr,
                    sign_user_ids: sign_user_arr,
                    plan_day: planDay
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            });
        },
        admin_resign_group: function (btn) {
            var url = ELN_URL + '?r=group-setting/set-group-sign-set';
            var result = this.is_checked_now(".sign-setting-group");
            var planDay = $(".sign-setting-group input[type='text']").val() || 0;
            $.ajaxFn({
                url: url,
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    sign_force_select: result[0],
                    remind_review: result[1],
                    remind_review_result: result[2],
                    //un_allow_edit: result[3],
                    email_creator: result[3],
                    plan_day: planDay
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                    }
                }
            })
        },
        search_name: function (e) {
            var t = e.parents(".re-sign-title").siblings("ul").find("li");
            t.hide();
            for (var a = e.siblings("#conditioner").val(), r = t.length, s = 0; s < r; s++) {
                var i = (t.eq(s).html() + t.eq(s).attr("data-userid")).toString();
                "-1" != i.indexOf(a) && t.eq(s).show()
            }
        },
        //仅显示应用开通人员
        onlyOpen: function (btn) {
            var that = this;
            if (!btn.hasClass('active')) {
                this.upData.only_apply = 1;
            } else {
                delete this.upData.only_apply;
            }
            this.upData.page = 1;
            that.expTemplateList();
        },
        // 仅显示历史成员
        onlyHistory: function (btn) {
            var that = this;
            if (!btn.hasClass('active')) {
                this.upData.only_history = 1;
            } else {
                delete this.upData.only_history;
            }
            this.upData.page = 1;
            that.expTemplateList();
        },
        //获取table选择的input框的id。
        getChoosedUserIds: function () {
            var userIds = [];
            var userId;
            $('.exp_conetnt.active .eln_setting_table  input:visible:not(#checkAll):checked').each(function (index, el) {
                userId = $.trim($(this).closest('tr').attr('data-user_id'));
                userId && userIds.push(userId);
            });
            return userIds;
        },

        getChoosedUserNames: function () {
            var userNames = [];
            var userName;
            $('.exp_conetnt.active .eln_setting_table input:visible:not(#checkAll):checked').each(function (index, el) {
                userName = $.trim($(this).closest('tr').attr('data-user_name'));
                userName && userNames.push(userName);
            });
            return userNames;
        },

        //设置实验核查人员
        set_check_user: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/set-check-user',
                data: {
                    group_id: btn.parents("td").attr("group_id"),
                    user_id: btn.attr("data-userid"),
                    is_group: 0
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置实验核查人员
        set_check_user_submit_btn: function (btn) {

            var user_ids = $('#user_ids').attr('idbox');
            var group_ids = $('#group_ids').attr('idbox');
            var group_name = $('#group_ids').val();
            var user_name = $('#user_ids').val();

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/set-check-user-submit',
                data: {
                    user_ids: user_ids,
                    group_ids: group_ids,
                    group_name: group_name,
                    user_name: user_name,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();

                    }
                }
            })
        },

        set_company_auth_config: function (btn) {
            var user_id = this.getChoosedUserIds();
            if (user_id.length == 0) {
                $.showAlert(mainLang('select_user'));
                return;
            }

            $.ajaxFn({
                url: ELN_URL + "?r=group-setting/set-company-auth-config",
                type: "post",
                data: {
                    user_id: user_id,

                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                    }
                }
            })
        },

        //设置实验核查人员
        company_auth_config_submit_btn: function (btn) {

            var user_id = this.getChoosedUserIds();
            var user_name = this.getChoosedUserNames();
            if (user_id.length == 0) {
                $.showAlert(mainLang('select_user'));
                return;
            }
            /*组装其他参数*/
            var auth_config_read = $('#auth_config_read').prop('checked') ? 1 : 0;
            var auth_config_write = $('#auth_config_write').prop('checked') ? 1 : 0;
            var decimal_config_read = $('#decimal_config_read').prop('checked') ? 1 : 0;
            var decimal_config_write = $('#decimal_config_write').prop('checked') ? 1 : 0;
            var group_config_read = $('#group_config_read').prop('checked') ? 1 : 0;
            var use_static_read = $('#use_static_read').prop('checked') ? 1 : 0;
            var company_dict_read = $('#company_dict_read').prop('checked') ? 1 : 0;
            var company_dict_write = $('#company_dict_write').prop('checked') ? 1 : 0;
            var login_log_read = $('#login_log_read').prop('checked') ? 1 : 0;
            var share_log_read = $('#share_log_read').prop('checked') ? 1 : 0;
            var view_log_read = $('#view_log_read').prop('checked') ? 1 : 0;
            var export_log_read = $('#export_log_read').prop('checked') ? 1 : 0;
            var set_log_read = $('#set_log_read').prop('checked') ? 1 : 0;
            var set_conmany_template_write = $('#set_conmany_template_write').prop('checked') ? 1 : 0;
            var book_manage_read = $('#book_manage_read').prop('checked') ? 1 : 0; // ADD BY HKK 2019/9/30 记录本管理权限
            var book_manage_write = $('#book_manage_write').prop('checked') ? 1 : 0; // ADD BY HKK 2019/9/30 记录本管理权限
            var instruments_manage_read = $('#instruments_manage_read').prop('checked') ? 1 : 0; // ADD BY HKK 2019/10/25 仪器库管理权限
            var instruments_manage_write = $('#instruments_manage_write').prop('checked') ? 1 : 0; // ADD BY HKK 2019/10/25 仪器库管理权限
            var company_general_read = $('#company_general_read').prop('checked') ? 1 : 0;   // add by hkk 2019/12/4 企业通用设置
            var company_general_write = $('#company_general_write').prop('checked') ? 1 : 0;  // add by hkk 2019/12/4 企业通用设置
            var that = this;

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/set-company-auth-config-submit',
                data: {
                    user_id: user_id,
                    user_name: user_name,
                    auth_config_read: auth_config_read,
                    auth_config_write: auth_config_write,
                    decimal_config_read: decimal_config_read,
                    decimal_config_write: decimal_config_write,
                    group_config_read: group_config_read,
                    use_static_read: use_static_read,
                    company_dict_read: company_dict_read,
                    company_dict_write: company_dict_write,
                    book_manage_read:  book_manage_read,  // ADD BY HKK 2019/9/30 记录本管理权限
                    book_manage_write: book_manage_write, // ADD BY HKK 2019/9/30 记录本管理权限
                    instruments_manage_read: instruments_manage_read, // ADD BY HKK 2019/10/25 仪器库管理权限
                    instruments_manage_write: instruments_manage_write, // ADD BY HKK 2019/10/25 仪器库管理权限
                    login_log_read: login_log_read,
                    share_log_read: share_log_read,
                    view_log_read: view_log_read,
                    export_log_read: export_log_read,
                    set_log_read: set_log_read,
                    set_conmany_template_write: set_conmany_template_write,
                    company_general_read: company_general_read,
                    company_general_write: company_general_write,
                },
                success: function (data) {
                    if (data.status == 1) {
                        //that.compmanySettingAuth();
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        require('tab').reloadActiveTag(); //刷新当前标签页
                    }
                }
            })
        },

        // 群成员配置权限(查看)
        set_group_auth_config: function (btn) {
            var user_id = this.getChoosedUserIds();
            if (user_id.length == 0) {
                $.showAlert(mainLang('select_user'));
                return;
            }

            $.ajaxFn({
                url: ELN_URL + "?r=group-setting/set-group-auth-config",
                type: "post",
                data: {
                    user_id: user_id,

                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                    }
                }
            })
        },

        // 群成员配置权限(提交)
        group_auth_config_submit_btn: function (btn) {
            var userId = this.getChoosedUserIds();
            var userName = this.getChoosedUserNames();
            if (userId.length == 0) {
                $.showAlert(mainLang('select_user'));
                return;
            }

            var enabledAuth = [];
            var disabledAuth = [];
            $('.group-member-auth-setting-wrap .auth-item-checkbox').each(function () {
                if ($(this).prop('checked')) {
                    enabledAuth.push($(this).attr('id'));
                } else {
                    disabledAuth.push($(this).attr('id'));
                }
            });

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/set-group-auth-config-submit',
                data: {
                    user_id: userId,
                    user_name: userName,
                    enabled_auth: enabledAuth,
                    disabled_auth: disabledAuth
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        require('tab').reloadActiveTag(); //刷新当前标签页
                    }
                }
            });
        },

        // add by szq 2020/6/12 批量方法模板确认导入
        batch_add_son_temp_submit_btn: function(btn) {
            //上传完成 开始处理
            var file_info = {};
            file_info.dep_path = $("#dep_path").val();
            file_info.save_name = $("#save_name").val();
            file_info.file_name = $("#file_name").val();
            if (file_info.dep_path === "") {
                $.showAlert(mainLang('please_upload_file'));
                return
            }
            $.ajaxFn({
                url: ELN_URL + '?r=template/batch-add-son-temp-submit',
                data: file_info,
                type: 'POST',
                success: function (data) {
                    if (1 == data.status) {
                        $.showAlert(mainLang('add_success'));
                        $.closeModal();
                        require('tab').reloadActiveTag(); //刷新当前标签页
                    } else {
                        $.showAlert(mainLang('upload_ext_error'));
                    }
                }
            });
        },

        // 物料表小数点设置
        submit_decimal_form: function (btn) {
            var data = $.formSerializeFn($(".exp_conetnt.active #set_decimal_form"));
            //验证里面的参数都需要为整数
            var re = /^\+?[1-9]\d*$/;
            var reactant_eq = $(".exp_conetnt.active .reactant_eq").val();
            if (!re.test(reactant_eq)) {
                $.showAlert("Reactant Eq " + jsLang['must_be_a_number']);
                return false;
            }
            var reactant_n = $(".exp_conetnt.active .reactant_n").val();
            if (!re.test(reactant_n)) {
                $.showAlert("Reactant N " + jsLang['must_be_a_number']);
                return false;
            }
            var reactant_mw = $(".exp_conetnt.active .reactant_mw").val();
            if (!re.test(reactant_mw)) {
                $.showAlert("Reactant MW " + jsLang['must_be_a_number']);
                return false;
            }
            var reactant_mass = $(".exp_conetnt.active .reactant_mass").val();
            if (!re.test(reactant_mass)) {
                $.showAlert("Reactant Mass " + jsLang['must_be_a_number']);
                return false;
            }
            var reactant_d = $(".exp_conetnt.active .reactant_d").val();
            if (!re.test(reactant_d)) {
                $.showAlert("Reactant D " + jsLang['must_be_a_number']);
                return false;
            }
            var reactant_v = $(".exp_conetnt.active .reactant_v").val();
            if (!re.test(reactant_v)) {
                $.showAlert("Reactant V " + jsLang['must_be_a_number']);
                return false;
            }
            var reagent_eq = $(".exp_conetnt.active .reagent_eq").val();
            if (!re.test(reagent_eq)) {
                $.showAlert("Reagent Eq " + jsLang['must_be_a_number']);
                return false;
            }
            var reagent_n = $(".exp_conetnt.active .reagent_n").val();
            if (!re.test(reagent_n)) {
                $.showAlert("Reagent N " + jsLang['must_be_a_number']);
                return false;
            }
            var reagent_mw = $(".exp_conetnt.active .reagent_mw").val();
            if (!re.test(reagent_mw)) {
                $.showAlert("Reagent MW " + jsLang['must_be_a_number']);
                return false;
            }
            var reagent_mass = $(".exp_conetnt.active .reagent_mass").val();
            if (!re.test(reagent_mass)) {
                $.showAlert("Reagent Mass " + jsLang['must_be_a_number']);
                return false;
            }
            var reagent_d = $(".exp_conetnt.active .reagent_d").val();
            if (!re.test(reagent_d)) {
                $.showAlert("Reagent D " + jsLang['must_be_a_number']);
                return false;
            }
            var reagent_v = $(".exp_conetnt.active .reagent_v").val();
            if (!re.test(reagent_v)) {
                $.showAlert("Reagent V " + jsLang['must_be_a_number']);
                return false;
            }
            var product_n = $(".exp_conetnt.active .product_n").val();
            if (!re.test(product_n)) {
                $.showAlert("Product N " + jsLang['must_be_a_number']);
                return false;
            }
            var product_mw = $(".exp_conetnt.active .product_mw").val();
            if (!re.test(product_mw)) {
                $.showAlert("Product MW " + jsLang['must_be_a_number']);
                return false;
            }
            var product_theo_mass = $(".exp_conetnt.active .product_theo_mass").val();
            if (!re.test(product_theo_mass)) {
                $.showAlert("Product Theo Mass " + jsLang['must_be_a_number']);
                return false;
            }
            var product_yield = $(".exp_conetnt.active .product_yield").val();
            if (!re.test(product_yield)) {
                $.showAlert("Product Yield " + jsLang['must_be_a_number']);
                return false;
            }
            const solvent_eq = $('.solvent_eq', '.exp_conetnt.active').val();
            if (!re.test(solvent_eq)) {
                $.showAlert(`Solvent Eq ${jsLang['must_be_a_number']}`);
                return false;
            }
            const solvent_n = $('.solvent_n', '.exp_conetnt.active').val();
            if (!re.test(solvent_n)) {
                $.showAlert(`Solvent N ${jsLang['must_be_a_number']}`);
                return false;
            }
            const solvent_mw = $('.solvent_mw', '.exp_conetnt.active').val();
            if (!re.test(solvent_mw)) {
                $.showAlert(`Solvent MW ${jsLang['must_be_a_number']}`);
                return false;
            }
            var solvent_b = $(".exp_conetnt.active .solvent_b").val();
            if (!re.test(solvent_b)) {
                $.showAlert("Solvent B " + jsLang['must_be_a_number']);
                return false;
            }
            var solvent_mass = $(".exp_conetnt.active .solvent_mass").val();
            if (!re.test(solvent_mass)) {
                $.showAlert("Solvent Mass " + jsLang['must_be_a_number']);
                return false;
            }
            var solvent_d = $(".exp_conetnt.active .solvent_d").val();
            if (!re.test(solvent_d)) {
                $.showAlert("Solvent D " + jsLang['must_be_a_number']);
                return false;
            }
            var solvent_bp = $(".exp_conetnt.active .solvent_bp").val();
            if (!re.test(solvent_bp)) {
                $.showAlert("Solvent b.p. " + jsLang['must_be_a_number']);
                return false;
            }
            //验证完成
            /*组装其他参数*/

            var companyData = data;

            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/company-setting-decimal-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();

                        window.companyDecimalSetting = {
                            reactant: {
                                "eq": companyData["reactant_eq"],
                                "n": companyData["reactant_n"],
                                "mw": companyData["reactant_mw"],
                                "mass": companyData["reactant_mass"],
                                "d": companyData["reactant_d"],
                                "v": companyData["reactant_v"],
                            },
                            reagent: {
                                "eq": companyData["reagent_eq"],
                                "n": companyData["reagent_n"],
                                "mw": companyData["reagent_mw"],
                                "mass": companyData["reagent_mass"],
                                "d": companyData["reagent_d"],
                                "v": companyData["reagent_v"],
                            },
                            solvent: {
                                'eq': companyData['solvent_eq'],
                                'n': companyData['solvent_n'],
                                'mw': companyData['solvent_mw'],
                                "b": companyData["solvent_b"],
                                "mass": companyData["solvent_mass"],
                                "d": companyData["solvent_d"],
                                "bp": companyData["solvent_bp"],
                            },
                            product: {
                                "n": companyData["product_n"],
                                "mw": companyData["product_mw"],
                                "theo_mass": companyData["product_theo_mass"],
                                "yield": companyData["product_yield"],
                            },
                        };

                        window.decimalPlaces = {

                            // 反应物小数位
                            R_Eq: parseInt(window.companyDecimalSetting["reactant"]["eq"]),      // 基准数
                            R_N: parseInt(window.companyDecimalSetting["reactant"]["n"]),       // 物质的量mol
                            R_MW: parseInt(window.companyDecimalSetting["reactant"]["mw"]),      // 分子量
                            R_Mass: parseInt(window.companyDecimalSetting["reactant"]["mass"]),    // 质量
                            R_D: parseInt(window.companyDecimalSetting["reactant"]["d"]),       // 密度
                            R_V: parseInt(window.companyDecimalSetting["reactant"]["v"]),       // 体积
                            R_C: 3,       // 浓度
                            R_Purity: 2,  // 纯度

                            // 催化剂小数位
                            C_Eq: parseInt(window.companyDecimalSetting["reagent"]["eq"]),      // 基准数
                            C_N:  parseInt(window.companyDecimalSetting["reagent"]["n"]),        // 物质的量mol
                            C_MW:  parseInt(window.companyDecimalSetting["reagent"]["mw"]),       // 分子量
                            C_Mass:  parseInt(window.companyDecimalSetting["reagent"]["mass"]),     // 质量
                            C_D:  parseInt(window.companyDecimalSetting["reagent"]["d"]),        // 密度
                            C_V:  parseInt(window.companyDecimalSetting["reagent"]["v"]),       // 体积
                            C_C: 3,       // 浓度
                            C_Purity: 2,  // 纯度

                            // 溶剂小数位
                            S_Ratio: parseInt(window.companyDecimalSetting["solvent"]["bp"]),    // 溶剂体积比
                            S_Mass: parseInt(window.companyDecimalSetting["solvent"]["mass"]),     // 质量
                            S_D: parseInt(window.companyDecimalSetting["solvent"]["d"]),        // 密度
                            S_V: parseInt(window.companyDecimalSetting["solvent"]["b"]),        // 体积

                            // 产物小数位
                            P_N: parseInt(window.companyDecimalSetting["product"]["n"]),       // 物质的量mol
                            P_MW: parseInt(window.companyDecimalSetting["product"]["mw"]),      // 分子量
                            P_Mass: parseInt(window.companyDecimalSetting["product"]["theo_mass"]),     // 质量
                            P_Yield: parseInt(window.companyDecimalSetting["product"]["yield"]),   // 收率
                            P_Purity: 2   // 纯度

                        };

                    }
                }
            })
        },

        // 重置物料表小数点
        reset_decimal_form: function (btn) {
            //changed by xieyuxiang 2022.11.3 物料表小数点页面的重置需求改为重置回最最最原始的版本而不是重置回上一次保存的版本
            $(".exp_conetnt.active #set_decimal_form").find('input.text').each(function () {
                $(this).attr('value',$(this).attr('data-default_value'));
                $(this).val($(this).attr('data-default_value'));
            })
        },

        //add by wy 2023/3/16 物料表设置-中英文物料名称设置
        submit_iupac_show_setting: function (btn) {
            var iupac_setting = $(".exp_conetnt.active input[name=iupac_show_setting]:checked").val();
            var data = {
                'iupac_setting': iupac_setting,
            }
            $.ajaxFn({
                url: ELN_URL + '?r=group-setting/company-iupac-setting-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();

                    }
                }
            })
        }
    }

    var printPageSetting = {
        //页面设置
        page_setting: function () {
            $.ajaxFn({
                url: ELN_URL + '?r=setting/print-logo-view',
                data: {
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                    type: 'page'
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".print_page_set").remove();
                        $("body").append(data.data.html);
                        $(".print_page_set").modal('show');
                    }
                }
            })
        },
        //切换tab
        changePrintTab: function (btn) {
            btn.addClass('active').siblings('.tab-hd').removeClass('active');

            $(btn.attr('target')).addClass('active').siblings('.print-tab-con').removeClass('active');

        },
        //获取模板，替换下面的内容。
        getTepl: function (groupid) {
            var url = ELN_URL + '?r=setting/get-logo';
            var that = this;
            $.ajaxFn({
                url: url,
                type: 'get',
                data: {
                    group_id: groupid,
                    type: 'page'
                },
                success: function (data) {
                    if (1 == data.status) {
                        $(".line-tab-con").replaceWith(data.data.html);

                        $('.tepl_ul').hide();
                        // $(".trangle-print").hide();

                        $(".line-tab-box .tab-hd:eq(0)").trigger('click');
                    }
                }
            })
        },

        //事件绑定
        page_bindEvent: function () {
            var that = this;
            // 以下注释的都是重复代码（与print_setting.js重复）
            // //检测电话输入。以及同步更新右侧
            // $("body").on('blur', '.phone', function (event) {
            //     var tel = $(this).val();
            //     if (tel == '') {
            //         $(this).nextAll(".errorMsg").html('');
            //         $(".print_tel_span").html(tel).parent().hide();
            //         return;
            //     }
            //     var telRule = /^[\d\s]+$/;
            //     var res = telRule.test(tel);
            //     if (!res) {
            //         $(this).nextAll(".errorMsg").html(mainLang('phone_error'));
            //     } else {
            //         $(this).nextAll(".errorMsg").html('');
            //         $(".print_tel_span").html(tel).parent().show();
            //     }
            //
            // });

            // //检测网址输入。以及同步更新右侧
            // $("body").on('blur', 'input[name=company_site]', function (event) {
            //     var val = $(this).val();
            //     if (val == '') {
            //         $(this).nextAll(".errorMsg").html('');
            //         $(".print_url_span").html(val).parent().hide();
            //         return;
            //     }
            //     $(this).nextAll(".errorMsg").html('');
            //     $(".print_url_span").html(val).parent().show();
            // });
            //
            // //点击有页脚无页脚的切换
            // $("body").on('click', 'input[name=foot-checkbox]', function (event) {
            //     var checkVal = $('input[name=foot-checkbox]:checked').val();
            //     if (checkVal == 1) {
            //         $(".foot-con").slideDown('400');
            //         $(".print-con-foot").show();
            //     } else if (checkVal == 2) {
            //         $(".foot-con").slideUp('400');
            //         $(".print-con-foot").hide();
            //     }
            // });
            //
            // //提交页面所有信息
            // $("body").on('click', '#edit-setting-btn', function (event) {
            //     that.page_submit();
            // });
            //
            // //模板列表 标签显示
            // $('body').on('click', '.my_exp_detial.listmodule.setting_detail', function (event) {
            //     var theDom = $(this);
            //
            //     saveExperiment(function () {
            //         var obj = event.srcElement ? event.srcElement : event.target;
            //         if ($(obj).hasClass('close')) {
            //             return;
            //         }
            //         var groupId = that.getTabGroup();
            //         that.open(theDom, groupId);
            //     });
            // });

            //点击模板，重新获取页面
            $("body").on('click', '.tem_ul span:not(.on)', function (event) {
                var groupId = $(this).attr('data-id');
                $(this).addClass('on').siblings().removeClass('on');
                //获取页面信息。
                that.getTepl(groupId);
            });

            //点确定。通过canvas获取base64数据。上传。
            $("body").on('click', '.cut_logo_btn', function () {
                that.fileUpload();
            });

            //点击弹出裁剪图片的弹出框。并初始化剪切工具
            $('body').on('click', '.setting-logo-btn', function (event) {
                that.page_loadModal();
                //初始化的时候清空fileData;
                that.uploadFileData = null;

                $(".cut_logo_modal").modal('show');
                that.startCropper();
            });

            $('body').on('click', '.more_btn', function (event) {
                const box = $('.more_wrapper')
                if (box.hasClass('unfolded')) {
                    box.removeClass('unfolded');
                } else {
                    box.addClass('unfolded');
                }
            });

            function markNodes(treeObj, filter) {
                var allNodes = treeObj.transformToArray(treeObj.getNodes());
                for (var i = 0; i < allNodes.length; i++) {
                    allNodes[i].highlight = false;
                    treeObj.updateNode(allNodes[i]);
                }

                if (filter === '') {
                    return;
                }

                var nodes = treeObj.getNodesByParamFuzzy('name', filter);
                for (var i = 0; i < nodes.length; i++) {
                    nodes[i].highlight = true;
                    treeObj.updateNode(nodes[i]);
                    treeObj.expandNode(nodes[i].getParentNode(), true);
                }
            }
            $('body').on('input', '.project_box .search-box .search-input', function () {
                var treeBox = $(this).parent().next('.tree_box');
                var treeId = treeBox.attr('id');
                var treeObj = $.fn.zTree.getZTreeObj(treeId);
                var filter = $(this).val().trim();
                markNodes(treeObj, filter);
            });
            $('body').on('click', '.tree-check', function () {
                $(this).next().find('.search-input').val('');
                var treeId = $(this).next().find('.tree_box ').attr('id');
                var treeObj = $.fn.zTree.getZTreeObj(treeId);
                markNodes(treeObj, '');
            })
            $('body').on('change', '#share_with_approval', function (event) {
                if (!$('#share_with_approval').prop('checked')){
                    $('#send_approval_email').removeProp('checked');
                    $('.more_settings').hide();
                } else {
                    $('.more_settings').show();
                }
            })


                    //选择一个文件的操作,上传文件。
            require(['bin/ajaxfileupload'], function () {
                $("body").on('click', '.upload-print-logo', function (event) {
                    $(this).ajaxfileupload({
                        action: ELN_URL + '?r=upload/upload-file',
                        type: 'post',
                        params: {
                            type: 1
                        },
                        validate_extensions: false,
                        onComplete: function (data) {
                            $.closeLoading();
                            if (data.status == '1') {
                                var file = data.data;
                                //判断文件类型
                                var fileType = file.file_type;
                                if (!(fileType == 'image/jpeg' || fileType == 'image/jpg' || fileType == 'image/png')) {
                                    $.showAlert(mainLang('file_type_error'));
                                    return false;
                                }

                                //判断文件大小
                                if (Math.ceil(file.file_size / 1024) > 5120) {
                                    $.showAlert(mainLang('file_more_5m'));
                                    return false;
                                }

                                that.url = file.img_url;
                                that.uploadFileData = file;
                                that.startCropper();
                            }
                        },
                        onStart: function (a) {
                            var dom = this;

                            if (dom[0].files && dom[0].files[0]) {
                                if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                                    $.showAlert(mainLang('file_too_big'));
                                    setTimeout(function () {
                                        $.closeLoading();
                                    }, 100);
                                    return false;
                                }
                            }
                            $.loading();
                        },
                    });
                });
            });

            //选择一个文件的操作,上传文件。
            require(['bin/ajaxfileupload'], function () {
                $("body").on('click', '.upload-company-dict', function (event) {
                    $(this).ajaxfileupload({
                        action: ELN_URL + '?r=upload/upload-file',
                        type: 'post',
                        params: {
                            type: 2
                        },
                        validate_extensions: false,
                        onComplete: function (data) {
                            $.closeLoading();
                            if (data.status == '1') {
                                var file = data.data;
                                //判断文件类型
                                var fileType = file.file_type;
                                console.log(fileType);
                                var filename = file.save_name;
                                var index = filename.lastIndexOf(".");
                                var suffix = filename.substring(index+1);
                                //判断用后缀不用类型防止 wps生成的文件头 application/vnd.ms-office application/octet-stream 不被识别
                                if( suffix != 'xls' && suffix != 'xlsx'){
                                    $.showAlert(mainLang('only_access_xls'));
                                    return false;
                                }
//                                if (!(fileType == 'application/vnd.ms-excel' || fileType == 'application/zip' || fileType == 'application/vnd.ms-excel' || fileType == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || fileType == 'application/excel' || fileType == 'application/msexcel')) {
//                                    $.showAlert(mainLang('only_access_xls'));
//                                    return false;
//                                }

                                //判断文件大小
                                if (Math.ceil(file.file_size / 1024) > 5120) {
                                    $.showAlert(mainLang('file_more_5m'));
                                    return false;
                                }

                                $(".exp_conetnt.active #dep_path").val(file.dep_path);
                                $(".exp_conetnt.active #save_name").val(file.save_name);
                                $(".exp_conetnt.active #file_name").val(file.file_name);
                                $(".exp_conetnt.active #temp_file_name").html(file.file_name);

                                that.url = file.img_url;
                                that.uploadFileData = file;
                            }
                        },
                        onStart: function (a) {
                            var dom = this;

                            if (dom[0].files && dom[0].files[0]) {
                                if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                                    $.showAlert(mainLang('file_too_big'));
                                    setTimeout(function () {
                                        $.closeLoading();
                                    }, 100);
                                    return false;
                                }
                            }
                            $.loading();
                        },
                    });
                });

                // add by hkk 2020/8/5 分离我的词库上传文件
                $("body").on('click', '.upload-my-dict', function (event) {
                    $(this).ajaxfileupload({
                        action: ELN_URL + '?r=upload/upload-file',
                        type: 'post',
                        params: {
                            type: 2
                        },
                        validate_extensions: false,
                        onComplete: function (data) {
                            $.closeLoading();
                            if (data.status == '1') {
                                var file = data.data;
                                //判断文件类型
                                var fileType = file.file_type;
                                console.log(fileType);
                                var filename = file.save_name;
                                var index = filename.lastIndexOf(".");
                                var suffix = filename.substring(index+1);
                                //判断用后缀不用类型防止 wps生成的文件头 application/vnd.ms-office application/octet-stream 不被识别
                                if( suffix != 'xls' && suffix != 'xlsx'){
                                    $.showAlert(mainLang('only_access_xls'));
                                    return false;
                                }
//                                if (!(fileType == 'application/vnd.ms-excel' || fileType == 'application/zip' || fileType == 'application/vnd.ms-excel' || fileType == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || fileType == 'application/excel' || fileType == 'application/msexcel')) {
//                                    $.showAlert(mainLang('only_access_xls'));
//                                    return false;
//                                }

                                //判断文件大小
                                if (Math.ceil(file.file_size / 1024) > 5120) {
                                    $.showAlert(mainLang('file_more_5m'));
                                    return false;
                                }

                                $(".modal-body #dep_path").val(file.dep_path);
                                $(".modal-body #save_name").val(file.save_name);
                                $(".modal-body #file_name").val(file.file_name);
                                $(".modal-body #temp_file_name").html(file.file_name);

                                that.url = file.img_url;
                                that.uploadFileData = file;
                            }
                        },
                        onStart: function (a) {
                            var dom = this;

                            if (dom[0].files && dom[0].files[0]) {
                                if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                                    $.showAlert(mainLang('file_too_big'));
                                    setTimeout(function () {
                                        $.closeLoading();
                                    }, 100);
                                    return false;
                                }
                            }
                            $.loading();
                        },
                    });
                });
            });
            //绑定提交事件
            $("body").on('click', '.submit-company-dict', function (event) {
                var data = $.formSerializeFn($(".upload_form_for_company_dict"));
                console.log(data);
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/submit-company-dict',
                    data: data,
                    type: 'POST',
                    success: function (data) {
                        if (1 == data.status) {
                            $.showAlert(mainLang('save_success'));
                            $.closeModal();
                            // module.companyDict();
                        }
                    }
                });
            });

            //绑定排序事件
            $("body").on('click', '.search-user-static-order', function (event) {
                console.log();
                $('#sort').val($(this).attr('data-sort'));
                $('#order').val($(this).attr('data-order'));

                var data = $.formSerializeFn($("#search-user-static"));
                $.ajaxFn({
                    url: ELN_URL + '?r=group-setting/use-static',
                    data: data,
                    type: 'POST',
                    success: function (data) {
                        if (1 == data.status) {
                            $('.exp_conetnt').removeClass('search');

                            $('.exp_data_box').html(data.data.file);
                            $('.tool_data_box').html('');
                            //只在第一次请求的时候设置default_page_size;
                            if (!that.default_page_size) {
                                that.default_page_size = $(".page_box").attr('data-limit');
                            }

                            that.pageFn();
                            $(window).scrollTop(0);

                            //对placeholder的兼容处理。
                            $('input[placeholder]:visible').placeholder();
                            /*初始化日历*/
                            var dateOpts = {
                                format: 'yyyy-mm-dd',
                                autoclose: true,
                                minView: 2,
                                clearBtn: true,
                            };

                            $.fn.datetimepicker ? $('.use-static .datetimepicker').datetimepicker(dateOpts).on('click', function () {

                                if ($('.use-static [name="end_time"]').val() != '') {
                                    var startTimer = $('.use-static [name="end_time"]').val() + ' 01:00';
                                    $('.use-static [name="start_time"]').datetimepicker('setEndDate', startTimer);
                                }

                                if ($('.use-static [name="start_time"]').val() != '') {
                                    var endTimer = $('.use-static [name="start_time"]').val() + ' 01:00';
                                    $('.use-static [name="end_time"]').datetimepicker('setStartDate', endTimer);
                                }
                            }) : '';
                        }
                    }
                });
            });




            // //绑定记录本档案搜索提交事件 add by hkk 2019/9/29
            // $("body").on('click', '.search-book-file-manage', function (event) {
            //
            //     // 获取查询条件和 页数信息
            //     var group_ids,user_ids,book_status;
            //
            //     if($("#search-book-file-manage #group_ids").attr('idbox')){
            //         group_ids = $("#search-book-file-manage #group_ids").attr('idbox').split(',');
            //     }else{
            //         group_ids = []; // 为空相当于全选
            //     }
            //
            //     if($("#search-book-file-manage #user_ids").attr('idbox')){
            //         user_ids = $("#search-book-file-manage #user_ids").attr('idbox').split(',')
            //     }else{
            //         user_ids = []; // 为空相当于全选
            //     }
            //
            //     if($("#search-book-file-manage #book_status").val() =='2'){
            //         book_status = ['0','1'];
            //     }else{
            //         book_status = $("#search-book-file-manage #book_status").val();
            //     }
            //
            //
            //     var data = {
            //         group_ids:group_ids,
            //         user_ids:user_ids,
            //         book_status:book_status,
            //         page:1,
            //         limit:$('.pager-select:visible').val() || this.default_page_size || undefined,
            //     };
            //
            //     console.log(data)
            //     $.ajaxFn({
            //         url: ELN_URL + '?r=group-setting/book-file-manage',
            //         data: data,
            //         type: 'POST',
            //         success: function (data) {
            //             if (1 == data.status) {
            //                 $('.exp_conetnt').removeClass('search');
            //
            //                 $('.eln_setting_table').html(data.data.file);
            //
            //                 $('.tool_data_box').html('');
            //
            //                 //只在第一次请求的时候设置default_page_size;
            //                 if (!that.default_page_size) {
            //                     that.default_page_size = $(".page_box").attr('data-limit');
            //                 }
            //
            //                 that.pageFn();
            //                 $(window).scrollTop(0);
            //
            //                 //对placeholder的兼容处理。
            //                 $('input[placeholder]:visible').placeholder();
            //             }
            //         }
            //     });
            // });


     /*       //绑定仪器库搜索提交事件 add by hkk 2019/10/30
            $("body").on('click', '.search-instrument-manage', function (event) {

                // 获取查询条件和页数信息
                var data = $.formSerializeFn($(".exp_conetnt.active #search-instrument-manage"));
                data['page'] = 1;
                data['limit'] = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
                data['needUpdateAllPage'] = 0;
                data['type'] = module.type;
                module.upData.page = 1;

                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/manage',
                    data: data,
                    type: 'POST',
                    success: function (data) {
                        if (1 == data.status) {
                            $('.exp_conetnt').removeClass('search');

                            $('.exp_conetnt.active .instruments_table').html(data.data.file);

                        }
                    }
                });
            });*/

        /*    //绑定仪器库我的预约界面搜索提交事件 add by hkk 2019/10/30
            $("body").on('click', '.search-my-instrument-book', function (event) {

                // 获取查询条件和页数信息
                var data = $.formSerializeFn($(".exp_conetnt.active #search-my-instrument-book"));
                data['page'] = 1;
                data['limit'] = $('.exp_conetnt.active .pager-select:visible').val() || this.default_page_size || undefined;
                data['needUpdateAllPage'] = 0;
                data['type'] = module.type;
                module.upData.page = 1;


                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/my-book',
                    data: data,
                    type: 'POST',
                    success: function (data) {
                        if (1 == data.status) {
                            $('.exp_conetnt').removeClass('search');
                            $('.exp_conetnt.active .instruments_book_table').html(data.data.file);

                        }
                    }
                });
            });*/

            window.errorImg = function (obj) {
                obj.src = '/image/error.png';
            };
        },
        //提交全部
        page_submit: function () {
            var that = this;
            var data = $.formSerializeFn($(".page_print_setting"));

            var checkFoot = $("input[name=foot-checkbox]:checked").val();
            //无页脚
            if (checkFoot == 2) {
                delete data['company_site'];
                delete data['company_tel'];
                delete data['company_name'];
            }
            delete data['foot-checkbox'];
            delete data['print-group'];

            //要输入页脚的时候就要检查这些输入
            if (checkFoot == 1) {
                var errorMsgDoms = $(".page_print_setting .errorMsg");
                //检查输入
                for (var i = errorMsgDoms.length - 1; i >= 0; i--) {
                    if (errorMsgDoms.eq(i).html() != '') {
                        $.showAlert(mainLang('check_input'));
                        return false;
                    }
                }
            }

            //鹰群
            var groupIdArr = $("#curId").val();
            var url ='?r=setting/set-print-logo';
            console.log($("#is_general").val());
            if($("#is_general").val() == 1){
                url = '?r=setting/general-set-print-logo'
            }

            // var checkLen = groupIdArr.length;
            // if (checkLen == 0) {
            // 	$.showAlert(mainLang('select_group'));
            // 	return;
            // }
            data.group_id = [groupIdArr];

            $.ajaxFn({
                url: ELN_URL + url,
                data: data,
                success: function (data) {
                    if (1 == data.status) {
                        $.showAlert(mainLang('save_success'));
                        $.closeModal();
                    }
                }
            })
        },
        page_close: function () {
            $.closeModal();
        },
        //加载剪切图片的modal。
        page_loadModal: function () {
            var src = $(".setting-logo-img").attr('src');
            this.url = src;
            //载入模板
            if (!this.indrawTemp) {
                this.indrawTemp = _.template(require('text!popup/logo_cut_modal.html'))();
            }

            if ($(".cut_logo_modal").length < 1) {
                $("body").append(this.indrawTemp);
            }
            //因为每次modal都被删除了，所以要重新设置。
            this.$container = $("#cropper");
            this.$avatarWrapper = this.$container.find('.cut_container');
            this.$avatarPreview = this.$container.find('.avatar-preview');

            this.$avatarPreview.find('img').attr('src', src);
        },

        //初始化cropper插件。
        startCropper: function () {
            var _this = this;

            this.$img = $('<img src="' + this.url + '">');
            this.$avatarWrapper.html(this.$img);

            this.$img.cropper({
                aspectRatio: 131 / 52,
                preview: _this.$avatarPreview.selector,
                strict: false,
                autoCropArea: 1, //定义自动剪裁区域的大小
                // mouseWheelZoom:false,
                crop: function (data) {
                    _this.json = {
                        'x1': data.x,
                        'y1': data.y,
                        'h': data.height,
                        'w': data.width
                    };
                }
            });

        },
        //停止插件
        stopCropper: function () {
            if (this.active) {
                this.$img.cropper('destroy');
                this.$img.remove();
                this.active = false;
            }
        },
        //上传base64文件。剪切后的图片上传。
        fileUpload: function () {
            var that = this;
            var upData = that.json;

            var oriName; //文件名。
            if (that.uploadFileData) {
                upData.deep_path = that.uploadFileData.dep_path;
                upData.save_name = that.uploadFileData.save_name;
                oriName = that.uploadFileData.file_name;
            } else {
                var $infoWrap = $(".logo-input");
                upData.deep_path = $.trim($infoWrap.find('[name=dep_path]').val());
                upData.save_name = $.trim($infoWrap.find('[name=save_name]').val());
                oriName = $.trim($infoWrap.find('[name=file_name]').val());
            }

            if (!upData.deep_path) {
                $.showAlert('请上传图片！');
            }

            var canvas = this.$img.cropper('getCroppedCanvas');
            upData.base64url = canvas.toDataURL('image/png', 1);
            if (upData.base64url) {
                upData.save_name = upData.save_name.slice(0, upData.save_name.lastIndexOf(".") + 1) + 'png';
            }

            $.ajaxFn({
                url: ELN_URL + '?r=upload/cut-image',
                data: upData,
                success: function (data) {
                    if (1 == data.status) {
                        var inputHtml = '';
                        inputHtml += '<input type="hidden" name="dep_path" value="' + upData.deep_path + '" />';
                        inputHtml += '<input type="hidden" name="save_name" value="' + upData.save_name + '" />';
                        inputHtml += '<input type="hidden" name="file_name" value="' + oriName + '" />';

                        //要上传的部分。
                        $(".logo-input").html(inputHtml);

                        //修改页面上的两个logo图标。
                        $(".setting-logo-img").attr('src', data.data);

                        //隐藏模态框放在最后。因为隐藏之后。这个模态框就被删除了。
                        $(".cut_logo_modal").modal('hide');
                    }
                }
            })
        },

        //设置企业级权限
        set_company_auth: function (btn) {
            var type = $(btn).attr('type');
            if(type == 'batch'){
                var inputLen = $('.exp_conetnt.active .eln_setting_table_temp input:not(#checkbox_all):checked').length;
                if(inputLen==0){
                    $.showAlert(mainLang('select_user'));
                    return;
                }
                var ids = new Array();
                //循环所有选中的值
                $('.exp_conetnt.active .eln_setting_table_temp input:not(#checkbox_all):checked').each(function(index, element) {
                    //追加到数组中
                    //Bug#36768 plug环境，eln-管理设置-企业级权限-权限管理-搜索用户后，给用户分配企业级权限，服务器内部错误
                    if ($(this).parents("tr").attr("data-user_id")) {
                        ids.push($(this).parents("tr").attr("data-user_id"));
                    }
                });

            }
            if(type == 'single'){
                var ids = new Array();
                ids.push(btn.parents("tr").attr("data-user_id"));
            }


                       // 如果选择的大于一个。给该按钮加上disabled。

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-company-auth',
                data: {
                    user_id:  ids,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级权限
        set_company_auth_submit: function (btn) {
            var data = {
                user_id:$(btn).attr('data-user_id'),
                view_company_auth:$('.view_company_auth').prop('checked') ? 1 : 0,
                edit_company_auth:$('.edit_company_auth').prop('checked') ? 1 : 0,
                view_company_role:$('.view_company_role').prop('checked') ? 1 : 0,
                edit_company_role:$('.edit_company_role').prop('checked') ? 1 : 0,
                view_set_check_user:$('.view_set_check_user').prop('checked') ? 1 : 0,
                edit_set_check_user:$('.edit_set_check_user').prop('checked') ? 1 : 0,

                login_log_read:$('.login_log_read').prop('checked') ? 1 : 0,
                share_log_read:$('.share_log_read').prop('checked') ? 1 : 0,
                view_log_read:$('.view_log_read').prop('checked') ? 1 : 0,
                export_log_read:$('.export_log_read').prop('checked') ? 1 : 0,
                set_log_read:$('.set_log_read').prop('checked') ? 1 : 0,
                change_book_group_log_read:$('.change_book_group_log_read').prop('checked') ? 1 : 0,

                share_to_visible_user: $('.share_to_visible_user').prop('checked') ? 1 : 0,
                share_to_visible_group_user: $('.share_to_visible_group_user').prop('checked') ? 1 : 0,
                share_to_visible_department_user: $('.share_to_visible_department_user').prop('checked') ? 1 : 0,

                use_static_read:$('.use_static_read').prop('checked') ? 1 : 0,
                set_conmany_template_write:$('.set_conmany_template_write').prop('checked') ? 1 : 0,
                view_full_template:$('.view_full_template').prop('checked') ? 1 : 0,
                edit_full_template:$('.edit_full_template').prop('checked') ? 1 : 0,
                view_function_template:$('.view_function_template').prop('checked') ? 1 : 0,
                edit_function_template:$('.edit_function_template').prop('checked') ? 1 : 0,
                view_decimal_config:$('.view_decimal_config').prop('checked') ? 1 : 0,
                edit_decimal_config:$('.edit_decimal_config').prop('checked') ? 1 : 0,
                view_company_dict:$('.view_company_dict').prop('checked') ? 1 : 0,
                edit_company_dict:$('.edit_company_dict').prop('checked') ? 1 : 0,
                view_book_manage:$('.view_book_manage').prop('checked') ? 1 : 0,
                edit_book_manage:$('.edit_book_manage').prop('checked') ? 1 : 0,
                view_instruments_manage:$('.view_instruments_manage').prop('checked') ? 1 : 0,
                view_other_inscada_data:$('.view_other_inscada_data').prop('checked') ? 1 : 0,
                view_instruments_manage_my:$('.view_instruments_manage_my').prop('checked') ? 1 : 0,
                edit_instruments_manage:$('.edit_instruments_manage').prop('checked') ? 1 : 0,
                visible_user_template_manage:$('.visible_user_template_manage').prop('checked') ? 1 : 0,
                company_template_manage:$('.company_template_manage').prop('checked') ? 1 : 0,
                edit_template_manage:$('.edit_template_manage').prop('checked') ? 1 : 0,
                view_company_general:$('.view_company_general').prop('checked') ? 1 : 0,
                edit_company_general:$('.edit_company_general').prop('checked') ? 1 : 0,
                edit_help:$('.edit_help').prop('checked') ? 1 : 0,

            };

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-company-auth-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        require('tab').reloadActiveTag(); //刷新当前标签页
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级权限
        set_group_auth_submit: function (btn) {
            //分享的参数
            var user_ids = $('.shareData #default-share-all #user_ids').attr('idbox');
            var checkArr = this.is_checked_now(".shareData .share-part");


            // add by hkk 2019/10/11 加上批量设置多个默认鹰群
            var group_ids = $('.shareData #default-share-all #group_ids').attr('idbox');


            var shareApprovalNodes = $('.shareData #share_with_approval').prop('checked') ? require('approval').getNodes($('.shareData .more_settings'),'',0) : []; // 审批节点，未勾选主动分享需要审批时为空
            if ($('.shareData #share_with_approval').prop('checked') && shareApprovalNodes == 0) {
                $.showAlert(mainLang('pls_set_approval_node'));
                return false;
            }

            var sendApprovalEmail = $('.shareData #send_approval_email').prop('checked') ? 1 : 0;

            var defaultShareByProject = $('.shareData #by-project-task').prop('checked') ? 1 : 0;

            //按项目/任务设置的默认分享鹰群、人员，
            var projectTaskSetting = {
                to_groups: [],
                to_users: []
            };
            $('.shareData #default-share-groups .def_share_item').each(function () { //分享到鹰群的每个设置
                var treeId = $(this).find('.tree_box').attr('id');
                var tree = $.fn.zTree.getZTreeObj(treeId);
                var nodes = tree.getCheckedNodes();
                var setting = {
                    projects: [],
                    tasks: [],
                    groups: []
                }
                nodes.forEach(function (node) {
                    if (node.type == 0) { //项目
                        setting.projects.push(node.id);
                    } else { //目标和任务
                        setting.tasks.push(node.id);
                    }
                });
                setting.projects.sort();
                setting.tasks.sort();
                var groupIdsStr = $(this).find('[name=group_ids]').attr('idbox');
                if (groupIdsStr) {
                    setting.groups = groupIdsStr.split(',').sort();
                }
                if (setting.projects.length + setting.tasks.length > 0 && setting.groups.length > 0) {
                    projectTaskSetting.to_groups.push(setting);
                }
            });
            $('.shareData #default-share-users .def_share_item').each(function () { //分享到人员的每个设置
                var treeId = $(this).find('.tree_box').attr('id');
                var tree = $.fn.zTree.getZTreeObj(treeId);
                var nodes = tree.getCheckedNodes();
                var setting = {
                    projects: [],
                    tasks: [],
                    users: []
                }
                nodes.forEach(function (node) {
                    if (node.type == 0) { //项目
                        setting.projects.push(node.id);
                    } else { //目标和任务
                        setting.tasks.push(node.id);
                    }
                });
                setting.projects.sort();
                setting.tasks.sort();
                var userIdsStr = $(this).find('[name=user_ids]').attr('idbox');
                if (userIdsStr) {
                    setting.users = userIdsStr.split(',').sort();
                }
                if (setting.projects.length + setting.tasks.length > 0 && setting.users.length > 0) {
                    projectTaskSetting.to_users.push(setting);
                }
            });


            var shareData = {
                group_id: $(btn).attr('data-group_id'),
                user_id: $(btn).attr('data-user_id'),
                is_group: 0,//批量也是0。鹰群设置才是1。
                share_to_user: checkArr[0],
                share_to_this_group: checkArr[1],
                share_to_my_group: checkArr[2],
                share_to_other_group: checkArr[3],
                share_with_approval: this.getCheckStatus($('.share-setting #share_with_approval')),
                group_ids: group_ids, // ADD BY HKK 2019/10/11 原来只有上面一个默认鹰群，改为多个
                user_ids: user_ids, //默认分享用户
                approver: shareApprovalNodes,
                send_approval_email: sendApprovalEmail,
                by_project_task: defaultShareByProject,
                project_task_setting: projectTaskSetting,
                coauthor_book: this.getCheckStatus($('.share-setting #coauthor_book')),
                coauthor_group_book: this.getCheckStatus($('.share-setting #coauthor_group_book')),
            };
            //群内权限
            var groupAuthData = {
                user_id:$(btn).attr('data-user_id'),
                group_id:$(btn).attr('data-group_id'),

                //群内权限的参数
                set_eln_manage:$('.set_eln_manage').prop('checked') ? 1 : 0,
                set_share_config:$('.set_share_config').prop('checked') ? 1 : 0,
                set_export_config:$('.set_export_config').prop('checked') ? 1 : 0,
                set_witness_config:$('.set_witness_config').prop('checked') ? 1 : 0,
                set_pretrial_config:$('.set_pretrial_config').prop('checked') ? 1 : 0,

                set_template_config:$('.set_template_config').prop('checked') ? 1 : 0,
                set_sign_config:$('.set_sign_config').prop('checked') ? 1 : 0,
                set_collaboration_config:$('.set_collaboration_config').prop('checked') ? 1 : 0,
                set_group_page_config:$('.set_group_page_config').prop('checked') ? 1 : 0,
                set_group_check_config:$('.set_group_check_config').prop('checked') ? 1 : 0,

                set_check_reopen_config:$('.set_check_reopen_config').prop('checked') ? 1 : 0,
                set_require_config:$('.set_require_config').prop('checked') ? 1 : 0,
                set_create_book_config:$('.set_create_book_config').prop('checked') ? 1 : 0,
                set_group_role_config:$('.set_group_role_config').prop('checked') ? 1 : 0,
                set_other_config:$('.set_other_config').prop('checked') ? 1 : 0,
                set_group_normal_config:$('.set_group_normal_config').prop('checked') ? 1 : 0,
            };

            //打印设置的参数
            var groupId = $(btn).attr('data-group_id');
            var userId = $(btn).attr('data-user_id');
            var checkArr = this.is_checked_now(".printData");
            var is_group = 0;

            var printData =  {
                    group_id: groupId,
                    user_id: userId,
                    is_group: is_group,
                    pdf_single: checkArr[1] ? 1 : checkArr[0],
                    pdf_book: checkArr[1],
                    word_single: checkArr[3] ? 1 : checkArr[2],
                    word_book: checkArr[3],
                    //more_page: checkArr[4],
                    print_history: checkArr[4],
                    print_revision_trace: checkArr[5],
                    print_comment: checkArr[6],
            };
            //打印设置完成
            //复核设置
            var userIds = [userId];
            var witnessSetting = {};

            var $modal = $('.witnessData');
            var remindTrigger = $('#remind_trigger').val();
            var remindWith = $('#remind_with').val();

            var $userInput = $('input.visible-user-input', $modal);
            var userIdStr = $userInput.attr('idbox');
            $('#remind_others', $modal).is(':checked') ? (userIdStr = userIdStr) : (userIdStr="");
            if(remindWith){
                for(var i =0 ; i<remindWith.length; i++) {
                    if (remindWith[i] == 'remind_with_message') {
                        var remind_with_message = 1
                    }
                    if (remindWith[i] == 'remind_with_email') {
                        var remind_with_email = 1
                    }
                }
            }

            witnessSetting = {
                remind_trigger: remindTrigger,
                remind_day_limit: $('.remind_day_limit', $modal).val(),
                remind_with_message: remind_with_message ? 1 : 0,
                remind_with_email: remind_with_email ? 1 : 0,
                remind_day_period: $('.remind_day_period', $modal).val(),
                remind_creator: $('#remind_creator', $modal).is(':checked') ? 1 : 0,
                remind_others: $('#remind_others', $modal).is(':checked') ? 1 : 0,
                remind_other_users: (userIdStr !== '' ? userIdStr.split(',') : []),
                approval_nodes: require('approval').getNodes($('.witnessData'), '', 0)
            };

            var witnessData =  {
                group_id: groupId,
                user_ids: userIds,
                setting: witnessSetting
            };
            //复核设置完成
            //预审设置
            var userIds = [userId];
            var pretrialSetting = {};

            // 针对所有实验的设置
            var allSettingChecked = $('#all_exp_need_pretrial').is(':checked');
            if (allSettingChecked) {
                var approvalNodes = require('approval').getNodes($('.pretrialData #all_exp_user'),'',0);
                if (approvalNodes.length == 0) {
                    $.showAlert(mainLang('pls_set_approval_node'));
                    return false;
                }
                pretrialSetting.all_setting = {
                    approval_nodes: approvalNodes
                };
            }

            // 针对InDraw的设置
            var indrawCheckedArr = [];
            $('.danger-item').each(function () {
                if ($(this).is(':checked')) {
                    indrawCheckedArr.push($(this).attr('data-type'));
                }
            });
            if (indrawCheckedArr.length > 0) {
                var approvalNodes = require('approval').getNodes($('#indraw_user'),'',0);
                if (approvalNodes.length == 0) {
                    $.showAlert(mainLang('pls_set_approval_node'));
                    return false;
                }
                pretrialSetting.indraw_setting = {
                    checked_arr: indrawCheckedArr,
                    approval_nodes: approvalNodes
                };
            }

            // 针对项目的设置
            var _valid = 1;
            var projectChecked = $('#project_need_pretrial').is(':checked');
            if (projectChecked) {
                var projectSettingArr = [];
                $('.project-setting-line').each(function () {
                    var projectIds = $(this).find('[name=project_ids]').attr('idbox');
                    if (projectIds) {
                        projectIds = projectIds.split(',');
                        var approvalNodes = require('approval').getNodes($(this),'',0);
                        if (approvalNodes.length == 0) {
                            $.showAlert(mainLang('pls_set_approval_node'));
                            _valid = 0;
                            return false;
                        }
                        projectSettingArr.push({
                            project_ids: projectIds,
                            approval_nodes: approvalNodes
                        });
                    }
                });
                if (projectSettingArr.length > 0) {
                    pretrialSetting.project_setting = projectSettingArr;
                }
            }
            if (!_valid) {
                return false;
            }

            pretrialSetting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
            pretrialSetting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;

            var pretrialData =  {
                group_id: groupId,
                user_ids: userIds,
                setting: pretrialSetting
            };
            //预审设置完成

            //签字设置
            var userIds = [userId];
            var signingSetting = {};

            signingSetting.approval_nodes = require('approval').getNodes($('.signData #signing_user_ids'),'',0);
            signingSetting.send_remind_email = $('#send_remind_email').is(':checked') ? 1 : 0;
            signingSetting.submit_reason_required = $('#submit_reason_required').is(':checked') ? 1 : 0;
            signingSetting.save_exp_check = $('#save_exp_check').is(':checked') ? 1 : 0;
            if ($('#re_edit_approval_user_ids').is(':checked')) {
                var reEditApprovalUserIds = $('.re-edit-setting [name=user_ids]').attr('idbox');
                if (!reEditApprovalUserIds) {
                    $.showAlert(mainLang('pls_set_reedit_approval'));
                    return false;
                }
                if (reEditApprovalUserIds) {
                    reEditApprovalUserIds = reEditApprovalUserIds.split(',');
                    signingSetting.re_edit_approval_user_ids = reEditApprovalUserIds;
                }
            }

            var signingData =  {
                group_id: groupId,
                user_ids: userIds,
                setting: signingSetting
            };
            //签字设置完成
            //其他设置
            var otherSetting = {};
            var $modal = $('.otherData .setOther');
            otherSetting.allow_cbg = $('.otherData #allow_cbg').is(':checked') ? 1 : 0;
            var needApprovalChecked = $('.otherData #cbg_need_approval').is(':checked');
            if (needApprovalChecked) {
                var approvalNodes = require('approval').getNodes($('.otherData #approval_selector_container'),'',0);
                if (approvalNodes.length == 0) {
                    $.showAlert(mainLang('pls_set_approval_node'));
                    return false;
                }
                otherSetting.cbg_approval_nodes = approvalNodes;
            }
            otherSetting.allow_proxy = $('.otherData #allow_proxy').is(':checked') ? 1 : 0;
            if (otherSetting.allow_proxy) {
                var proxyUserIds = $('.otherData .proxy-container [name=user_ids]').attr('idbox');
                if (proxyUserIds) {
                    proxyUserIds = proxyUserIds.split(',');
                    otherSetting.proxy_user_ids = proxyUserIds;
                }
                var proxyExpireDate = $('.otherData .proxy-container .proxy_expire_date').val();
                otherSetting.proxy_expire_date = proxyExpireDate;
            }
            // 代办审核设置
            otherSetting.allow_dfa = $('.otherData #allow_dfa').is(':checked') ? 1 : 0;
            if (otherSetting.allow_dfa) {
                var dfaUserIds = $('[name=dfa_user_ids]', $modal).attr('idbox');
                if (dfaUserIds) {
                    dfaUserIds = dfaUserIds.split(',');
                    otherSetting.dfa_user_ids = dfaUserIds;
                }
            }
            //创建全文模板权限 按钮为确认时 not_allow_create_full_temp = 0
            otherSetting.not_allow_create_full_temp =  !$('.otherData #not_allow_create_full_temp').is(':checked') ? 1 : 0;

            var otherData =  {
                group_id: groupId,
                user_ids: userIds,
                setting: otherSetting
            };
            //其他设置完成




            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-auth-submit',
                data: {
                    'group_auth_data':groupAuthData,
                    'share_data':shareData,
                    'print_data':printData,
                    'witness_data':witnessData,
                    'pretrial_data':pretrialData,
                    'signing_data':signingData,
                    'other_data':otherData,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },




        //设置企业级权限
        view_company_auth: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/view-company-auth',
                data: {
                    user_name: btn.attr("data_user_name"),
                    user_id: btn.attr("data_user_id"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },
        //设置企业级角色
        set_company_role: function (btn) {

            var type = $(btn).attr('type');
            if(type == 'batch'){
                var inputLen = $('.exp_conetnt.active .eln_setting_table_temp input:not(#checkbox_all):checked').length;
                if(inputLen==0){
                    $.showAlert(mainLang('select_user'));
                    return;
                }
                var ids = new Array();
                //循环所有选中的值
                $('.exp_conetnt.active .eln_setting_table_temp input:not(#checkbox_all):checked').each(function(index, element) {
                    //追加到数组中
                    ids.push($(this).parents("tr").attr("data-user_id"));
                });

            }
            if(type == 'single'){
                var ids = new Array();
                ids.push(btn.parents("tr").attr("data-user_id"));
            }

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-company-role',
                data: {
                    user_id:  ids,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },
        //设置群内权限
        set_group_auth: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-auth',
                data: {
                    group_id: btn.attr("data_group_id"),
                    user_id: btn.attr("data_user_id"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置群内权限
        view_group_auth: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/view-group-auth',
                data: {
                    group_id: btn.attr("data_group_id"),
                    user_id: btn.attr("data_user_id"),
                    user_name: btn.attr("data_user_name"),
                    user_real_name: btn.attr("data-user-real-name"),
                    group_role_ids: btn.attr("data_group_role_ids"),
                    group_role_names: btn.attr("data_group_role_names"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置用户橘色
        set_role: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-auth',
                data: {

                    user_id: btn.attr("data-userid"),

                },
                success: function (data) {
                    if (data.status == 1) {

                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置角色列表
        set_role_manage: function (btn) {

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-role-manage',
                data: {
                    group_id: btn.parents("td").attr("group_id"),
                    user_id: btn.attr("data-userid"),
                    is_group: 0
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级权限
        set_company_role_submit: function (btn) {
            var data = {
                user_id:$(btn).attr('data-user_id'),
                role_ids:$('.select_role').find('[name="role_ids"]').attr('idbox'),
                role_names:$('.select_role').find('[name="role_ids"]').attr('title'),
            };

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-company-role-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        require('tab').reloadActiveTag(); //刷新当前标签页
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级角色
        set_group_role: function (btn) {
            // console.log('da');
            var type = $(btn).attr('type');
            if(type == 'batch'){
                var inputLen = $('.exp_conetnt.active .eln_setting_table input:not(#checkbox_all):checked').length;
                if(inputLen==0){
                    $.showAlert(mainLang('select_user'));
                    return;
                }
                var ids = new Array();
                //循环所有选中的值
                $('.exp_conetnt.active .eln_setting_table input:not(#checkbox_all):checked').each(function(index, element) {
                    //追加到数组中
                    ids.push($(this).parents("tr").attr("data-user_id"));
                });

            }
            if(type == 'single'){
                var ids = new Array();
                ids.push(btn.parents("tr").attr("data-user_id"));
            }

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-role',
                data: {
                    user_id:  ids,
                    group_id: $(".exp_conetnt.active .top-group-tabs .on").attr("data-id"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);

                        // if (type === 'batch') {
                        //     $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] +  mainLang('setting_tips_for_batch'));
                        // } else if (type === 'single') {
                        //     $('.group-member-setting .modal-footer .tips').html(jsLang['tip_'] +  mainLang('setting_tips'));
                        // }

                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级权限
        set_group_role_submit: function (btn) {
            var data = {
                user_id:$(btn).attr('data-user_id'),
                role_ids:$('.select_role').find('[name="role_ids"]').attr('idbox'),
                role_names:$('.select_role').find('[name="role_ids"]').attr('title'),
                group_id: $('.exp_conetnt.active .top-group-tabs .on').attr('data-id'),
            };

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-role-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        $('.exp_conetnt.active .current.page-btn').trigger('click') //刷新当前标签页
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级角色
        set_group_role_auth: function (btn) {
            console.log('da');
            var type = $(btn).attr('type');
            if(type == 'batch'){
                var inputLen = $('.exp_conetnt.active .eln_setting_table input:not(#checkbox_all):checked').length;
                if(inputLen==0){
                    $.showAlert(mainLang('select_user'));
                    return;
                }
                var ids = new Array();
                //循环所有选中的值
                $('.exp_conetnt.active .eln_setting_table input:not(#checkbox_all):checked').each(function(index, element) {
                    //追加到数组中
                    ids.push($(this).parents("tr").attr("data-user_id"));
                });

            }
            if(type == 'single'){
                var ids = new Array();
                ids.push(btn.parents("tr").attr("data-user_id"));

            }
            var group_id = $(btn).attr('data-groupid');
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-role-auth',
                data: {
                    user_id:  ids,
                    group_id: group_id,
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级权限
        set_group_role_auth_submit: function (btn) {
            var data = {
                user_id:$(btn).attr('data-user_id'),
                role_ids:$('.select_role').find('[name="role_ids"]').attr('idbox'),
                role_names:$('.select_role').find('[name="role_ids"]').val(),
                group_id: $(btn).attr('data-group_id'),
            };

            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/set-group-role-auth-submit',
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(jsLang['success']);
                        $.closeModal();
                        //require('tab').reloadActiveTag(); //刷新当前标签页
                        //Bug 4085 滑动条位置不变
                        $('.exp_conetnt.active .current.page-btn').trigger('click') //刷新当前标签页

                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        //设置企业级权限
        company_setting_search: function (btn) {
            console.log(123)
            Array.intersect = function () {
                var result = new Array();
                var obj = {};
                for (var i = 0; i < arguments.length; i++) {
                    for (var j = 0; j < arguments[i].length; j++) {
                        var str = arguments[i][j];
                        if (!obj[str]) {
                            obj[str] = 1;
                        } else {
                            obj[str]++;
                            if (obj[str] == arguments.length) {
                                result.push(str);
                            }
                        }
                    }
                }
                return result;
            }
           //$('body').on('click', '.company_setting_search', function () {
                var user_ids = $(".exp_conetnt.active input[name=user_ids]").attr('idbox');
                var selected_UsersNames = $(".exp_conetnt.active input[name=user_ids]").attr('value');
                var group_ids = $(".exp_conetnt.active input[name=group_ids]").attr('idbox');
                var department_ids = $(".exp_conetnt.active input[name=dep_ids]").attr('data');
                var role_ids = $(".exp_conetnt.active input[name=role_ids]").attr('idbox');

                document.cookie = "user_ids=" + user_ids;
                document.cookie = "selectedUsersNames=" + selected_UsersNames;
                document.cookie = "group_ids=" + group_ids;
                document.cookie = "department_ids=" + department_ids;
                if(role_ids==''){
                    document.cookie = "role_ids=" + "all"
                }else {
                    document.cookie = "role_ids=" + role_ids;
                }

            $(".exp_conetnt.active .eln_setting_table_temp").find(".tr").each(function (i) {
                    //if (i > 0) {
                    $(this).hide();
                    //}
                });

                $(".exp_conetnt.active .eln_setting_table_temp").find(".tr").each(function (i) {
                    //if (i > 0) {
                    //第一 四个条件全有
                    if (user_ids.length>0 && group_ids.length > 0 && role_ids.length > 0 && department_ids.length > 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);

                        if(userResult.length>0 && roleResult.length>0 && depResult.length>0 && groupResult.length>0){
                            $(this).show();
                        }
                    }

                    //第2-1 三个组合 user_ids group_ids role_ids
                    if (user_ids.length>0 && group_ids.length > 0 && role_ids.length > 0 && department_ids.length == 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);

                        if(userResult.length>0 && roleResult.length>0  && groupResult.length>0){
                            $(this).show();
                        }
                    }

                    //第2-2 三个组合 user_ids group_ids role_ids
                    if (user_ids.length>0 && group_ids.length > 0 && role_ids.length == 0 && department_ids.length > 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);

                        if(userResult.length>0 && depResult.length>0  && groupResult.length>0){
                            $(this).show();
                        }
                    }

                    //第2-3 三个组合 user_ids group_ids role_ids
                    if (user_ids.length>0 && group_ids.length == 0 && role_ids.length > 0 && department_ids.length > 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);

                        if(userResult.length>0 && depResult.length>0  && roleResult.length>0){
                            $(this).show();
                        }
                    }

                    //第2-4 三个组合 user_ids group_ids role_ids
                    if (user_ids.length==0 && group_ids.length > 0 && role_ids.length > 0 && department_ids.length > 0) {
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);

                        if(groupResult.length>0 && depResult.length>0  && roleResult.length>0){
                            $(this).show();
                        }
                    }

                    //第3-1 三个组合 user_ids group_ids
                    if (user_ids.length>0 && group_ids.length > 0 && role_ids.length == 0 && department_ids.length == 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(userResult.length>0 && groupResult.length>0){
                            $(this).show();
                        }
                    }

                    //第3-2 三个组合 user_ids group_ids
                    if (user_ids.length>0 && group_ids.length == 0 && role_ids.length > 0 && department_ids.length == 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(userResult.length>0 && roleResult.length>0){
                            $(this).show();
                        }
                    }

                    //第3-3 三个组合 user_ids group_ids
                    if (user_ids.length>0 && group_ids.length == 0 && role_ids.length == 0 && department_ids.length > 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(userResult.length>0 && depResult.length>0){
                            $(this).show();
                        }
                    }

                    //第3-4 三个组合 user_ids group_ids
                    if (user_ids.length==0 && group_ids.length > 0 && role_ids.length > 0 && department_ids.length == 0) {
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(groupResult.length>0 && roleResult.length>0){
                            $(this).show();
                        }
                    }

                    //第3-5 三个组合 user_ids group_ids
                    if (user_ids.length==0 && group_ids.length > 0 && role_ids.length == 0 && department_ids.length > 0) {
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(groupResult.length>0 && depResult.length>0){
                            $(this).show();
                        }
                    }

                    //第3-6 三个组合 user_ids group_ids
                    if (user_ids.length==0 && group_ids.length == 0 && role_ids.length > 0 && department_ids.length > 0) {
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(roleResult.length>0 && depResult.length>0){
                            $(this).show();
                        }
                    }

                    //第4-1 三个组合 user_ids group_ids
                    if (user_ids.length>0 && group_ids.length == 0 && role_ids.length == 0 && department_ids.length == 0) {
                        user_id_arr = $(this).attr('data-user-id').split(',');
                        user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(userResult.length>0 ){
                            $(this).show();
                        }
                    }

                    //第4-2 三个组合 user_ids group_ids
                    if (user_ids.length==0 && group_ids.length > 0 && role_ids.length == 0 && department_ids.length == 0) {
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        group_id_arr = $(this).attr('data-group-id').split(',');
                        group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(groupResult.length>0 ){
                            $(this).show();
                        }
                    }

                    //第4-3 三个组合 user_ids group_ids
                    if (user_ids.length==0 && group_ids.length == 0 && role_ids.length > 0 && department_ids.length == 0) {
                        console.log(14)
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        //department_id_arr = $(this).attr('data-department-id').split(',');
                        //department_ids_arr = department_ids.split(',');

                        role_id_arr = $(this).attr('data-role-id').split(',');
                        role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        //depResult = Array.intersect(department_id_arr,department_ids_arr);
                        console.log(roleResult)
                        if(roleResult.length>0 ){
                            $(this).show();
                        }
                    }

                    //第4-4 三个组合 user_ids group_ids
                    if (user_ids.length==0 && group_ids.length == 0 && role_ids.length == 0 && department_ids.length > 0) {
                        //user_id_arr = $(this).attr('data-user-id').split(',');
                        //user_ids_arr = user_ids.split(',');

                        //group_id_arr = $(this).attr('data-group-id').split(',');
                        //group_ids_arr = group_ids.split(',');

                        department_id_arr = $(this).attr('data-department-id').split(',');
                        department_ids_arr = department_ids.split(',');

                        //role_id_arr = $(this).attr('data-role-id').split(',');
                        //role_ids_arr = role_ids.split(',');

                        //userResult = Array.intersect(user_id_arr,user_ids_arr);
                        //groupResult = Array.intersect(group_id_arr,group_ids_arr);
                        //roleResult = Array.intersect(role_id_arr,role_ids_arr);
                        depResult = Array.intersect(department_id_arr,department_ids_arr);


                        if(depResult.length>0 ){
                            $(this).show();
                        }
                    }



                    //三个条件全不满足
                    if (user_ids.length==0 && group_ids.length == 0 && role_ids.length == 0 && department_ids.length == 0) {
                        if($(this).attr('data-role-id') == '5'){
                            $(this).hide();
                        }
                        else{
                            $(this).show();
                        }


                    }
                    //}
                });
            //});

        },

        //设置企业级权限
        company_setting_search_server: function (btn) {
            console.log(123)

            //$('body').on('click', '.company_setting_search', function () {
            var user_ids = $(".exp_conetnt.active input[name=user_ids]").attr('idbox');
            var selected_UsersNames = $(".exp_conetnt.active input[name=user_ids]").attr('value');
            var group_ids = $(".exp_conetnt.active input[name=group_ids]").attr('idbox');
            var department_ids = $(".exp_conetnt.active input[name=dep_ids]").attr('data');
            var role_ids = $(".exp_conetnt.active input[name=role_ids]").attr('idbox');

            document.cookie = "user_ids=" + user_ids;
            document.cookie = "selectedUsersNames=" + selected_UsersNames;
            document.cookie = "group_ids=" + group_ids;
            document.cookie = "department_ids=" + department_ids;
            if(role_ids==''){
                document.cookie = "role_ids=" + "all"
            }else {
                document.cookie = "role_ids=" + role_ids;
            }

            $('.exp_title .tag.on').trigger('dblclick');



        },

        get_user_auth: function (btn) {
            var tab = require('tab');

            var tagId = tab.getTag('getUserAuthContent', [btn.parents("td").attr("group_id"),btn.attr("data-userid"),btn.attr("data-userName")]);
            if(tagId) {
                require('get_html').getUserAuthContent(btn.parents("td").attr("group_id"),btn.attr("data-userid"),btn.attr("data-userName")).then(function (res) {
                    require('tab').switchTag(tagId, res.data.contentHtml);
                });
            }
            else {
                require('get_html').getUserAuthContent(btn.parents("td").attr("group_id"),btn.attr("data-userid"),btn.attr("data-userName")).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [btn.parents("td").attr("group_id"),btn.attr("data-userid"),btn.attr("data-userName")],
                            func: 'getUserAuthContent',
                            name: mainLang('view_user_auth')+'-'+btn.attr("data-userName"),
                            title: mainLang('view_user_auth')+'-'+btn.attr("data-userName")
                        }
                        tab.openTag(obj);
                    }
                });
            }

        },

        //设置群内权限
        view_group_role_auth: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/view-group-role-auth',
                data: {
                    group_id: btn.attr("data_group_id"),
                    user_id: btn.attr("data_user_id"),
                    user_name: btn.attr("data_user_name"),
                    group_role_ids: btn.attr("data_group_role_ids"),
                    group_role_names: btn.attr("data_group_role_names"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        auth_compare: function (btn) {
            var tab = require('tab');

            var tagId = tab.getTag('getAuthCompareContent', [btn.attr("data-groupId")]);
            if(tagId) {
                require('get_html').getAuthCompareContent(btn.attr("data-groupId")).then(function (res) {
                    require('tab').switchTag(tagId, res.data.contentHtml);
                });
            }
            else {
                require('get_html').getAuthCompareContent(btn.attr("data-groupId")).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [btn.attr("data-groupId")],
                            func: 'getAuthCompareContent',
                            name: mainLang('group_setting'),
                            title: mainLang('group_setting')
                        }
                        tab.openTag(obj);
                    }
                });
            }

        },

        //设置企业级权限
        view_company_auth_by_role: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/view-company-auth-by-role',
                data: {
                    role_id: btn.attr("data_role_id"),
                    user_name: btn.attr("data_user_name"),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            })
        },

        genMyAuthPage : function () {
            var tab = require('tab');
            var tagId = tab.getTag('myAuth');
            if (tagId) {
                require('tab').switchTag(tagId);
            } else {
                require('get_html').myAuth().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            func: 'myAuth',
                            params: [],
                            name: res.data.tabName,
                            title: res.data.tabName,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },
        loadGroupAuth: function (btn) {
            var groupId = $(btn).data('id');
            $.ajaxFn({
                url: ELN_URL + '?r=company-setting/view-user-auth',
                data: {
                    user_id: USERID,
                    group_id: groupId,
                },
                success: function (res) {
                    if (res.status) {
                        $('#auth_box').html(res.data.contentHtml);
                        $(btn).siblings().removeClass('on');
                        $(btn).addClass('on');
                    }
                }
            });
        },


    }
    //合并到一个对象
    $.extend(module, printPageSetting);
    module.init();
    module.bindEvent();
    module.page_bindEvent();
    return module;
})
