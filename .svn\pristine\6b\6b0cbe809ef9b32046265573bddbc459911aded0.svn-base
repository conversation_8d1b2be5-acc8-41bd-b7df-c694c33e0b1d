<?php
namespace frontend\services;

use frontend\core\CommonModel;
use frontend\core\CommonServer;
use frontend\interfaces\CenterInterface;
use frontend\models\AnalysisModel;
use frontend\models\BiologyModel;
use frontend\models\ChemModel;
use frontend\models\DefineTableKeyModel;
use frontend\models\EditorModel;
use frontend\models\ExcelModel;
use frontend\models\ExperimentCommentModel;
use frontend\models\ProductModel;
use frontend\models\ReferenceModel;
use frontend\models\ShareModel;
use frontend\models\StructdataKey;
use frontend\models\SubstrateModel;
use frontend\models\TemplateActionLogModel;
use frontend\models\TemplateConfig;
use frontend\models\TemplateForGroupModel;
use frontend\models\TemplateHistoryModel;
use frontend\models\TemplateHistoryNewModel;
use frontend\models\TemplateHistoryRelayModel;
use frontend\models\TemplateIntableData;
use frontend\models\TemplateIntextData;
use frontend\models\TemplateModel;
use frontend\models\TemplateRelayModel;
use frontend\models\TemplateRuleIndraw;
use frontend\models\TemplateUserHide;
use frontend\models\UploadModel;
use frontend\services\modules\BioModuleServer;
use frontend\services\modules\CommentModuleServer;
use frontend\services\modules\CustomTableModuleServer;
use frontend\services\modules\DefineTableModuleServer;
use frontend\services\modules\EditorModuleServer;
use frontend\services\modules\ExcelModuleServer;
use frontend\services\modules\IndrawModuleServer;
use frontend\services\modules\RefModuleServer;
use frontend\services\modules\TlcModuleServer;
use frontend\services\modules\UploadModuleServer;
use frontend\services\modules\XSheetServer;
use service\models\ineln\RecycleModel;
use yii\db\Exception;
use yii;

/**
 * 实验模板
 *
 * <AUTHOR> @copyright 2016-2-29
 */
class TempleServer extends CommonServer {
    // // 操作类型配置数组
    // private static $actionTypes = [
    //     'CREATE'    => 'create',    // 创建
    //     'SAVE'      => 'save',      // 保存
    //     'PUBLISH'   => 'publish',   // 发布
    //     'SUBMIT_AUDIT' => 'submit_audit', // 提交审核
    //     'CANCEL_AUDIT' => 'cancel_audit', // 撤销审核
    //     'AUDIT_AGREE' => 'audit_agree', // 审核通过
    //     'AUDIT_REFUSE' => 'audit_refuse', // 审核拒绝
    //     'REMOVE' => 'remove', // 删除至回收站
    //     'TRANSFER' => 'transfer', // 转交
    //     'RESTORE' => 'restore', // 从回收站恢复
    // ];

    private static $sqlType = [//数据库中type的字段
    	'exp' => 1,
    	'temp' => 2,
    	'his' => 3,
        'temp_his' => 4
    ];

    /**
     * 保存全局模板 只保存组件列表和位置，数据不保存，与getTempById方法一致，暂时未使用
     *
     * <AUTHOR> @copyright 2016-3-11
     * @param int $id 模板id
     * @param number $isUse 是否需要获取之后进行直接使用
     * @param number $temp_type 1表示全局 2表示子模板
     */
    public function getTemp($id, $isUse=FALSE, $temp_type=1) {


        if(2 == $temp_type){
            $temp = (new TemplateModel())->getSubContent($id);

            return $this->success($temp);
        }

        if(4 == $temp_type){
            $temp = (new TemplateModel())->getInTableTemp($id);

            return $this->success($temp);
        }

        $temp = (new TemplateModel())->getTemp($id);


        if(empty($temp)){
        	return $this->fail(\Yii::t('temp', 'no_temp'));
        }

        $id  = $temp['id'];
        $compentArr = \Yii::$app->params['component'];

        $chemData['base_data'] = (new ChemModel())->getChemData($id, self::$sqlType['temp']);

        $subData = [];
        $proData = [];
        $chemDetail = [];
        $detailsData = [];

        if(!empty($chemData['base_data'])){
            $chemId = $chemData['base_data']['id'];
            $subData = (new SubstrateModel())->getSubDataByChemId($chemId, self::$sqlType['temp']);
            $proData = (new ProductModel())->getProDataByChemId($chemId, self::$sqlType['temp']);
            $detailsData = (new ChemServer())->getDetailsDataByChemId($chemId, self::$sqlType['temp'])['data'];

            /*$analyData = (new AnalysisModel())->getAnalysisData($chemId, self::$sqlType['temp']);

            foreach ($proData as $key=>$pro){
                foreach ($analyData as $key1=>$ana){
                    if($ana['product_id'] == $pro['product_id']){
                        $proData[$key]['analy_data'][$key1] = $ana;
                    }
                }
            }*/
            $chemData['sub_data'] = $subData;
            $chemData['pro_data'] = $proData;
            $chemData['details_data'] = $detailsData;

            $chemDetail = [
	           'class' => $chemData['base_data']['class'],
	           'component_id' => $chemData['base_data']['component_id'],
	           'draw_name' => $chemData['base_data']['draw_name'],
	           'descript' => $chemData['base_data']['descript'],
            ];
        }


        if($isUse){

            $postData['substrate_data'] = $subData;
            $postData['product_data'] = $proData;
            $postData['chem_detail'] = $chemDetail;
            $postData['chem_data'] = $chemData['base_data'];
            $postData['details_data'] = $detailsData;

            $postData['operation_data'] = (new EditorModel())->listEditorById($id, $compentArr['operation'], self::$sqlType['temp']);
            $postData['discuss_data'] = (new EditorModel())->listEditorById($id, $compentArr['discuss'], self::$sqlType['temp']);
            $postData['diy_data'] = (new EditorModel())->listEditorById($id, $compentArr['diy'], self::$sqlType['temp']);
            $postData['lite_data'] = (new EditorModel())->listEditorById($id, $compentArr['lite'], self::$sqlType['temp']);
            $postData['abstract_data'] = (new EditorModel())->listEditorById($id, $compentArr['abstract'], self::$sqlType['temp']);

            $postData['excel_data'] = (new ExcelModel())->listExcelById($id, self::$sqlType['temp'], TRUE);

            $postData['upload_img_data'] = (new UploadModel())->listUploadById($id, $compentArr['picture'], self::$sqlType['temp']);
            $postData['upload_file_data'] = (new UploadModel())->listUploadById($id, $compentArr['upload'], self::$sqlType['temp']);
            $postData['wechat_pic_data'] = (new UploadModel())->listUploadById($id, $compentArr['wechat_pic'], self::$sqlType['temp']);

            $postData['reference_data'] = (new ReferenceModel())->listReference($id, self::$sqlType['temp']);
            $postData['biology_data'] = (new BiologyModel())->listBiology($id, self::$sqlType['temp']);
            $postData['comment_data'] = (new ExperimentCommentModel())->getExpComList($id, self::$sqlType['temp']);
            $postData['define_data'] = (new DefineTableKeyModel())->defineDataByParentId($id, self::$sqlType['temp']);
            $postData['tlc_data'] = (new TlcServer())->getDataByExpId($id, self::$sqlType['temp']);
            $postData['custom_data'] = (new IntegleTableServer())->getTempDataByTempId($id, self::$sqlType['temp']);
            $postData['xsheet_data'] = (new XSheetServer())->getDataByExpId($id, self::$sqlType['temp']);
            $postData['base_data'] = $temp;



            return $this->success($postData);
        }

        $returnData = [];
        $modelType = [
        	'1' => new ChemModel(),
        	'2' => new EditorModel(),
        	'3' => new EditorModel(),
        	'4' => new EditorModel(),
        	'11' => new EditorModel(),
        	'12' => new EditorModel(),
        	'7' => new UploadModel(),
        	'8' => new UploadModel(),
        	'5' => new ExcelModel(),
        	'6' => new TemplateRelayModel(),
        	'9' => new ReferenceModel(),
        	'10' => new BiologyModel(),
        	'13' => new DefineTableKeyModel(),
        	'14' => new UploadModel()
        ];

        $relayOrder = [
            '1' => 20,
            '2' => 40,
            '3' => 50,
            '4' => 60,
            '5' => 80,
            '6' => 120,
            '7' => 100,
            '8' => 110,
            '9' => 90,
            '10' => 30,
            '11' => 70,
            '12' => 10,
            '13' => 130,
            '14' => 140,
            '15' => 150,
            '16' => 160
        ];


        $expRelatIdArr = [];


        $relayData = (new TemplateRelayModel())->relayDataByTempId($id);



        foreach ($relayData as $key=>$relay){//不要问我为什么写这么恶心的代码，我也不想、、、、
            $class = $relay['class'];
            $commentId = $relay['component_id'];

             if((!intval($class) && (0 != $class)) || (!intval($class) && ('0' != $class)) || (empty($class) && ('0' != $class))){
                $class = $relayOrder[$commentId];
            }

            if(isset($expRelatIdArr[$class])){
                $expRelatIdArr[$class] = ++$relayOrder[$commentId];
                $class = $expRelatIdArr[$class];
            }

            $expRelatIdArr[$class] = $class;

            $class = trim($class);

            $relayId = $relay['id'];

            if(1 == $commentId){
                $returnData[$class]['chemData'] = $chemData;
            }
            if (2 == $commentId){
                $returnData[$class]['operationData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (3 == $commentId){
                $returnData[$class]['discussData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (4 == $commentId){
                $returnData[$class]['diyData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (5 == $commentId){
                $returnData[$class]['excelData'] = $modelType[$commentId]->excelDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (6 == $commentId){
                $returnData[$class]['commentData'] = $modelType[$commentId]->relayDataByRelayId($relayId);
            }
            if (7 == $commentId){
                $returnData[$class]['uploadData'] = $modelType[$commentId]->uploadDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (8 == $commentId){
                $returnData[$class]['pictureData'] = $modelType[$commentId]->uploadDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (9 == $commentId){
                $returnData[$class]['referenceData'] = $modelType[$commentId]->referenceDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (10 == $commentId){
                $returnData[$class]['biologyData'] = $modelType[$commentId]->biologyDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (11 == $commentId){
                $returnData[$class]['liteData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (12 == $commentId){
                $returnData[$class]['abstractData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (13 == $commentId){
                $returnData[$class]['defineData'] = $modelType[$commentId]->defineDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (14 == $commentId){
                $returnData[$class]['wechatData'] = $modelType[$commentId]->uploadDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (16 == $commentId){
                $returnData[$class]['tlcData'] = (new TlcServer())->getDataByRelayId($relayId, self::$sqlType['temp']);
            }
            /*if (18 == $commentId){
                // 多功能表格
                $returnData[$class]['customData'] = (new IntegleTableServer())->getDataByRelayId($relayId, self::$sqlType['temp']);
            }*/
        }
        ksort($returnData);
        $temp['tempRelay'] = $returnData;



        return $this->success($temp);
    }

    /**
     * 保存全局模板 只保存组件列表和位置，数据不保存，与getTempById方法一致，暂时未使用
     *
     * <AUTHOR> @copyright 2016-3-11
     * @param int $id 模板id
     * @param number $isUse 是否需要获取之后进行直接使用
     * @param number $temp_type 1表示全局 2表示子模板
     */
    public function getTempNew($id, $isUse=FALSE, $temp_type=1) {
        if(2 == $temp_type){
            $temp = (new TemplateModel())->getSubContent($id);

            return $this->success($temp);
        }

        $temp = (new TemplateModel())->getTemp($id);

        if(empty($temp)){
        	return $this->fail(\Yii::t('temp', 'no_temp'));
        }

        $id  = $temp['id'];
        $compentArr = \Yii::$app->params['component'];

        $chemData['base_data'] = (new ChemModel())->getChemData($id, self::$sqlType['temp']);

        $subData = [];
        $proData = [];
        $chemDetail = [];

        if(!empty($chemData['base_data'])){
            $chemId = $chemData['base_data']['id'];
            $subData = (new SubstrateModel())->getSubDataByChemId($chemId, self::$sqlType['temp']);
            $proData = (new ProductModel())->getProDataByChemId($chemId, self::$sqlType['temp']);

            $analyData = (new AnalysisModel())->getAnalysisData($chemId, self::$sqlType['temp']);

            foreach ($proData as $key=>$pro){
                foreach ($analyData as $key1=>$ana){
                    if($ana['product_id'] == $pro['product_id']){
                        $proData[$key]['analy_data'][$key1] = $ana;
                    }
                }
            }
            $chemData['sub_data'] = $subData;
            $chemData['pro_data'] = $proData;

            $chemDetail = [
	           'class' => $chemData['base_data']['class'],
	           'component_id' => $chemData['base_data']['component_id'],
	           'draw_name' => $chemData['base_data']['draw_name'],
	           'descript' => $chemData['base_data']['descript'],
            ];
        }

        if($isUse){

            $postData['substrate_data'] = $subData;
            $postData['product_data'] = $proData;
            $postData['chem_detail'] = $chemDetail;
            $postData['chem_data'] = $chemData['base_data'];

            $postData['operation_data'] = (new EditorModel())->listEditorByIdNew($id, $compentArr['operation'], self::$sqlType['temp']);
            $postData['discuss_data'] = (new EditorModel())->listEditorByIdNew($id, $compentArr['discuss'], self::$sqlType['temp']);
            $postData['diy_data'] = (new EditorModel())->listEditorByIdNew($id, $compentArr['diy'], self::$sqlType['temp']);
            $postData['lite_data'] = (new EditorModel())->listEditorByIdNew($id, $compentArr['lite'], self::$sqlType['temp']);
            $postData['abstract_data'] = (new EditorModel())->listEditorByIdNew($id, $compentArr['abstract'], self::$sqlType['temp']);

            $postData['excel_data'] = (new ExcelModel())->listExcelById($id, self::$sqlType['temp'], TRUE);

            $postData['upload_img_data'] = (new UploadModel())->listUploadById($id, $compentArr['picture'], self::$sqlType['temp']);
            $postData['upload_file_data'] = (new UploadModel())->listUploadById($id, $compentArr['upload'], self::$sqlType['temp']);
            $postData['wechat_pic_data'] = (new UploadModel())->listUploadById($id, $compentArr['wechat_pic'], self::$sqlType['temp']);

            $postData['reference_data'] = (new ReferenceModel())->listReference($id, self::$sqlType['temp']);
            $postData['biology_data'] = (new BiologyModel())->listBiology($id, self::$sqlType['temp']);
            $postData['comment_data'] = (new ExperimentCommentModel())->getExpComList($id, self::$sqlType['temp']);
            $postData['define_data'] = (new DefineTableKeyModel())->defineDataByParentId($id, self::$sqlType['temp']);
            $postData['base_data'] = $temp;

            return $this->success($postData);
        }

        $returnData = [];
        $modelType = [
        	'1' => new ChemModel(),
        	'2' => new EditorModel(),
        	'3' => new EditorModel(),
        	'4' => new EditorModel(),
        	'11' => new EditorModel(),
        	'12' => new EditorModel(),
        	'7' => new UploadModel(),
        	'8' => new UploadModel(),
        	'5' => new ExcelModel(),
        	'6' => new TemplateRelayModel(),
        	'9' => new ReferenceModel(),
        	'10' => new BiologyModel(),
        	'13' => new DefineTableKeyModel(),
        	'14' => new UploadModel()
        ];

        $relayOrder = [
            '1' => 20,
            '2' => 40,
            '3' => 50,
            '4' => 60,
            '5' => 80,
            '6' => 120,
            '7' => 100,
            '8' => 110,
            '9' => 90,
            '10' => 30,
            '11' => 70,
            '12' => 10,
            '13' => 130,
            '14' => 140
        ];


        $expRelatIdArr = [];

        $relayData = (new TemplateRelayModel())->relayDataByTempId($id);

        foreach ($relayData as $key=>$relay){//不要问我为什么写这么恶心的代码，我也不想、、、、
            $class = $relay['class'];
            $commentId = $relay['component_id'];

             if((!intval($class) && (0 != $class)) || (!intval($class) && ('0' != $class)) || (empty($class) && ('0' != $class))){
                $class = $relayOrder[$commentId];
            }

            if(isset($expRelatIdArr[$class])){
                $expRelatIdArr[$class] = ++$relayOrder[$commentId];
                $class = $expRelatIdArr[$class];
            }

            $expRelatIdArr[$class] = $class;

            $class = trim($class);

            $relayId = $relay['id'];

            if(1 == $commentId){
                $returnData[$class]['chemData'] = $chemData;
            }
            if (2 == $commentId){
                $returnData[$class]['operationData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (3 == $commentId){
                $returnData[$class]['discussData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (4 == $commentId){
                $returnData[$class]['diyData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (5 == $commentId){
                $returnData[$class]['excelData'] = $modelType[$commentId]->excelDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (6 == $commentId){
                $returnData[$class]['commentData'] = $modelType[$commentId]->relayDataByRelayId($relayId);
            }
            if (7 == $commentId){
                $returnData[$class]['uploadData'] = $modelType[$commentId]->uploadDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (8 == $commentId){
                $returnData[$class]['pictureData'] = $modelType[$commentId]->uploadDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (9 == $commentId){
                $returnData[$class]['referenceData'] = $modelType[$commentId]->referenceDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (10 == $commentId){
                $returnData[$class]['biologyData'] = $modelType[$commentId]->biologyDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (11 == $commentId){
                $returnData[$class]['liteData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (12 == $commentId){
                $returnData[$class]['abstractData'] = $modelType[$commentId]->editoDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (13 == $commentId){
                $returnData[$class]['defineData'] = $modelType[$commentId]->defineDataByRelayId($relayId, self::$sqlType['temp']);
            }
            if (14 == $commentId){
                $returnData[$class]['wechatData'] = $modelType[$commentId]->uploadDataByRelayId($relayId, self::$sqlType['temp']);
            }
        }

        ksort($returnData);
        $temp['tempRelay'] = $returnData;

        return $this->success($temp);
    }

    /**
     * Notes:
     * Author: zhu huajun
     * Date: 2019/2/27 15:48
     * @param $id
     * @param $status
     * @return array
     */
    public function getTempById($id, $status = 1) {
        $query = TemplateModel::find()->select('id, user_id, name AS temp_name, type, is_company,tfrom, descript AS temp_descript, keywords, title, create_time,update_time, is_system, system_type, define_item, step,subtype_id')->where([
            'id' => $id,
        ]);

        if ($status == 1) {
            $query->andWhere([
                'status' => 1
            ]);
        }

        $temp = $query->asArray()->one();
        $defaultTemplate = [697,698];

        if(empty($temp)){
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }

        if(in_array($id,$defaultTemplate)){
            switch($temp['temp_name']){
                case 'Classic biological template':
                case '经典生物模板':
                    $temp['temp_name'] = \Yii::t('views/set_require', 'bio_template');
                    break;
                case 'Classic chemical template':
                case '经典化学模板':
                    $temp['temp_name'] = \Yii::t('views/set_require', 'chem_template');
                    break;
            }
        }

        $tempData = [
            'base_data' => $temp
        ];

        $currentLang = $_SESSION['eln_lang'];

        // 获取模块
        $modules = TemplateRelayModel::find()->where([
            'template_id' => $id,
            'status' => 1
        ])->orderBy('class ASC')->asArray()->all();


        // 遍历获取模块数据
        $moduleDataArr = [];
        $templateConfig = new TemplateConfig();
        foreach($modules as $key=>&$module) {
            switch($module['component_id']) {
                case \Yii::$app->params['component']['chem']:
                    $dataResult = (new IndrawModuleServer())->getData($module['id'], 2);
                    // 获取物料表必填字段
                    $templateRuleIndraw = TemplateRuleIndraw::findOne(['relay_id' => $module['id']]);
                    $indrawRequiredFields = $templateRuleIndraw ? $templateRuleIndraw->getAttributes(['reactant', 'solvent', 'condition', 'product', 'details']) :
                        ['reactant' => null, 'solvent' => null, 'condition' => null, 'product' => null, 'details' => null];
                    foreach ($indrawRequiredFields as $name=>$col) {
                        $colData = json_decode($col, true);
                        $fieldList = !empty($colData) && is_array($colData) ? $colData : [];
                        foreach ($fieldList as $field) {
                            $module[$name][$field] = 1;
                        }
                    }
                    break;
                case \Yii::$app->params['component']['operation']:
                case \Yii::$app->params['component']['discuss']:
                case \Yii::$app->params['component']['lite']:
                case \Yii::$app->params['component']['abstract']:
                    $dataResult = (new EditorModuleServer())->getData($module['id'], 2);
                    $module['is_require'] = isset($templateConfig->getRequireData($id, $module['id'])['is_require']) ? $templateConfig->getRequireData($id, $module['id'])['is_require'] : 0;
                    if(in_array($id,$defaultTemplate)){
                        switch($module['name']){
                            case '实验操作':
                                $module['name'] = \Yii::t('module', 'operation');
                                break;
                            case '结果与讨论':
                                $module['name'] = \Yii::t('module', 'discuss');
                                break;
                        }
                    }
                    break;
                case \Yii::$app->params['component']['tlc']:
                    $require = $templateConfig->getRequireData($id, $module['id']);
                    $module['tlc_eluent']=isset($require['tlc_eluent'])?$require['tlc_eluent']:0;
                    $module['tlc_ratio']=isset($require['tlc_ratio'])?$require['tlc_ratio']:0;
                    $module['tlc_cdr']=isset($require['tlc_cdr'])?$require['tlc_cdr']:0;
                    $dataResult = (new TlcModuleServer())->getData($module['id'], 2);
                    break;
                case \Yii::$app->params['component']['upload']:
                case \Yii::$app->params['component']['picture']:
                    $module['is_require'] = isset($templateConfig->getRequireData($id, $module['id'])['is_require']) ? $templateConfig->getRequireData($id, $module['id'])['is_require'] : 0;
                    $dataResult = (new UploadModuleServer())->getData($module['id'], 2);
                    if(in_array($id,$defaultTemplate)){
                        switch($module['name']){
                            case '上传文件':
                                $module['name'] = \Yii::t('module', 'file');
                                break;
                            case '上传图片':
                                $module['name'] = \Yii::t('module', 'picture');
                                break;
                        }
                    }
                    break;
                case \Yii::$app->params['component']['comment']:
                    $dataResult = (new CommentModuleServer())->getData($module['id'], 2);
                    $module['name'] = in_array($id,$defaultTemplate) ? \Yii::t('module', 'comment'):$module['name'];
                    break;
                case \Yii::$app->params['component']['define_table']:
                    $dataResult = (new DefineTableModuleServer())->getData($module['id'], 2);

                    $define = $templateConfig->getRequireData($id, $module['id']);
                    $module['define_field1']=isset($define['define_field1'])?$define['define_field1']:0;
                    $module['define_field2']=isset($define['define_field2'])?$define['define_field2']:0;
                    $module['define_field3']=isset($define['define_field3'])?$define['define_field3']:0;
                    $module['define_field4']=isset($define['define_field4'])?$define['define_field4']:0;
                    $module['define_field5']=isset($define['define_field5'])?$define['define_field5']:0;
                    $module['define_field6']=isset($define['define_field6'])?$define['define_field6']:0;
                    $module['define_field7']=isset($define['define_field7'])?$define['define_field7']:0;
                    $module['define_field8']=isset($define['define_field8'])?$define['define_field8']:0;
                    $module['define_field9']=isset($define['define_field9'])?$define['define_field9']:0;
                    $module['define_field10']=isset($define['define_field10'])?$define['define_field10']:0;
                    $module['define_field11']=isset($define['define_field11'])?$define['define_field11']:0;
                    $module['define_field12']=isset($define['define_field12'])?$define['define_field12']:0;
                    $module['define_field13']=isset($define['define_field13'])?$define['define_field13']:0;
                    $module['define_field14']=isset($define['define_field14'])?$define['define_field14']:0;
                    $module['define_field15']=isset($define['define_field15'])?$define['define_field15']:0;
                    $module['define_field16']=isset($define['define_field16'])?$define['define_field16']:0;
                    $module['define_field17']=isset($define['define_field17'])?$define['define_field17']:0;
                    $module['define_field18']=isset($define['define_field18'])?$define['define_field18']:0;
                    $module['define_field19']=isset($define['define_field19'])?$define['define_field19']:0;
                    $module['define_field20']=isset($define['define_field20'])?$define['define_field20']:0;
                    $module['define_field21']=isset($define['define_field21'])?$define['define_field21']:0;
                    if(in_array($id,$defaultTemplate)){
                        switch($module['name']){
                            case '参考文献':
                                $module['name'] = \yii::t('views/set_require','reference');
                                $dataResult['data']['field_key']['field1'] = \yii::t('views/set_require','reference_name');
                                $dataResult['data']['field_key']['field2'] = \yii::t('views/set_require','reference_year');
                                $dataResult['data']['field_key']['field3'] = \yii::t('views/set_require','reference_volume');
                                $dataResult['data']['field_key']['field4'] = \yii::t('views/set_require','reference_chapter');
                                $dataResult['data']['field_key']['field5'] = \yii::t('views/set_require','reference_page');
                                $dataResult['data']['field_key']['field6'] = \yii::t('views/set_require','reference_link');
                                break;
                            case '自定义表格':
                                $module['name'] = \yii::t('views/set_require','define_table');
                                break;
                            case '材料与仪器':
                                $module['name'] = \yii::t('views/set_require','biology');
                                $dataResult['data']['field_key']['field1'] = \yii::t('views/set_require','biology_name');
                                $dataResult['data']['field_key']['field2'] = \yii::t('views/set_require','biology_batchno');
                                $dataResult['data']['field_key']['field3'] = \yii::t('views/set_require','biology_specifications');
                                $dataResult['data']['field_key']['field4'] = \yii::t('views/set_require','biology_item');
                                $dataResult['data']['field_key']['field5'] = \yii::t('views/set_require','biology_manufacturer');
                                $dataResult['data']['field_key']['field6'] = \yii::t('views/set_require','biology_remarks');
                                break;
                        }
                    }

                    break;
                case \Yii::$app->params['component']['custom_table']:
                    $dataResult = (new CustomTableModuleServer())->getData($module['id'], 2);
                    break;
                case \Yii::$app->params['component']['xsheet']: // add by hkk 2020/3/12
                    $dataResult = (new XSheetServer())->getData($module['id'], 2);
                    if(in_array($id,$defaultTemplate)){
                        switch($module['name']){
                            case '生物原始数据':
                                $module['name'] = \Yii::t('views/set_require', 'bio_raw_data');
                                break;
                        }
                    }
                    $dataResult['data']['height'] =  json_decode($module['config'],true) ? json_decode($module['config'],true)['height'] : 525; // add by hkk 2020/7/10
                    break;
            }
            $data = !empty($dataResult['data']) ? $dataResult['data'] : [];
            $module['height'] = isset($data['height']) ? $data['height'] : null;
            $moduleDataArr[] = [
                'info' => $module,
                'data' => !empty($dataResult['data']) ? $dataResult['data'] : []
            ];
        }
        $tempData['module_data_arr'] = $moduleDataArr;

        return $this->success($tempData);
    }

    /**
     * Notes: 保存模板
     * Author: zhu huajun
     * Date: 2019/3/15 15:51
     * @param $tempData
     */
    public function saveTemp($tempData, $action='update') {
        // 获取当前用户信息，用于审批判断
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[\Yii::$app->view->params['curr_user_id']],1);
        $userDetail = $userList[0]; // 获取用户详细信息
        
        // 获取企业模板审批设置
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $setting = json_decode($setting, true);
        // $needApproval = false;

        // 启动事务
        $transaction = \Yii::$app->integle_ineln->beginTransaction();

        // 检查模板是否存在（更新模式）
        $tempId = !empty($tempData['base_data']['template_id']) ? $tempData['base_data']['template_id'] : 0;
        if(!empty($tempId)) {
            $temp = TemplateModel::find()->where([
                'id' => $tempId
            ])->one();
            if ($temp['status'] == 0) {
                return $this->fail(''); // 模板已被删除，返回失败
            }
        }
        
        // 创建新模板实例或使用现有模板
        if(empty($temp)) {
            $temp = new TemplateModel();
        }

        //新的获取模板是否需要审批的判断，用来替代上面的旧的审批设置，保存 || 修改 模板在企业设置中都属于create_template类型,针对全文模板
        $needApproval = $this->checkTemplateApproval(null, 'create_template', $temp['type'], $temp['subtype_id'], $userDetail, 0, $setting);

        $tempData['insertData']['keywords'] = isset($tempData['base_data']['keywords']) ? $tempData['base_data']['keywords'] : ''; // add by hkk 2019/12/25  模板保存关键字和标题
        $tempData['insertData']['title'] = isset($tempData['base_data']['title']) ? $tempData['base_data']['title'] : ''; // add by hkk 2019/12/25 模板保存关键字和标题
        $tempData['insertData']['step'] = $needApproval ? 1 : 3; // 设置模板状态：1=待审核，3=已通过 根据是否需要审核设置模板状态(by zhu huajun at 2020/4/26 14:57)

        $temp->setAttributes($tempData['insertData']);
        
        // 新增模板时设置所有者
        if($action=='add'){
            $temp['user_id'] = \Yii::$app->view->params['curr_user_id'];
        }

        // 保存模板基本信息
        if(!$temp->save()) {
            $transaction->rollBack();
            return $this->fail(current($temp->getFirstErrors()));
        }

        // 获取模板ID（新增时会生成）
        $tempId = $temp['id'];
        
        // 处理模块数据（如果存在）
        if(!empty($tempData['module_data'])) {
            // 遍历保存各模块数据
            foreach ($tempData['module_data'] as $moduleData) {
                // 新增模式下移除real_id，避免ID冲突
                if($action == 'add') {
                    unset($moduleData['info']['real_id']);
                }
                
                // 根据组件类型调用对应的保存方法
                $comId = $moduleData['info']['component_id'];
                switch($comId) {
                    case \Yii::$app->params['component']['chem']:
                        $saveResult = (new IndrawModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['operation']:
                    case \Yii::$app->params['component']['discuss']:
                    case \Yii::$app->params['component']['lite']:
                    case \Yii::$app->params['component']['abstract']:
                        $saveResult = (new EditorModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['tlc']:
                        $saveResult = (new TlcModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['reference']:
                        $saveResult = (new RefModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['biology']:
                        $saveResult = (new BioModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['upload']:
                    case \Yii::$app->params['component']['picture']:
                        $saveResult = (new UploadModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['comment']:
                        $saveResult = (new CommentModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['define_table']:
                        $saveResult = (new DefineTableModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['custom_table']:
                        $saveResult = (new CustomTableModuleServer())->save($tempId, $moduleData, 2);
                        break;
                    case \Yii::$app->params['component']['xsheet']:
                        $saveResult = (new XSheetServer())->save($tempId, $moduleData, 2);
                        break;
                }

                // 检查模块保存结果
                if($saveResult['status'] == 0) {
                    $transaction->rollBack();
                    return $this->fail($saveResult['info']);
                }
            }
        }

        // 保存模板适用鹰群关系
        $groupIds = $tempData['insertData']['group_ids'];
        $saveTempGroup = $this->_saveTempGroup($groupIds, $tempId);
        if ($saveTempGroup !== true) {
            $transaction->rollBack();
            return $this->fail($saveTempGroup);
        }

        // 提交事务
        $transaction->commit();
        
        // 获取当前模板的模块信息，用于结构化数据处理
        $modules = TemplateRelayModel::find()->where([
            'template_id' => $tempId,
            'status' => 1
        ])->orderBy('class ASC')->asArray()->all();

        // 更新结构化数据字段信息
        // 第一步 获取结构化数据字段信息
        $structDataServer = new StructDataServer();
        $templateField= $structDataServer->getStructDataFieldByConfig($tempId);
        // 第二步处理旧数据 根据模板id 将旧数据全部状态置0

        StructdataKey::updateAll(['status'=>0],['template_id'=>$tempId]);
        foreach($templateField as $key=>$field){

            //print_r($field);
            $where = [
                'template_id'=>$tempId,
                'relay_id'=>$field['relay_id'],
                'field_key'=>$field['name']
            ];
            $keyInfo = StructdataKey::find()->where($where)->asArray()->one();
            if ($keyInfo) {
                if ($keyInfo['status'] == 0) {
                    StructdataKey::updateAll(['status' => 1], ['id' => $keyInfo['id']]);
                }
            } else {

                $fieldData['template_id'] = $tempId;
                $fieldData['relay_id'] = $field['relay_id'];
                $fieldData['field_key'] = $field['name'];
                $fieldData['sort'] = 0;
                $fieldData['status'] = 1;
                $structdataKey = new StructdataKey();
                $structdataKey->setAttributes($fieldData);
                if (!$structdataKey->save()) {
                    return $this->fail(current($structdataKey->getFirstErrors()));
                }

            }
        }
        //保存完成

        // 记录日志
        if (@getVar($tempData['auto_save']) != 1) {
            $this->addActionLog($tempId, $action === 'add' ? TemplateActionLogModel::CREATE : TemplateActionLogModel::SAVE, \Yii::$app->view->params['curr_user_id']);
        }

        return $this->success([
            'temp_id' => $tempId,
            'modules' =>$modules,
            'need_approval' => $needApproval,
        ]);
    }

    /**
     * 新建模板
     *
     * <AUTHOR>
     * @return Ambigous <boolean, \service\models\ineln\multitype:, multitype:, unknown>
     */
    public function addTemp($postData) {
        $tran = \Yii::$app->integle_ineln->beginTransaction();
        $isSub = FALSE;
        if (isset($postData['is_sub']) && $postData['is_sub']) {
            $isSub = TRUE;
        }
        $result = (new TemplateModel())->addTemplate($postData, FALSE, $isSub);
        if ( empty( $result['status'] ) ){
            $tran->rollBack();
            return $result;
        }
        $tempId = $result['data'];

        // 添加适用鹰群
        $groupIds = $postData['insertData']['group_ids'];
        $tempId = $result['data'];
        $saveTempGroup = $this->_saveTempGroup($groupIds, $tempId);
        if ( $saveTempGroup !== true ){
            $tran->rollBack();
            return $this->fail($saveTempGroup);
        }


        $tran->commit();
        return $this->success([
            'temp_id' => $tempId
        ]);
    }

    /**
     * 新建模板
     *
     * <AUTHOR>
     * @return Ambigous <boolean, \service\models\ineln\multitype:, multitype:, unknown>
     */
    public function addTempByCopy($postData) {
        $tran = \Yii::$app->integle_ineln->beginTransaction();
        $isSub = FALSE;
        if (isset($postData['is_sub']) && $postData['is_sub']) {
            $isSub = TRUE;
        }
        //$result = (new TemplateModel())->addTemplate($postData, FALSE, $isSub);
        $result = (new TemplateModel())->addTemplateByCopy($postData, FALSE, $isSub);

        if ( empty( $result['status'] ) ){
            $tran->rollBack();
            return $result;
        }

        /* deprecated
        // 添加适用鹰群
        //  $groupIds = $postData['insertData']['group_ids'];
        //  $tempId = $result['data'];
        //  $saveTempGroup = $this->_saveTempGroup($groupIds, $tempId);
        //  if ( $saveTempGroup !== true ){
        //      $tran->rollBack();
        //      return $this->fail($saveTempGroup);
        //  }
        */

        $tran->commit();
        return $result;
    }

    /**
     * 插入子模板
     * <AUTHOR> @copyright 2016-5-7
     *
     * @param arary $postData ['temp_real'=>'模板组件数据','insertData'=>'模板基本数据']
     * @return Ambigous <\service\models\ineln\Ambigous, boolean, \service\models\ineln\multitype:, multitype:>
     */
    public function addSubTemp($postData){
        return (new TemplateModel())->addSubTemp($postData);
    }

    public function saveSubTemp($id, $data) {
        $temp = TemplateModel::findOne($id);
        if ($temp) {
            $temp->name = @getVar($data['name']);
            $temp->descript = @getVar($data['descript']);
            $temp->subtype_id = @getVar($data['subtype_id']);
            $content = @getVar($data['content']);
            $content = str_replace("&quot;times new roman&quot;", "'times new roman'", $content);
            $content = str_replace("&quot;comic sans ms&quot;", "'comic sans ms'", $content);
            $content = str_replace("&quot;andale mono&quot;", "'andale mono'", $content);
            $content = str_replace("&quot;arial black&quot;", "'arial black'", $content);
            $content = str_replace("&quot;avant garde&quot;", "'avant garde'", $content);
            //$temp->content = $content;   content 和 img存到内容表中
            $temp->step = @getVar($data['step']);
            $img = @getVar($data['img']);
            // if (!empty($img)) {
            //     $temp->img = $img;
            // }
            $type = $temp['type'];
            
            // 如果是文本方法模板(type=2)，需要将content内容保存到TemplateIntextData表
            if (!empty($type) && $type == 2) {
                $tempId = $id;
                $tempIntext = TemplateIntextData::find()
                    ->where(['template_id' => $tempId])
                    ->one();
                
                // 如果没有记录则创建新的
                if (!$tempIntext) {
                    $tempIntext = new TemplateIntextData();
                    $tempIntext->template_id = $tempId;
                }
                
                // 设置content内容
                $tempIntext->content = $content;
                
                // 保存失败则返回错误
                if (!$tempIntext->save()) {
                    return $this->fail(current($tempIntext->getFirstErrors()));
                }
            }

            // 如果是表格方法模板(type=4)，需要将content,img内容保存到TemplateIntableData表
            if (!empty($type) && $type == 4) {
                $tempId = $id;
                $tempInable = TemplateIntableData::find()
                    ->where(['template_id' => $tempId])
                    ->one();
                
                // 如果没有记录则创建新的
                if (!$tempInable) {
                    $tempInable = new TemplateIntableData();
                    $tempInable->template_id = $tempId;
                }
                
                // 设置content内容
                $tempInable->content = $content;
                $img = @getVar($data['img']);
                if (!empty($img)) {
                     $tempInable->img = $img;
                 }                
                // 保存失败则返回错误
                if (!$tempInable->save()) {
                    return $this->fail(current($tempInable->getFirstErrors()));
                }
            }            
            if ($temp->save()) {
                return $this->success([]);
            }
        }

        return $this->fail('');
    }

    /**
     * 保存模板适用的鹰群
     *
     * @date: 2018年7月6日 上午9:22:43
     * <AUTHOR>
     * @param array $groupIds 鹰群id
     * @param integer $tempId 模板id
     * @return bool|string
     */
    private function _saveTempGroup(array $groupIds, $tempId){
        if ( empty( $groupIds ) ){
            $groupIds = [0];
        }

        $tran = \Yii::$app->integle_ineln->beginTransaction();
        // 删除所有鹰群
        $updateResult = TemplateForGroupModel::updateAll(['status'=>0], ['template_id'=>$tempId, 'status'=>1]);
        if ( $updateResult === false ){
            \Yii::error('更新（逻辑删除）模板适用鹰群失败', __METHOD__);
            $tran->rollBack();
            return false;
        }

        // 添加鹰群
        foreach ( $groupIds as $groupId ){
            if($groupId == '') {
                $groupId = 0;
            }
            $tempForGroup = TemplateForGroupModel::find()->where(['template_id'=>$tempId, 'group_id'=>$groupId])->one();
            if ( !$tempForGroup ){
                $tempForGroup = new TemplateForGroupModel();
                $tempForGroup->setAttributes([
                    'template_id'=>$tempId,
                    'group_id'=>$groupId
                ]);
            }
            $tempForGroup->status = 1;
            if ( !$tempForGroup->save() ){
                $tran->rollBack();
                return current($tempForGroup->getFirstErrors());
            }
        }

        $tran->commit();
        return true;
    }




    /**
     * 复制模板
     *
     * <AUTHOR> @copyright 2016-3-28
     *
     * @param array $postData ['idArr'=>'复制的id，可以是数组或者单个id','user_id'=>'用户id','insertData'=>'模板基本数据']
     * @return Ambigous <\service\controllers\json, multitype:number string unknown >
     */
    public function duplicTemp($postData) {
        if (empty($postData['idArr']) || empty($postData['user_id']) || empty($postData['insertData'])) {
            return $this->fail(\Yii::t('base', 'lost_params').':idArr, user_id, insertData');
        }

        $idArr = $postData['idArr'];

        if (!is_array($idArr)) {
            $idArr = [
                $idArr
            ];
        }

        $userId = $postData['user_id'];
        $insertData = $postData['insertData'];

        $tempModel = new TemplateModel();
        $duplData = $tempModel->getTempByIds($idArr);



        if(empty($duplData)){
        	return $this->fail(\Yii::t('base', 'no_temp_data'));
        }

        $newInsertData = [];
        $saveData = [];
        $relayModel = new TemplateRelayModel();

        /* $needApproval = !empty($setting['require_approval']); */
        //获取用户详细信息
        $userDetail = (new CenterInterface())->getUserAllInfoByCompanyId([$userId]);
        $userId2Detail = array_column($userDetail, null, 'user_id');

        //获取企业模板审批设置
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $cmpTempSetting = json_decode($setting, true);

        foreach ($duplData as $key=>$data) {
            $relayData = $this->getTemp($data['id'], TRUE);

            // 获取模板管理审批设置，判断是否需要审核

            if (isset($data['type'])&&($data['type'] == 1 || $data['type'] == 2)) { // 全文模板 || 方法模板 才需要审批
                $needApproval = $this->checkTemplateApproval(null, 'create_template', $data['type'], $data['subtype_id'], $userId2Detail[$userId], 0, $cmpTempSetting);
            } else {
                $needApproval = false;
            }


            if(empty($relayData['data'])){
            	continue;
            }

            $relayData = $relayData['data'];

            $insertData['title'] = !empty($relayData['base_data']['title']) ? $relayData['base_data']['title'] : '';
            $insertData['keywords'] = !empty($relayData['base_data']['keywords']) ? $relayData['base_data']['keywords'] : '';
            $insertData['define_item'] = !empty($relayData['base_data']['define_item']) ? $relayData['base_data']['define_item'] : ''; // add by hkk 2019/10/17
            $insertData['step'] = $needApproval ? 1 : 3; // 根据是否需要审核设置模板状态(by zhu huajun at 2020/4/26 14:57)
            $relayData['insertData'] = $insertData;

            $saveData[ ] = $relayData;
        }

        if(!empty($saveData)){
            $tran = \Yii::$app->getDb()->beginTransaction();

            $res = $tempModel->addTemplate($saveData, TRUE);


            if ( isset($res['status']) && $res['status'] == 1 ){
                $groupIds = $postData['group_ids'];
                $tempId = $res['data'];
                // 添加适用鹰群
                $addGroup = $this->_saveTempGroup($groupIds, $tempId);
                if ( $addGroup !== true ){
                    $tran->rollBack();
                    return $this->fail($addGroup);
                }
                $tran->commit();
            } else {
                $tran->rollBack();
            }

        }

        return $res;
    }

    /**根据模板id列表获取全文模板 || 方法模板的类型
     * @param array $tempIds
     * @return array ['id'=>模板信息]
     * @auther: dx
     * @date:2022/9/5
     */
    public function getFullOrFuncTempTypeByIds($tempIds) {
        if (empty($tempIds)) {
            return $this->fail('no temp id provided!!!');
        }
        //获取模板信息
        $tempsData = TemplateModel::find()->where([
            'id' => $tempIds
        ])->asArray()->all();

        foreach ($tempsData as $key => &$temp) { //仅获取全文模板 || 方法模板的类型
            if (in_array($temp['type'], [1,2,4])) { // 全文模板 || 方法模板 才需要审批
                $isCompany = ($temp['is_company'] == 1) ? 'company' : 'normal'; //is_company: 1:企业模板,0:非企业模板
                $isFullTemp = ($temp['type'] == 1) ? 'full' : 'function';    //type: 1:全文模板,2:方法模板
                $templateType = sprintf('%1$s_%2$s', $isCompany, $isFullTemp);
                $temp['temp_type_str'] = $templateType;
            } else {
                $temp['temp_type_str'] = 'other_temp';
            }
        }
        $tempId2Res = array_column($tempsData, null, 'id');
        return $this->success($tempId2Res);
    }

    /**
     * @param array $tempIds
     * @param int $userId
     * @param string $operate_type = 'create_template' || 'share_template'
     * @return array ['id'=>模板信息]
     * @throws Exception
     * @auther: dx
     * @date:2022/9/5
     */
    public function getIfTempNeedApproval($tempIds, $userId, $operate_type) {
        if (empty($tempIds)) {
            throw (new Exception('no template id provided for check approval'));
        }
        //获取模板类型
        $tempRes = (new TempleServer())->getFullOrFuncTempTypeByIds($tempIds);
        if (empty($tempRes['status'])) {
            return $this->fail('no template id found!!!');
        }
        $tempData = $tempRes['data'];
        //获取企业模板审批设置
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $cmpTempSetting = json_decode($setting, true);
        //获取用户详细信息
        $userDetail = (new CenterInterface())->getUserAllInfoByCompanyId([$userId]);
        $userId2Detail = array_column($userDetail, null, 'user_id');
        foreach ($tempData as $key => &$temp) {
            switch ($temp['type']) {
                case 1:
                case 2:
                case 4://全文模板 || 方法模板 才需要审批
                    $needApproval = $this->checkTemplateApproval(null, $operate_type, $temp['type'], $temp['subtype_id'], $userId2Detail[$userId], 0, $cmpTempSetting);
                    break;
                default:   //其他模板不需要审批
                    $needApproval = false;
            }
            $temp['need_approval'] = $needApproval;
        }
        return $this->success($tempData);
    }

    /**查找指定模板的类型，返回模板类型的字符串
     * @param $tempIds
     * @return array|\yii\db\ActiveRecord[] normal_full || company_full || normal_function || company_function
     * @throws Exception
     * @auther: dx
     * @date:2022/9/16
     */
    public static function formatTemplateType($tempIds) {
        if (empty($tempIds)) {
            throw new Exception('no temp id provided!!!');
        }
        $temps = TemplateModel::find()->where([
                'id' => $tempIds,
                'status' => 1,
            ])->asArray()->all();
        foreach ($temps as &$temp) {
            $is_company_str = !empty($temp['is_company']) ? 'company' : 'normal';   //是否是企业模板
            switch ($temp['type']) { //模板类型：1=全文模板(full)，2=方法模板(function)，others=默认模板，表格模板
                case 1:
                    $type_str = 'full';
                    break;
                case 2:
                case 4:
                    $type_str = 'function';
                    break;
                default:
                    $type_str = 'others';
            }
            $temp['template_type'] = ($type_str == 'others') ? 'others' : sprintf('%1$s_%2$s', $is_company_str, $type_str);//其他模板暂时不考虑是否是企业级
        }
        return array_column($temps, null, 'id');
    }

    /**
     * 删除模板
     *
     * @param id 模板主键
     */
    public function delTemp($postData) {
        if (empty($postData['idArr'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'lost_params').'：user_id');
        }

        $idArr = $postData['idArr'];

        $res = (new TemplateModel())->delTemp($idArr, $postData['user_id']);

        
        // 记录痕迹
        if(!is_array($idArr)) {
            $idArr = [$idArr];
        }
        foreach($idArr as $tempId) {
            $this->addActionLog($tempId, TemplateActionLogModel::REMOVE);
        }

        return $res;
    }

    /**
     * 获取模板组件
     *
     * <AUTHOR> @copyright 2016-3-24
     * @param int $id 模板id
     * @return Ambigous <\service\controllers\json, multitype:number string unknown >|multitype:Ambigous <\yii\db\ActiveRecord, unknown>
     */
    public function actionGetCompentById($id) {
        $postData = $this->actionGetTemp($id, TRUE);

        $newPostData = [];
        foreach ($postData as $key=>$data){
            $key = \yii\helpers\StringHelper::underScoreToCamelCase($key);

            $newPostData[$key] = $data;
        }
        return $newPostData;
    }

    /**
     * 将模板设为经典模板
     *
     * <AUTHOR> @copyright 2016-3-30
     *
     * @param arary $postData ['id'=>'模板的id']
     * @return Ambigous <\service\controllers\json, multitype:number string unknown >|number
     */
    public function setTemp($postData) {

        if (empty($postData['id'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }

        $id = $postData['id'];

        $res = (new TemplateModel())->setSystemTemp($id);

        if(empty($res)){
        	return $this->fail(\Yii::t('base', 'failed'));
        }

        return $this->success($res);
    }

    /**
     *
     * <AUTHOR>
     * 模板列表
     * @return Ambigous <\service\models\ineln\Ambigous, multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function listTempLate($user_id, $isData='') {
        $type = NULL;
        if(!empty($isData)){
        	$type = 1;
        }

        $tempLateData = (new TemplateModel())->listTemplate($user_id, $type);

        foreach ($tempLateData as $key=>$val) {
            if (!empty($val['edit_user_id'])) {

                if (!isset($tempLateData[$key]['edit_user_name'])) {
                    $tempLateData[$key]['edit_user_name'] = '';
                }

                // 通过接口获取用户name
                $userDetail = (new CenterInterface())->getUserByUserId($val['edit_user_id']);
                $tempLateData[$key]['edit_user_name'] = isset($userDetail['real_name']) ? $userDetail['real_name'] : '';
            }
        }
        return $this->success($tempLateData);
    }


    /**
     * 获取新建实验可以用的模板
     *
     * <AUTHOR> @copyright 2017-7-19
     * @param arary $params ['group_ids'=>'鹰群ids','user_id'=>'用户id']
     * @return Ambigous <\frontend\core\array(status,, multitype:string int string mixed >
     */
    public function tempForExp($params){
        $tempLateData = (new TemplateModel())->tempListForExp($params);

        return $this->success($tempLateData);
    }

    /**
     * Notes: 获取用于实验筛选的模板列表
     * Author: zhu huajun
     * Date: 2020/5/7 10:46
     * @param $params
     * @return array
     */
    public function tempForExpFilter($params) {
        $query = TemplateModel::find()->select('temple.id,temple.name')
            ->from(TemplateModel::tableName() . ' AS temple')
            ->leftJoin(ShareModel::tableName() . ' AS share', 'share.parent_id = temple.id AND share.user_id = temple.user_id')
            ->where([
                'temple.status' => 1,
                'temple.type' => 1,
            ])->andWhere([
                'or',
                [
                    'share.to_group_id' => $params['group_ids'],
                    'share.type' => 2,
                    'share.status' => 1,
                    'share.share_type' => 1,
                ],
                [
                    'share.to_user_id' => $params['user_id'],
                    'share.type' => 2,
                    'share.status' => 1,
                    'share.share_type' => 1,
                ],
                [
                    'temple.user_id' => $params['user_id']
                ],
                [
                    'temple.is_system' => 1
                ],
                [
                    'temple.is_company' => 1,
                    //'temple.company_id' => $params['company_id'] //company_id字段被废弃
                ]
            ])->orderBy('temple.create_time DESC');

        $list = $query->asArray()->all();

        return $this->success($list);
    }

    /**
     * 子模板列表
     *
     * <AUTHOR> @copyright 2016-4-19
     *
     * @param $postData ['component_id'=>'组件id','group_id'=>'鹰群id','user_id'=>'用户id']
     */
    public function subListTemp($postData){

        if(empty($postData['component_id']) || empty($postData['group_id']) || empty($postData['user_id'])){
        	return $this->fail(\Yii::t('base', 'lost_params').'：component_id、group_id、user_id');
        }

    	$data = (new TemplateModel())->subListTemp($postData['user_id'], $postData['component_id'], $postData['group_id'],1, $postData['company_id']);

    	return $this->success($data);
    }

    /**
     * 获取用户所在的所有鹰群的名字和鹰群编号,鹰群ID
     *
     * <AUTHOR>
     * @copyright 2016--4-17
     * @return
     */
    public function listUserGroup($data, $companyId=0){
        $user_id = $data['user_id'];
        //$user_id = 31;
        $groupList = (new CenterInterface())->elnGroupListByUserId($user_id, $companyId);

        $result = [
        'group_ids' => array_column($groupList, 'group_id'),
        'group_list' => $groupList
        ];
        if(!empty($result)){
            /*所有鹰群的ID*/
            foreach ($result['group_list'] as $k){
                $data[]=[
                'group_id'=>$k['group_id'],
                'group_code'=>$k['group_code'],
                'name'=>  $k['group_name']
                ];
            }
            return $data;
        }else{
            return $result;
        }
    }

    /**
     * 获取用户分享过此组模板的用户列表和鹰群列表
     *
     * @param $user_id 用户ID
     * @param $parent_id 模板ID
     * <AUTHOR>
     * @copyright
     * @return
     */
    public function listUserAndGroup($data){

        /*  $user_id = 31;
         $parent_ids = [65,66,67,68];  */

        $user_id = $data['user_id'];
        $parent_ids = $data['parent_ids'];

        $res = (new ShareModel())->listUserAndGroup($user_id, $parent_ids);
        $result = [];
        foreach ($res as $k){
            foreach ($k as $v){

                if($v['to_user_id']==0){
                    $result['to_group_ids'][]=$v['to_group_id'];
                }else{
                    $result['to_user_ids'][]=$v['to_user_id'];
                }
            }
        }

        return $this->success($result);
    }

    /**
     * 获取模板适用的鹰群列表
     *
     * @date: 2018年7月6日 上午11:17:01
     * <AUTHOR>
     * @param integer $templateId
     * @return array
     */
    public function listTempForGroup($templateId, $userId){
        // 获取鹰群id列表
        if($templateId) {
            $groupIds = TemplateForGroupModel::find()->where(['template_id'=>$templateId, 'status'=>1])->select('group_id')->column();
        }
        else {
           $groupIds=[];
        }

        // 获取我的鹰群列表
        $elnGroupList = (new CenterInterface())->elnGroupListByUserId($userId);
        //print_r($elnGroupList);exit;
        foreach ($elnGroupList as $key=>$group){
            $elnGroupList[$key]['is_check']=0;
            if ( in_array($group['group_id'], $groupIds) ){
                //unset($elnGroupList[$key]);
                $elnGroupList[$key]['is_check']=1;
            }
        }

        return $elnGroupList;
    }

    /**获取模板相关鹰群
     * @param $tempId
     * @param $userId
     * @return array
     * @auther: dx
     * @date:2022/9/1
     */
    public function getGroupIdsRelayTemp($tempId) {
        // 获取鹰群id列表
        if($tempId) {
            $groupIds = TemplateForGroupModel::find()->where(['template_id'=>$tempId, 'status'=>1])->select('group_id')->column();
        }
        else {
            $groupIds=[];
        }
        return $groupIds;
    }

    /**
     * 设置企业模板
     *
     * @param id 模板主键
     */
    public function setCompanyTemp($postData) {
        if (empty($postData['idArr'])) {
            return $this->fail(\Yii::t('temp', 'select_temp'));
        }
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'lost_params').'：user_id');
        }
        $idArr = $postData['idArr'];

        try {
            $res = (new TemplateModel())->setCompanyTemp(
                $idArr,
                $postData['user_id'],
                $postData['status'],
                $postData['company_id']
            );
            $resData = @getVar($res['data'], []);
            if (empty($res['status'])) {
                return $this->fail($res['info'], ['tipType' => @getVar($resData['tipType'], '')]);
            }
            return $res;
        } catch (Exception $e) {
            \Yii::info('setCompanyTemp 异常失败 Exception: ' . $e->getMessage());
        } catch (\yii\base\Exception $e) {
            \Yii::info('setCompanyTemp 异常失败 \yii\base\Exception: ' . $e->getMessage());
        }

        return $this->fail(\Yii::t('base', 'failed'));
    }


    /**
     *
     * <AUTHOR>
     * 模板列表
     * @return Ambigous <\service\models\ineln\Ambigous, multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function listTempLateNew($where, $limit = null, $page = null, $orderBy = null, $orderType = null) {
        $query = TemplateModel::find()->from(TemplateModel::tableName() . ' t');
        $query->select[] = 't.*';
        $query->select[] = 'u.status isHide';

        $query->leftJoin(TemplateUserHide::tableName() . ' u', 't.id = u.temp_id AND u.user_id = :user_id', [':user_id' => $where['user_id']]);

        // switch ($where['module_type']) {
        //     case 1:
        //         $query->andWhere(['t.type' => [1, 3]]);
        //         break;
        //     case 2:
        //         $query->andWhere(['t.type' => [2, 4]]);
        //         break;
        //     case 3:
        //         $query->andWhere(['or',
        //             ['t.is_system' => 1],
        //             ['t.email' => '<EMAIL>']]);
        //         break;
        // }


        //添加对template_main_types和template_sub_types的筛选
    
        if(!empty($where['template_main_types']) && is_array($where['template_main_types'])) {
            $mainConditions = ['or']; // 主类型条件组，使用OR连接
            
            // 处理template_main_types和template_sub_types，构建条件组mainConditions，并添加到查询中。
            foreach($where['template_main_types'] as $mainType) { // 循环处理每个主类型。
                // 特殊处理mainType为3的情况，添加额外的查询条件。
                    if($mainType == 3) {
                        $mainConditions[] = ['or',
                            ['=', 't.is_system', 1],
                            ['=', 't.email', '<EMAIL>']
                        ];
                        continue;
                    }
                    
                    // 检查是否有对应的子类型
                    if(!empty($where['template_sub_types']) && isset($where['template_sub_types'][$mainType]) && !empty($where['template_sub_types'][$mainType])) {
                        // 有子类型，构建 (type = mainType AND (subtype = x OR subtype = y...))
                        $subConditions = ['or'];
                        foreach($where['template_sub_types'][$mainType] as $subType) {
                            $subConditions[] = ['=', 't.subtype_id', $subType];
                        }
                        
                        $mainConditions[] = ['and',
                            ['=', 't.type', $mainType],
                            $subConditions
                        ];
                    } else {
                        // 没有子类型，只判断主类型
                        $mainConditions[] = ['=', 't.type', $mainType];
                    }
            }
            
            // 将构建的条件添加到查询中
            if(count($mainConditions) > 1) { // 确保有至少一个条件
                $query->andWhere($mainConditions);
            }
        }

        if (isset($where['module_keywords']) && !empty($where['module_keywords'])) {
            $query->andWhere([
                'or',
                ['like', 't.name', $where['module_keywords']],
                ['like', 't.descript', $where['module_keywords']],
                ['like', 't.content', $where['module_keywords']],
                ['like', 't.title', $where['module_keywords']],
                ['like', 't.keywords', $where['module_keywords']],
            ]);
        }

        // add by hkk 2019/11/19  加上用户id过滤或系统模板或企业模板
        if (!empty($where['user_id']) && !empty($where['company_id']) ) {
            /// todo 有查看全公司模板权限时,视作可以查看公司所有模板,其他情况都视作需要根据用户筛选 bug#34847 cmt dx
            /// 查看模板权限是通过用户id筛选限制,查看全公司模板权限可以覆盖查看可见用户模板
            if (empty($where['company_template_manage'])) {    //如果不能管理企业所有模板
                $query->andWhere([
                    'or',
                    ['t.user_id' => $where['user_id']],
                    ['=','t.is_system' , 1],
                    ['=', 't.is_company', 1],
                ]);
            }
        }
        $query->andWhere(['t.status' => 1]);
        // end by hkk 2019/11/19

		//added by xieyuxiang 2022.8.23 增加模板来源
        if (isset($where['module_from'])) {
            switch ($where['module_from']) {
                case 1:
                    $query->andWhere(['t.is_company' => 1]);
                    break;
                case 2:
                    $query->andWhere(['t.user_id' => $where['user_id'], 't.tfrom' => 2]);
                    break;
                case 3:
                    $query->andWhere(['t.tfrom' => 4]);
                    break;
                case 4:
                    $query->andWhere(['t.tfrom' => 1]);
                    break;
                case 5:
                    $query->andWhere(['or',
                        ['t.is_system' => 1],
                        ['t.email' => '<EMAIL>']]);
                    break;
                case 6:
                    $query->andWhere(['t.tfrom' => 3]);
                    break;
            }
        }

        //added by xieyuxiang 2022.8.29 模板管理页面传入的模板拥有者筛选和是否为企业模板筛选
        if(isset($where['module_owner'])&& $where['module_owner']!=0)
        {
            $query->andWhere(['t.user_id'=>$where['module_owner']]);
        }
        if(isset($where['is_company'])&&$where['is_company']!=0)
        {
            if($where['is_company']==1)
            {
                $query->andWhere(['t.is_company'=>1]);
            }
            else
            {
                $query->andWhere(['t.is_company'=>0]);
            }
        }
        $totalCount = $query->count();
        $query->offset(($page - 1) * $limit)->limit($limit);
        $query->orderBy('t.id desc'); // EA.update_time DESC ,
        $result = $query->asArray()->all();

        if (!empty($result)) {  //修改 经典生物 和 经典化学 模板的翻译
            $result = array_column($result, null, 'id');
            array_walk($result, function (&$temp) {
                switch ($temp['name']) {
                    case 'Classic biological template':
                        $temp['name'] = \Yii::t('views/set_require', 'bio_template');
                        break;
                    case 'Classic chemical template':
                        $temp['name'] = \Yii::t('views/set_require', 'chem_template');
                        break;
                }
            });
        }

        //添加获取最新生效中的历史版本版本号，添加到模板列表中，用于显示最新版本号。
        if (!empty($result)) {
            $tempIds = array_keys($result); //获取模板ID列表，用于获取最新生效中的历史版本版本号。
            $tempHistory = TemplateHistoryNewModel::find()
                ->where([
                    'template_id' => $tempIds,
                    'status' => TemplateHistoryNewModel::$status['is_agreed'], // 审核通过且已生效状态
                ])
                ->orderBy('id DESC') // 最新的记录
                ->asArray()
                ->all();
            $tempHistory = array_column($tempHistory, null, 'template_id');
            array_walk($result, function (&$temp) use ($tempHistory) {
                $temp['active_version'] = @getVar($tempHistory[$temp['id']]['user_version'], '');
            });
        }


        //根据鹰群ID 获取鹰群信息

        $data['totalCount'] = $totalCount;
        $data['temp_list'] = $result;


        return $data;
    }

    /**
     * Notes: 查询模板是否为当前用户可查看(包括分享给我的模板)
     * Author: he siliang
     * Date : 2024/6/26 18:40
     * @param $userId
     * @param $tempId
     * @return bool
     */
    public function checkTemplateReadable($userId, $tempId)
    {
        if (empty($userId)) {
            return false;
        }

        // 查询此条记录
        $templateRecord = TemplateModel::find()->Where(['id' => $tempId, 'status' => 1])->one();
        if (empty($templateRecord)) {
            return false;
        }

        // 判断是否是企业/系统模板或者由用户创建
        if (1 == $templateRecord['is_company']
            || 1 == $templateRecord['is_system']
            || $userId == $templateRecord['user_id']) {
            return true;
        }

        // 获取用户所在的鹰群列表
        $groupList = (new CenterInterface())->elnGroupListByUserId($userId);
        $groupIds = array_column($groupList, 'group_id');

        // 判断是否是分享给用户
        $shareRecord = ShareModel::find()->Where([
            'type' => 2, // 对应模板的分享
            'status' => CommonModel::STATUS_ACTIVE,
            'parent_id' => $tempId
        ])->andWhere(['OR', ['to_user_id' => $userId], ['to_group_id' => $groupIds]])->one();
        if (!empty($shareRecord)) {
            return true;
        }

        // 判断是否由用户审核
        $approvalRes = (new ApprovalServer())->listApprovalByType($userId, \Yii::$app->params['approval_type']['template'], [
            'business_id' => $tempId
        ]);
        if (@getVar($approvalRes['total'], 0) > 0) {
            return true;
        }

        //<editor-fold desc="判断用户是否为分享模板的审批人">
        $approvalTypeShareTemp = \Yii::$app->params['approval_type']['template_share'];
        // 模板分享的businessId参数错误, 不能通过business参数筛选
        $shareTempApproval = (new ApprovalServer())->listApprovalByType($userId, $approvalTypeShareTemp, []);
        // 分享给当前用户的所有模板审批
        $shareToUserAllApprovalList = yii\helpers\ArrayHelper::getValue($shareTempApproval, 'list', []);
        // 分享给当前用户的当前模板审批
        $shareToUserCurrTempApprovalList = array_values(array_filter($shareToUserAllApprovalList, function ($approval) use ($tempId) {
            $apvExtraDataJson = yii\helpers\ArrayHelper::getValue($approval, 'extra_data');
            $apvExtraData = json_decode($apvExtraDataJson, true);
            $shareTempIdList = yii\helpers\ArrayHelper::getValue($apvExtraData, 'temp_id_arr', []);
            return in_array($tempId, $shareTempIdList);
        }));
        if (count($shareToUserCurrTempApprovalList) > 0) {
            return true;
        }
        //</editor-fold>


        // 获取公司设置
        $companySettings = CompanyAuthServer::getCompanyAuthByUserId($userId, 1);

        //! 判断是否有 查看全公司模板权限 (查看模板管理（公司所有模板）)
        $companyTemplateManage = yii\helpers\ArrayHelper::getValue($companySettings,
            ['company_feature', 'company_template_manage'], '0');
        if (!empty($companyTemplateManage)) {
            return true;
        }

        //! 判断是否有 查看可见人员模板权限 (查看模板管理（可见人员的模板）)
        $visibleUserTemplateManage = yii\helpers\ArrayHelper::getValue($companySettings,
            ['company_feature', 'visible_user_template_manage'], '0');

        if (!empty($visibleUserTemplateManage)) {
            $userList = (new CenterInterface())->getVisibleUsers($userId);
            $visUserIds = array_column($userList, 'user_id');
            // 如果是可见人员，即可查看
            if (!empty($templateRecord['user_id'])) {
                if (in_array($templateRecord['user_id'], $visUserIds)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**为approvalList中的每个approval添加模板信息
     * @param array $approvalList 获得的审批列表
     * @param array $filter 筛选条件
     * @param int $page 获得的审批列表
     * @param int $limit 获得的审批列表
     * @return array $approvalList [0=>[..., 'temp_details'=>[]]]
     * @auther: dx
     * @date:2022/8/26
     */
    public function getShareTemplateData($approvalList, $filter, $page = 1, $limit = 15) {
        $createTempIds = [];
        $shareTempIds = [];

        array_walk($approvalList, function (&$approval) use (&$createTempIds, &$shareTempIds) {
            $approval['extra_data'] = json_decode($approval['extra_data'], true);
            switch ($approval['type']) {
                case \Yii::$app->params['approval_type']['template_share_batch']:
                case \Yii::$app->params['approval_type']['template_share']: //如果是分享模板审批
                    $shareTempIds = array_merge($shareTempIds, $approval['extra_data']['temp_id_arr']);
                    break;
                case \Yii::$app->params['approval_type']['publish_template']: //如果是发布模板审批
                    $shareTempIds[] = $approval['extra_data']['template_id'];
                    break;
                default:    //默认为创建模板审批
                    $shareTempIds[] = $approval['business_id'];
            }
        });
        $tempIds = array_values(array_unique(array_merge($createTempIds, $shareTempIds)));

        // if (!empty($filter['template_type'])) {     //筛选模板类型
        //     $temp_filter_types = $filter['template_type'];
        //     $filter['type'] = isset($filter['type']) ? $filter['type'] : [];//防止模板类型筛选条件不存在的问题
        //     foreach ($temp_filter_types as $temp_filter_type) {
        //         //! 根据前端筛选模板类型构造模板类型筛选参数
        //         switch ($temp_filter_type) {
        //             case 1:
        //                 $filter['is_company'][] = 0;
        //                 $filter['type'][] = 1;
        //                 break;
        //             case 2:     //普通方法模板
        //                 $filter['is_company'][] = 0;
        //                 //$filter['type'][] = [2, 4];
        //                 array_push($filter['type'], 2, 4);
        //                 break;
        //             case 3:     //企业全文模板，企业模板审批通过step区分
        //                 $filter['is_company'][] = 1;
        //                 $filter['type'][] = 1;
        //                 break;
        //             case 4:     //企业方法模板
        //                 $filter['is_company'][] = 1;
        //                 //$filter['type'][] = [2, 4];
        //                 array_push($filter['type'], 2, 4);
        //                 break;
        //         }
        //     }


        //     unset($temp_filter_type);
        // }       


        $temp_details = $this->getTemplateByIdAndFilters($tempIds, $filter);
        $temp_details_map = array_column($temp_details, null, 'id');

        $approval_list = [];
        foreach ($approvalList as $approval) {
            $extraData = $approval['extra_data'];
            switch ($approval['type']) {
                case \Yii::$app->params['approval_type']['template']:
                    $app_temp_ids = [$approval['business_id']];
                    break;
                case \Yii::$app->params['approval_type']['template_share_batch']:
                case \Yii::$app->params['approval_type']['template_share']:
                    $app_temp_ids = $extraData['temp_id_arr'];
                    break;
                case \Yii::$app->params['approval_type']['set_company_template']: // 设为企业模板
                    $app_temp_ids = [$approval['business_id']];
                    break;
                case \Yii::$app->params['approval_type']['publish_template']: // 发布模板审批
                    $app_temp_ids = [$extraData['template_id']];
                    break;
            }
            array_walk($app_temp_ids, function ($temp_id) use (&$approval, $temp_details_map) {
                if (!empty($temp_details_map[$temp_id])) {//筛选出了模板才展示
                    $approval['temp_details'][$temp_id] = $temp_details_map[$temp_id];
                    // $approval_list[] = $approval;
                }
            });
            // bug#10298, 模板批量分享为了兼容老数据, 一个审批对应多个模板的情况
            $approval_list[] = $approval;
        }

        $total = count($approval_list);
        // 根据审批分页来处理数据
        $startIndex = ($page - 1) * $limit;
        $approval_list = array_slice($approval_list, $startIndex, $limit);

        return [
            'total' => $total,
            'list' => $approval_list
        ];
    }

    /**根据模板id查询模板所有信息
     * @param array $tempIds 模板id arr
     * @param array $filters 筛选条件
     * @return array|\yii\db\ActiveRecord[] 所有模板所有信息 ['id'=>[]]
     * @throws Exception
     * @auther: dx
     * @date:2022/8/26
     */
    public function listTemplateByIds($tempIds, $filters) {
        $query = TemplateModel::find()->where([
            'id' => $tempIds,
            /*'status' => 1*/
        ]);
        return $query->asArray()->all();
    }


    /** 根据模板id和过滤条件查找模板信息
     * @param $temp_ids
     * @param $filters
     * @return array|\yii\db\ActiveRecord[]
     * @auther: dx
     * @date:2023/1/7
     */
    public function getTemplateByIdAndFilters($temp_ids, $filters) {
        $query = TemplateModel::find()->where([
            'id' => $temp_ids,
        ]);

        //是否企业模板的筛选
        if (!empty($filters['is_company'])) {
            $normal_approve_temp_filter = [];//需要审批的普通模板筛选条件
            $company_approve_temp_filter = [];//需要审批的企业模板筛选条件

            if (in_array(0, $filters['is_company'])) {//审批对应模板是普遍模板
                $normal_approve_temp_filter = [
                    'and',
                    ['<>', 'step', TemplateModel::$step['is_company_temp_approving']],
                    ['is_company' => 0],
                ];
            }
            if (in_array(1, $filters['is_company'])) {//审批对应模板是企业模板
                $company_approve_temp_filter = [
                    'or',
                    ['and', ['step' => TemplateModel::$step['is_company_temp_approving']], ['is_company' => 0]],
                    ['is_company' => 1],
                ];
            }
            $query->andWhere([
                'or',
                $normal_approve_temp_filter,
                $company_approve_temp_filter
            ]);
        }

        //template_main_types 来自于template_main_types，是“，”分隔的字符串，为空时传入空数组
        if(empty($filters['template_main_types'])) {
            $filters['template_main_types'] = [];
        } else {
            $filters['template_main_types'] = explode(',', $filters['template_main_types']);//转化为数组，才能和数据库中的值比较。
        }
        //template_sub_types来自于template_subtypes_json，是json格式的字符串，需要转化为字符串数组，才能和数据库中的值比较，为空时传入空数组。
        if(empty($filters['template_subtypes_json'])) {
            $filters['template_sub_types'] = [];
        } else {
            $filters['template_sub_types'] = json_decode($filters['template_subtypes_json'], true);
        }
        // 按模板类型筛选
        if (!empty($filters['template_main_types']) && is_array($filters['template_main_types'])) {
            $mainConditions = ['or']; // 主类型条件组，使用OR连接
            
            // 处理主类型和子类型，构建条件组
            foreach ($filters['template_main_types'] as $mainType) {
                // 特殊处理系统模板
                if ($mainType == 3) {
                    $mainConditions[] = [
                        'or',
                        ['=', 'is_system', 1],
                        ['=', 'email', '<EMAIL>']
                    ];
                    continue;
                }
                
                // 检查是否有对应的子类型
                if (!empty($filters['template_sub_types']) && 
                    isset($filters['template_sub_types'][$mainType]) && 
                    !empty($filters['template_sub_types'][$mainType])) {
                    
                    // 有子类型，构建 (type = mainType AND (subtype = x OR subtype = y...))
                    $subConditions = ['or'];
                    foreach ($filters['template_sub_types'][$mainType] as $subType) {
                        $subConditions[] = ['=', 'subtype_id', $subType];
                    }
                    
                    $mainConditions[] = [
                        'and',
                        ['=', 'type', $mainType],
                        $subConditions
                    ];
                } else {
                    // 没有子类型，只判断主类型
                    $mainConditions[] = ['=', 'type', $mainType];
                }
            }
            
            // 将构建的条件添加到查询中
            if (count($mainConditions) > 1) { // 确保有至少一个条件
                $query->andWhere($mainConditions);
            }
        }

        if (!empty($filters['page']) && !empty($filters['limit'])) {
            $query->limit($filters['limit'])->offset(($filters['page'] - 1) * $filters['limit']);
        }

        $temps = $query->asArray()->all();
        return $temps;
    }

    /**
     *全公司内模板回收站列表
     */
    public function listRecycle($where, $limit, $page, $orderBy, $orderType,$isSystem=1) {
        $query = TemplateModel::find()->from(TemplateModel::tableName() . ' t');
        $query->select[] = 't.*';
        $query->select[] = 'u.status isHide';

        $query->leftJoin(TemplateUserHide::tableName() . ' u', 't.id = u.temp_id AND u.user_id = :user_id', [':user_id' => $where['user_id']]);

        $query->innerJoin(RecycleModel::tableName() . ' r', 't.id = r.parent_id AND r.type = 2 AND r.status = 1');
        $query->select[] = 'r.user_id operator';
        $query->select[] = 'r.update_time remove_time';
        $query->select[] = 'r.id recycle_id';

        if ($where['module_type'] > 0) {
            if ($where['module_type'] == 2) {
                $query->andWhere([
                    'or',
                    ['=', 't.type', 2],
                    ['=', 't.type', 4],
                ]);
            } else {
                $query->andWhere(['t.type' => $where['module_type']]);
            }

//            $query->andWhere([
//                'and',
//                ['=', 't.type', $where['module_type']],
//            ]);
        }

        if (isset($where['module_keywords']) && !empty($where['module_keywords'])) {
            $query->andWhere([
                'or',
                ['like', 't.name', $where['module_keywords']],
                ['like', 't.descript', $where['module_keywords']],
                ['like', 't.content', $where['module_keywords']],
                ['like', 't.title', $where['module_keywords']],
                ['like', 't.keywords', $where['module_keywords']],
            ]);
        }

        // add by hkk 2019/11/19  加上用户id过滤或系统模板或企业模板
        if($isSystem==1){
            if (!empty($where['user_id']) && !empty($where['company_id']) ) {
                $query->andWhere([
                    'or',
                    ['=','t.user_id' , $where['user_id']],
                    ['=','t.is_system' , 1],
                    ['and',
                        ['=', 't.is_company', 1],
                        ['=', 't.company_id', $where['company_id']]
                    ]
                ]);
            }
        }

        if($isSystem==0){
            if (!empty($where['user_id']) && !empty($where['company_id']) ) {
                $query->andWhere([
                    'or',
                    ['=','t.user_id' , $where['user_id']],
                    ['in','t.is_system' , [0,1]],
                    ['and',
                        ['=', 't.is_company', 1],
                        ['=', 't.company_id', $where['company_id']]
                    ]
                ]);
            }
        }


        $query->andFilterWhere(['t.user_id' => $where['owner_id']]);
        $query->andFilterWhere(['r.user_id' => $where['operator_id']]);

        $query->andWhere(['t.status' => 0]);
        $visibleUserList = (new CenterInterface())->getVisibleUsers(\Yii::$app->view->params['curr_user_id']);
        $visibleUserIds = array_column($visibleUserList, 'id');
        if($isSystem==0){
            $visibleUserIds=array_merge($visibleUserIds,[0]);
        }
        $query->andWhere(['t.user_id' => $visibleUserIds]);

        // end by hkk 2019/11/19


        $totalCount = $query->count();
        $query->offset(($page - 1) * $limit)->limit($limit);
        //$query->orderBy('t.id desc'); // EA.update_time DESC ,
        $query->orderBy('r.update_time desc');
        $result = $query->asArray()->all();

        foreach($result as $key=>$item){
            if (in_array($item['id'], [697, 698])) {
                switch ($item['name']) {
                    case 'Classic biological template':
                    case '经典生物模板':
                        $result[$key]['name'] = \Yii::t('views/set_require', 'bio_template');
                        break;
                    case 'Classic chemical template':
                    case '经典化学模板':
                        $result[$key]['name'] = \Yii::t('views/set_require', 'chem_template');
                        break;
                }
            }
        }

        //根据鹰群ID 获取鹰群信息

        $data['totalCount'] = $totalCount;
        $data['temp_list'] = $result;


        return $data;
    }

    /**
     * Notes: 获取模板的修订设置
     *
     * Author: zhu huajun
     * Date: 2019/12/4 15:16
     * @param $templateId
     * @return array
     */
    public function getRevisionSetting($templateId) {
        $relays = TemplateRelayModel::find()->where([
            'template_id' => $templateId,
            'component_id' => [2, 3, 11, 12], // 修订模式只针对文本编辑器模块
            'status' => 1
        ])->orderBy('class ASC')->asArray()->all();

        $returnData = [];
        foreach($relays as $relay) {
            // 配置字段项
            $config = json_decode($relay['config'], true);

            array_push($returnData, [
                'id' => $relay['id'],
                'name' => $relay['name'],
                'revision_mode' => !empty($config['revision_mode']),
            ]);
        }

        return $this->success($returnData);
    }

    /**
     * Notes: 获取模板的修订设置
     * Author: zhu huajun
     * Date: 2019/12/4 15:31
     * @param $settings
     * @return array
     */
    public function saveRevisionSetting($settings) {
        foreach($settings as $s) {
            if(empty($s['relay_id'])) {
                continue;
            }

            $relay = TemplateRelayModel::findOne($s['relay_id']);
            if($relay) {
                $config = json_decode($relay['config'], true);
                $config['revision_mode'] = $s['revision_mode'];
                $relay['config'] = json_encode($config);
                $relay->save();
            }
        }
        return $this->success([]);
    }
    /**
     * 插入子模板
     * <AUTHOR> @copyright 2016-5-7
     *
     * @param arary $postData ['temp_real'=>'模板组件数据','insertData'=>'模板基本数据']
     * @return Ambigous <\service\models\ineln\Ambigous, boolean, \service\models\ineln\multitype:, multitype:>
     */
    public function addXsTemp($postData){

        return (new TemplateModel())->addXsTemp($postData);
    }

    /**
     * 子模板列表
     *
     * <AUTHOR> @copyright 2016-4-19
     *
     * @param $postData ['component_id'=>'组件id','group_id'=>'鹰群id','user_id'=>'用户id']
     */
    public function xsListTemp($postData){


        $groupList = (new CenterInterface())->elnGroupListByUserId($postData['user_id'], 1);
        $groupIdArr = array_column($groupList, 'group_id');

    	$data = (new TemplateModel())->xsListTemp($postData['user_id'],  $groupIdArr,1, 1);
    	return $this->success($data);
    }

    /**
     * 子模板列表
     *
     * <AUTHOR> @copyright 2016-4-19
     *
     * @param $postData ['component_id'=>'组件id','group_id'=>'鹰群id','user_id'=>'用户id']
     */
    public function getXsTempById($id){
    	$data = (new TemplateModel())->getXsContent($id);

        // added by xyx 2023.10.20 bug36459 有些模版加密了，需要解密
        if (!(substr($data['content'], 0, 1) == '[' || substr($data['content'], 0, 1) == '{')) {
            $data['content'] = gzdecode(base64_decode($data['content']));
        }
        return $this->success($data);
    }

    /**
     * Notes: 获取模板的修订设置
     * Author: zhu huajun
     * Date: 2019/12/4 15:31
     * @param $settings
     * @return array
     */
    public function saveSyncWms($relayId,$data) {

            $relay = TemplateRelayModel::findOne($relayId);
            if($relay) {
                $config = json_decode($relay['config'], true);
                $config['sync_wms'] = $data;
                $relay['config'] = json_encode($config);
                $relay->save();
            }

        return $this->success([]);
    }

    /**
     * Notes: 获取模板的修订设置
     * Author: zhu huajun
     * Date: 2019/12/4 15:31
     * @param $settings
     * @return array
     */
    public function getDefineKeyByRelayId($relayId) {

        $data = (new DefineTableModuleServer())->getData($relayId,2);
        return $data;

    }


    /**
     * Notes: 获取模板基础信息
     *
     * Author: zhu huajun
     * Date: 2020/4/27 15:51
     * @param $tempId
     * @return array
     */
    public function getBasicById($tempId) {
        $template = TemplateModel::find()->where([
            'id' => $tempId
        ])->asArray()->one();

        if($template) {
            return $this->success($template);
        }

        return $this->fail('');
    }

    /**
     * Notes: 更新模板状态
     *
     * Author: zhu huajun
     * Date: 2020/4/27 11:31
     * @param $tempId
     * @param $step
     * @return array
     */
    public function updateStep($tempId, $step) {
        TemplateModel::updateAll([
            'step' => $step
        ], [
            'id' => $tempId
        ]);
        return $this->success([]);
    }

    /**
     * Notes: 批量查询模板基本信息
     * Author: zhu huajun
     * Date: 2020/4/27 13:40
     * @param $templateIds
     * @return array
     */
    public function listBasicByIds($templateIds) {
        $list = TemplateModel::find()->where([
            'id' => $templateIds
        ])->asArray()->all();
        return $this->success($list);
    }


    /**
     * Notes: 添加模板痕迹
     *
     * Author: zhu huajun
     * Date: 2020/4/29 10:59
     * @param $templateId
     * @param $type
     * @param $content
     * @param $remark
     * @return array
     */
    public function addHistory($templateId, $type, $content = null, $remark = '',$transferValue='') {
        // 获取最近一条痕迹
        $lastHistory = TemplateHistoryModel::find()->where([
            'template_id' => $templateId
        ])->orderBy('id DESC')->asArray()->one();

        $version = @getVar($lastHistory['version'], 0);
        if (empty($content)) {
            $content = @getVar($lastHistory['content'], '');
        }
        $newHistory = new TemplateHistoryModel();
        //下一版本号
        $nextVersion=(int)$version;
        if($type == TemplateHistoryModel::TYPE_SAVE){
            $nextVersion+=1;
        }
        $newHistory->setAttributes([
            'template_id' => $templateId,
            'type' => $type,
            'content' => $content,
            'remark' => $remark,
            'version' => $nextVersion,
            'create_by' => \Yii::$app->view->params['curr_user_id'],
            'transfer_value' => $transferValue
        ]);

        if(!$newHistory->save()) {
            return $this->fail('');
        }

        return $this->success([]);
    }

    /**
     * Notes: 获取痕迹列表
     *
     * Author: zhu huajun
     * Date: 2020/4/29 14:25
     * @param $templateId
     * @return array
     */
    public function listHistory($templateId) {
        $historyList = TemplateHistoryModel::find()->where([
            'template_id' => $templateId
        ])->orderBy('id DESC')->asArray()->all();
        return $this->success($historyList);
    }

    /**
     * Notes: 获取痕迹内容
     *
     * Author: zhu huajun
     * Date: 2020/4/29 15:00
     * @param $historyId
     * @return array
     */
    public function getHistory($historyId) {
        $history = TemplateHistoryModel::find()->where([
            'id' => $historyId
        ])->asArray()->one();
        return $this->success($history);
    }

    /**
     * Notes: 获取对接库存的配置信息
     * Author: zhu huajun
     * Date: 2019/12/4 15:31
     * @param $relayId
     * @return array
     */
    public function getSyncWms($relayId) {
        $syncData = [];

        $relay = TemplateRelayModel::findOne($relayId);
        if ($relay) {
            $config = json_decode($relay['config'], true);
            $syncData = isset($config['sync_wms']) ? $config['sync_wms'] : [];
        }

        return $syncData;
    }

    /**
     * 保存全局模板 只保存组件列表和位置，数据不保存，与getTempById方法一致，暂时未使用
     *
     * <AUTHOR> @copyright 2016-3-11
     * @param int $id 模板id
     * @param number $isUse 是否需要获取之后进行直接使用
     * @param number $temp_type 1表示全局 2表示子模板
     */
    public function getSubTemp($id, $isUse=FALSE, $temp_type=1) {


        if(2 == $temp_type){
            $temp = (new TemplateModel())->getSubTemplate($id);

            return $this->success($temp);
        }


    }

    /**
     * Notes: 显示或隐藏模板
     * Author: szq
     * Date: 2020/7/23 15:41
     * @param $isHide
     * @param $tempId
     * @param $userId
     * @return array
     */
    public function hideOrShowTemp($isHide, $tempId, $userId) {

        if ($isHide == 0) { // 设置隐藏
            $dbModel = TemplateUserHide::findOne([
                'user_id' => $userId,
                'temp_id' => $tempId,
            ]);

            // 无则新增
            if (!$dbModel) {
                $dbModel = new TemplateUserHide();
                $dbModel->setAttributes([
                    'user_id' => $userId,
                    'temp_id' => $tempId,
                ]);
            }

            // 修改
            $dbModel['status'] = 1;
            $dbModel->save();
        } else { // 设置显示
            TemplateUserHide::updateAll(['status' => 0], [
                'user_id' => $userId,
                'temp_id' => $tempId,
            ]);
        }

        return $this->success([]);

    }

    /**
     * Notes: 转让模板的业务接口 TODO：step还未考虑转让审核功能
     * Author: xie yuxiang
     * Date : 2022/8/23 10:20
     * @param $tempId 模板的id
     * @param $transferTo 模板转让对象的id
     * @param $transferUserId 转让动作执行者的id
     * @param $previousId 模板上一任拥有者的id
     */
    public function updateTransferTemp($tempId,$transferTo,$transferUserId,$previousId){
        TemplateModel::updateAll([
            'tfrom' => 4,//4代表转让
            'user_id'=> $transferTo,
            'transfer_id'=>$transferUserId,
            'previous_id'=>$previousId
            //step以后在增加审核功能后也需要修改
        ], [
            'id' => $tempId
        ]);


        //记录日志 
        $transferFrom = (new CenterInterface())->getUserByUserId($previousId)['real_name'];
        $transferTo = (new CenterInterface())->getUserByUserId($transferTo)['real_name'];
        $extraData = [
            'transferFrom' => $transferFrom, 
            'transferTo' => $transferTo,
        ];
        $this->addActionLog($tempId, TemplateActionLogModel::TRANSFER, $transferUserId, json_encode($extraData));
       
        return $this->success([]);
    }


    /**
     * Notes: 判断模板需不需要审批
     * Author: lcy
     * Date : 2022/8/23 10:20
     * @param $tempId 模板的id (可不用)
     * @param string $operate_type 操作类型 create_template（包括修改模板） || share_template
     * @param string $templateType 模板类型 1为全文模板 2为文本方法模板 4为表格方法模板
     * @param string $templateSubtype 模板子类型id
     * @param array $userDetail 用户信息
     * @param array $returnType 返回数据类型
     * @param array $cmpTempSetting 企业模板设置（可选）
     */
    public function checkTemplateApproval($tempId,$operate_type,$templateType,$templateSubtype,$userDetail, $returnType=0, $cmpTempSetting=[]){
        // 获取通用设置，判断是否需要审核
        if (empty($cmpTempSetting)) {   //如果外界没有传入企业模板设置，就查询
            $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
            $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
            $setting = json_decode($setting, true);
        } else {
            $setting = $cmpTempSetting;
        }
        $needApproval=false;
        $needApproveSetting = [];//mod dx,returnType=1时返回的审批信息

        if (!empty($setting)) {
            foreach($setting as $approval){
                $approval['user_type']=isset($approval['user_type'])?$approval['user_type']:1; //如果没有参数 默认按鹰群
                //templateType 为 1,2,4 时， main_type分别为"full","intext","intable"
                $main_type = $templateType == "2"?"intext":($templateType == "4"?"intable":"full");
                //检查approval[$main_type]所对应的列表中是否包含等于templateSubtype的id
                $approval[$main_type] = isset($approval[$main_type]) ? $approval[$main_type] : [];
                $subTypeIds = array_column($approval[$main_type],'id');
                $subTypeExisted = false;
                // 检查子类型是否存在
                if(isset($templateSubtype) ) {
                    // 将templateSubtype转为字符串进行比较
                    $strTemplateSubtype = (string)$templateSubtype;
                    foreach($subTypeIds as $id) {
                        if($strTemplateSubtype === (string)$id) {
                            $subTypeExisted = true;
                            break;
                        }
                    }
                }

                if(!$subTypeExisted){
                    continue;
                }
                //按鹰群
                if(@getVar($approval['user_type']) ==1 && @getVar($approval[$operate_type])==1  && $subTypeExisted){
                    //如果为空则
                    if(empty($approval['group_ids'])){
                        $needApproval=true;
                        $needApproveSetting = $approval;
                        break;
                    }else{
                        $approvalGroupIds = explode(',',$approval['group_ids']);
                        $userGroupIds = is_array($userDetail['group_ids']) ? $userDetail['group_ids'] : explode(',',$userDetail['group_ids']);
                        if(array_intersect($userGroupIds,$approvalGroupIds)){
                            $needApproval=true;
                            $needApproveSetting = $approval;
                            break;
                        }
                    }
                }
                //按部门
                if(@getVar($approval['user_type']) ==2 && @getVar($approval[$operate_type])==1  && $subTypeExisted){
                    //如果为空则
                    if(empty($approval['department_ids'])){
                        $needApproval=true;
                        $needApproveSetting=$approval;
                        break;
                    }else{
                        $approvalDepartmentIds = explode(',',$approval['department_ids']);
                        $userDetail['dep_ids']=isset($userDetail['dep_ids'])?$userDetail['dep_ids']:(isset($userDetail['department_ids'])?$userDetail['department_ids']:'');
                        $userDepartmentIds = is_array($userDetail['dep_ids']) ? $userDetail['dep_ids'] : explode(',',$userDetail['dep_ids']);
                        if(array_intersect($userDepartmentIds,$approvalDepartmentIds)){
                            $needApproval=true;
                            $needApproveSetting=$approval;
                            break;
                        }
                    }
                }
            }
        }

        //这个返回
        if ($returnType==1){
            return $needApproveSetting; //mod dx,修改为返回命中的审批组设置, 并返回调试信息
        }
        else{
            return $needApproval;
        }

    }

    /**
     * 判断当前用户新建、保存模板是否需要审核
     */
    public static function needApproval($userId) {
        // 获取通用设置，判断是否需要审核
        $userList = (new CenterInterface())->getUserAllInfoByCompanyId(1,'',[$userId],1);
        $userDetail = $userList[0]; //获取用户信息 根据用户信息获取审批信息
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $setting = json_decode($setting, true);
        $needApproval=false;
        if (!empty($setting)) {
            foreach($setting as $approval){

                $approval['user_type']=isset($approval['user_type'])?$approval['user_type']:1; //如果没哟参数 默认按鹰群
                //按鹰群

                if($approval['user_type'] ==1){
                    //如果为空则
                    if(empty($approval['group_ids'])){
                        $needApproval=true;
                    }else{
                        $approvalGroupIds = explode(',',$approval['group_ids']);
                        $userGroupIds = explode(',',$userDetail['group_ids']);
                        if(array_intersect($userGroupIds,$approvalGroupIds)){
                            $needApproval=true;
                        }
                    }
                }
                //按部门
                if($approval['user_type'] ==2){
                    //如果为空则
                    if(empty($approval['department_ids'])){
                        $needApproval=true;
                    }else{
                        $approvalDepartmentIds = explode(',',$approval['department_ids']);
                        $userDepartmentIds = explode(',',$userDetail['dep_ids']);
                        if(array_intersect($userDepartmentIds,$approvalDepartmentIds)){
                            $needApproval=true;
                        }
                    }
                }

            }
        }
        return $needApproval;
    }

    /**
     * 复制文本方法模板
     *
     * <AUTHOR> Qin
     *
     * @param int $old_temp_id 要复制的模板ID
     * @param array $insertData 新模板的基本数据
     * @param int $userId 用户ID
     * @return array 结果数组
     */
    public function duplicIntextTemp($old_temp_id, $insertData, $userId) {
        $tran = \Yii::$app->integle_ineln->beginTransaction();
        
        try {
            // 获取原模板数据
            $oldTemp = TemplateModel::findOne([
                'id' => $old_temp_id,
            ]);
            
            if (empty($oldTemp)) {
                return $this->fail(\Yii::t('base', 'no_temp_data'));
            }
            
            // 获取原模板的文本内容
            $oldIntextData = TemplateIntextData::find()
                ->where(['template_id' => $old_temp_id])
                ->one();
            
            if ($oldIntextData) {
                $insertData['content'] = $oldIntextData->content;
            }
            
            $insertData['descript'] = $oldTemp->descript;
            
            // 创建新模板
            $data = [
                'is_sub' => TRUE,
                'insertData' => $insertData,
                'temp_real' => [['component_id' => 2]]
            ];
            
            $result = (new TemplateModel())->addSubTemp($data);
            if (empty($result['status'])) {
                $tran->rollBack();
                return $result;
            }
            
            $tran->commit();
            return $this->success([
                'temp_id' => $result['data']
            ]);
            
        } catch (\Exception $e) {
            $tran->rollBack();
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 复制表格方法模板
     *
     * <AUTHOR> Qin
     *
     * @param int $old_temp_id 要复制的模板ID
     * @param array $insertData 新模板的基本数据
     * @param int $userId 用户ID
     * @return array 结果数组
     */
    public function duplicIntableTemp($old_temp_id, $insertData, $userId) {
        $tran = \Yii::$app->integle_ineln->beginTransaction();
        
        try {
            // 获取原模板数据
            $oldTemp = TemplateModel::findOne([
                'id' => $old_temp_id,
            ]);
            
            if (empty($oldTemp)) {
                return $this->fail(\Yii::t('base', 'no_temp_data'));
            }
            
            // 获取原模板的文本内容
            $oldIntableData = TemplateIntableData::find()
                ->where(['template_id' => $old_temp_id])
                ->one();
            
            if ($oldIntableData) {
                $insertData['content'] = $oldIntableData->content;
                $insertData['img'] = $oldIntableData->img; 
            }
            
            $insertData['descript'] = $oldTemp->descript;
            
            // 创建新模板
            $data = [
                'is_sub' => TRUE,
                'insertData' => $insertData
            ];
            
            $result = (new TemplateModel())->addSubTemp($data);
            if (empty($result['status'])) {
                $tran->rollBack();
                return $result;
            }
            
            $tran->commit();
            return $this->success([
                'temp_id' => $result['data']
            ]);
            
        } catch (\Exception $e) {
            $tran->rollBack();
            return $this->fail($e->getMessage());
        }
    }

    /**
     * Note: 翻译默认模板的名称
     * @param $name
     * @return string
     * <AUTHOR>
     * @date 2023/3/9 17:38
     */
    public function translateName($name) {
        if ($name === 'Classic biological template' || $name === '经典生物模板') {
            return \Yii::t('views/set_require', 'bio_template');
        }
        if ($name === 'Classic chemical template' || $name === '经典化学模板') {
            return \Yii::t('views/set_require', 'chem_template');
        }
        return $name;
    }

    /**
     * Notes: 记录模板操作日志并生成历史记录
     * 
     * 该方法用于记录模板的各种操作，并根据操作类型决定是否生成新的历史记录。
     * 操作日志记录所有操作，而历史记录只记录特定类型的操作（如创建、保存、发布等）。
     * 
     * @param int|null $templateId 模板ID，如果为null，则从历史记录ID获取模板ID
     * @param string $action 操作类型(create/save/publish/等)
     * @param int|null $userId 操作用户ID，如果为null则使用当前登录用户ID
     * @param string|null $extraData 额外数据，用于存储操作日志的额外信息，如操作详情等。可以根据需要自定义格式，json格式。
     * @param string|null $user_version 用户自定义版本号，如果为null则不设置
     * @param string|null $status 版本状态，如果为null则不设置
     * @param string|null $effect_date 生效日期，如果为null则不设置
     * @param int|null $historyId 历史记录ID，如果为null则生成新的历史记录或者自动获取最新历史记录ID
     * 
     * @return array 操作结果，包含状态和消息
     * 
     * Author: zhu huajun
     * Date: 2025/6/3 14:25
     */
    public function addActionLog($templateId = null, $action, $userId = null, $extraData = null, $user_version = null, $status = null, $effect_date = null, $historyId = null) {
        // 如果未传入用户ID，则使用当前登录用户ID
        if ($userId === null) {
            $userId = \Yii::$app->view->params['curr_user_id'];
        }

        //支持不传入templateId，而是传入historyId
        if(empty($templateId) && isset($historyId)){
            $templateId = TemplateHistoryNewModel::findOne($historyId)->template_id; // 获取模板ID
        }

        // 二次检查templateId是否存在
        $template = TemplateModel::find()->where(['id' => $templateId])->one();
        if (empty($template)) {
            return $this->fail('template_not_found');
        }

        // 目前只给全文模板添加痕迹
        if ($template['type'] != 1) {
            return $this->fail('template_type_not_match');
        }

        // 初始化操作日志记录
        $actionLog = new TemplateActionLogModel();
        $actionLog->setAttributes([
            'template_id' => $templateId,
            'action_code' => $action,
            'create_by' => $userId,
            'extra_data' => $extraData, // 额外数据，用于存储操作日志的额外信息
        ]);

        // 保存操作日志记录
        if (!$actionLog->save()) {
            return $this->fail(current($actionLog->getFirstErrors()));
        }

        // 定义需要生成历史记录的操作类型
        $historyActions = [
            TemplateActionLogModel::CREATE, 
            TemplateActionLogModel::SAVE, 
            TemplateActionLogModel::PUBLISH,     // 无需审批时的发布
            TemplateActionLogModel::SUBMIT_PUBLISH_AUDIT, // 提交发布审批
        ];

        if (in_array($action, $historyActions)) {
            // 调用历史记录生成方法
            $res = $this->addHistoryNew($templateId, $userId, [
                'id' => $actionLog['id'],
                'code' => $action,
            ], $user_version, $status, $effect_date);
            
            if (!empty($res['status'])) {
                // 关联历史记录ID到操作日志
                $actionLog['history_id'] = $res['data'];
                $actionLog->save();
            }
        } else {
            // 如果传入了历史记录ID，则直接关联历史记录ID到操作日志
            if (isset($historyId)) {
                $actionLog['history_id'] = $historyId;
                $actionLog->save();
            } else {
                // 否则获取最新痕迹记录
                $lastHistory = TemplateHistoryNewModel::find()->where([
                    'template_id' => $templateId
                ])->orderBy('id DESC')->one();
                
                if ($lastHistory) {
                    // 关联历史记录ID到操作日志
                    $actionLog['history_id'] = $lastHistory['id'];
                    $actionLog->save();
                }
            }
        }

        return $this->success(['action_log_id' => $actionLog['id'], 'history_id' => $actionLog['history_id']]);
    }



    /**
     * Notes: 添加模板历史记录
     * 
     * 该方法用于创建模板的历史记录，记录模板在特定操作时的状态。
     * 对于发布操作，会增加内部版本号并处理其他版本的状态更新。
     * 
     * @param int $templateId 模板ID
     * @param int $userId 操作用户ID
     * @param array $action 操作信息数组，需包含：
     *     - 'id' 操作ID
     *     - 'code' 操作代码
     * @param string|null $user_version 用户自定义版本号
     * @param string|null $status 版本状态
     * @param string|null $effect_date 生效日期
     * 
     * @return array 操作结果，包含状态和新创建的历史记录ID
     * 
     * Author: zhu huajun
     * Date: 2025/6/3 14:25
     */
    public function addHistoryNew($templateId, $userId, $action, $user_version = null, $status = null, $effect_date = null) {
        // 获取模板信息
        $template = TemplateModel::find()
            ->where(['id' => $templateId])->asArray()->one();
        if (empty($template)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }

        // 获取最近一条痕迹的版本号
        $lastHistory = TemplateHistoryNewModel::find()->select('inner_version')->where([
            'template_id' => $templateId
        ])->orderBy('id DESC')->one();
        $version = @getVar($lastHistory['inner_version'], 0);

        //仅当操作为发布或提交发布审批时，版本号才会增加
        $inner_version = $version; //默认版本号与最近一个版本的版本号相同，仅当操作为发布时，版本号才会增加1，其他操作版本号不变
        if ($action['code'] == TemplateActionLogModel::PUBLISH || $action['code'] == TemplateActionLogModel::SUBMIT_PUBLISH_AUDIT) { // 发布和提交发布审批时，版本号增加1。
            $inner_version = $version + 1;
        }

        //判断生效日期是否在今日之后
        if ($action['code'] == TemplateActionLogModel::PUBLISH &&!empty($effect_date) && $effect_date > date('Y-m-d')) {
            $status = TemplateHistoryNewModel::$status['is_pending'];
        }

        //判断发布操作时是否立即生效
        if ($status == TemplateHistoryNewModel::$status['is_agreed'] && ($action['code'] == TemplateActionLogModel::PUBLISH)) { 
            $actual_effect_time = date('Y-m-d H:i:s');
        }

        // 开启事务
        $transaction = \Yii::$app->db->beginTransaction();

        // 插入模板历史记录
        $newHistory = new TemplateHistoryNewModel();
        $newHistory->setAttributes([
            'template_id' => $templateId,
            'name' => $template['name'],
            'descript' => $template['descript'],
            'title' => $template['title'],
            'keywords' => $template['keywords'],
            'define_item' => $template['define_item'],
            'action_code' => @getVar($action['code'], ''),
            'action_id' => @getVar($action['id'], 0),
            'inner_version' => $inner_version,
            'user_version' => $user_version,
            'status' => $status,
            'effect_date' => $effect_date,
            'actual_effect_time' => isset($actual_effect_time) ? $actual_effect_time : null, //实际生效时间，仅在发布和发布审批通过并立即生效操作时有效，其他操作为null。
            'create_by' => $userId
        ]);
        if (!$newHistory->save()) {
            $transaction->rollBack();
            return $this->fail(current($newHistory->getFirstErrors()));
        }

        // 处理模板发布和提交发布审批时对其他版本的状态更新
        if ($action['code'] == TemplateActionLogModel::PUBLISH || $action['code'] == TemplateActionLogModel::SUBMIT_PUBLISH_AUDIT) { // 发布和提交发布审批时，需要处理其他版本的状态更新。
            $setInvaild = $status == TemplateHistoryNewModel::$status['is_agreed'] ? true : false;
            $this->handleTemplateVersionsOnPublish($templateId, $newHistory['id'], $setInvaild);
        }

        // 获取模板的所有模块
        $modules = TemplateRelayModel::find()->where([
            'template_id' => $templateId,
            'status' => 1
        ])->asArray()->all();

        // 遍历模块        
        foreach ($modules as $module) {
            $moduleServer = null;
            switch ($module['component_id']) {
                case \Yii::$app->params['component']['chem']:
                    $moduleServer = new IndrawModuleServer();
                    break;
                case \Yii::$app->params['component']['operation']:
                case \Yii::$app->params['component']['discuss']:
                case \Yii::$app->params['component']['lite']:
                case \Yii::$app->params['component']['abstract']:
                case \Yii::$app->params['component']['diy']:
                    $moduleServer = new EditorModuleServer();
                    break;
                case \Yii::$app->params['component']['tlc']:
                    $moduleServer = new TlcModuleServer();
                    break;
                case \Yii::$app->params['component']['reference']:
                    $moduleServer = new RefModuleServer();
                    break;
                case \Yii::$app->params['component']['biology']:
                    $moduleServer = new BioModuleServer();
                    break;
                case \Yii::$app->params['component']['upload']:
                case \Yii::$app->params['component']['picture']:
                    $moduleServer = new UploadModuleServer();
                    break;
                case \Yii::$app->params['component']['comment']:
                    $moduleServer = new CommentModuleServer();
                    break;
                case \Yii::$app->params['component']['define_table']:
                    $moduleServer = new DefineTableModuleServer();
                    break;
                case \Yii::$app->params['component']['custom_table']:
                    $moduleServer = new CustomTableModuleServer();
                    break;
                case \Yii::$app->params['component']['excel']:
                    $moduleServer = new ExcelModuleServer();
                    break;
                case \Yii::$app->params['component']['xsheet']: // add by hkk 2020/3/11
                    $moduleServer = new XSheetServer();
                    break;
            }

            if ($moduleServer) {
                // 根据模板中的模块生成对应痕迹的模块
                $copyRes = $moduleServer->copyTemplateModuleToHistory($module['id'], $newHistory['id']);
                if ($copyRes['status'] != 1) {
                    $transaction->rollBack();
                    return $this->fail($copyRes['info']);
                }

                // 复制模块数据
                $newModuleId = $copyRes['data'];
                $moduleServer->pasteData($module['id'], $newModuleId, 2, 4);
            }
        }

        $transaction->commit();

        return $this->success($newHistory['id']);
    }

    /**
     * Notes: 审核通过模板历史记录
     * 
     * 该方法用于审核通过模板的历史记录，更新其状态为已生效或已通过未生效状态。
     * 
     * @param int $historyId 历史记录ID
     * @param int $userId 审核用户ID 
     * @return array 操作结果
     * 
     * 
     */
    public function agreeHistory($historyId, $userId) {
        $history = TemplateHistoryNewModel::findOne($historyId);
        if (empty($history)) {
            return $this->fail(\Yii::t('temp', 'no_history'));
        }
        
        // 如果没有生效日期或者生效日期在今天之前，则更新历史记录状态为已生效
        if (empty($history->effect_date) || $history->effect_date <= date('Y-m-d')) {
            $history->status = TemplateHistoryNewModel::$status['is_agreed'];
            $history->actual_effect_time = date('Y-m-d H:i:s');
            $this->handleTemplateVersionsOnPublish($history->template_id, $historyId, true);
        }else {
            $history->status = TemplateHistoryNewModel::$status['is_pending'];
        }
        
        if (!$history->save()) {
            return $this->fail(current($history->getFirstErrors()));
        }
        
        return $this->success([]);
    }



    /**
     * 更新模板历史记录的状态，版本号，生效日期等信息
     */
    public function updateHistory($historyId, $status = null, $user_version = null, $effect_date = null) {
        $history = TemplateHistoryNewModel::findOne($historyId);
        if (empty($history)) {
            return $this->fail(\Yii::t('temp', 'no_history'));
        }
        
        if ($status !== null) {
            $history->status = $status;
        }
        if ($user_version !== null) {
            if ($user_version == $history->user_version) {
                return $this->fail(\Yii::t('temp', 'no_version_changed'));
            }
            if (!$this->checkVersionValid($history->template_id, $user_version)) {
                return $this->fail(\Yii::t('temp', 'version_repeat'));
            }
            $extraData = [
            'versionFrom' => $history->user_version, 
            'versionTo' => $user_version,
                ];
            $history->user_version = $user_version;
            $this->addActionLog($history->template_id, TemplateActionLogModel::EDIT_VERSION, \Yii::$app->view->params['curr_user_id'], json_encode($extraData), $historyId);
        }
        if ($effect_date !== null) {
            $extraData = [
            'dateFrom' => $history->effect_date, 
            'dateTo' => $effect_date,
            'version' => $history->user_version,
                ];
            $history->effect_date = $effect_date;
            $this->addActionLog($history->template_id, TemplateActionLogModel::EDIT_EFFECTIVE_DATE, \Yii::$app->view->params['curr_user_id'], json_encode($extraData), $historyId);
        }
        if (!$history->save()) {
            return $this->fail(current($history->getFirstErrors()));
        }
        
        return $this->success([]);
    }



    /**
     * Summary of getActionLogs
     * @param mixed $tempId
     */
    public function getActionLogs($tempId) {
        $logs = TemplateActionLogModel::find()->where([
            'template_id' => $tempId
        ])->asArray()->all();
        return $this->success($logs);
    }

    /**
     * 获取显示版本的详情，优先获取审核通过未生效状态的最新版本，如无则获取审核通过已生效状态的最新版本，如无则获取最新版本。
     * @param int $tempId 模板ID
     * @return array 返回成功或失败的结果，包含版本详情
     */
    public function getDisplayVesionDetail($tempId) {
        // 1. 优先获取审核中或者审核通过未生效状态的最新版本 (status=2或5)
        $pendingVersion = TemplateHistoryNewModel::find()
            ->where([
                'template_id' => $tempId,
                'status' => [TemplateHistoryNewModel::$status['is_approving'], TemplateHistoryNewModel::$status['is_pending']], // 审核中或者审核通过未生效状态
            ])
            ->orderBy('id DESC') // 最新的记录
            ->one();
            
        if (!empty($pendingVersion)) {
            return $this->success($pendingVersion);
        }
        
        // 2. 如果没有待生效的版本，获取最近一条已生效的版本 (status=3)
        $effectiveVersion = TemplateHistoryNewModel::find()
            ->where([
                'template_id' => $tempId,
                'status' => TemplateHistoryNewModel::$status['is_agreed'], // 审核通过且已生效状态
            ])
            ->orderBy('id DESC') // 最新的记录
            ->one();
            
        if (!empty($effectiveVersion)) {
            return $this->success($effectiveVersion);
        }
        
        // 3. 如果没有任何已发布的版本，获取最近一条任意状态的版本
        $anyVersion = TemplateHistoryNewModel::find()
            ->where([
                'template_id' => $tempId
            ])
            ->orderBy('id DESC')
            ->one();
            
        
        return $this->success($anyVersion);
    }

    /**
     * 获取模板的最大内部版本号,用于展示
     * @param mixed $tempId
     */
    public function getMaxInnerVersion($tempId) {
        $maxVersion = TemplateHistoryNewModel::find()
            ->where([
                'template_id' => $tempId
            ])
            ->max('inner_version');

        if (empty($maxVersion)) {
            return 0;
        }
        return $maxVersion;
    }    

    /**
     * 处理模板发布和提交发布审核时对其他版本的状态更新
     * 当一个新版本发布或者提交发布审核申请时，需要将其他已生效版本标记为失效
     * 同时将审核中的版本和已通过审核但未生效的版本标记为已取消状态
     * 
     * @param int $templateId 模板ID
     * @param int $excludeHistoryId 需要排除的历史记录ID（通常是新创建的记录）
     * @return array 处理结果
     */
    public function handleTemplateVersionsOnPublish($templateId, $excludeHistoryId = null, $setInvaild = true) {
        try {
            // 开启事务
            $transaction = \Yii::$app->db->beginTransaction();
            
            // 构建基本条件
            $baseCondition = ['template_id' => $templateId];
            
            // 如果提供了排除ID，添加到条件中
            if ($excludeHistoryId !== null) {
                $baseCondition = ['and', $baseCondition, ['!=', 'id', $excludeHistoryId]];
            }
            $count = 0;
            // 1. 将当前模板的所有已生效版本标记为失效
            if($setInvaild){
                $count = TemplateHistoryNewModel::updateAll(
                    ['status' => TemplateHistoryNewModel::$status['is_invalid']], // 设置为失效状态(7)
                    ['and', 
                        $baseCondition,
                        ['status' => TemplateHistoryNewModel::$status['is_agreed']] // 当前为已生效状态的版本(3)
                    ]
                );
            }
            
            // 2. 先获取所有需要取消的审核中的版本ID
            $approvingHistories = TemplateHistoryNewModel::find()
                ->select('id, user_version, status')
                ->where(['and',
                    $baseCondition,
                    ['status' => TemplateHistoryNewModel::$status['is_approving']] // 当前为审核中状态的版本(2)
                ])
                ->asArray()
                ->all();
            $count += count($approvingHistories);
            // 为审核中的版本添加取消发布的操作日志
            foreach ($approvingHistories as $history) {
                $extraData = [
                    'version' => $history['user_version'],
                    'status' => $history['status'],
                ];
                $this->addActionLog(
                    $templateId, 
                    TemplateActionLogModel::CANCEL_PUBLISH, 
                    \Yii::$app->view->params['curr_user_id'], 
                    json_encode($extraData), 
                    $history['id']
                );
            }
            
            // 将审核中的版本标记为已取消状态
            TemplateHistoryNewModel::updateAll(
                ['status' => TemplateHistoryNewModel::$status['is_canceled']], // 设置为已取消状态(6)
                ['and',
                    $baseCondition,
                    ['status' => TemplateHistoryNewModel::$status['is_approving']] // 当前为审核中状态的版本(2)
                ]
            );
            
            // 3. 先获取所有需要取消的已通过审核但未生效的版本ID
            $pendingHistories = TemplateHistoryNewModel::find()
                ->select('id, user_version, status')
                ->where(['and',
                    $baseCondition,
                    ['status' => TemplateHistoryNewModel::$status['is_pending']] // 当前为审核通过未生效状态的版本(5)
                ])
                ->asArray()
                ->all();
            $count += count($pendingHistories);
            
            // 为已通过审核但未生效的版本添加取消发布的操作日志
            foreach ($pendingHistories as $history) {
                $extraData = [
                    'version' => $history['user_version'],
                    'status' => $history['status'],
                ];
                $this->addActionLog(
                    $templateId, 
                    TemplateActionLogModel::CANCEL_PUBLISH, 
                    \Yii::$app->view->params['curr_user_id'], 
                    json_encode($extraData), 
                    $history['id']
                );
            }
            
            // 将已通过审核但未生效的版本标记为已取消状态
            TemplateHistoryNewModel::updateAll(
                ['status' => TemplateHistoryNewModel::$status['is_canceled']], // 设置为已取消状态(6)
                ['and',
                    $baseCondition,
                    ['status' => TemplateHistoryNewModel::$status['is_pending']] // 当前为审核通过未生效状态的版本(5)
                ]
            );
            
            $transaction->commit();
            //返回更新的数量和模板id
            return $this->success(['template_id' => $templateId, 'count' => $count]);
        } catch (\Exception $e) {
            if (isset($transaction)) {
                $transaction->rollBack();
            }
            \Yii::error('处理模板版本状态失败: ' . $e->getMessage(), 'template');
            return $this->fail($e->getMessage());
        }
    }
    

    /** 获取执行发布操作过的模板版本列表 */
    public function getVersionList($tempId) {
        $versionList = TemplateHistoryNewModel::find()
            ->where(['template_id' => $tempId]) // 仅获取当前模板的版本列表
            ->andWhere(['<>', 'status', TemplateHistoryNewModel::$status['is_script']]) // 不获取草稿状态的版本列表
            ->orderBy('id DESC')
            ->asArray()
            ->all();
        return $this->success($versionList);
    }

    /**版本号非重校验 
     * @param int $tempId 模板ID
     * @param string $userVersion 用户版本号
     * 版本号有重复时，返回false
     * return boolean
    */
    public function checkVersionValid($tempId, $userVersion) {
        $userVersion = trim($userVersion);
        $versionList = TemplateHistoryNewModel::find()
            ->where(['template_id' => $tempId, 'user_version' => $userVersion])
            ->one();
        if (!empty($versionList)) {
            return false; // 版本号已存在，返回false。
        }
        return true; // 版本号不存在，返回true。
    }


    /**
     * Notes: 取消发布
     * Author: Zheyu Qin
     * Date: 2025/06/11
     * @param int $historyId 历史记录ID
     * @return array 操作结果
     */
    public function cancelPublish($historyId) {
        $history = TemplateHistoryNewModel::findOne($historyId);
        if (empty($history)) {
            return $this->fail(\Yii::t('temp', 'no_history'));
        }
        //只有审核中和待生效状态可以取消发布
        if (!in_array($history->status, [TemplateHistoryNewModel::$status['is_approving'], TemplateHistoryNewModel::$status['is_pending']])) {
            return $this->fail(\Yii::t('temp', 'cancel_publish_error'));
        }
        if ($history->status == TemplateHistoryNewModel::$status['is_approving']) {
            // 撤销审批
            (new ApprovalServer())->cancelApproval(\Yii::$app->params['approval_type']['publish_template'], $historyId);
        }
        // 添加取消发布操作日志
        $extraData = [
            'version' => $history->user_version,
            'status' => $history->status,
        ];
        $this-> addActionLog($history->template_id, TemplateActionLogModel::CANCEL_PUBLISH, \Yii::$app->view->params['curr_user_id'], json_encode($extraData), $historyId);

        $history->status = TemplateHistoryNewModel::$status['is_canceled'];
        if (!$history->save()) {
            return $this->fail(current($history->getFirstErrors()));
        }
        
        return $this->success([]);
    }


    /**
     * 获取模板历史版本详细信息
     * 
     * @param int $id 历史版本ID
     * @return array 包含历史版本详细信息的数组
     */
    public function getTempHistoryById($id) {
        // 获取历史版本基本信息
        $history = TemplateHistoryNewModel::find()
            ->select('id, template_id, name, descript, title, keywords, define_item, action_code, action_id, inner_version, user_version, status, effect_date, create_by, create_time, actual_effect_time')
            ->where(['id' => $id])
            ->asArray()
            ->one();
        
        if (empty($history)) {
            return $this->fail(\Yii::t('temp', 'no_history'));
        }
        
        $tempId = $history['template_id'];
        
        // 获取模板基本信息
        $temp = TemplateModel::find()
            ->select('id, user_id, name AS temp_name, type, is_company, tfrom, descript AS temp_descript, keywords, title, create_time, update_time, is_system, system_type, define_item, step, subtype_id')
            ->where(['id' => $tempId])
            ->asArray()
            ->one();
        
        if (empty($temp)) {
            return $this->fail(\Yii::t('temp', 'no_temp'));
        }
        
        // 准备返回数据
        $tempData = [
            'base_data' => $temp,
            'history_data' => $history
        ];
        
        // 获取历史版本关联的模块数据
        $modules = TemplateHistoryRelayModel::find()
            ->where(['history_id' => $id, 'status' => 1])
            ->orderBy('class ASC')
            ->asArray()
            ->all();
        
        // 遍历获取模块数据
        $moduleDataArr = [];
        foreach ($modules as $key => &$module) {
            $moduleInfo = [
                'info' => [
                    'id' => $module['id'],
                    'component_id' => $module['component_id'],
                    'component_type' => $module['component_type'],
                    'name' => $module['name'],
                    'class' => $module['class'],
                    'config' => $module['config'],
                    'template_relay_id' => $module['template_relay_id']
                ],
                'data' => []
            ];
            
            // 根据组件类型获取对应的数据
            switch ($module['component_id']) {
                case \Yii::$app->params['component']['chem']:
                    $dataResult = (new IndrawModuleServer())->getData($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['operation']:
                case \Yii::$app->params['component']['discuss']:
                case \Yii::$app->params['component']['lite']:
                case \Yii::$app->params['component']['abstract']:
                    $dataResult = (new EditorModuleServer())->getData($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['tlc']:
                    $dataResult = (new TlcModuleServer())->getData($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['upload']:
                case \Yii::$app->params['component']['picture']:
                    $dataResult = (new UploadModuleServer())->getData($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['comment']:
                    $dataResult = (new CommentModuleServer())->getData($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['define_table']:
                    $dataResult = (new DefineTableModuleServer())->getData($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['reference']:
                    $dataResult = (new ReferenceModel())->referenceDataByRelayId($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult)) {
                        $moduleInfo['data'] = $dataResult;
                    }
                    break;
                    
                case \Yii::$app->params['component']['biology']:
                    $dataResult = (new BiologyModel())->biologyDataByRelayId($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult)) {
                        $moduleInfo['data'] = $dataResult;
                    }
                    break;
                    
                case \Yii::$app->params['component']['custom_table']:
                    $dataResult = (new IntegleTableServer())->getDataByRelayId($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
                    
                case \Yii::$app->params['component']['xsheet']:
                    $dataResult = (new XSheetServer())->getDataByRelayId($module['id'], self::$sqlType['temp_his']);
                    if (!empty($dataResult['data'])) {
                        $moduleInfo['data'] = $dataResult['data'];
                    }
                    break;
            }
            
            $moduleDataArr[] = $moduleInfo;
        }
        
        $tempData['module_data_arr'] = $moduleDataArr;
        
        return $this->success($tempData);
    }   

}
