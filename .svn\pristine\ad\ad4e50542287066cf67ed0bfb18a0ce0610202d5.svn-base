<?php

namespace frontend\models;

use Yii;

/**
 * This is the model class for table "template_history_new".
 *
 * @property string $id
 * @property integer $template_id
 * @property integer $subtype_id
 * @property string $name
 * @property string $descript
 * @property string $title
 * @property string $keywords
 * @property string $define_item
 * @property string $action
 * @property integer $action_id
 * @property integer $inner_version
 * @property string $user_version
 * @property integer $create_by
 * @property string $create_time
 * @property integer $status
 * @property string $effect_date
 * @property string $actual_effect_time
 */
class TemplateHistoryNewModel extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'template_history_new';
    }


    //模板历史版本status字段
    static public $status = [
        'is_script' => 1,   // 草稿状态
        'is_approving' => 2, // 审核中状态
        'is_agreed' => 3,    // 审核通过且已生效状态
        'is_refused' => 4,   // 审核拒绝状态
        'is_pending' => 5,   // 审核通过未生效状态
        'is_canceled' => 6, // 生效日期之前取消生效状态
        'is_invalid' => 7, // 其它版本生效后自动从生效变为失效状态
    ];
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['template_id', 'subtype_id','action_id', 'inner_version', 'create_by', 'status'], 'integer'],
            [['define_item'], 'string'],
            [['create_time', 'actual_effect_time'], 'safe'],
            [['name'], 'string', 'max' => 64],
            [['descript', 'title', 'keywords', 'user_version'], 'string', 'max' => 255],
            [['action_code'], 'string', 'max' => 32],
            [['effect_date'], 'safe']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'template_id' => 'Template ID',
            'subtype_id' => 'Subtype ID',
            'name' => 'Name',
            'descript' => 'Descript',
            'title' => 'Title',
            'keywords' => 'Keywords',
            'define_item' => 'Define Item',
            'action_code' => 'Action Code',
            'action_id' => 'Action ID',
            'inner_version' => 'Inner Version',
            'user_version' => 'User Version',
            'create_by' => 'Create By',
            'create_time' => 'Create Time',
            'status' => 'Status',
            'effect_date' => 'Effect Date',
            'actual_effect_time' => 'Actual Effect Time',
        ];
    }
}
