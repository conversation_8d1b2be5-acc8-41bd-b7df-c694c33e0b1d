<?php
return [

    //dgh20190720 顶部工具栏
   'add_module' => 'Add Component',
    'company' => 'Company',
    'normal' => 'Normal',
    'template' => ' template',
    'my_temp' => 'My templates',
    'temp_name' => 'Name',
    'share_to' => 'Share to',
    'share_temp' => 'Templates shared to me',
    'set_noraml_temp' => 'Classic template',
    'own_create' => 'Self created',
    'integle_share' => 'Integle shared',
    'normal_temp' => 'Classic template',
    'all_temp' => 'Global template', //dgh
    'son_temp' => 'Method template',
    'method_temp' => 'Method template',
    'name' => 'Template Name',
    'temp_type' => 'Type', //Templates shared to me
    'temp_from' => 'Source',
    'temp_content' => 'Template content',
    'temp_descript' => 'Template description',
    'select_temp' => 'Please select a template',
    'select_one_module' => 'Please select the corresponding component',
    'must_temp_name' => 'Template name is required',
    'no_rights' => 'Access denied, please contact System Admin./Group Owner/Group ELN Admin',
    'son_must_content' => 'Content is required in sub-template.',
    'son_must_module' => 'Components are required in sub-template.', //dgh 此处子模板是什么？
    'no_temp' => 'Template does not exist',
    'no_temp_data' => 'Template does not exist', //dgh 模板数据不存在
    'temp' => 'Template',
    'must_group_ids' => 'Group ID is required',
    'no_group'=>'There is no Group available.',
    'content'=>'Global',
    'method'=>'Method',
    'def'=>'Default',
    'keywords_tips'=>'Please enter the keywords',
    'all'=>'All',
    'down_export'=>'Method template(batch)',
    'download_export'=>'Download Template',
    'select_file'=>'Select File',
    'set_revision'=>'Start text deletion trail',
    'pls_select_revision_module' => 'Please select the module that needs to enable revision settings',
    'revision_tip_1' => '1. When using this template to record experiments, the checked modules will show the traces and reasons of modification',
    'revision_tip_2' => '2. Only the settings for the text editing module',
    'temp_approve_submit' => 'Submit',
    'submit_temp_audit' => 'Submit audit',
    'cancel_temp_audit' => 'Cancel audit',
    'cancel_company_temp_audit' => 'Cancel company template audit',
    'temp_audit' => 'Template Audit',
    'temp_approval' => 'Template approval',
    'confirm_to_submit_temp_audit' => 'Are you sure to submit the template audit?',
    'confirm_to_submit_temp_approval' => 'Are you sure to submit the template approval?',
    'confirm_to_submit_temp_audit_after_save' => 'The template is saved. Do you want to submit it for approval now?<br>If you want to submit it later, click “Close” then submit it for approval in Template list.',
    'auditor' => 'Auditor',
    'approver' => 'Approver',
    'history_action_1' => 'save template',
    'history_action_2' => 'submit audit',
    'history_action_3' => 'withdraw audit',
    'history_action_4' => 'audit approved',
    'history_action_5' => 'audit rejected',
    'history_action_6' => 'move to recycle',
    'history_action_7' => 'transfer template to',
    'history_action_8' => 'restore template',
    'view_my_temp' => 'View My templates',
    'view_temp_share_to_me' => 'Templates shared',
    'no_temp_power_tip' => 'You don\'t have permission to create template, please contact system admin',//新建模板提醒翻译
    'company_temp' =>'Company template',
    'transfer'=>'Transfer',
    'transfer_id'=>'Transferor Id',
    'previous_id'=>'Previous Template Owner Id',
    'remove_by' => 'Removed By',
    'pls_select_template_type' => 'Please select template type',
    'pls_select_template' => 'Please select template',
    'module_owner'=>'Module owner',
    'module_remover'=>'Module remover',
    'experiment'=>'Experiment',
    'copy'=>'Copy',
    'temp_is_approving' => 'This template is not audited , you can\'t change it to company template',
    'intext_template' => 'Intext template',
    'intable_template' => 'Intable template',
    'subtype'=>'Subtype',
    'template_type'=>'Template type',
    'uncategorized'=>'Uncategorized',
    'expand' => 'Expand',
    'collapse' => 'Collapse',
    'input_temp' => 'Please input template name',
    'new_temp' => 'New template',
    'approval_flow' => 'Approval flow',
    'publish_tip' => 'Only after the effective date is reached, the template can be used',
    'pls_set_approval_node' => 'Please set approval nodes',
    'pls_select_node_approval' => 'Please select approval user for node %s',
    'pls_select_node_coapproval' => 'Please select co-approval user for node %s',
    'publish' => 'Publish',
    'publish_success' => 'Publish success',
    'publish_template' => 'Publish Template',
    'version' => 'Version',
    'effective_date' => 'Effective date',
    'effective_date_tips' => 'Effective date must be greater than or equal to tomorrow',
    'version_repeat' => 'Version number repeat, please re-enter',
    'no_history_version' => 'No history version',
    'pending_version' => 'Pending version',
    'current_version' => 'Current version',
    'canceled_version' => 'Canceled version',
    'invalid_version' => 'Invalid version',
    'approving_version' => 'Approving version',
    'refused_version' => 'Refused version',
    'template_id_empty' => 'Template ID cannot be empty',
    'version_exists' => 'Version already exists, please enter a different one',
    'publish_submit_success' => 'The template publish request has been submitted, please wait for the audit to be approved',
    'publish_submit_success_tips' => 'The template publish request has been submitted, please wait for the audit to be approved',
    'publish_success_tips' => 'Publish success',
    'pls_input_version' => 'Please enter version',
    'pls_select_effect_date' => 'Please select effective date',
];
