@charset "utf-8";
/**/
* {
    margin: 0;
    padding: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
    font-style: normal;
}
*:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}
body {
    font-family: verdana;
    font-size: 13px;
    line-height: 1.54;
    color: #222;
    overflow: auto;

}

.overFlowHidden{
    overflow:hidden;
}
html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}
textarea{
    resize: none;
}
a {
    color: #1976D2;
    text-decoration: none;
}
.bule{
    color: #0079d1;
}
a:hover {
    color: #15478A;
}
input[type="number"]{
    -moz-appearance:textfield;
    -ms-appearance:textfield;
}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  -ms-appearance:textfield;
}
input[type="number"]{
  -moz-appearance: textfield;
}
a[href]:hover{
    text-decoration: underline;
}
a:hover{
    color: #25659C;
}
a._btn:hover{
    text-decoration: none;
}
a:active, a:hover, a:focus {
  outline: 0;
}
iframe{
    display: block;
}
table{
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
    text-align: center;
}
table td{
    border: 1px solid #ddd;
    padding: 4px 6px;
}
.list_title td{
    padding: 0px;
}
table .head{
    background: #f6f6f6;
    color: #41abe1;
}
[class*='_btn'], [class*='btn-']{
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    -khtml-user-select: none;
    user-select: none;
}
a:hover, a:active, a:focus {
    outline: none;
}
iframe{
    border: none;
}
ol, ul, li{
    list-style: none;
}
img {
    border: none;
    vertical-align: middle;
}
[class*='_btn'], .pagination, .loading_box {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    -khtml-user-select: none;
    user-select: none;
}
[class*='col-xs-'] {
    float: left;
    padding-right: 10px;
    padding-left: 10px;
}
.v_align{
    vertical-align: middle;
}
.red{
    color: red;
    margin-left: 3px;
}
.move{
    cursor: move;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.body_left{
    line-height: 28px;
    display: inline-block;
    width: 80px;
    // min-width:75px;
    text-align: right;
    vertical-align: top;
    white-space:nowrap;
}
.body_left.big{
    width: 115px;
}
.body_left.lg{
    width: 140px;
}
.body_left.vlg{
    width:185px;
}
.relative {
    position: relative;
}
.fixed{
    position: fixed;
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
}
.absolute {
    position: absolute;
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
}
.fl, .left {
    float: left;
}
.left{
    left: 0px;
}
.fr, .right{
    float: right;
}
.right{
    right: 0px;
    top: 0px;
}
.text_left {
    text-align: left;
}
.text_right {
    text-align: right;
}
.text_center {
    text-align: center;
}
.clear:before, .clear:after, .row:before, .row:after{
    display: table;
    content: " ";
}
.clear:after{
    clear: both;
}
.hidden {
    display: none !important;
}
.hide{
    display: none;
}
.visiblity {
    visibility: hidden;
}
.small_wrong, .small_right, .small_error{
    display: inline-block;
    width: 21px;
    height: 22px;
    vertical-align: middle;
    margin: -2px 2px 0px 0px;
}
input.default{
    /*color: #aaa !important;*/
}
button, input, textarea {  /*select*/
    font-family: inherit;
    font-size: 100%;
    margin: 0;
    vertical-align: baseline;
    *vertical-align: middle;
    line-height: inherit;
    border: none;
}
button, select {
    text-transform: none;
}
button, html input[type="button"], input[type="reset"], input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}
button[disabled], input[disabled] {
    cursor: default;
}
button::-moz-focus-inner, input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
input[type="radio"], input[type="checkbox"]{
    vertical-align: middle;
    margin: -1px 3px 0px 0px;
}
.disabled{
    pointer-events: none;
    opacity: 0.7;
    cursor: no-drop;
}
.disabled_special{
    pointer-events: auto;
    opacity: 1;
    cursor: auto;
}

.instrument_disabled {
    pointer-events: none;
    opacity: 0.4;
    cursor: no-drop;
}
.input_part{
    padding: 5px 0px;
}
.border-t{
    border-top: 1px solid #eaebee;
    border-bottom: 0;
    border-left: 0;
    border-right: 0;
}
.border-b {
    border-bottom: 1px solid #eaebee;
    border-top: 0;
    border-left: 0;
    border-right: 0;
}
.border-l {
    border-left: 1px solid #eaebee;
    border-top: 0;
    border-bottom: 0;
    border-right: 0;
}
.border-r {
    border-right: 1px solid #eaebee;
    border-left: 0;
    border-top: 0;
    border-bottom: 0;
}
.border_all {
    border: 1px solid #eaebee;
}
.input, .form-control {
    display: block;
    width: 100%;
    height: 28px;
    padding: 4px 0px;
    font-size: 12px;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 3px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    -ms-transition: border-color ease-in-out .15s, -ms-box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control-noborder {
    border-color: #fff;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0);
}
textarea.input, textarea.form-control {
    height: 80px;
}
.input:focus, .form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
}
.input.iblock, .form-control.iblock{
    width: auto;
}
input.error, textarea.error, input.error:focus, textarea.error:focus, .error, .error:hover{
    border-color: #ff9797 !important;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(199,37,78, .6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(199,37,78, .6);
}
.btn.disabled, .btn.disabled:hover{
    background-color: #428bca;
    border-color: #357ebd;
    pointer-events: none;
    cursor: not-allowed;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
    opacity: .65;
}
.iblock {
    display: inline-block;
}
.block {
    display: block;
}
.btn {
    display: inline-block;
    padding: 5px 12px;
    margin-bottom: 0;
    font-size: 13px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
    text-align: center;
    min-width: 64px;
}
.btn-default {
    color: #333;
    background-color: #f8f8f8;
    border-color: #ccc;
    padding: 4px 12px;
}
.btn-default:hover, .btn-default:active {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
}
.btn-primary {
    color: #fff;
    background-color: #10a7ee;
    padding: 4px 12px;
}
.btn-primary:hover, .btn-primary:active {
    color: #fff;
    background-color: #008ed1;
}
.a-line {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100%;
}
.overhidden{
    overflow: hidden;
}
.close_all {
    background: url('../image/eln.png') -57px top;
    width: 11px;
    height: 11px;
    position: fixed;
    right: 5px;
    top: 88px;
    text-shadow: 0 1px 0 #fff;
    cursor: pointer;
    z-index: 105;
    opacity: 0.4;
}
.close_all:hover {
    background: url('../image/eln.png') -44px top;
    opacity: 1;
}
.close {
    float: right;
    font-size: 21px;
    font-weight: bold;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2;
}
.close:hover, .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=50);
    opacity: .5;
}
button.close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
}
table {
    background-color: transparent;
}
.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 20px;
}
.table td{
    border-color: #ddd;
}
.table td:last-child{
    border-right: none;
}
.modul_line .table{
    margin-bottom: 0px;
}
.literature_table .td_h, .addbiological_title{
    background: #fcfafa;
}
th{
    line-height: 36px;
}
th{
    font-weight: normal;
}
td{
    padding: 8px;
    word-wrap: break-word;
    word-break:break-all;
}
thead{
    background: #eee;
    border: 1px solid #ddd;
    line-height: 28px;
}
.tr_bg{
    background: #eee;
}
.tr_bg td{
    white-space:nowrap;
}
.show_tip{
    position: fixed;
    z-index: 99999792;
    padding: 30px 100px;
    left: 50%;
    top: 15%;
    background: #fff;
    box-shadow: 0px 0px 15px #666;
    -moz-box-shadow: 0px 0px 15px #666;
    -webkit-box-shadow: 0px 0px 15px #666;
    font-size: 15px;
    border: 3px solid #ddd;
    white-space: nowrap;
}
.follow_tip{
    position: absolute;
    z-index: 5001;
    background: #fff;
    border: 1px solid #ccc;
    box-shadow: 0px 0px 10px #a5a5a5;
    -moz-box-shadow: 0px 0px 10px #a5a5a5;
    -webkit-box-shadow: 0px 0px 10px #a5a5a5;
    padding: 10px 20px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
}

.follow_tip:before {
    content: '';
    width: 11px;
    height: 7px;
    display: inline-block;
    position: absolute;
    background: url(/image/image.png) -247px top;
    top: -7px;
    left: 11px;
    /*right: 27px;*/
}

.follow_tip:after{
    content: '';
    display: block;
    width: 100%;
    height: 7px;
    left: 0px;
    top: -7px;
    position: absolute;
}
/*.follow_tip:after, .follow_tip:before{*/
/*    content: '◆';*/
/*    position: absolute;*/
/*    left: 8px;*/
/*    font-size: 28px;*/
/*}*/
/*.follow_tip:after{*/
/*    color: #fff;*/
/*    bottom: -20px;*/
/*}*/
/*.follow_tip:before{*/
/*    color: #ddd;*/
/*    bottom: -21px;*/
/*}*/
/*.follow_tip.bottom:after{*/
/*    bottom: inherit;*/
/*    top: -20px;*/
/*}*/
/*.follow_tip.bottom:before{*/
/*    bottom: inherit;*/
/*    top: -21px;*/
/*}*/

input, button, select{
    outline: 0;
}
select.angle_input{
    padding: 0px 6px;
}
.angle_input{
    display: block;
    width: 100%;
}
.angle_input.iblock{
    display: inline-block;
    width: auto;
}
textarea.angle_input{
    height: 80px;
}
.comment_box textarea.angle_input{
    height: 80px;
}
input[type=text], input[type=number], input[type=password], .angle_input{
    height: 28px;
    padding: 4px 8px;
    color: #666;
    border: 1px solid #dcdcdc;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control {
    border: 1px solid transparent !important;
}
.inscada_operate_notes {
    width:420px;
    padding: 4px 8px;
    color: #666666;
    border: 1px solid #dcdcdc;
    border-radius:3px;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
input[type=text]:focus, input[type=number]:focus, .angle_input:focus, .inscada_operate_notes:focus{
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
}
input[readonly], input[readonly]:focus{
    border: none;
    padding: 6px 8px;
    color: #666;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}
h3{
    font-weight: inherit;
    font-size: inherit;
    margin: 0px;
}
// .loading_box{
//     text-align: center;
//     background: #eee;
//     background: rgba(0,0,0,.1);
//     z-index: 900002;
//     top: 0px;
//     left: 0px;
//     width: 100%;
//     height: 100%;
//     border-radius: 20px;
// }
// .loading_box img{
//     width: 100px;
//     border-radius: 20px;
//     margin-right: 8px;
// }
.loading_box{
    position:fixed;
    width:100%;
    height:100%;
    // top: 50%;
    // left: 50%;
    background: rgba(0,0,0,.3);
    color: #fff;

    text-align: center;
    box-shadow: 0px 0px 5px rgba(0,0,0,.3);
    padding: 0 0;
    .border-radius();
    text-shadow: 0px 0px 2px #000;
    // margin: -32px 0 0 -32px;
    z-index: 999999;
    // border-radius: 30px;
    top:0;

}
 .loading_box .middle{
    position: absolute;
    width: 70px;
    height: 70px;
    border-radius: 15px;
    left: 50%;
    top: 50%;
    margin: -35px 0 0 -35px;
}
.upload_form{
    display: inline-block;
    text-indent: 0px;
    line-height: 1.5;
}
.input_file_btn{
    white-space: nowrap;
    overflow: hidden;
    display: block;
    padding: 5px 11px;
    position: relative;
    background: #10a7ee;
    border-radius: 3px;
    -moz-border-radius: 3px;
    cursor: pointer;
    position: relative;
    color: #fff;
}
.input_file_btn:hover{
    background: #009ce5;
}

.upload_form.past_smiles{
    padding: 5px 11px;
}
.file_tip{
    display: none;
    background: #fff;
    color: #333;
    top: 31px;
    box-shadow: 0px 0px 5px #999;
    border: 1px solid #ccc;
    padding: 0px 7px;
    border-radius: 3px;
    min-width: 190px;
    right: 0px;
    z-index: 10;
}
.file_modlue .file_tip{
    right: inherit;
    left: 0px;
}
.upload_form:hover .file_tip{
    display: block;
    word-break: break-all;
    width: 301px;
    top: inherit;
    bottom: 31px;
}
.upload_form input[type="file"]{
    position: absolute;
    right: 0px;
    top: 0px;
    font-family: Arial;
    font-size: 118px;
    margin: 0px;
    padding: 0px;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    opacity: 0;
}
.footer_backend {
    background-color: #3d3d48;
    height: 40px;
    line-height: 40px;
    padding: 0px 20px;
    color: #fff;
}
.footer_backend a{
    color: #fff;
}

.pagination{
    text-align: center;
    overflow: hidden;
    padding-top: 10px;
}
.pagination a, .pagination span{
    margin: 0px 2px;
    padding: 5px 6px;
    display: inline-block;
}
.pagination a:hover{
    text-decoration: none;
    background: #7DC8EF;
    border-color: #7DC8EF;
    color :#fff;
}
.pagination .disabled:hover, .pagination .disabled a:hover {
    background: none;
    border-color: #eaebee;
    color: #999;
}
.form-control input, .form-control input:focus{
    box-shadow: none;
    -webkit-box-shadow: none;
    border: none;
}
.form-control.dis_table{
    padding: 0px 4px;
    margin: 10px 0px;
}
.dis_table{
    display: table;
}
.dis_table .dis_td{
    display: table-cell;
    width: 100%;
}
.dis_table .btn_{
    width: 1%;
    height: 34px;
    background: transparent;
    vertical-align: top;
}
.dis_table .btn_ select, .dis_table .btn_ button{
    height: 100%;
}
.dis_table .sublimt_share{
    background: none;
    white-space:nowrap;
    height: 100%;
    padding: 0px 10px;
}
.exp_title .my_exp_detial{
    position: relative;
    padding: 0px 16px 0px 8px;
    white-space: nowrap;
    text-align: center;
}
.exp_title .store{
    color: #FF0000;
}
.exp_title .store:hover{
    color: #FF0000;
}
.exp_title .store.on{
    color: #0079d1;
}
.my_exp_detial .close{
    position: absolute;
    right: -4px;
    background: url("/image/eln.png") -57px top;
    top: 4px;
    width: 11px;
    height: 11px;
    z-index: 40;
    opacity: .4;
}
.my_exp_detial .close:hover, .my_exp_detial.on .close{
    background: url("/image/eln.png") -44px top;
    opacity: 1;
}
.my_exp_detial.on .close:hover{
    background: url("/image/eln.png") -44px -11px;
    width: 14px;
    height: 14px;
    top: 2px;
    right: -6px;
}
.my_exp_detial.on .close{
    color: #fff;
}
.vertical-top{
    vertical-align: top;
}
.vertical_middle{
    vertical-align: middle;
}
.pad20{
    padding: 20px;
}
@media only screen and (max-width: 1366px) {
    .file_tip{
        left: -50px;
    }
}

.col-xs-12 {
    width: 100%;
}
.col-xs-11 {
    width: 91.66666667%;
}
.col-xs-10 {
    width: 83.33333333%;
}
.col-xs-9 {
    width: 75%;
}
.col-xs-8 {
    width: 66.66666667%;
}
.col-xs-7 {
    width: 58.33333333%;
}
.col-xs-6 {
    width: 50%;
}
.col-xs-5 {
    width: 41.66666667%;
}
.col-xs-4 {
    width: 33.33333333%;
}
.col-xs-3 {
    width: 25%;
}
.col-xs-2 {
    width: 16.66666667%;
}
.col-xs-1 {
    width: 8.33333333%;
}
input[readonly]:focus, select[readonly]:focus{
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}
.data_table td{
    border-color: #e6e6e6;
}
.quick_add_box{
    padding: 0px 0px 0px 38px;
    padding-bottom: 5px;
    background-color: #F0F0EE;
}
.quick_add_box button{
    width: 35px;
    height: 31px;
    border: 1px solid #e5e5e5;
}
.quick_add_box button[disabled]{
    opacity: .4;
    -webkit-box-opacity: .4;
    -moz-opacity: .4;
}
.quick_add_box button{
    background: -webkit-repeating-linear-gradient(#fff, #f7f7f7 50%, #eeede8 100%);
    background: -o-repeating-linear-gradient(#fff, #f7f7f7 50%, #eeede8 100%);
    background: -moz-repeating-linear-gradient(#fff, #f7f7f7 50%, #eeede8 100%);
    background: repeating-linear-gradient(#fff, #f7f7f7 50%, #eeede8 100%);
    vertical-align: middle;
}
.quick_add_box button:after{
    content: '';
    width: 26px;
    height: 22px;
    background: url('/image/eln.png');
    display: inline-block;
    vertical-align: middle;
}
.quick_add_box .quick_btn:after{
    background-position: -127px top;
}
.quick_add_box .substrates:after{
    background-position: -153px top;
}
.quick_add_box .catalysts:after{
    background-position: -179px top;
}
.quick_add_box .solvent:after{
    background-position: -205px top;
}
.quick_add_box .product:after{
    background-position: -231px top;
}
.quick_add_box input{
    /*margin: 1px 0px;*/
    width: 100%;
}
.en .search_eln_btn{
    text-align: left;
    width: 132px;
    padding: 20px 20px;
    top: 187px;
}
.pointer{
    cursor: pointer;
}

.beauty-disabled-select[disabled]::-ms-expand {
  display: none;
}
.beauty-disabled-select[disabled] {
  -webkit-appearance: none;
  -moz-appearance: none;
}

.beauty-disabled-select[disabled]:hover {
  -webkit-appearance: none;
  -moz-appearance: none;
}
.beauty-disabled-select[disabled]:hover::-ms-expand {
  display: none;
}

.search_quick_select{
    width: 160px;
    height: 32px;
    float: left;
    border: 1px solid #ddd;
}
.search_quick_wrap{
    margin-left: 165px;
    margin-right: 5px;
}
input[type=text].search_quick{
    height: 32px;
    border: 1px solid #ddd;
}
/*有编辑按钮的。*/
.quick_edit_wrap + .arrow_chendraw_wrap{
    left:1px;
    bottom: 28px;
}
.arrow_chendraw_wrap{
    left:1px;
    bottom: 2px;
    display: none;
}
.choose_quick_li:hover .arrow_chendraw_wrap{
    display: block;
}
.filter_select_box li{
    position: relative;
}
.log_part a, .log_part a:hover {
    color: #222;
    text-decoration: none;
    cursor: default;
}


/*白色背景+灰色边框的列表*/
.menu-bk-white-border-gray {
    background: #FFFFFF;
    border: 1px solid #cccccc;
    box-shadow: 0 1px 3px #AAAAAA;
}


.hover-parent-show-children__parent {}
.hover-parent-show-children__parent .hover-parent-show-children__children {
    display: none;
}
.hover-parent-show-children__parent:hover .hover-parent-show-children__children {
    display: block;
}
.hover-parent-show-children__parent .hover-parent-show-children-i-block__children {
    display: none;
}
.hover-parent-show-children__parent:hover .hover-parent-show-children-i-block__children {
    display: inline-block;
}


.border-between__parent {}
.border-between__parent .border-between__children:not(:first-child) {
    border-top: 1px solid #cccccc;
}

.vue-app-container input {
    padding: 0;
    border: none;
    box-shadow: none;
}

/* 版本状态标签样式 */
.status-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
}

.status-default {
    color: #333;
    background-color: #f4f4f4;
    border-color: #ddd;
}

.status-primary {
    color: #fff;
    background-color: #1388FF;
    border-color: #0d7bea;
}

.status-success {
    color: #fff;
    background-color: #67C23A;
    border-color: #5daf34;
}

.status-info {
    color: #fff;
    background-color: #909399;
    border-color: #82848a;
}

.status-warning {
    color: #fff;
    background-color: #E6A23C;
    border-color: #d09235;
}

.status-danger {
    color: #fff;
    background-color: #F56C6C;
    border-color: #e05656;
}

.status-tag:hover {
    opacity: 0.85;
}
