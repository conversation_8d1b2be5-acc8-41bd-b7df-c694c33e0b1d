<script setup lang="ts">
import type { InFormContainerType } from '@/components/in-form/interface.ts'
import InFormContainer from '@/components/in-form/InFormContainer.vue'
import InFormLayout from '@/components/in-form/InFormLayout.vue'
import Loading from '@/components/loading/Loading.vue'
import {Broadcast, BroadcastNamespace, MockRelationDatasheet} from '@/components/in-form'
import { InstrumentsBookMineDataSheet } from './InstrumentsBookManageDataSheet'

import { computed, ref, toValue } from 'vue'
import { uid } from 'radash'
import { useI18n } from 'vue-i18n';
import {  useDepartmentStore, useGroupStore, useUserStore,type IContextMenu,getDefaultActionMap,ShortcutActionName,type IClickRelationItem,type IDatasheetPackMap, } from '@integle/form'

import axios from "axios";
import {ElMessage} from "element-plus";
import InstrumentBookingCreateDialog from "@/components/InstrumentBookingCreateDialog.vue";
import {useMenuStore} from "@/components/context-menu";
import instrumentsBookMineRightMenu from "@/components/instrumentsBookManage/instrumentsBookManageRightMenu.vue";
import InstrumentsBookMineToolBar from "@/components/instrumentsBookManage/InstrumentsBookManageToolBar.vue";
import {UiContextMenu} from "../../../../ui";
import DeleteBookingDialog from "@/components/DeleteBookingDialog.vue";
const instrumentBookingCreateRef = ref(null)

const { t } = useI18n();
const instrumentsBookMineData = ref<any>({
      fieldList: [],
      dataList: [],
      extra: []
}
)
const fieldList = [
  {
    "id": "1",
    "name": "预约人",
    "fieldName": "createBy",
    "type": 41,
    "property": null,
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Booker",
    "required": false
  },
  {
    "id": "2",
    "name": "预约仪器",
    "fieldName": "bookedInstrument",
    "type": 1,
    "property": null,
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Booked Instrument",
    "required": false
  },
  {
    "id": "3",
    "name": "仪器ID",
    "fieldName": "instrumentId",
    "type": 32,
    "property": {
      "datasheetId": "instrumentId",
      "isMulti": false
    },
    "isSystem": "1",
    "propertyEn": {
      "datasheetId": "instrumentId",
      "isMulti": false
    },
    "nameEn": "Instrument ID",
    "required": false
  },
  {
    "id": "4",
    "name": "预约时间",
    "fieldName": "bookingTime",
    "type": 4,
    "property": {
      isRange:true,
      includeTime:true,
      dateFormat:1,
    },
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Booking Time",
    "required": false
  },
  {
    "id": "5",
    "name": "预约状态",
    "fieldName": "bookingStatus",
    "type": 24,
    "property": {
      "options": [
        {
          "id": "1",
          "color": "#2DA3B5",
          "label": "已预约"
        },
        {
          "id": "2",
          "color": "#2D3E9D",
          "label": "使用中"
        },
        {
          "id": "3",
          "color": "#FAD746",
          "label": "已完成"
        },
        {
          "id": "4",
          "color": "#B6C937",
          "label": "已撤销"
        },

      ],
      "withColor": true
    },
    "propertyEn": {
      "options": [
        {
          "id": "1",
          "color": "#2DA3B5",
          "label": "Booked Successfully"
        },
        {
          "id": "2",
          "color": "#B6C937",
          "label": "Cancelled"
        },
        {
          "id": "3",
          "color": "#2D3E9D",
          "label": "Completed"
        },
        {
          "id": "4",
          "color": "#FAD746",
          "label": "In Progress"
        }
      ],
      "withColor": true
    },
    "isSystem": "1",
    "nameEn": "Booking Status",
    "required": false
  },
  {
    "id": "6",
    "name": "记录本页码",
    "fieldName": "relatedExperimentBtn",
    "type": 32,
    "property": {
      "datasheetId": "relatedExperimentBtn",
      "isMulti": true
    },
    "isSystem": "1",
    "propertyEn": {
      "datasheetId": "relatedExperimentBtn",
      "isMulti": true
    },
    "nameEn": "Record Page Link",
    "required": false
  },
  {
    "id": "7",
    "name": "备注",
    "fieldName": "remark",
    "type": 1,
    "property": null,
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Remark",
    "required": false
  },
  {
    "id": "8",
    "name": "创建时间",
    "fieldName": "createTime",
    "type": 42,
    "property": null,
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Create Time",
    "required": false
  },
  {
    "id": "9",
    "name": "最近修改人",
    "fieldName": "updateBy",
    "type": 43,
    "property": null,
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Last Modified By",
    "required": false
  },
  {
    "id": "10",
    "name": "最近修改时间",
    "fieldName": "updateTime",
    "type": 44,
    "property": null,
    "isSystem": "1",
    "propertyEn": null,
    "nameEn": "Last Modified Time",
    "required": false
  }
]
const extra = { users:[ {
    "id": "456",
    "userId": "456",
    "role": [
      "1",
      "90"
    ],
    "apply": [
      "2",
      "6",
      "10",
      "1",
      "7",
      "13"
    ],
    "displayUserName": "岑红梅(cenhongmei)",
    "name": "cenhongmei",
    "realName": "岑红梅",
    "nickName": "岑红梅",
    "leaderId": null,
    "status": "1",
    "locked": false,
    "email": "<EMAIL>",
    "groupIds": [
      "72",
      "535",
      "536",
      "590",
      "602",
      "604",
      "606",
      "617",
      "618",
      "619",
      "648",
      "675",
      "696",
      "728",
      "791",
      "832",
      "870"
    ],
    "groupNames": [
      "测试456",
      "chm鹰群",
      "1122",
      "代号009",
      "chm-03",
      "chm-03的鹰群",
      "测试5.3.4权限",
      "测试鹰群权限",
      "测试鹰群权限-2",
      "chm-test",
      "CHM-01",
      "20240428",
      "chm-04",
      "1212",
      "李明的群1",
      "测试项目负责人",
      "CMS测试群-XQX"
    ],
    "departmentIds": [
      "671"
    ],
    "departmentNames": [
      "chm01"
    ]
  }]}
const isLoading = ref<boolean>(false);
const instrumentsBookMineEmpty = computed(() => {
  return instrumentsBookMineData.value.dataList && instrumentsBookMineData.value.dataList.length === 0;
})
const isCanWrite = ref(true);

// const bookList = {
//   my_book_list:[
//     {
//       "instrument_id": "2666",
//       "id": "281",
//       "start_time": "2025-06-12 12:00:00",
//       "end_time": "2025-06-12 13:00:00",
//       "related_experiment": "N150001-001",
//       "create_time": "2025-06-11 18:04:13",
//       "update_time": "2025-06-11 20:03:18",
//       "update_by": "1160",
//       "create_by": "1160",
//       "remark": "qweq",
//       "reminder": "4",
//       "status": 1,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2665",
//       "id": "279",
//       "start_time": "2025-06-12 10:30:00",
//       "end_time": "2025-06-12 11:59:00",
//       "related_experiment": "",
//       "create_time": "2025-06-11 18:03:47",
//       "update_time": "2025-06-11 19:35:09",
//       "update_by": "1160",
//       "create_by": "1160",
//       "remark": "",
//       "reminder": "0",
//       "status": 1,
//       "name": "数据仪器-20241126",
//       "batch_number": "20241126",
//       "available_slots": "[[\"06:00\",\"11:59\"],[\"14:51\",\"23:59\"]]",
//       "max_advance_day": null,
//       "min_advance": "{\"value\":\"2\",\"unit\":\"hour\"}",
//       "max_booking_duration": "{\"value\":\"\",\"unit\":\"\"}"
//     },
//     {
//       "instrument_id": "2665",
//       "id": "280",
//       "start_time": "2025-06-12 14:51:00",
//       "end_time": "2025-06-12 17:00:00",
//       "related_experiment": "",
//       "create_time": "2025-06-11 18:03:47",
//       "update_time": "2025-06-11 18:03:47",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": "0",
//       "status": 1,
//       "name": "数据仪器-20241126",
//       "batch_number": "20241126",
//       "available_slots": "[[\"06:00\",\"11:59\"],[\"14:51\",\"23:59\"]]",
//       "max_advance_day": null,
//       "min_advance": "{\"value\":\"2\",\"unit\":\"hour\"}",
//       "max_booking_duration": "{\"value\":\"\",\"unit\":\"\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "237",
//       "start_time": "2025-06-11 03:00:00",
//       "end_time": "2025-06-11 04:00:00",
//       "related_experiment": "",
//       "create_time": "2025-06-11 10:00:49",
//       "update_time": "2025-06-11 10:00:49",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": "0",
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "236",
//       "start_time": "2025-06-10 01:30:00",
//       "end_time": "2025-06-10 02:30:00",
//       "related_experiment": "",
//       "create_time": "2025-06-10 17:55:33",
//       "update_time": "2025-06-10 17:55:33",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": "0",
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "235",
//       "start_time": "2025-06-10 00:00:00",
//       "end_time": "2025-06-10 01:00:00",
//       "related_experiment": "N150001-001",
//       "create_time": "2025-06-10 17:45:43",
//       "update_time": "2025-06-10 17:45:43",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": "0",
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2652",
//       "id": "218",
//       "start_time": "2025-06-03 21:22:00",
//       "end_time": "2025-06-06 00:00:00",
//       "related_experiment": "",
//       "create_time": "2025-06-03 13:12:23",
//       "update_time": "2025-06-05 17:05:24",
//       "update_by": "1148",
//       "create_by": "1160",
//       "remark": "",
//       "reminder": null,
//       "status": 3,
//       "name": "12141545",
//       "batch_number": "ssss",
//       "available_slots": "[[\"06:00\",\"16:59\"],[\"17:51\",\"23:59\"]]",
//       "max_advance_day": null,
//       "min_advance": "{\"value\":\"1\",\"unit\":\"min\"}",
//       "max_booking_duration": "{\"value\":\"11\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "217",
//       "start_time": "2025-05-31 00:00:00",
//       "end_time": "2025-05-31 22:59:59",
//       "related_experiment": "",
//       "create_time": "2025-05-30 11:24:22",
//       "update_time": "2025-05-30 13:30:27",
//       "update_by": "1160",
//       "create_by": "1160",
//       "remark": "sss",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "216",
//       "start_time": "2025-05-30 00:00:00",
//       "end_time": "2025-05-30 23:59:59",
//       "related_experiment": "",
//       "create_time": "2025-05-29 19:28:33",
//       "update_time": "2025-05-29 19:28:33",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "215",
//       "start_time": "2025-05-29 00:00:00",
//       "end_time": "2025-05-29 21:58:31",
//       "related_experiment": "N2010001-001",
//       "create_time": "2025-05-29 13:58:54",
//       "update_time": "2025-05-29 13:58:54",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "11",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2665",
//       "id": "132",
//       "start_time": "2025-05-31 06:00:00",
//       "end_time": "2025-05-31 11:59:00",
//       "related_experiment": "",
//       "create_time": "2025-05-28 15:24:46",
//       "update_time": "2025-05-28 15:24:46",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": null,
//       "status": 3,
//       "name": "数据仪器-20241126",
//       "batch_number": "20241126",
//       "available_slots": "[[\"06:00\",\"11:59\"],[\"14:51\",\"23:59\"]]",
//       "max_advance_day": null,
//       "min_advance": "{\"value\":\"2\",\"unit\":\"hour\"}",
//       "max_booking_duration": "{\"value\":\"\",\"unit\":\"\"}"
//     },
//     {
//       "instrument_id": "2665",
//       "id": "133",
//       "start_time": "2025-05-31 06:00:00",
//       "end_time": "2025-05-31 11:59:00",
//       "related_experiment": "",
//       "create_time": "2025-05-28 15:24:46",
//       "update_time": "2025-05-28 15:51:43",
//       "update_by": "1160",
//       "create_by": "1160",
//       "remark": "qweqw",
//       "reminder": null,
//       "status": 3,
//       "name": "数据仪器-20241126",
//       "batch_number": "20241126",
//       "available_slots": "[[\"06:00\",\"11:59\"],[\"14:51\",\"23:59\"]]",
//       "max_advance_day": null,
//       "min_advance": "{\"value\":\"2\",\"unit\":\"hour\"}",
//       "max_booking_duration": "{\"value\":\"\",\"unit\":\"\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "99",
//       "start_time": "2025-05-23 11:00:00",
//       "end_time": "2025-05-23 13:59:59",
//       "related_experiment": "N2010001-001,N2020001-001",
//       "create_time": "2025-05-22 19:24:43",
//       "update_time": "2025-05-22 19:24:43",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "98",
//       "start_time": "2025-05-22 00:00:00",
//       "end_time": "2025-05-23 00:00:00",
//       "related_experiment": "N2010001-001,N2020001-001",
//       "create_time": "2025-05-22 13:52:37",
//       "update_time": "2025-05-26 14:24:37",
//       "update_by": "1160",
//       "create_by": "1160",
//       "remark": "666xxx",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "97",
//       "start_time": "2025-05-16 19:55:00",
//       "end_time": "2025-05-17 22:50:00",
//       "related_experiment": "",
//       "create_time": "2025-05-16 11:02:32",
//       "update_time": "2025-05-16 16:31:51",
//       "update_by": null,
//       "create_by": "1160",
//       "remark": "",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     },
//     {
//       "instrument_id": "2666",
//       "id": "96",
//       "start_time": "2025-05-16 14:50:00",
//       "end_time": "2025-05-16 18:45:00",
//       "related_experiment": "",
//       "create_time": "2025-05-14 10:34:19",
//       "update_time": "2025-05-16 11:01:53",
//       "update_by": "1160",
//       "create_by": "1160",
//       "remark": "",
//       "reminder": null,
//       "status": 3,
//       "name": "数值-********",
//       "batch_number": "********",
//       "available_slots": "[[\"00:00\",\"20:59\"]]",
//       "max_advance_day": "5",
//       "min_advance": "{\"value\":\"\",\"unit\":\"\"}",
//       "max_booking_duration": "{\"value\":\"1\",\"unit\":\"hour\"}"
//     }
//   ],
//   totalCount: "10",
//   extra: [
//     {
//       id: "1148",
//       user_id: "1148",
//       email: null,
//       phone: null,
//       name: "cq1",
//       nick_name: "cqtest1",
//       real_name: "cqtest1",
//       company_name: "",
//       country: "",
//       province: "",
//       city: "",
//       detail_address: "",
//       post_code: "",
//       contact_phone: "",
//       leader_id: "1135",
//       office_phone: "",
//       qq: "",
//       gender: "0",
//       job: "",
//       small_img: "",
//       big_img: "",
//       status: "1",
//       login_time: "2025-06-09 10:35:23",
//       role: ["1", "85"],
//       apply: ["2", "6", "11", "1", "7", "13"],
//       display_user_name: "cqtest1(cq1)",
//       account: null,
//       locked: false
//     },
//     {
//       id: "1160",
//       user_id: "1160",
//       email: null,
//       phone: null,
//       name: "zhangshiming",
//       nick_name: "张世明",
//       real_name: "张世明",
//       company_name: "",
//       country: "",
//       province: "",
//       city: "",
//       detail_address: "",
//       post_code: "",
//       contact_phone: "",
//       leader_id: null,
//       office_phone: "",
//       qq: "",
//       gender: "0",
//       job: "",
//       small_img: "",
//       big_img: "",
//       status: "1",
//       login_time: "2025-06-09 10:00:40",
//       role: ["1"],
//       apply: ["2"],
//       display_user_name: "张世明(zhangshiming)",
//       account: null,
//       locked: false
//     }
//   ],
//   instruments_list:[
//     {
//       id: "********",
//       name: "********"
//     },
//   ]
// }


// 第一次刷新外传的数据
const { bookList } = defineProps({
  bookList: {
    type: Object,
    default: {}
  },
})

console.log(bookList)

const isFirstRefresh = ref(true) // 是否是第一次进入

const handleData = (bookList: any) => {
  return bookList.my_book_list?.map((booking: any) => ({
    id: booking.id, // 唯一标识
    instrument_id: booking.instrument_id,
    booking_id: booking.booking_id,
    createBy: booking.create_by, // 人员id
    bookedInstrument: booking.name, // 仪器名称
    instrumentId: booking.batch_number, // 仪器ID
    bookingTime: [new Date(booking.start_time).getTime(), new Date(booking.end_time).getTime()], // 预定时间
    bookingStatus: booking.status, // 预定状态
    remark: booking.remark, // 备注
    updateBy: booking.update_by, // 人员id
    createTime: booking.create_time, // 创建时间
    updateTime: booking.update_time, // 更新时间
    available_slots: booking.available_slots,
    max_advance_day: booking.max_advance_day,
    min_advance: JSON.parse(booking.min_advance || '{"value":"","unit":""}'),
    max_booking_duration: JSON.parse(booking.max_booking_duration || '{"value":"","unit":""}'),
    warn: booking.warn,
    related_experiment: booking.related_experiment,
    ...(booking.related_experiment && { relatedExperimentBtn: booking.related_experiment?.split(',') })
  }));
};
// 将数据转成createDialog可以使用的形式可以用的

const instrumentsList = ref<{id: string, name: string}[]>([])
const relatedExperimentBtnList = ref<{id:string,name: string, url: string}[]>([])

const loadData = async () => {
  if( !isFirstRefresh.value ) {
    isLoading.value = true;
    try {
      const url = '/?r=instrument-booking/get-instrument-booking-info'

      const response = await axios.get(url,{

        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      const data = response.data;

      if(data.status === 1) {
        instrumentsBookMineData.value.dataList = handleData(data.data.data)
        console.log(instrumentsBookMineData.value.dataList)
        instrumentsBookMineData.value.fieldList = fieldList
        instrumentsBookMineData.value.extra = data.data.data.extra

        isCanWrite.value = data.data.data.isCanWrite === 1

        // 处理关联实验
        const uniqueRelatedExperiment = Array.from(
            new Set(
                instrumentsBookMineData.value.dataList
                    ?.flatMap((book: any) => book.related_experiment.split(','))
                    .filter((item: any) => item !== null && item !== undefined && item !== "")
            )
        );
        relatedExperimentBtnList.value = uniqueRelatedExperiment?.map((item: any) => ({
          id: item,
          name: item,
          url: 'https://idataeln.integle.com/?exp_id=' + item,
        }))

        const uniqueBatchNumbers = [...new Set(data.data.data.my_book_list.map((book: any) => book.batch_number))];
        instrumentsList.value = uniqueBatchNumbers.map(batch_number => ({
          id: batch_number,
          name: batch_number
        }));
        isLoading.value = false
      }
    }
    catch (error) {
    } finally {
      isLoading.value = false;
    }
  }
  else {
    instrumentsBookMineData.value.dataList = handleData(bookList)
    instrumentsBookMineData.value.fieldList = fieldList
    instrumentsBookMineData.value.extra = bookList.extra
    isCanWrite.value = bookList.isCanWrite === 1

    // 处理关联实验
    const uniqueRelatedExperiment = Array.from(
        new Set(
            instrumentsBookMineData.value.dataList
                ?.flatMap((book: any) => book.related_experiment)
                .filter((item: any) => item !== null && item !== undefined && item !== "")
        )
    );
    relatedExperimentBtnList.value = uniqueRelatedExperiment?.map((item: any) => ({
      id: item,
      name: item,
      url: 'https://idataeln.integle.com/?exp_id=' + item,
    }))

    const uniqueBatchNumbers = [...new Set(bookList.my_book_list.map((book: any) => book.batch_number))];
    instrumentsList.value = uniqueBatchNumbers.map(batch_number => ({
      id: batch_number,
      name: batch_number
    }));
  }
  isFirstRefresh.value = false

  // TODO: 获取我的预约数据
  // 赋值instrumentsBookMineData

  // useGroupStore().setGroupList(userInfoRef.groups ?? [])   如果需要的话部门鹰群信息的话
  // useDepartmentStore().setDepartmentList(userInfoRef.departments ?? [])
}
loadData()

const datasheetPack = computed(() => {
  // if (InstrumentsBookMineEmpty.value) {
  //   return InstrumentsBookMineDataSheet.emptyDatasheetPack()
  // }
  const dataList = toValue(instrumentsBookMineData).dataList
  const fieldList = toValue(instrumentsBookMineData).fieldList
  const datasheetId = Broadcast.build({
    namespace: BroadcastNamespace.instrumentsBookMine,
    id: toValue(uid(8)),
  }).getDatasheetId()

  const res = InstrumentsBookMineDataSheet.buildDatasheetPack(toValue({
    datasheetId,
    fieldList,
    recordList:dataList,
    property: {
      name: t('instrumentsBookMine.tableName'),
    },
  }))
  return res
})
const relationDatasheetIdMap = computed(() => {
  return {
    instrumentId: Broadcast.build({ namespace: BroadcastNamespace.instrumentId, id: '' }).getDatasheetId(),
    relatedExperimentBtn: Broadcast.build({ namespace: BroadcastNamespace.relatedExperimentBtn, id: '' }).getDatasheetId(),
  }
})
const datasheetPackMap = computed(() => {
  const res: IDatasheetPackMap = {}
  res[relationDatasheetIdMap.value.instrumentId] = MockRelationDatasheet.buildDatasheetPack({
    dataList:toValue(instrumentsList),
    datasheetId: relationDatasheetIdMap.value.instrumentId,
  })
  res[relationDatasheetIdMap.value.relatedExperimentBtn] = MockRelationDatasheet.buildDatasheetPack({
    dataList:toValue(relatedExperimentBtnList),
    datasheetId: relationDatasheetIdMap.value.relatedExperimentBtn,
  })
  return res
})

/**
 * Todo：右键菜单
 * 批量操作菜单
 * 视图缺少甘特图
 * 筛选默认条件
 * 排序默认条件
 */
function editBooking(idList: string[]) {
  const editInfo = instrumentsBookMineData.value.dataList.filter((i: Object) => i.id === idList[0])
  console.log(editInfo[0])
  instrumentBookingCreateRef.value.openDialogEdit({
    ...editInfo[0],
    instrument_id: editInfo[0].instrument_id,
    id: editInfo[0].booking_id,
    name: editInfo[0].bookedInstrument,
    start_time: timestampToDateTime(editInfo[0].bookingTime[0]),
    end_time: timestampToDateTime(editInfo[0].bookingTime[1]),
    source: 'instrumentsBookMine',
    available_slots: Array.isArray(editInfo[0].available_slots) ? editInfo[0].available_slots : JSON.parse(editInfo[0].available_slots || '[]'),
  })
}
const deleteDialogVisible = ref(false)
const deleteBookingInfo = ref([])
const deleteLoading = ref(false)

// 处理删除确认
const handleDeleteConfirm = async () => {
  console.log(1)
  deleteLoading.value = true
  try {
    const url = '/?r=instrument-booking/batch-delete-instrument-booking'

    const list = deleteBookingInfo.value
    const response = await axios.post(url, {
      list
    },{

      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data
    if(data.status == 1) {
      ElMessage({
        showClose: true,
        message: t('instrumentsBookMine.deleteSuccess'),
        type: 'success',
        offset: window.innerHeight / 8
      })
      deleteDialogVisible.value = false
      await loadData()
    } else {
      ElMessage({
        showClose: true,
        message: t('instrumentsBookMine.deleteFail'),
        type: 'error',
        offset: window.innerHeight / 8
      })
    }
  } catch (error) {
    ElMessage({
      showClose: true,
      message: t('instrumentsBookMine.deleteFail'),
      type: 'error',
      offset: window.innerHeight / 8
    })
  } finally {
    deleteLoading.value = false
    deleteDialogVisible.value = false
  }
}

// 处理删除取消
const handleDeleteCancel = () => {
  deleteDialogVisible.value = false
}


async function cancelBooking (idList: string[]) {
  deleteBookingInfo.value = idList
  deleteDialogVisible.value = true
  // handleDeleteConfirm(idList)

}

const inFormContainerRef = ref<InFormContainerType>()

const { setContextMenu } = useMenuStore()
function convertKeysToCamelCase(obj: any) {
  if (typeof obj !== 'object' || obj === null) return obj;

  const newObj:{[key:string]:any} = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      newObj[camelKey] = typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])
          ? convertKeysToCamelCase(obj[key])
          : obj[key];
    }
  }

  return newObj;
}
function formOnMounted() {
  if (!inFormContainerRef.value) {
    return
  }
  const userInfoRef = instrumentsBookMineData.value?.extra.map((item: any) => convertKeysToCamelCase(item))

  useUserStore().setUserList(userInfoRef ?? [])
  const { useDatasheetStore } = inFormContainerRef.value
  const { shortcutActionManager } = useDatasheetStore()
  shortcutActionManager.bind(ShortcutActionName.OpenContextMenu, (payload: IContextMenu) => {
    const { recordId, fieldId } = payload.cell
    if (!recordId || !fieldId) {
      return
    }
    setContextMenu({
      visible: true,
      x: payload.x,
      y: payload.y,
      slot: instrumentsBookMineRightMenu,
      slotProps: {
        ...payload,
        informData: toValue(datasheetPack),
        isCanWrite: toValue(isCanWrite),
        expandRecord: (recordId: string) => {
          const defaultActionMap = getDefaultActionMap()
          const defaultExpandRecordAction = defaultActionMap[ShortcutActionName.ExpandRecord]
          defaultExpandRecordAction && defaultExpandRecordAction({ recordId })
        },
        editBooking,
        cancelBooking,
      },
    })
  })
  shortcutActionManager.bind(ShortcutActionName.ClickRelationItem, (payload: IClickRelationItem) => {
    const {cell} = payload
    const {recordId} = cell
    console.log('ClickRelationItem',
        toValue(datasheetPack).snapshot.recordMap[recordId]
    );
    const instrumentId = toValue(datasheetPack).snapshot.recordMap[recordId].data.instrument_id
    if(cell.fieldId == "instrumentId" && instrumentId) {
      $.ajaxFn({
        url: ELN_URL + '?r=instrument/get-page',
        data: {
          instrumentId: instrumentId,
          type: 'view',
        },
        success: function(res) {
          if (res.status == 1) {
            $(".instrument_page").remove();
            $("body").append(res.data.file);
            $(".instrument_page").modal('show');
          }
        }
      })
    }
    /**
     * TODO: 需要完善关联字段的跳转逻辑
     */
  })
}


// 时间戳 -》2025-06-11 18:03:47
function timestampToDateTime(timestamp: any) {
  // 创建日期对象
  const date = new Date(timestamp);

  // 获取年月日时分秒
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  // 返回格式化后的时间字符串
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
</script>

<template>
  <InFormLayout
      :loading="isLoading"
      :empty="instrumentsBookMineEmpty"

  >
    <template #loading>
      <Loading class="loading" />
    </template>
    <template #form-container>
      <InFormContainer
          ref="inFormContainerRef"
          :datasheet-pack="datasheetPack"
          :datasheet-pack-map="datasheetPackMap"
          @on-mounted="formOnMounted"
      >
        <template #toolbar-selected="{ selectIds }">
          <InstrumentsBookMineToolBar
              :selected-ids="selectIds"
              :inform-data="datasheetPack"
              :isCanWrite="isCanWrite"
              @editBooking="editBooking(selectIds.map((item) => String(item)))"
              @cancelBooking="cancelBooking(selectIds.map((item) => String(item)))"
          />
        </template>
      </InFormContainer>
    </template>
  </InFormLayout>
  <DeleteBookingDialog
      :visible="deleteDialogVisible"
      :loading="deleteLoading"
      @confirmBook="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
  />
  <UiContextMenu />

  <InstrumentBookingCreateDialog ref="instrumentBookingCreateRef" @refreshBookMine="loadData"></InstrumentBookingCreateDialog>

</template>

<style scoped lang="scss">

</style>
