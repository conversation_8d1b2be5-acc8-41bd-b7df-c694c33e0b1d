<?php

namespace frontend\models;

/**
 * This is the model class for table "template_action_log".
 *
 * @property string $id
 * @property integer $template_id
 * @property string $action_code 操作代码
 * @property integer $history_id
 * @property integer $create_by
 * @property string $create_time
 * @property integer $status
 * @property string $extra_data 额外数据

 * 模板操作日志模型
 * 
 * 操作类型常量定义:
 * - create: 创建模板
 * - save: 保存模板
 * - publish: 发布模板
 * - submit_audit: 提交审核
 * - cancel_audit: 撤销审核
 * - audit_agree: 审核通过
 * - audit_refuse: 审核拒绝
 * - remove: 移入回收站
 * - transfer: 转让
 * - restore: 还原
 * - re_edit: 重新编辑
 * - submit_publish_audit: 提交发布审核
 * - cancel_publish: 撤销发布
 * - publish_audit_agree: 发布审核通过
 * - publish_audit_refuse: 发布审核拒绝
 * - edit_effective_date: 修改生效日期
 * - edit_version: 修改版本
 */
class TemplateActionLogModel extends \yii\db\ActiveRecord
{
    // 操作类型常量 - 直接使用字符串
    const CREATE = 'create';
    const SAVE = 'save';
    const PUBLISH = 'publish';
    const SUBMIT_AUDIT = 'submit_audit';
    const CANCEL_AUDIT = 'cancel_audit';
    const AUDIT_AGREE = 'audit_agree';
    const AUDIT_REFUSE = 'audit_refuse';
    const REMOVE = 'remove';
    const TRANSFER = 'transfer';
    const RESTORE = 'restore';
    const RE_EDIT = 're_edit';

    const SUBMIT_PUBLISH_AUDIT = 'submit_publish_audit';
    const CANCEL_PUBLISH = 'cancel_publish';
    const PUBLISH_AUDIT_AGREE = 'publish_audit_agree';
    const PUBLISH_AUDIT_REFUSE = 'publish_audit_refuse';   

    const EDIT_EFFECTIVE_DATE = 'edit_effective_date';
    const EDIT_VERSION = 'edit_version';

    
    
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'template_action_log';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['template_id', 'history_id', 'create_by', 'status'], 'integer'],
            [['create_time'], 'safe'],
            [['action_code'], 'string', 'max' => 255],
            [['extra_data'], 'string']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'template_id' => 'Template ID',
            'action_code' => 'Action Code',
            'history_id' => 'History ID',
            'create_by' => 'Create By',
            'create_time' => 'Create Time',
            'status' => 'Status',
            'extra_data' => 'Extra Data',
        ];
    }
}

