<?php
use yii\helpers\Html;
use frontend\models\CompanyDictValue;
use frontend\models\CompanyDictCategory;
use frontend\models\TemplateHistoryNewModel;
?>


<script>
    // RTFJS.bundle 自动加载它的依赖
    // require(['RTFJS.bundle'], function(RTFJS) {
    //     window.RTFJS = RTFJS
    // });
    // RTFJS 最好不要全局引入，已改为在 ueditor.all.js 中引入 
    // 若要在其它地方使用 RTFJS，应写为 require(['RTFJS.bundle'], function(RTFJS) { RTFJS.Document(buffer) ... })
    if (!window.UE) {
        let version = '<?=Yii::$app->params['res_version']?>';
        const scriptList1 = [
            '/ueditor/ueditor.config.js', '/ueditor/ueditor.all.js', 
            '/ueditor/third-party/kityformula-plugin/addKityFormulaDialog.js',
            '/ueditor/third-party/kityformula-plugin/getKfContent.js',
            '/ueditor/third-party/kityformula-plugin/defaultFilterFix.js',
        ];
        for (let i = 0; i < scriptList1.length; ++i) {
            $('head').append('<script src="' + scriptList1[i] + '?ver=' + version + '"><\/script>');
        }
        // 建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败
        // 这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->

        if('en' == window.lang) {
            let scriptList2 = [
                '/ueditor/third-party/insertmetial/insertmetial_en.js',
                '/ueditor/third-party/inserttemplate/inserttemplate_en.js',
                '/ueditor/third-party/createtemplate/createtemplate_en.js',
                '/ueditor/third-party/addFromInstruments/addFromInstruments_en.js',
                '/ueditor/third-party/addFromSequence/addFromSequence_en.js', // add by hkk 2023/11/3
                '/ueditor/third-party/indraw/indraw_en.js',
                '/ueditor/third-party/insertcheckbox/insertcheckbox_en.js',
                '/ueditor/third-party/insertradio/insertradio_en.js',
                '/ueditor/third-party/ai-chat/aiChat_en.js',
                '/ueditor/lang/en/en.js'
            ];
            for (let i = 0; i < scriptList2.length; ++i) {
                $('head').append('<script src="' + scriptList2[i] + '?ver=' + version + '"><\/script>');
            }
        } else {
            let scriptList2 = [
                '/ueditor/third-party/insertmetial/insertmetial.js',
                '/ueditor/third-party/inserttemplate/inserttemplate.js',
                '/ueditor/third-party/createtemplate/createtemplate.js',
                '/ueditor/third-party/addFromInstruments/addFromInstruments.js',
                '/ueditor/third-party/addFromSequence/addFromSequence.js', // add by hkk 2023/11/3
                '/ueditor/third-party/indraw/indraw.js',
                '/ueditor/third-party/insertcheckbox/insertcheckbox.js',
                '/ueditor/third-party/insertradio/insertradio.js',
                '/ueditor/third-party/ai-chat/aiChat.js',
                '/ueditor/lang/zh-cn/zh-cn.js'
            ];
            for (let i = 0; i < scriptList2.length; ++i) {
                $('head').append('<script src="' + scriptList2[i] + '?ver=' + version + '"><\/script>');
            }
        }
             
    }
    
</script>

<!--   <script type="text/javascript" charset="utf-8" src="/ueditor/lang/zh-cn/zh-cn.js"></script>-->
<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
<?php
$lang = \Yii::$app->language;
//en-US  zh-CN

$bodyLang = 'cn';
if(Yii::$app->params['language_list']['en'] == $lang){
    $bodyLang = 'en';
}
?>


<?php
	$session = Yii::$app->session;
	$userInfo = $session->get('userinfo');
	$company_id = isset($userInfo['current_company_id']) ? $userInfo['current_company_id'] : 0;

        //获取企业词库所有值并重组
        $dictValues =  CompanyDictValue::find()->select('id,parent_id,dict_value')->where(['company_id'=>$company_id,'status'=>1])->asArray()->all();
        if(empty($dictValues)){
            $dictValues =  CompanyDictValue::find()->select('id,parent_id,dict_value')->where(['company_id'=>0,'status'=>1])->asArray()->all();//CompanyDictValue::findAll(['company_id'=>$company_id,'status'=>0]);
        }
        //按照分类重组
        $dictGroups = [];
        foreach($dictValues as $dictValue){
            $dictGroups[$dictValue['parent_id']][]= $dictValue['dict_value'];
        }



        //获取企业词库名称列表（js用）
        $categoryData = CompanyDictCategory::find()->select('id,dict_filed as name')->asArray()->all();


        foreach($categoryData as $key=>$value){
            $categoryData[$key]['dict']= isset($dictGroups[$value['id']])?$dictGroups[$value['id']]:[];
        }


        $categoryDataJson= json_encode($categoryData);

        //编辑器用
        $item['generalreagent']['data'] = json_encode($dictGroups[\Yii::$app->params['company_dict_category'][12]]);//编辑器	常用试剂
        $item['generalreagent']['name'] = '试剂';//编辑器	常用试剂
        if('en' == $bodyLang){
            $item['generalreagent']['name'] = 'Reagent';//编辑器	常用试剂
        }
        //12
        $item['generaldevice']['data'] = json_encode($dictGroups[\Yii::$app->params['company_dict_category'][13]]);;//编辑器	常用仪器
        $item['generaldevice']['name'] = '仪器';//编辑器	常用仪器
        if('en' == $bodyLang){
            $item['generaldevice']['name'] = 'Instrument';//编辑器	常用试剂
        }

        //13
        $item['generalmetial']['data'] = json_encode($dictGroups[\Yii::$app->params['company_dict_category'][14]]);// 14,//编辑器	常用耗材
        $item['generalmetial']['name'] = '耗材';//编辑器	常用耗材
        if('en' == $bodyLang){
            $item['generalmetial']['name'] = 'Material';//编辑器	常用试剂
        }

        //14
        $item['eluent']['data'] = json_encode($dictGroups[\Yii::$app->params['company_dict_category'][15]]);//编辑器	洗脱液
        $item['eluent']['name'] = '洗脱液';//编辑器	洗脱液
        if('en' == $bodyLang){
            $item['eluent']['name'] = 'Eluant';//编辑器	常用试剂
        }
        //15
        $item['chromatography']['data'] = json_encode($dictGroups[\Yii::$app->params['company_dict_category'][16]]);//编辑器	层析柱类型
        $item['chromatography']['name'] = '层析柱';//编辑器	层析柱类型
        if('en' == $bodyLang){
            $item['chromatography']['name'] = 'Column';//编辑器	常用试剂
        }
        //16
        $item['ph']['data'] = json_encode($dictGroups[\Yii::$app->params['company_dict_category'][17]]);//编辑器	层析柱类型
        $item['ph']['name'] = 'pH值';//编辑器	层析柱类型
        if('en' == $bodyLang){
            $item['ph']['name'] = 'pH';//编辑器	常用试剂
        }

        //17
        $item['deuteration']['data'] = json_encode(empty($dictGroups[\Yii::$app->params['company_dict_category'][18]]) ? [] : $dictGroups[\Yii::$app->params['company_dict_category'][18]]);//编辑器	层析柱类型
        $item['deuteration']['name'] = '氘代试剂';//编辑器	层析柱类型
        if('en' == $bodyLang){
            $item['deuteration']['name'] = 'Deuterated reagent';//编辑器	常用试剂
        }
	?>

<?php foreach($item as $key=>$value){

?>
<script>
    var item_list_<?php echo $key?> = JSON.parse(<?php echo json_encode($value['data']);?>);
    registerUI();

    function registerUI() {
        if (!window.UE) {
            if (typeof(window.registerUITimmer) != "undefined") {
                clearTimeout(window.registerUITimmer)
            }
            window.registerUITimmer = setTimeout(registerUI, 500)  
        }
        
        UE.registerUI('<?php echo $key;?>',function(editor,uiName){
            //创建下拉菜单中的键值对，这里我用字体大小作为例子
            var items = [];
            for(var i= 0,ci;ci=item_list_<?php echo $key?>[i++];){
                items.push({
                    //显示的条目
                    label:'' + ci + '',
                    //选中条目后的返回值
                    value:ci,
                    //针对每个条目进行特殊的渲染
                    renderLabelHtml:function () {
                        //这个是希望每个条目的字体是不同的
                        return '<div class="edui-label %%-label" style="line-height:2;font-size:' +
                            this.value + 'px;">' + (this.label || '') + '</div>';
                    }
                });
            }
            //创建下拉框
            var combox = new UE.ui.Combox({
                //需要指定当前的编辑器实例
                editor:editor,
                //添加条目
                items:items,
                //当选中时要做的事情
                onselect:function (t, index) {


                    //拿到选中条目的值
                    //editor.execCommand(uiName, this.items[index].value);
                    editor.execCommand('inserthtml', this.items[index].value );
                    console.log( this.items[index].value);

                },
                //提示
                title:'<?php echo $value['name']?>',
                //当编辑器没有焦点时，combox默认显示的内容
                initValue:'<?php echo $value['name']?>'
            });


            return combox;
        })/*index 指定添加到工具栏上的那个位置，默认时追加到最后,editorId 指定这个UI是那个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮*/;

    }
    </script>
<?php
    }
?>
<!-- /////////////编辑器script结束/////////// -->

<?php
$compentArr = \Yii::$app->params['component'];
$baseData = isset($data['base_data'])?$data['base_data']:'';
// 调试信息 - 打印所有传入的变量
echo '<script>console.log("All variables passed to template.php:", ' . json_encode(get_defined_vars()) . ');</script>';
echo '<script>console.log("data variable:", ' . json_encode($data) . ');</script>';
echo '<script>console.log("baseData:", ' . json_encode($baseData) . ');</script>';
echo '<script>console.log("templateSubtypeList:", ' . json_encode(isset($templateSubtypeList) ? $templateSubtypeList : []) . ');</script>';
echo '<script>console.log("actionArr:", ' . json_encode($actionArr) . ');</script>';

$templateSubtypeList = isset($templateSubtypeList) ? $templateSubtypeList : [];

// 预处理版本确认信息
$needPendingConfirm = false;
$needAuditConfirm = false;
$displayUserVersion = '';

if (isset($displayVersion) && !empty($displayVersion)) {
    if ($displayVersion['status'] == '2') {
        $needAuditConfirm = true;
    }
    if ($displayVersion['status'] == '5') {
        $needPendingConfirm = true;
    }
    $displayUserVersion = $displayVersion['user_version'];
}
?>

<script>
	require(['exp_module', 'reference_abstract', 'exp_data_new', 'exp_add', 'tlc', 'file']);
</script>
<input type="hidden" id="mudole_come_from" value="<?php echo isset($baseData['tfrom']) ? $baseData['tfrom'] : 1;?>" />
<input type="hidden" id="exp_user_id" value="<?php echo $user_id;?>" />
<input type="hidden" id="temp_id" value="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>" />
<input type="hidden" id="exp_edit" value="<?php echo $this->params['edit'];?>" />

<input type="hidden" id="is_template_page" value=1>
<input type="hidden" id="type" value=2>

<div class="tool_data_box">
    <div class="tool_nav nav-bar-height47">
        <?php if(in_array('add_module', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn" data-type="showmodule"><i class="ico module"></i><?php echo Yii::t('temp', 'add_module');?></a>
        <?php endif;?>

        <?php if(in_array('save', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="savemodule" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico save"></i><?php echo Yii::t('base', 'save');?></a>
        <?php endif;?>

        <?php if(in_array('set_require', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="setrequiremodule" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico set_require"></i><?php echo Yii::t('exp_info', 'set_require');?></a>
        <?php endif;?>

        <?php if(in_array('set_struct_data', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="setstructdatamodule" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico set_struct_data"></i><?php echo Yii::t('exp_info', 'set_struct_data');?><i class="ask-ico font-ico hover-show-info"><div style="line-height:25px;height:50px"><?php echo Yii::t('base', 'set_struct_data_tips');?></div></i></a>
        <?php endif;?>

        <?php if(in_array('set_revision', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="getRevisionSetting" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico set_revision"></i><?php echo Yii::t('temp', 'set_revision');?></a>
        <?php endif;?>

        <?php if(in_array('cancel_audit', $actionArr)):?>
            <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="cancelTemplateAudit" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico revoke"></i><?php echo Yii::t('temp', 'cancel_temp_audit');?></a>
        <?php endif;?>

        <?php if(in_array('audit', $actionArr)):?>
            <a href="javascript:void(0)" class="click_btn tool_btn" data-type="approval" data-action="agree"   data-bussinessType="<?php echo \Yii::$app->params['approval_type']['template'];?>" data-bussinessId="<?php echo @getVar($baseData['id'], 0);?>"><i class="ico agree"></i><?php echo Yii::t('views/exp_list', 'approval_agree')?></a>
            <a href="javascript:void(0)" class="click_btn tool_btn" data-type="approval" data-action="refuse"  data-bussinessType="<?php echo \Yii::$app->params['approval_type']['template'];?>" data-bussinessId="<?php echo @getVar($baseData['id'], 0);?>"><i class="ico refuse"></i><?php echo Yii::t('views/exp_list', 'approval_refuse')?></a>
        <?php endif;?>

        <?php if(in_array('edit', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="startTemplateEdit" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico edit"></i><?php echo Yii::t('base', 'edit');?></a>
        <?php endif;?>
        <?php if (in_array($baseData['type'], [1, 3])): ?>
        <a href="<?= '/?r=/file/preview-template&template_id=' . $baseData['id'] . '&paper_size=A4' ?>" target="_blank" class="click_btn tool_btn"><i
                    class="ico print"></i><?php echo Yii::t('base', 'print') ?></a>
        <?php endif; ?>
        <?php //if(in_array('copy', $actionArr)):?>
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="copymodule" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>"><i class="ico copy"></i><?php echo Yii::t('base', 'copy');?></a>
        <?php //endif;?>
        
        <a href="javascript:void(0)" class="click_btn tool_btn click_module_tool" data-type="showHistory" data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>" data-temp-power="<?php echo isset($tempPower) ? $tempPower : 0; ?>" data-effect-mode="<?php echo isset($templateEffectMode) ? $templateEffectMode : 1; ?>" data-update-time="<?php echo isset($baseData['update_time']) ? $baseData['update_time'] : ''; ?>"><i class="ico history"></i><?php echo Yii::t('base', 'history');?></a>
        <div id="template-history-dialog" style="display: none; position: absolute;"></div> 

        <?php if(in_array('submit_audit', $actionArr)):?>
        <a href="javascript:void(0)" 
           class="click_btn tool_btn click_module_tool" 
           style="float: right; 
                  margin-right: 20px; 
                  margin-top: 5px;
                  color: #1388FF; 
                  border: 1px solid #1388FF; 
                  background: transparent; 
                  padding: 0 5px; 
                  height: 28px !important; 
                  line-height: 26px !important; 
                  font-size: 12px !important;
                  min-height: 28px !important;
                  box-shadow: none !important;
                  border-radius: 3px !important;" 
           data-type="submitTemplateAudit" 
           data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>">
           <?php echo Yii::t('temp', 'submit_temp_audit');?>
        </a>
        <?php endif;?>

        <?php if(in_array('submit_publish_audit', $actionArr)):?>        
        <a href="javascript:void(0)" 
           class="click_btn tool_btn click_module_tool" 
           style="float: right; 
                  margin-right: 20px; 
                  margin-top: 5px;
                  color: #1388FF; 
                  border: 1px solid #1388FF; 
                  background: transparent; 
                  padding: 0 5px; 
                  height: 28px !important; 
                  line-height: 26px !important; 
                  font-size: 12px !important;
                  min-height: 28px !important;
                  box-shadow: none !important;
                  border-radius: 3px !important;" 
           data-type="submitPublishAudit" 
           data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>" 
           data-need-audit-confirm="<?php echo $needAuditConfirm ? '1' : '0'; ?>" 
           data-need-pending-confirm="<?php echo $needPendingConfirm ? '1' : '0'; ?>" 
           data-display-version="<?php echo $displayUserVersion; ?>">
           <?php echo Yii::t('temp', 'publish');?>
        </a>        
        <?php endif;?>

        <?php if(in_array('publish', $actionArr)):?>
        <a href="javascript:void(0)" 
           class="click_btn tool_btn click_module_tool btn-primary" 
           style="float: right; 
                  margin-right: 20px; 
                  margin-top: 5px;
                  padding: 0 5px; 
                  color: white; 
                  height: 28px !important; 
                  line-height: 26px !important; 
                  font-size: 12px !important;
                  min-height: 28px !important;
                  box-shadow: none !important;
                  border-radius: 3px !important;" 
           data-type="publishTemplate" 
           data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>" 
           data-max-inner-version="<?php echo $maxInnerVersion; ?>" 
           data-need-pending-confirm="<?php echo $needPendingConfirm ? '1' : '0'; ?>" 
           data-need-audit-confirm="<?php echo $needAuditConfirm ? '1' : '0'; ?>"
           data-display-version="<?php echo $displayUserVersion; ?>">
           <?php echo Yii::t('temp', 'publish');?>
        </a>
        <?php endif;?>        

        <?php if(in_array('submit_publish_audit', $actionArr) || in_array('publish', $actionArr)):?>
        <?php 
            // 定义状态标签的样式映射
            $statusClasses = [
                TemplateHistoryNewModel::$status['is_pending'] => 'warning',    // 待生效
                TemplateHistoryNewModel::$status['is_agreed'] => 'success',     // 已生效
                TemplateHistoryNewModel::$status['is_canceled'] => 'info',      // 已取消
                TemplateHistoryNewModel::$status['is_invalid'] => 'info',       // 已失效
                TemplateHistoryNewModel::$status['is_approving'] => 'primary',  // 审核中
                TemplateHistoryNewModel::$status['is_refused'] => 'danger',     // 已拒绝
            ];
            
            // 使用已经传递的 displayVersion 数据
            $versionValue = Yii::t('temp', 'no_history_version');
            $statusClass = '';
            $statusText = '';
            
            if (!empty($displayVersion) && isset($displayVersion['user_version'])) {
                $versionValue = $displayVersion['user_version'];
                
                switch ($displayVersion['status']) {
                    case TemplateHistoryNewModel::$status['is_pending']:
                        $statusText = Yii::t('temp', 'pending_version_short');
                        break;
                    case TemplateHistoryNewModel::$status['is_agreed']:
                        $statusText = Yii::t('temp', 'current_version_short');
                        break;
                    case TemplateHistoryNewModel::$status['is_canceled']:
                        $statusText = Yii::t('temp', 'canceled_version_short');
                        break;
                    case TemplateHistoryNewModel::$status['is_invalid']:
                        $statusText = Yii::t('temp', 'invalid_version_short');
                        break;
                    case TemplateHistoryNewModel::$status['is_approving']:
                        $statusText = Yii::t('temp', 'approving_version_short');
                        break;
                    case TemplateHistoryNewModel::$status['is_refused']:
                        $statusText = Yii::t('temp', 'refused_version_short');
                        break;
                    default:
                        $statusText = '';
                        break;
                }
                
                $statusClass = isset($statusClasses[$displayVersion['status']]) ? $statusClasses[$displayVersion['status']] : 'default';
                
                echo '<script>console.log("displayVersion:", ' . json_encode($displayVersion) . ');</script>';      
            }
        ?>
        <div style="float: right; margin-right: 10px; line-height: 40px; display: flex; align-items: center;">
            <span style="margin-right: 5px; "><?php echo $versionValue; ?></span>
            <?php if (!empty($statusText)): ?>
            <a href="javascript:void(0)" 
               class="click_module_tool click_btn tool_btn " 
               data-type="showHistory" 
               data-id="<?php echo isset($baseData['id']) ? $baseData['id'] : '0'?>" 
               data-temp-power="<?php echo isset($tempPower) ? $tempPower : 0; ?>" 
               data-effect-mode="<?php echo isset($templateEffectMode) ? $templateEffectMode : 1; ?>" 
               data-update-time="<?php echo isset($baseData['update_time']) ? $baseData['update_time'] : ''; ?>"
               style="display: inline-block !important; 
                      padding: 0px 4px !important; 
                      border-radius: 2px !important; 
                      font-size: 11px !important; 
                      cursor: pointer !important; 
                      text-decoration: none !important;
                      background-color: transparent !important;
                      margin: 0 !important;
                      height: auto !important;
                      line-height: 16px !important;
                      <?php 
                        switch($statusClass) {
                            case 'primary': 
                                echo 'color: #1388FF !important; border: 0.5px solid #1388FF !important;'; 
                                break;
                            case 'success': 
                                echo 'color: #67C23A !important; border: 0.5px solid #67C23A !important;'; 
                                break;
                            case 'info': 
                                echo 'color: #909399 !important; border: 0.5px solid #909399 !important;'; 
                                break;
                            case 'warning': 
                                echo 'color: #E6A23C !important; border: 0.5px solid #E6A23C !important;'; 
                                break;
                            case 'danger': 
                                echo 'color: #F56C6C !important; border: 0.5px solid #F56C6C !important;'; 
                                break;
                            default: 
                                echo 'color: #909399 !important; border: 0.5px solid #909399 !important;'; 
                                break;
                        }
                      ?>
                      white-space: nowrap !important;
                      vertical-align: middle !important;
                      text-align: center !important;
                      min-width: 0 !important;
                      width: auto !important;
                      float: none !important;
                      box-shadow: none !important;">
                <?php echo $statusText; ?>
            </a>
            <?php endif; ?>
        </div>
        <?php endif;?>        
    </div>
</div>

<!--bug#30058，取消用户查看模板时，禁用页面的操作，防止没有“修改模板管理”权限的用户在审批实验时页面被禁用 mod dx-->
<!--<div class="exp_data_box --><?php //if(/*$user_id != $baseData['user_id'] || deleted by xieyuxiang 2022.10.24有模板修改权限的人员也可以修改模板*/ empty($tempPower)){ echo 'disabled';} ?><!--">-->
<div class="exp_data_box ">
	<?php //if($user_id == $baseData['user_id'] ){ deleted by xieyuxiang 2022.10.24有模板修改权限的人员也可以修改模板?>
	<!-- 实验信息 -->
	<div class="relative" <?php //echo empty($tempPower) ? "style='margin-top:-33px'" : '' ?>;/*如果没有按钮权限，将实验内容框向上移动27px*/">
        <?=  $this->renderFile(Yii::$app->getViewPath().'/experiment/layouts/exp_info.php', [
                'baseData'=>empty($baseData) ? NULL : $baseData,
                'type' => isset($type) ? $type: '',
                'weather_json' => [],
                'groupList' => $groupList,
                'default_group_ids' => $default_group_ids,
                'isCheck' => isset($isCheck)?:($isCheck=false),
                'is_system'=>$baseData['is_system'],
                'base_data_rule'=>isset($base_data_rule)?$base_data_rule:null,
                'display_weather'=>1,
                'tempPower' => $tempPower,
                'templateSubtypeList' => $templateSubtypeList
        ]); ?>
    	<?php  echo $this->renderFile(Yii::$app->getViewPath().'/experiment/layouts/add_module.php'); ?>
	</div>
	<?php //}?>

	<!-- 实验模块 -->
	<div class="exp_content drag_box" >
		<?php
		foreach($data['module_data_arr'] as $module) {
            $viewData = [
                'module' => $module,
                'experimentId' => $baseData['id'],
                'type' => isset($type) ? $type : '',
                'is_template_page' => 1,
                'tmpPower' => $tempPower,
                'module_edit' => 1
            ];
			$viewFile = '';
			switch($module['info']['component_id']) {
				case \Yii::$app->params['component']['chem']: // InDraw
					$viewData['saltList'] = $saltList; //add by zhj 2019/3/14
					$viewData['expUserId'] = isset($baseData['user_id']) ? $baseData['user_id'] : 0;
					$viewFile = '/experiment/module/indraw_v2.php';
					break;
				case \Yii::$app->params['component']['operation']:
				case \Yii::$app->params['component']['discuss']:
				case \Yii::$app->params['component']['lite']:
				case \Yii::$app->params['component']['abstract']:
					$viewFile = '/experiment/module/editor.php';
					break;
				case \Yii::$app->params['component']['tlc']:
					$viewFile = '/experiment/module/tlc.php';
					break;
				case \Yii::$app->params['component']['reference']:
					$viewFile = '/experiment/module/reference.php';
					break;
				case \Yii::$app->params['component']['biology']:
					$viewFile = '/experiment/module/biology.php';
					break;
				case \Yii::$app->params['component']['upload']:
					$viewFile = '/experiment/module/file.php';
					break;
				case \Yii::$app->params['component']['picture']:
					$viewFile = '/experiment/module/image.php';
					break;
				case \Yii::$app->params['component']['comment']:
					$viewFile = '/experiment/module/comment.php';
					break;
				case \Yii::$app->params['component']['define_table']:
					$viewFile = '/experiment/module/define.php';
					break;
				case \Yii::$app->params['component']['custom_table']:
					$viewFile = '/experiment/module/custom_table.php';
					break;
                case \Yii::$app->params['component']['xsheet']:// add by hkk 2020/3/11
                    $viewFile = '/experiment/module/xsheet.php';
                    break;
			}
			if(!empty($viewFile)) {
				echo $this->renderFile(Yii::$app->getViewPath() . $viewFile, $viewData);
			}
		}
		?>
	</div>
</div>

<script type="text/javascript">
    window.currentUserInfo = <?= !empty($currentUserInfo) ? json_encode($currentUserInfo) : '{}';?>; // xsheet内部可以获取当前用户信息
</script>

