a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:59:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:53:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:57:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:4:{s:4:"host";s:19:"dev.eln.integle.com";s:6:"accept";s:3:"*/*";s:14:"content-length";s:2:"69";s:12:"content-type";s:33:"application/x-www-form-urlencoded";}s:15:"responseHeaders";a:2:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";}s:5:"route";s:33:"eln-interface/update-static-login";s:6:"action";s:70:"frontend\controllers\ElnInterfaceController::actionUpdateStaticLogin()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:69:"user_id=1135&static_day=2025-06-17&login_time=2025-06-17+16%3A25%3A09";s:17:"Decoded to Params";a:3:{s:7:"user_id";s:4:"1135";s:10:"static_day";s:10:"2025-06-17";s:10:"login_time";s:19:"2025-06-17 16:25:09";}}s:6:"SERVER";a:37:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:11:"HTTP_ACCEPT";s:3:"*/*";s:14:"CONTENT_LENGTH";s:2:"69";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:52:"D:/integle2025/eln_5.3.11_dev/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"49640";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:35:"r=eln-interface/update-static-login";s:11:"REQUEST_URI";s:37:"/?r=eln-interface/update-static-login";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1750148708.549;s:12:"REQUEST_TIME";i:1750148708;}s:3:"GET";a:1:{s:1:"r";s:33:"eln-interface/update-static-login";}s:4:"POST";a:3:{s:7:"user_id";s:4:"1135";s:10:"static_day";s:10:"2025-06-17";s:10:"login_time";s:19:"2025-06-17 16:25:09";}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}s:3:"log";a:1:{s:8:"messages";a:17:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750148708.5765779;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750148708.587075;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750148708.702379;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750148708.703228;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1750148708.704375;i:4;a:0:{}}i:5;a:5:{i:0;s:52:"Route requested: 'eln-interface/update-static-login'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1750148708.704397;i:4;a:0:{}}i:6;a:5:{i:0;s:47:"Route to run: eln-interface/update-static-login";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1750148708.7109311;i:4;a:0:{}}i:7;a:5:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1750148708.716496;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:103;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:86:"Running action: frontend\controllers\ElnInterfaceController::actionUpdateStaticLogin()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1750148708.7165301;i:4;a:0:{}}i:9;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1750148708.7275159;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:89:"SELECT * FROM `use_static_login` WHERE (`user_id`='1135') AND (`static_day`='2025-06-17')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.750355;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `use_static_login`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.75177;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:18;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static_login' AND kcu.table_name = 'use_static_login'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7537961;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:21;a:5:{i:0;s:81:"UPDATE `use_static_login` SET `exp_login_num`=`exp_login_num`+1 WHERE `id`='9818'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.7543111;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:24;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `use_static`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.8824461;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:27;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static' AND kcu.table_name = 'use_static'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.884757;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:30;a:5:{i:0;s:86:"UPDATE `use_static` SET `last_login_time`='2025-06-17 16:25:09' WHERE `user_id`='1135'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.885426;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}}}s:9:"profiling";a:3:{s:6:"memory";i:8409040;s:4:"time";d:0.44815993309020996;s:8:"messages";a:16:{i:10;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1750148708.727546;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1750148708.7503059;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:89:"SELECT * FROM `use_static_login` WHERE (`user_id`='1135') AND (`static_day`='2025-06-17')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.75038;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:89:"SELECT * FROM `use_static_login` WHERE (`user_id`='1135') AND (`static_day`='2025-06-17')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7508371;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `use_static_login`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.751807;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:17;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `use_static_login`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.753159;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:19;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static_login' AND kcu.table_name = 'use_static_login'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7538249;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:20;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static_login' AND kcu.table_name = 'use_static_login'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7542579;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:22;a:5:{i:0;s:81:"UPDATE `use_static_login` SET `exp_login_num`=`exp_login_num`+1 WHERE `id`='9818'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.7543261;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:23;a:5:{i:0;s:81:"UPDATE `use_static_login` SET `exp_login_num`=`exp_login_num`+1 WHERE `id`='9818'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.8794429;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:25;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `use_static`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.8825591;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:26;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `use_static`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.884268;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:28;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static' AND kcu.table_name = 'use_static'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.884805;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:29;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static' AND kcu.table_name = 'use_static'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.8853281;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:31;a:5:{i:0;s:86:"UPDATE `use_static` SET `last_login_time`='2025-06-17 16:25:09' WHERE `user_id`='1135'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.8854549;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:32;a:5:{i:0;s:86:"UPDATE `use_static` SET `last_login_time`='2025-06-17 16:25:09' WHERE `user_id`='1135'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148709.009125;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}}}s:2:"db";a:1:{s:8:"messages";a:14:{i:13;a:5:{i:0;s:89:"SELECT * FROM `use_static_login` WHERE (`user_id`='1135') AND (`static_day`='2025-06-17')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.75038;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:89:"SELECT * FROM `use_static_login` WHERE (`user_id`='1135') AND (`static_day`='2025-06-17')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7508371;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1639;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `use_static_login`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.751807;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:17;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `use_static_login`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.753159;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:19;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static_login' AND kcu.table_name = 'use_static_login'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7538249;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:20;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static_login' AND kcu.table_name = 'use_static_login'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.7542579;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:22;a:5:{i:0;s:81:"UPDATE `use_static_login` SET `exp_login_num`=`exp_login_num`+1 WHERE `id`='9818'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.7543261;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:23;a:5:{i:0;s:81:"UPDATE `use_static_login` SET `exp_login_num`=`exp_login_num`+1 WHERE `id`='9818'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.8794429;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1643;s:8:"function";s:17:"updateAllCounters";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:25;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `use_static`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.8825591;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:26;a:5:{i:0;s:35:"SHOW FULL COLUMNS FROM `use_static`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.884268;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:28;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static' AND kcu.table_name = 'use_static'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.884805;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:29;a:5:{i:0;s:607:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'use_static' AND kcu.table_name = 'use_static'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750148708.8853281;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:31;a:5:{i:0;s:86:"UPDATE `use_static` SET `last_login_time`='2025-06-17 16:25:09' WHERE `user_id`='1135'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148708.8854549;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}i:32;a:5:{i:0;s:86:"UPDATE `use_static` SET `last_login_time`='2025-06-17 16:25:09' WHERE `user_id`='1135'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750148709.009125;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\ElnInterfaceController.php";s:4:"line";i:1655;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"68512664ab526";s:3:"url";s:63:"http://dev.eln.integle.com/?r=eln-interface/update-static-login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1750148709;s:10:"statusCode";i:200;s:8:"sqlCount";i:7;s:9:"mailCount";i:0;}}