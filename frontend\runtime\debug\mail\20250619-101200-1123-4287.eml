Message-ID: <<EMAIL>>
Date: Thu, 19 Jun 2025 10:11:58 +0800
Subject: http://dev.eln.integle.com/ 2025-06-19 10:11:56
 =?UTF-8?Q?=E7=B3=BB=E7=BB=9F=E6=8A=A5=E9=94=99?=
From: Integle message <<EMAIL>>
To: <EMAIL>, <EMAIL>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: quoted-printable

2025-06-19 10:11:58
[::1][-][0vd1scddjptlgvq6v4umqol4j7][error][yii\db\IntegrityException]
exception 'PDOException' with message 'SQLSTATE[23000]: Integrity
constraint violation: 1048 Column 'template_id' cannot be null' in
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\Command.php:798
Stack trace:
#0
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\Command.php(798):
PDOStatement->execute()
#1 D:\integle2025\eln_5.3.11_dev\common\components\Command.php(29):
yii\db\Command->execute()
#2
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\Schema.php(448):
common\components\command->execute()
#3
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\ActiveRecord.php(457):
yii\db\Schema->insert('<span class=3D"st...', '[<span class=3D"s...')
#4
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\ActiveRecord.php(427):
yii\db\ActiveRecord->insertInternal('<span class=3D"ke...')
#5
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\BaseActiveRecord.php(=
593):
yii\db\ActiveRecord->insert('<span class=3D"ke...', '<span
class=3D"ke...')
#6
D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php(59):
yii\db\BaseActiveRecord->save()
#7
D:\integle2025\eln_5.3.11_dev\frontend\controllers\StructDataController.ph=
p(141):
frontend\services\StructDataServer->createDataView('[<span
class=3D"s...')
#8 [internal function]:
frontend\controllers\StructDataController->actionSaveDataView()
#9
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\InlineAction.php(55=
):
call_user_func_array('[<span class=3D"t...', '[]')
#10
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams('[<span class=3D"s...')
#11
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('<span class=3D"st...', '[<span
class=3D"s...')
#12
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('<span class=3D"st...', '[<span class=3D"s...')
#13
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Application.php(375=
):
yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_5.3.11_dev\frontend\web\index.php(33):
yii\base\Application->run()
#15 {main}

Next exception 'yii\db\IntegrityException' with message
'SQLSTATE[23000]: Integrity constraint violation: 1048 Column
'template_id' cannot be null
The SQL being executed was: INSERT INTO `structdata_dataview` (`id`,
`name`, `template_id`, `create_by`) VALUES (NULL, '2333', NULL, 1135)'
in D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\Schema.php:628
Stack trace:
#0
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\Command.php(808):
yii\db\Schema->convertException(Object(PDOException), 'INSERT INTO
`st...')
#1 D:\integle2025\eln_5.3.11_dev\common\components\Command.php(29):
yii\db\Command->execute()
#2
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\Schema.php(448):
common\components\command->execute()
#3
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\ActiveRecord.php(457):
yii\db\Schema->insert('<span class=3D"st...', '[<span class=3D"s...')
#4
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\ActiveRecord.php(427):
yii\db\ActiveRecord->insertInternal('<span class=3D"ke...')
#5
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\db\BaseActiveRecord.php(=
593):
yii\db\ActiveRecord->insert('<span class=3D"ke...', '<span
class=3D"ke...')
#6
D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php(59):
yii\db\BaseActiveRecord->save()
#7
D:\integle2025\eln_5.3.11_dev\frontend\controllers\StructDataController.ph=
p(141):
frontend\services\StructDataServer->createDataView('[<span
class=3D"s...')
#8 [internal function]:
frontend\controllers\StructDataController->actionSaveDataView()
#9
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\InlineAction.php(55=
):
call_user_func_array('[<span class=3D"t...', '[]')
#10
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams('[<span class=3D"s...')
#11
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('<span class=3D"st...', '[<span
class=3D"s...')
#12
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('<span class=3D"st...', '[<span class=3D"s...')
#13
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Application.php(375=
):
yii\web\Application->handleRequest(Object(yii\web\Request))
#14 D:\integle2025\eln_5.3.11_dev\frontend\web\index.php(33):
yii\base\Application->run()
#15 {main}
Additional Information:
Array
(
    [0] =3D> 23000
    [1] =3D> 1048
    [2] =3D> Column 'template_id' cannot be null
)

2025-06-19 10:11:56
[::1][-][0vd1scddjptlgvq6v4umqol4j7][info][application] $_GET =3D [
    'r' =3D> 'struct-data/save-data-view'
]

$_COOKIE =3D [
    'eln_page_limit' =3D> '15'
    'ldap_check' =3D> '0'
    'integle_session' =3D> '0vd1scddjptlgvq6v4umqol4j7'
    'sims_u' =3D> '38828f261ee60584144cf546b2ff9ece'
    'lock_interval' =3D> '180'
    'center_language' =3D> 'CN'
    'page_type' =3D> '0'
    'last_active_time' =3D> '1750299116642'
]

$_SESSION =3D [
    '__flash' =3D> []
    'userinfo' =3D> [
        'user_id' =3D> '1135'
        'email' =3D> null
        'name' =3D> 'chenqi'
        'phone' =3D> null
        'ticket' =3D> '38828f261ee60584144cf546b2ff9ece'
        'reg_time' =3D> '1744077856'
        'Token' =3D> '7eb44480540d6e80df79fce77c791828'
        'register_from' =3D> ''
        'from_ldap' =3D> '0'
        'gender' =3D> '0'
        'nick_name' =3D> ''
        'contact_phone' =3D> ''
        'real_name' =3D> '=E9=99=88=E5=A5=87'
        'point' =3D> '0'
        'company_name' =3D> ''
        'job' =3D> ''
        'office_phone' =3D> ''
        'qq' =3D> ''
        'country' =3D> ''
        'province' =3D> ''
        'city' =3D> ''
        'detail_address' =3D> ''
        'post_code' =3D> ''
        'id_card' =3D> ''
        'big_img' =3D> ''
        'small_img' =3D> ''
        'unread_message' =3D> '2'
        'default_group' =3D> '0'
        'contact_email' =3D> ''
        'role_ids' =3D> '1,84'
        'department' =3D> []
        'id' =3D> '1135'
        'groups' =3D> [
            0 =3D> [
                'id' =3D> '1'
                'name' =3D> '=E5=85=AC=E5=8F=B8=E7=BE=A4'
                'role' =3D> '1'
            ]
            1 =3D> [
                'id' =3D> '598'
                'name' =3D> 'cq1'
                'role' =3D> '3'
            ]
        ]
        'current_company_id' =3D> '1'
        'app_access' =3D> 1
    ]
    'eln_lang' =3D> 'zh-CN'
]

$_SERVER =3D [
    'MIBDIRS' =3D> 'D:/xampp/php/extras/mibs'
    'MYSQL_HOME' =3D> '\\xampp\\mysql\\bin'
    'OPENSSL_CONF' =3D> 'D:/xampp/apache/bin/openssl.cnf'
    'PHP_PEAR_SYSCONF_DIR' =3D> '\\xampp\\php'
    'PHPRC' =3D> '\\xampp\\php'
    'TMP' =3D> '\\xampp\\tmp'
    'HTTP_HOST' =3D> 'dev.eln.integle.com'
    'HTTP_CONNECTION' =3D> 'keep-alive'
    'CONTENT_LENGTH' =3D> '59'
    'HTTP_USER_AGENT' =3D> 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)
AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' =3D> 'application/json, text/plain, */*'
    'CONTENT_TYPE' =3D> 'application/json'
    'HTTP_ORIGIN' =3D> 'http://dev.eln.integle.com'
    'HTTP_REFERER' =3D> 'http://dev.eln.integle.com/'
    'HTTP_ACCEPT_ENCODING' =3D> 'gzip, deflate'
    'HTTP_ACCEPT_LANGUAGE' =3D> 'zh-CN,zh;q=3D0.9'
    'HTTP_COOKIE' =3D> 'eln_page_limit=3D15; ldap_check=3D0;
integle_session=3D0vd1scddjptlgvq6v4umqol4j7;
sims_u=3D38828f261ee60584144cf546b2ff9ece; lock_interval=3D180;
center_language=3DCN; page_type=3D0; last_active_time=3D1750299116642'
    'PATH' =3D> 'C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\javapath;C:\\Program Files
(x86)\\PerkinElmerInformatics\\ChemOffice2017\\ChemScript\\Lib;C:\\WINDOW=
S\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System3=
2\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program
Files\\dotnet\\;C:\\Program Files
(x86)\\DSOC\\ExtractContent;C:\\Program Files
(x86)\\DSOC\\ExtractContent64\\OCR;D:\\Program
Files\\TortoiseSVN\\bin;D:\\Program
Files\\Java\\jdk-1.8\\bin;D:\\Program
Files\\php\\php-5.6.40-Win32-VC11-x64;D:\\composer;D:\\Program
Files\\Git\\cmd;D:\\Program
Files\\nodejs\\node_global\\node_modules;D:\\nvm;D:\\nvm4w\\nodejs;D:\\Pr=
ogram
Files\\nodejs\\node_global;D:\\Program
Files\\wget-1.21.4-win64;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Program
Files\\JetBrains\\IntelliJ IDEA 2024.1.4\\bin;;D:\\Program
Files\\JetBrains\\PhpStorm
2024.1.4\\bin;;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;=
D:\\Program
Files\\JetBrains\\WebStorm
2024.1.5\\bin;;D:\\Users\\chenc\\AppData\\Local\\Programs\\Microsoft
VS Code\\bin;D:\\Program Files\\cursor\\resources\\app\\bin'
    'SystemRoot' =3D> 'C:\\WINDOWS'
    'COMSPEC' =3D> 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' =3D>
'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'WINDIR' =3D> 'C:\\WINDOWS'
    'SERVER_SIGNATURE' =3D> '<address>Apache/2.4.38 (Win64)
OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port
80</address>
'
    'SERVER_SOFTWARE' =3D> 'Apache/2.4.38 (Win64) OpenSSL/1.0.2q
PHP/5.6.40'
    'SERVER_NAME' =3D> 'dev.eln.integle.com'
    'SERVER_ADDR' =3D> '::1'
    'SERVER_PORT' =3D> '80'
    'REMOTE_ADDR' =3D> '::1'
    'DOCUMENT_ROOT' =3D> 'D:/integle2025/eln_5.3.11_dev/frontend/web'
    'REQUEST_SCHEME' =3D> 'http'
    'CONTEXT_PREFIX' =3D> ''
    'CONTEXT_DOCUMENT_ROOT' =3D>
'D:/integle2025/eln_5.3.11_dev/frontend/web'
    'SERVER_ADMIN' =3D> 'postmaster@localhost'
    'SCRIPT_FILENAME' =3D>
'D:/integle2025/eln_5.3.11_dev/frontend/web/index.php'
    'REMOTE_PORT' =3D> '65273'
    'GATEWAY_INTERFACE' =3D> 'CGI/1.1'
    'SERVER_PROTOCOL' =3D> 'HTTP/1.1'
    'REQUEST_METHOD' =3D> 'POST'
    'QUERY_STRING' =3D> 'r=3Dstruct-data/save-data-view'
    'REQUEST_URI' =3D> '/?r=3Dstruct-data/save-data-view'
    'SCRIPT_NAME' =3D> '/index.php'
    'PHP_SELF' =3D> '/index.php'
    'REQUEST_TIME_FLOAT' =3D> 1750299116.746
    'REQUEST_TIME' =3D> 1750299116
]