<template>
  <div id="isInstrumentBookingCreateDialogDiv" >
    <ElDialog class="isInstrumentBookingCreateDialogDivOut"
              v-model="isInstrumentBookingCreateDialog"
              @close="isInstrumentBookingCreateDialog = false"
              :title="$t( !type ? 'InstrumentBookingCreateDialog.create' : 'InstrumentBookingCreateDialog.edit')"
              width="772" id="isInstrumentBookingCreateDialogOut">
      <ElRow class="instrumentBookingCreateRow" v-loading="loading">
        <ElCol style="max-width: 360px;margin-right: 16px;">
          <ElForm
              label-position="top" ref="instrumentBookingCreateFormRef" :rules="validateForm" :model="instrumentBookingCreateForm" id="isInstrumentBookingConfigDialogForm" style="padding-top: 3px;"
          >
            <ElFormItem  :label="$t('InstrumentBookingCreateDialog.name')" prop="instrumentName">
              <ElAutocomplete
                  v-model="instrumentBookingCreateForm.instrumentName"
                  :fetch-suggestions="querySearchInstrumentBooking"
                  :placeholder="$t('InstrumentBookingCreateDialog.tips1')"
                  @clear="instrumentBookingHtml = []"
                  @select="nameSelectChange"
                  @change="nameSelectChange"
                  :debounce="500"
                  clearable
                  value-key="name"
                  style="width: 360px"
                  :disabled="type === 1 || nameDisabled"
                  :popper-append-to-body="false"
                  :teleported="true"
                  append-to="#isInstrumentBookingCreateDialogDiv"
                  :loading="nameLoading"
              />
            </ElFormItem>
            <ElFormItem :label="$t('InstrumentBookingCreateDialog.time')" prop="time">
              <template v-if="type === 1 && (new Date(bookOld.start_time) < new Date()) ">
                <div class="timeBox">
                  <span>{{ instrumentBookingCreateForm.time[0] }}</span>
                  <span>-</span>
                  <ElDatePicker
                      v-model="instrumentBookingCreateForm.time[1]"
                      type="datetime"
                      :placeholder="$t('InstrumentBookingCreateDialog.end_time')"
                      style="max-width: 180px"
                      popper-class="instrumentBookingCreateTime"
                      :disabled="(new Date(bookOld.end_time) < new Date())"
                      :disabled-date="disabledEndDate"
                      @change="renderNowInstrumentBooking"
                  />
                </div>
              </template>
              <template v-else>
                <ElDatePicker
                    v-model="instrumentBookingCreateForm.time"
                    :class="{ errorColor: timeErrors }"
                    popper-class="instrumentBookingCreateTime"
                    :style="{boxShadow: timeErrors ? '0 0 0 1px rgb(246, 121, 86)' : ''}"
                    type="datetimerange"
                    is-range
                    range-separator="-"
                    :start-placeholder="$t('InstrumentBookingCreateDialog.start_time')"
                    :end-placeholder="$t('InstrumentBookingCreateDialog.end_time')"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    format="YYYY:MM:DD HH:mm"
                    @change="renderNowInstrumentBooking"
                    :disabled-date="disabledDate"
                    :disabled="isTimePickerAction"
                    :clear="clearTimePicker"
                />
              </template>
              <p v-if="timeErrors" style="color: rgb(246, 121, 86);font-size: 12px;font-weight: 400;margin-top: 4px;margin-bottom: 0;">{{ timeErrors }}</p>
            </ElFormItem>
            <ElFormItem :label="$t('InstrumentBookingCreateDialog.warn')">
              <ElSelect v-model="instrumentBookingCreateForm.warn" :placeholder="$t('InstrumentBookingCreateDialog.warnP')" style="width: 360px">
                <ElOption
                    v-for="item in warnOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem :label="$t('InstrumentBookingCreateDialog.book_num')">
              <ElSelect
                  v-model="instrumentBookingCreateForm.relatedExperiment"
                  ref="experimentSelectRef"
                  multiple
                  filterable
                  remote
                  :max-collapse-tags="3"
                  reserve-keyword
                  :placeholder="$t('InstrumentBookingCreateDialog.bookP')"
                  :remote-method="querySearchInstrumentBookingRelateExperiment"
                  :loading="relatedExperimentLoading"
                  style="width: 360px"
              >
                <ElOption
                    v-for="item in instrumentRelateExperimentOption"
                    :key="item.exp_code"
                    :label="item.exp_code"
                    :value="item.exp_code"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem :label="$t('InstrumentBookingCreateDialog.remark')" style="padding-bottom: 28px;">
              <ElInput
                  v-model="instrumentBookingCreateForm.remark"
                  :rows="3"
                  :maxlength="200"
                  type="textarea"
              />
            </ElFormItem>
            <ElFormItem id="instrumentCreateBtn">
              <ElButton @click="isInstrumentBookingCreateDialog = false">{{ t('InstrumentBookingCreateDialog.cancel') }}</ElButton>
              <ElButton type="primary" @click="submitForm(instrumentBookingCreateFormRef)" style="background: rgb(115, 102, 255);border:none;">{{ t('InstrumentBookingCreateDialog.sure') }}</ElButton>
            </ElFormItem>
          </ElForm>
        </ElCol>
        <ElCol style="max-width: 340px;display: flex;flex-direction: column;align-items: flex-start"  v-loading="loading">
          <div class="instrumentScheduleOut">
            <div class="instrumentScheduleOut-header">
              <span style="color: rgb(48, 48, 51);font-size: 14px;font-weight: 500;margin-right: 125px;text-wrap: nowrap">{{ formattedDate }}</span>
              <ElButton style="margin-right: 4px;" @click="turnToday">{{ t('InstrumentBookingCreateDialog.today') }}</ElButton>
              <ElIcon
                  @click="turnLeftDay"
                  :size="12" color="rgb(106, 106, 115)"
                  style="cursor: pointer;margin-left: 10px;">
                <ArrowLeft/>
              </ElIcon>
              <ElIcon
                  @click="turnRightDay"
                  :size="12" color="rgb(106, 106, 115)"
                  style="cursor: pointer;margin-left: 10px;">
                <ArrowRight
                />
              </ElIcon>
            </div>
            <div class="instrumentScheduleOut-container">
              <div class="instrumentScheduleIns" >

                <div class="instrumentScheduleIns-now" :style="{top: nowTimeTop + 'px'}" v-if="(new Date()).getDate() === calendarTime.getDate()">
                  <div class="instrumentScheduleIns-nowCircle"></div>
                  <div class="instrumentScheduleIns-nowLine"></div>
                </div>

                <div  style="height: 0; position: relative;">
                  <div class="instrumentScheduleAlready" v-for="(item, index) in instrumentBookingHtml" :style="{ top: item.top + 'px', height: item.height + 'px' }">{{ item.name }}</div>
                </div>

<!--                <div class="instrumentBookingNowHtmlOut"  style="position: relative;height: 0;z-index:999;" v-if="instrumentBookingCreateForm.time[0] && instrumentBookingCreateForm.time[1]">-->
<!--                  <div class="instrumentBookingNowDragContainer">-->
<!--                    <div class="instrumentBookingNowDragContainerItem"-->
<!--                         :class="{ 'dragging': draggingEvent, 'errorArea': timeErrors, 'safeArea': !timeErrors }"-->
<!--                         :style="getEventStyle(instrumentBookingCreateForm)"-->
<!--                         @mousedown="startDrag($event, instrumentBookingCreateForm, 'move')"-->
<!--                    >-->
<!--                      &lt;!&ndash; 顶部拖拽手柄 &ndash;&gt;-->
<!--                      <div-->
<!--                          class="resize-handle resize-handle-top"-->
<!--                          :class="{ 'errorAreaCircle': timeErrors }"-->
<!--                          @mousedown.stop="startDrag($event, instrumentBookingCreateForm, 'resize-top')"-->
<!--                          title="拖拽调整开始时间"-->
<!--                      ></div>-->

<!--                      <div class="event-content">-->
<!--                        <div class="event-title" v-if="!timeErrors">{{ t('InstrumentBookingCreateDialog.nowBook') }} {{ timeErrors }}</div>-->
<!--                      </div>-->

<!--                      &lt;!&ndash; 底部拖拽手柄 &ndash;&gt;-->
<!--                      <div-->
<!--                          class="resize-handle resize-handle-bottom"-->
<!--                          :class="{ 'errorAreaCircle': timeErrors }"-->
<!--                          @mousedown.stop="startDrag($event, instrumentBookingCreateForm, 'resize-bottom')"-->
<!--                          title="拖拽调整结束时间"-->
<!--                      ></div>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </div>-->


                <div class="instrumentBookingNowHtmlOut" v-if="isSelectTime && topDragAreaHeight !== 0"  style="position: relative;height: 0">
                  <div   :class="['instrumentScheduleNowArea', timeErrors  ? 'errorArea' : 'safeArea']"
                         :style="{ top: topDragAreaTop + 'px', height: topDragAreaHeight + 'px' }">
                    <span style="user-select: none;" v-if="!timeErrors">{{ t('InstrumentBookingCreateDialog.nowBook') }}</span>
                    <div class="instrumentScheduleNowArea-circle1" :style="{ border: timeErrors? '1px solid rgb(241, 154, 72)': '1px solid rgb(115, 102, 255)'}"></div>
                    <!--                  <div class="instrumentScheduleNowArea-topDragArea">-->
                    <!--                  </div>-->
                    <!--                  <div class="instrumentScheduleNowArea-bottomDragArea"></div>-->
                    <div class="instrumentScheduleNowArea-circle2" :style="{ border: timeErrors? '1px solid rgb(241, 154, 72)': '1px solid rgb(115, 102, 255)'}"></div>
                  </div>
                </div>

                <div class="instrumentScheduleIns-item" v-for="i in 24">
                  <div class="instrumentScheduleIns-itemLeft">
                    <span v-if="i !== 1" style="position: relative;left: 12px; bottom: 10px;color: rgb(106, 106, 115);font-family: HarmonyOS Sans SC;">{{ formatTime( i - 1) }}</span>
                  </div>
                  <div class="instrumentScheduleIns-itemRight"></div>
                </div>
              </div>
            </div>
          </div>
        </ElCol>
      </ElRow>
      <ElDialog class="otherBookTime"  :align-center="true" v-model="isOtherBookingTime"  style="width: 400px;">
        <div class="otherBookingTime">
          <div class="otherBookingTimeLeft">
            <ElIcon
                :size="20" color="rgb(241, 154, 72)">
              <Warning />
            </ElIcon>
          </div>
          <div class="otherBookingTimeRight">
            <p style="font-weight: 500;font-size: 16px;">{{ t('InstrumentBookingCreateDialog.otherBook1') }}</p>
            <p>{{ t('InstrumentBookingCreateDialog.otherBook2') }}{{ formatTimeSlots(instrumentBookingCreateForm.detail?.available_slots) }}</p>
            <p>{{ t('InstrumentBookingCreateDialog.otherBook3') }} <span style="color:rgb(115, 102, 255)">{{ formatDateTimeSlots(instrumentOtherBookingTime) }}</span></p>
          </div>
        </div>
        <div class="otherBookingBtn">
          <el-button @click="cancelForm">取消</el-button>
          <el-button style="background: rgb(115, 102, 255);color: white;border:1px solid rgb(115, 102, 255);" @click="moreCreateAdd">确认</el-button>
        </div>
      </ElDialog>
    </ElDialog>
  </div>

</template>


<script setup>
import 'element-plus/dist/index.css' // 引入 Element UI 的 CSS
import {
  ElDialog, ElCol, ElRow, ElForm, ElInput, ElFormItem, ElDatePicker, ElSelect, ElOption, ElButton, ElIcon, ElMessage
} from 'element-plus'
import {ref, onMounted, nextTick, computed, watch, defineEmits } from "vue"; // 按需引入组件
import { useI18n } from 'vue-i18n';
import { ArrowLeft, ArrowRight, Warning } from "@element-plus/icons-vue";
import axios from 'axios'
import { debounce } from 'lodash'
const emit = defineEmits(['closeDialog', 'refreshBookMine'])
const sourceType = ref('')

// 时间管理
const calendarTime = ref(new Date())

// 外界传入内容
const type = ref(0)  // 0 创建 1编辑
let instrument_props = ref({})
// 向外暴露打开的方法
const  openDialogCreate = (instrument) => {
  type.value = 0

  isInstrumentBookingCreateDialog.value = true
  instrument_props.value = null
  instrument_props.value = instrument
  console.log(instrument_props.value )
  const { name, id, time, warn , related_experiment, remark, reminder, source } = instrument_props.value
  instrumentBookingCreateForm.value = {
    instrumentName: name,
    time,
    relatedExperiment: related_experiment && typeof related_experiment === 'string'
        ? related_experiment.split(',')
        : related_experiment,
    instrumentId: id,
    warn: Number(warn) || Number(reminder) || 0,
    remark,
    detail: instrument_props.value
  }
  calendarTime.value = new Date(time[0])

  sourceType.value = source // 来源页面

  console.log(instrumentBookingCreateForm.value)
  // 获取预约记录
  if(instrument_props.value.name) {
    getBookingDetail({ id: instrument_props.value.id}, true)
  }
}

const bookOld = ref({}) // 存储编辑预约的原始数据
const openDialogEdit  = bookList => {
  type.value = 1
  isInstrumentBookingCreateDialog.value = true
  bookOld.value = bookList
  console.log(bookList)
  const { name, id, instrument_id, start_time, end_time, related_experiment, remark, warn , reminder, source } = bookList
  instrumentBookingCreateForm.value = {
    instrumentName: name,
    time: [start_time, end_time],
    warn: Number(warn) || Number(reminder) || 0,
    relatedExperiment: related_experiment ? related_experiment?.split(','): related_experiment,
    instrumentId: instrument_id,
    id,
    remark,
    detail: bookList
  }

  sourceType.value = source // 来源页面

  calendarTime.value = new Date(start_time)

  if(instrument_id) {
    getBookingDetail({ id: instrument_id }, true)
  }
}
defineExpose({
  openDialogCreate,
  openDialogEdit
})


const { t } = useI18n();
const instrumentBookingCreateFormRef = ref(null)
const bookList = ref([
  {
    "instrument_id": "2666",
    "id": "97",
    "start_time": "2025-06-06 19:55:00",
    "end_time": "2025-06-06 22:50:00",
    "related_experiment": "",
    "create_time": "2025-05-16 11:02:32",
    "remark": "",
    "name": "数值-20250401",
    "batch_number": "20250401",
    "specification": "",
    "model": null,
    "user_name": "张世明",
    available_slots: [
      ["00:00", "12:59"],
      ["18:00", "21:59"],
    ],
    max_advance_day: 2,
    min_advance: {
      value: "2",
      unit: "day"
    },
    max_booking_duration: {
      value: "2",
      unit: "day"
    },
  },
  {
    "instrument_id": "2666",
    "id": "96",
    "start_time": "2025-05-15 14:50:00",
    "end_time": "2025-05-16 18:45:00",
    "related_experiment": "",
    "create_time": "2025-05-14 10:34:19",
    "remark": "",
    "name": "数值-20250401",
    "batch_number": "20250401",
    "specification": "",
    "model": null,
    "user_name": "张世明"
  }
])
const isInstrumentBookingCreateDialog = ref(false)
const instrumentBookingCreateForm = ref({
  instrumentName: instrument_props.value?.name ||  '',
  instrumentId: instrument_props.value?.id ||  '', // 仪器id
  time: instrument_props.value?.time ||  [],
  warn: instrument_props.value?.warn || 0,
  relatedExperiment: [],
  remark: instrument_props.value?.remark || '',
  detail: instrument_props.value || {},
})

// 是否本地测试
const dev = ref(false)




// 表单校验
const validateForm = ref({
  instrumentName: [
    { required: true, message: '请选择', trigger: 'blur' },
  ],
  time: [
      { required: true, message: '', trigger: 'blur' },
  ]
})

// 显示的时间
const formattedDate = computed(() => {
  const year = calendarTime.value.getFullYear();
  const month = calendarTime.value.getMonth() + 1; // 月份是从0开始的，所以需要加1
  const day = calendarTime.value.getDate();

  return `${year}年${month}月${day}日`; // 拼接年月日
});

const nameLoading = ref(false)
// 数据置空 -> 查找仪器 -> 选定仪器 -> 查找该仪器预约的相关信息 -> 渲染相关信息
const querySearchInstrumentBooking = async (name, cb) => {
  // 测试用
  if( !name ) {
    return
  }

  nameLoading.value = true
  try {
    const url = '/?r=instrument/get-instrument-by-name'

    const response = await axios.post(url, {
      name
    },{

      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data;

    nameLoading.value = false;
    cb(data.data.instruments)
  }
  catch (error) {
  } finally {
    nameLoading.value = false
  }
}

const nameSelectChange = (e) => {
  // 发生更改才会触发 触发后->查询该仪器下的预约记录->渲染
  if(!e) {
    instrumentBookingHtml.value = []
    instrumentBookingCreateForm.value.time = []
    isSelectTime.value = false
    timeErrors.value = ''
    return
  }
  instrumentBookingCreateForm.value.instrumentId = e.id

  instrumentBookingCreateForm.value.detail = {
    ... e,  // 保留原有属性
    available_slots: e.available_slots?  JSON.parse(e.available_slots): null,
    min_advance: e.min_advance ? JSON.parse(e.min_advance): null,
    max_booking_duration: e.max_booking_duration? JSON.parse(e.max_booking_duration) : null
  };

  getBookingDetail({ id: e.id});
}


const getBookingDetail = debounce(async ({ id, refreshNow = false}, isBigLoading = false) => {
  bookList.value = []
  loading.value = isBigLoading
  try {
    const url = '/?r=instrument/get-book-by-id'

    const response = await axios.post(url, {
      id,
      day: calendarTime.value
    },{

      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data;


    bookList.value = type.value === 1? data.data?.book_list.filter(item => item.id !== instrumentBookingCreateForm.value.id) : data.data?.book_list
    if(bookList.value.length > 0) {
      renderAlreadyInstrumentBooking()
    }
    if(instrumentBookingCreateForm.value.time[0] || refreshNow) {
      renderNowInstrumentBooking()
    }
  }
  catch (error) {
    if(instrumentBookingCreateForm.value.time[0] || refreshNow) {
      renderNowInstrumentBooking()
    }
  } finally {
    loading.value = false
  }
})


const nameDisabled = ref(false) // 名称禁止选择
// 来源于老页面
const { oldItem, oldStatus } = defineProps({
  oldItem: {
    type: Object,
    default: {}
  },
  oldStatus: {
    type: Number,
    default: 0
  },
  closeBookCreate: {
    type: Function,
    default: null
  }
});

onMounted(() => {

  initDialog()

  if(oldItem) {
    // 老页面有数据传过来

    instrumentBookingCreateForm.value.instrumentId = oldItem?.id
    instrumentBookingCreateForm.value.instrumentName = oldItem?.name
    instrumentBookingCreateForm.value.time = oldItem?.time
    instrumentBookingCreateForm.value.detail = oldItem


    if(oldItem?.id) {
      nameDisabled.value = true
      isInstrumentBookingCreateDialog.value = true
    }
    type.value = oldStatus
    if(instrumentBookingCreateForm.value.instrumentId) {
      getBookingDetail({ id: instrumentBookingCreateForm.value.instrumentId}, true)
    }
  }

})


const initDialog = () => {
  // 初始化
  timeErrors.value = ''
  topDragAreaTop.value = 0
  topDragAreaHeight.value = 0
  instrumentBookingHtml.value = []
  loading.value = false
}

watch( instrumentBookingCreateForm, (n, o) => {
  initDialog()
})

const clearTimePicker = (e) => {
  timeErrors.value = ''
  instrumentBookingCreateForm.value.time = []
  renderAlreadyInstrumentBooking()
}

const renderAlreadyInstrumentBooking = () => {
  instrumentBookingHtml.value = []
  bookList.value.forEach(i => {
    const { top, height } = calculateTimePosition(i.start_time, i.end_time, calendarTime.value)
    if(height > 0) {
      instrumentBookingHtml.value.push({top, height, name: i.user_name})
    }
  })
}

const calculateTimePosition = (startTime, endTime, selectTime) =>  {
  // 将时间字符串转换为 Date 对象
  const start = new Date(startTime);
  const end = new Date(endTime);
  const select = new Date(selectTime);
  // 设置 24 小时的时间范围，0点对应的时间
  const startOfDay = new Date(select);
  startOfDay.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00

  const endOfDay = new Date(select);
  endOfDay.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59

  // 计算三个时间段的交集
  const rangeStart = Math.max(start, startOfDay); // 取三个时间段的最晚开始时间
  const rangeEnd = Math.min(end, endOfDay); // 取三个时间段的最早结束时间

  // 如果没有交集，返回 { top: 0, height: 0 }
  if (rangeStart >= rangeEnd) {
    return { top: 0, height: 0 };
  }

  // 计算时间范围的持续时间，单位为毫秒
  const totalDuration = endOfDay - startOfDay; // 24 小时的总时长（毫秒）
  const rangeDuration = rangeEnd - rangeStart; // 交集的持续时间（毫秒）

  // 计算该时间范围在 24 小时内的比例
  const heightRatio = rangeDuration / totalDuration;
  const topRatio = (rangeStart - startOfDay) / totalDuration;

  // 计算最终的 height 和 top
  const height = heightRatio * 1152; // 计算该时间范围所占的高度
  const top = topRatio * 1152; // 计算该时间范围的起始位置

  return { top, height };
}

const instrumentOtherBookingTime = ref([])
const isOtherBookingTime = ref(false)

// 计算与禁用时间的交集，并拆分多条记录
const calculateAvailableTimeSlots = (available_slots = [['00:00', '23:59']], bookingTime) => {
  // 参数验证
  if (!Array.isArray(available_slots) || !Array.isArray(bookingTime) || bookingTime.length !== 2) {
    return { slots: [], isModified: false };
  }

  // 辅助函数：格式化日期时间
  const formatDateTime = (date) => {
    const pad = (n) => n.toString().padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:00`;
  };

  // 辅助函数：检查是否为全天时间段
  const isFullDaySlot = (slot) => {
    return (slot[0] === '00:00' && slot[1] === '00:00') ||
        (slot[0] === '00:00' && slot[1] === '23:59');
  };

  // 检查是否所有时间段都是全天
  const allSlotsAreFullDay = available_slots.every(isFullDaySlot);
  if (allSlotsAreFullDay) {
    return { slots: [bookingTime], isModified: false };
  }

  // 辅助函数：检查时间是否在可用时间段内
  const isTimeInSlot = (time, slot) => {
    const [startHour, startMin] = slot[0].split(':').map(Number);
    const [endHour, endMin] = slot[1].split(':').map(Number);

    const timeHour = time.getHours();
    const timeMin = time.getMinutes();

    const timeInMinutes = timeHour * 60 + timeMin;
    const startInMinutes = startHour * 60 + startMin;
    const endInMinutes = endHour * 60 + endMin;

    return timeInMinutes >= startInMinutes && timeInMinutes <= endInMinutes;
  };

  // 转换预约时间
  const bookingStart = new Date(bookingTime[0]);
  const bookingEnd = new Date(bookingTime[1]);

  // 检查预约时间是否有效
  if (bookingStart >= bookingEnd) {
    return { slots: [], isModified: false };
  }

  // 获取开始日期和结束日期
  const startDate = new Date(bookingStart.getFullYear(), bookingStart.getMonth(), bookingStart.getDate());
  const endDate = new Date(bookingEnd.getFullYear(), bookingEnd.getMonth(), bookingEnd.getDate());

  // 如果预约时间在一天内
  if (startDate.getTime() === endDate.getTime()) {
    // 检查是否完全在某个可用时间段内
    for (const slot of available_slots) {
      if (isTimeInSlot(bookingStart, slot) && isTimeInSlot(bookingEnd, slot)) {
        return { slots: [bookingTime], isModified: false };
      }
    }
  }

  let result = [];

  // 遍历每一天
  for (let currentDate = new Date(startDate); currentDate <= endDate; currentDate.setDate(currentDate.getDate() + 1)) {
    // 遍历每个可用时间段
    for (const slot of available_slots) {
      // 跳过全天时间段
      if (isFullDaySlot(slot)) {
        continue;
      }

      const [startHour, startMin] = slot[0].split(':').map(Number);
      const [endHour, endMin] = slot[1].split(':').map(Number);

      // 创建当天的时间段
      const slotStart = new Date(currentDate);
      const slotEnd = new Date(currentDate);
      slotStart.setHours(startHour, startMin, 0, 0);
      slotEnd.setHours(endHour, endMin, 59, 999);

      // 计算与预约时间的交集
      const intersectionStart = new Date(Math.max(slotStart.getTime(), bookingStart.getTime()));
      const intersectionEnd = new Date(Math.min(slotEnd.getTime(), bookingEnd.getTime()));

      // 如果有有效交集
      if (intersectionStart < intersectionEnd) {
        result.push([
          formatDateTime(intersectionStart),
          formatDateTime(intersectionEnd)
        ]);
      }
    }
  }

  // 检查结果是否与原始预约时间相同
  const isModified = result.length !== 1 ||
      result[0][0] !== bookingTime[0] ||
      result[0][1] !== bookingTime[1];

  return { slots: result, isModified };
};


const formatDateTimeSlots = (timeSlots) => {
  if (!timeSlots || timeSlots.length === 0) {
    return '';
  }

  // 格式化单个时间段的函数
  const formatSingleSlot = (slot) => {
    const startDate = new Date(slot[0]);
    const endDate = new Date(slot[1]);

    // 格式化日期部分
    const dateStr = `${startDate.getFullYear()}年${startDate.getMonth() + 1}月${startDate.getDate()}日`;

    // 格式化时间部分
    const startTime = `${startDate.getHours().toString().padStart(2, '0')}:${startDate.getMinutes().toString().padStart(2, '0')}`;
    const endTime = `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;

    return `${dateStr}${startTime}-${endTime}`;
  };

  // 如果只有一个时间段，直接返回
  if (timeSlots.length === 1) {
    return formatSingleSlot(timeSlots[0]);
  }

  // 多个时间段用顿号分隔
  return timeSlots.map(formatSingleSlot).join('、');
}

// 优化格式
const formatTimeSlots = (time) => {
  const timeSlots = time
  if (!timeSlots || timeSlots.length === 0) {
    return '';
  }

  // 如果只有一个时间段，直接返回
  if (timeSlots.length === 1) {
    return timeSlots[0].join('-');
  }

  // 多个时间段用顿号分隔
  return timeSlots.map(slot => slot.join('-')).join('、');
}

const getToday = () => {
  const currentDate = new Date();

// 获取年、月、日
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1;  // getMonth() 返回的月份是从 0 开始的，所以要加 1
  const day = currentDate.getDate();

// 格式化日期为 `2025年1月13日`
  return `${year}年${month}月${day}日`;
}

// 禁用今天之前的日期
const disabledDate = (date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // 将时间设置为 00:00:00，确保比较的是日期部分

  return date < today; // 如果选择的日期早于今天，则禁用
};

// 只能选择结束时间的情况下，禁止其选择起始时间之前的日期
const disabledEndDate = time => {
  const minDate = new Date('2025-05-21T00:00:00');
  // 获取当前时间
  const currentTime = new Date();
  // 获取昨天的日期
  const yesterday = new Date(currentTime);
  yesterday.setDate(currentTime.getDate() - 1); // 减去一天
  // 禁用小于最小日期且小于昨天时间的日期
  return time.getTime() < minDate.getTime() || time.getTime() < yesterday.getTime();
}

//  时间相关
// 拿到时间 -> 判断是否有冲突 -> 渲染
const timeErrors = ref('')
const timeErrorsDev = ref('11111')
// 渲染借用时间
const renderNowInstrumentBooking = () => {
  let errors = ''
  const ifPass = !(type.value === 1 && (new Date(bookOld.value.start_time)) < (new Date())) // 开始时间小于当前时间切是编辑状态可跳过部分验证
  timeErrors.value = ''
  topDragAreaTop.value = 0
  topDragAreaHeight.value = 0
  if(instrumentBookingCreateForm.value.time[0] && instrumentBookingCreateForm.value.time[1]) {
// 将新的时间字符串转换为 Date 对象
    const newStartDate = new Date(instrumentBookingCreateForm.value.time[0]);
    const newEndDate = new Date(instrumentBookingCreateForm.value.time[1]);
    // 检查是否有时间冲突
    const hasConflict = Array.isArray(bookList.value) && bookList.value.length > 0 && bookList.value.some((booking) => {
      const bookingStart = new Date(booking.start_time.replace(' ', 'T'));
      const bookingEnd = new Date(booking.end_time.replace(' ', 'T'));

      // 判断时间段是否有重叠
      return newStartDate < bookingEnd && newEndDate > bookingStart; // 新的时间段与已有预约有重叠
    });
    errors  = hasConflict ? t('InstrumentBookingCreateDialog.errorAlready'): errors
    // 判断选中的时间是否小于当前时间 仅在 编辑情况
    errors = (newStartDate < (new Date())) && type.value === 0? t('InstrumentBookingCreateDialog.error'): errors
    errors = (newEndDate < (new Date())) && type.value === 1? t('InstrumentBookingCreateDialog.error'): errors
    // 判断是否大于n 天、 instrumentBookingCreateForm.value.detail.max_advance_day
    if (instrumentBookingCreateForm.value.detail.max_advance_day && errors === '' && ifPass) {
      const startDate = new Date(instrumentBookingCreateForm.value.time[0]);
      const currentDate = new Date();

      // 计算最大可预约日期（当前时间 + max_advance_day 天）
      const maxAdvanceDate = new Date(currentDate);
      maxAdvanceDate.setDate(currentDate.getDate() + Number(instrumentBookingCreateForm.value.detail.max_advance_day));
      // 设置时间为当天的最后一刻

      // 检查预约开始时间是否超过最大可预约日期
      if (startDate > maxAdvanceDate) {
        errors = `${t('InstrumentBookingCreateDialog.errorMax1')}${instrumentBookingCreateForm.value.detail.max_advance_day}${t('InstrumentBookingCreateDialog.errorMax2')}`;
      }
    }
    // 至少提前
    if( instrumentBookingCreateForm.value.detail.min_advance?.value  && errors === '' && ifPass) {
      const currentDate = new Date();
      let startTime = new Date(instrumentBookingCreateForm.value.time[0]);
      const min_advance = instrumentBookingCreateForm.value.detail.min_advance
      let timeToCompare = new Date(currentDate);
      // 根据 min_advance 的单位修改时间
      switch (min_advance?.unit) {
        case 'min':
          timeToCompare.setMinutes(currentDate.getMinutes() + Number(min_advance.value));
          break;
        case 'hour':
          timeToCompare.setHours(currentDate.getHours() + Number(min_advance.value));
          break;
        case 'day':
          timeToCompare.setDate(currentDate.getDate() + Number(min_advance.value));
          break;
        default:
          console.error('Invalid unit');
      }
      errors = startTime < timeToCompare ? `${t('InstrumentBookingCreateDialog.errorMin1')}${min_advance?.value}${t('InstrumentBookingCreateDialog.' + min_advance?.unit)}${t('InstrumentBookingCreateDialog.errorMin2')}`: errors
    }

    // 针对于编辑状态下 不允许结束时间在可允许的借用时间范围之外 。 通过判断仅在起始时间小于当前时间下]
    if(  type.value === 1 && (new Date(bookOld.value.start_time)) < ( new Date())) {
      const flag = checkTimeSlot(new Date(instrumentBookingCreateForm.value.time[0]), instrumentBookingCreateForm.value.time[1], instrumentBookingCreateForm.value.detail?.available_slots)
      errors = !flag ? t('InstrumentBookingCreateDialog.errorAvailable') : errors
    }

    function checkTimeSlot(startTime, endTime, availableSlots) {
      // 格式化时间函数
      function formatDateTime(dateTimeStr) {
        if (dateTimeStr instanceof Date) {
          const date = dateTimeStr;
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        if (typeof dateTimeStr === 'string') {
          if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTimeStr)) {
            return dateTimeStr;
          }
          const date = new Date(dateTimeStr);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        return formatDateTime(new Date());
      }

      // 检查是否是全天时间段
      function isFullDaySlot(slots) {
        return slots.length === 1 &&
            slots[0][0] === "00:00" &&
            slots[0][1] === "23:59";
      }

      // 格式化输入的时间
      const formattedStartTime = formatDateTime(startTime);
      const formattedEndTime = formatDateTime(endTime);

      // 提取日期和时间部分
      const [startDate, startTimeStr] = formattedStartTime.split(' ');
      const [endDate, endTimeStr] = formattedEndTime.split(' ');

      // 如果是全天时间段，直接返回 true
      if (isFullDaySlot(availableSlots)) {
        return true;
      }

      // 检查是否跨天
      if (startDate !== endDate) {
        return false; // 非全天时间段不允许跨天
      }

      // 提取时间部分（去掉秒）
      const startTimeOnly = startTimeStr.substring(0, 5);
      const endTimeOnly = endTimeStr.substring(0, 5);

      // 检查是否在任意一个时间段内
      return availableSlots.some(slot => {
        const [slotStart, slotEnd] = slot;
        return startTimeOnly >= slotStart && endTimeOnly <= slotEnd;
      });
    }

    const max = instrumentBookingCreateForm.value.detail.max_booking_duration
    if(max?.value  && errors === '') {
      // 最多预约 max_booking_duration
      let startTime = new Date(instrumentBookingCreateForm.value.time[0]);
      let endTime = new Date(instrumentBookingCreateForm.value.time[1]);
      // 计算持续时间（单位：毫秒）
      let duration = endTime - startTime;
      // 根据 max_booking_duration 的单位，将持续时间转换为对应的单位
      let durationInUnits;
      switch (max?.unit) {
        case 'min':
          durationInUnits = duration / (1000 * 60);  // 毫秒转分钟
          break;
        case 'hour':
          durationInUnits = duration / (1000 * 60 * 60);  // 毫秒转小时
          break;
        case 'day':
          durationInUnits = duration / (1000 * 60 * 60 * 24);  // 毫秒转天
          break;
        default:
          console.error('Invalid unit');
          durationInUnits = 0;
      }
      errors = durationInUnits > max?.value ? `${t('InstrumentBookingCreateDialog.errorMaxDuration')}${max?.value}${t('InstrumentBookingCreateDialog.' + max?.unit)}`: errors
    }
    const { top, height } = calculateTimePosition(instrumentBookingCreateForm.value.time[0], instrumentBookingCreateForm.value.time[1], calendarTime.value)
    console.log(top, height)
    topDragAreaTop.value = top
    topDragAreaHeight.value = height
    isSelectTime.value = true
    timeErrors.value = errors

  //  去除编辑状态下结束时间小于当前时间的报错
    if((new Date(instrumentBookingCreateForm.value.time[1])) < (new Date()) && type === 1) {
      timeErrors.value = ''
    }
  }
}
// 计算去除禁用时间

// turnLeftDay 切换到前一天
const turnLeftDay = () => {
  calendarTime.value = new Date(calendarTime.value.getTime() - 24 * 60 * 60 * 1000); // 设置为昨天
  getBookingDetail({ id: instrumentBookingCreateForm.value.instrumentId , refreshNow: true}, true)

}

// turnRightDay
const turnRightDay = () => {
  calendarTime.value = new Date(calendarTime.value.getTime() + 24 * 60 * 60 * 1000); // 设置为昨天
  getBookingDetail({ id: instrumentBookingCreateForm.value.instrumentId , refreshNow: true}, true)

}

// turnToday 切换到今天
const turnToday = () => {
  calendarTime.value = new Date()
  getBookingDetail({ id: instrumentBookingCreateForm.value.instrumentId , refreshNow: true}, true)
}

// 控制时间选择器是否可被控制
const isTimePickerAction = computed(() => {
  // 仪器是否被选择
  return !(instrumentBookingCreateForm.value.instrumentName && instrumentBookingCreateForm.value.instrumentName.length > 0)
})



const loading = ref(false)
const relatedExperimentLoading = ref(false)
const warnOption = ref([
  { label: t('InstrumentBookingCreateDialog.warn0'), value: 0 },
  { label: t('InstrumentBookingCreateDialog.warn5m'), value: 1 },
  { label: t('InstrumentBookingCreateDialog.warn15m'), value: 2 },
  { label: t('InstrumentBookingCreateDialog.warn30m'), value: 3 },
  { label: t('InstrumentBookingCreateDialog.warn1h'), value: 4 },
  { label: t('InstrumentBookingCreateDialog.warn2h'), value: 5 },
  { label: t('InstrumentBookingCreateDialog.warn1d'), value: 6 }
])

const instrumentBookingHtml = ref([])





const topDragAreaTop = ref(100)
const topDragAreaHeight = ref(100)
const isSelectTime = ref(false)


const experimentSelectRef = ref(null)

// tag点击事件
const setupClickEvent = () => {
  nextTick(() => {
    const collapseTags = experimentSelectRef.value.tags
    if(collapseTags) {
      const childNodes = Array.from(collapseTags.childNodes[1].children);
      childNodes.forEach((tag) => {
        tag.addEventListener('click', e => {
          window.open('https://idataeln.integle.com/' + '?exp_id=' + e.target.innerHTML, '_blank');
          // window.open(ELN_URL + '?exp_id=' + event.target.innerHTML, '_blank');
        });
      });
    }
  });
};
const instrumentRelateExperimentOption = ref([])
const querySearchInstrumentBookingRelateExperiment = debounce(async (page) =>  {
  relatedExperimentLoading.value = true
  if(page && !dev.value) {
    try {
      const url = '/?r=experiment/get-exp-page-by-exp-page'

      const response = await axios.post(url, {
        page
      },{
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      const data = response.data;
      instrumentRelateExperimentOption.value =  data.data.exp
    }
    catch (error) {
    } finally {
      relatedExperimentLoading.value = false
    }
  } else {
    relatedExperimentLoading.value = false
  }
  setupClickEvent()

})




const nowTimeTop = computed(() => {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const totalMinutes = (hours * 60) + minutes;
  const totalDayMinutes = 24 * 60;
  const ratio = totalMinutes / totalDayMinutes;
  return ratio * 1152;
});
const formatTime = (i) => {
  // 将数字 i 转换成 2 位小时格式，补零
  return (i < 10 ? '0' + i : i) + ':00';
}

const submitForm = async (form) => {
  await form.validate((valid,fields) => {
    if(valid && !timeErrors.value) {
      console.log(instrumentBookingCreateForm.value)
      if (Array.isArray(instrumentBookingCreateForm.value.detail.available_slots)) {
        instrumentOtherBookingTime.value = []
        // slots 修改后的结果 isModified 是否发生修改
        const { slots , isModified } = calculateAvailableTimeSlots(instrumentBookingCreateForm.value.detail.available_slots, instrumentBookingCreateForm.value.time)
        // 条件一： 完全在不可预约时间段  条件二： 修改范围涉及到不可预约时间段 （修改一般 isModified = false ） 不可被更改
        if(isModified && JSON.stringify(slots) === '[]' || ( type.value === 1 && isModified )) {
          timeErrors.value = t('InstrumentBookingCreateDialog.errorOver')
          return
        }
        if(isModified && slots) {
          // 为true 则发生修改
          instrumentOtherBookingTime.value = slots
        }
      }
      if(instrumentOtherBookingTime.value.length > 0 ) {
        // 拆分多条情况下
        isOtherBookingTime.value = true
      } else {
        // 单条情况下
        oneCreateAdd()
      }
     }
  })
}

const oneCreateAdd = async () => {
  loading.value = true
  try {
    const url = '/?r=instrument/handle-instrument-booking'
    const response = await axios.post(url, {
      id: type.value === 1? instrumentBookingCreateForm.value.detail?.id : '',
      detail: {
        type: type.value,
        instrumentId: instrumentBookingCreateForm.value.instrumentId,
        instrumentName: instrumentBookingCreateForm.value.instrumentName,
        related_experiment: (instrumentBookingCreateForm.value.relatedExperiment || []).join(','),
        warn: instrumentBookingCreateForm.value.warn,
        remark: instrumentBookingCreateForm.value.remark,
        user: window.USERID,
      },
      timeArr: [
        {
          start_time: instrumentBookingCreateForm.value.time[0],
          end_time: instrumentBookingCreateForm.value.time[1],
        }
      ]
    },{

      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data;
    if(data.status == 1) {
      ElMessage({
        showClose: true,
        message: t(type.value? 'InstrumentBookingCreateDialog.editS': 'InstrumentBookingCreateDialog.createS'),
        type: 'success',
        offset: window.innerHeight / 8
      })
      cancelForm()
    } else {
      ElMessage({
        showClose: true,
        message: data.info,
        type: 'error',
        offset: window.innerHeight / 8
      })
    }
  }
  catch (error) {
  } finally {
    loading.value = false
  }
}

const moreCreateAdd = async () => {
  isOtherBookingTime.value = false
  let instruments = []
  instrumentOtherBookingTime.value.forEach( i => {
    instruments.push({
      start_time: i[0],
      end_time: i[1],
    })
  })
  console.log(2,instrumentOtherBookingTime.value)
  loading.value = true
  try {
    const url = '/?r=instrument/handle-instrument-booking'

    const response = await axios.post(url, {
      id: type.value === 1? instrumentBookingCreateForm.value.detail?.id : '',
      detail: {
        type: type.value,
        instrumentName: instrumentBookingCreateForm.value.instrumentName,
        related_experiment: (instrumentBookingCreateForm.value.relatedExperiment || []).join(','),
        remark: instrumentBookingCreateForm.value.remark,
        user: window.USERID,
        warn: instrumentBookingCreateForm.value.warn,
        instrumentId: instrumentBookingCreateForm.value.instrumentId,
        instrumentName: instrumentBookingCreateForm.value.instrumentName,
      },
      timeArr: instruments,
    },{

      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data;

    if(data.status == 1) {
      cancelForm()
      ElMessage({
        showClose: true,
        message: t(type.value? 'InstrumentBookingCreateDialog.editS': 'InstrumentBookingCreateDialog.createS'),
        type: 'success',
        offset: window.innerHeight / 8
      })
      emit('closeDialog')
    } else {
      ElMessage({
        showClose: true,
        message: data.info,
        type: 'error',
        offset: window.innerHeight / 8
      })
    }
  }
  catch (error) {
  } finally {
    loading.value = false
  }
}
const cancelForm = () => {
  isInstrumentBookingCreateDialog.value = false;
  initDialog();

  // 事件映射表
  const eventMap = {
    'instrumentsBookMine': 'refreshBookMine',
    'bookInstruments': 'closeDialog',
    'instrumentsBookManage': 'refreshBookManage'
  };

  // 获取对应的事件名，如果没有匹配到，默认为 undefined
  const eventToEmit = eventMap[sourceType.value];
  console.log(sourceType.value, eventToEmit)

  if (eventToEmit) {
    emit(eventToEmit);
  }
}



// 拖拽相关


// 拖拽相关状态
const draggingEvent = ref(null);
const dragType = ref(''); // 'move', 'resize-top', 'resize-bottom'
const dragStartY = ref(0);
const dragStartTime = ref({ start: '', end: '' });
const isDragging = ref(false);
const currentTime = ref(new Date());
const currentDate = computed(() => {
  return currentTime.value.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
});


// 拖拽功能
// 拖拽功能 - 重新设计
const startDrag = (event, eventData, type) => {
  // 强制停止任何现有的拖拽
  stopDrag();

  event.preventDefault();
  event.stopPropagation();

  // 获取当前事件的实际时间（从events数组中获取最新值）

  draggingEvent.value = eventData;
  dragType.value = type;
  dragStartY.value = event.clientY;
  isDragging.value = true;

  // 保存当前实际时间作为拖拽起始点
  dragStartTime.value = {
    start: eventData.time[0],
    end: eventData.time[1]
  };


  // 添加全局事件监听
  document.addEventListener('mousemove', handleDrag, { passive: false });
  document.addEventListener('mouseup', stopDrag, { passive: false });
  document.addEventListener('mouseleave', stopDrag, { passive: false });
};

const handleDrag = (event) => {
  // 严格检查拖拽状态
  if (!isDragging.value || !draggingEvent.value || !dragStartTime.value.start) {
    stopDrag();
    return;
  }

  event.preventDefault();

  // 获取拖拽开始时的时间（分钟）
  const originalStartMinutes = timeToMinutes(dragStartTime.value.start);

  // 修复 00:00 - 00:00 拖拽异常的问题， 通过将时间设置为23：59即可
  const end = new Date(dragStartTime.value.end)
  if (end.getHours() === 0 && end.getMinutes() === 0) {
    // 如果是 00:00，减去 1 天，并设置时间为 23:59
    end.setDate(end.getDate() - 1);
    end.setHours(23, 59, 59, 999);
    dragStartTime.value.end = end;
  }
  console.log(end)
  const originalEndMinutes = timeToMinutes(dragStartTime.value.end);
  const duration = originalEndMinutes - originalStartMinutes;

  // 计算鼠标移动的距离对应的时间变化
  const deltaY = event.clientY - dragStartY.value;
  const minutesPerPixel = 1440 / 1152; // 1440分钟 / 1152px = 1.25分钟/像素
  const deltaMinutes = deltaY * minutesPerPixel;

  let newStartMinutes, newEndMinutes;

  // 简化的计算逻辑
  if (dragType.value === 'move') {
    // 整体移动
    newStartMinutes = originalStartMinutes + deltaMinutes;
    newEndMinutes = newStartMinutes + duration;
  } else if (dragType.value === 'resize-top') {
    // 调整开始时间
    newStartMinutes = originalStartMinutes + deltaMinutes;
    newEndMinutes = originalEndMinutes;
  } else if (dragType.value === 'resize-bottom') {
    // 调整结束时间
    newStartMinutes = originalStartMinutes;
    newEndMinutes = originalEndMinutes + deltaMinutes;
  }

  console.log(newEndMinutes)

  // 应用边界限制 - 使用1439分钟（23:59）作为最大值
  const MAX_MINUTES = 1439; // 23:59

  if (dragType.value === 'move') {
    // 整体移动时的边界检查
    if (newStartMinutes < 0) {
      newStartMinutes = 0;
      newEndMinutes = duration;
    } else if (newEndMinutes > MAX_MINUTES) {
      newEndMinutes = MAX_MINUTES;
      newStartMinutes = MAX_MINUTES - duration;
    }
  } else if (dragType.value === 'resize-top') {
    // 顶部调整的边界检查
    newStartMinutes = Math.max(0, Math.min(originalEndMinutes - 15, newStartMinutes));
  } else if (dragType.value === 'resize-bottom') {
    // 底部调整的边界检查
    newEndMinutes = Math.max(originalStartMinutes + 15, Math.min(MAX_MINUTES, newEndMinutes));
  }

  // 更新事件时间
  const finalStartMinutes = Math.round(newStartMinutes);
  const finalEndMinutes = Math.round(newEndMinutes);

  // 简化的有效性检查 - 使用1439作为最大值
  if (finalStartMinutes >= 0 && finalEndMinutes <= 1439 && finalStartMinutes < finalEndMinutes) {
    const newStartTime = minutesToTime(finalStartMinutes);
    const newEndTime = minutesToTime(finalEndMinutes);
    console.log(newEndTime)
    // 实时更新事件时间，支持平滑拖拽 - 使用完整日期时间格式
    instrumentBookingCreateForm.value.time[0] = newStartTime
    instrumentBookingCreateForm.value.time[1] = newEndTime
    renderNowInstrumentBooking()
  }
};
const stopDrag = () => {
  // 移除所有事件监听器（先移除，避免重复调用）
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mouseleave', stopDrag);

  // 完全重置拖拽状态
  isDragging.value = false;
  draggingEvent.value = null;
  dragType.value = '';
  dragStartY.value = 0;
  dragStartTime.value = { start: '', end: '' };
};


// 时间转换辅助函数 - 支持完整日期时间格式
const timeToMinutes = (dateTimeStr) => {
  let date;

  // 判断时间格式并转换为Date对象
  if (typeof dateTimeStr === 'string') {
    if (dateTimeStr.includes('GMT') || dateTimeStr.includes('中国标准时间')) {
      // 处理 "Thu Jun 05 2025 21:45:29 GMT+0800 (中国标准时间)" 格式
      date = new Date(dateTimeStr);
    } else if (dateTimeStr.includes('-') && dateTimeStr.includes(':')) {
      // 处理 "2025-06-05 09:00:00" 或 "2020-04-06 18:45:00" 格式
      date = new Date(dateTimeStr);
    } else {
      // 其他格式尝试直接解析
      date = new Date(dateTimeStr);
    }
  } else if (dateTimeStr instanceof Date) {
    // 如果已经是Date对象
    date = dateTimeStr;
  } else {
    // 兜底处理
    date = new Date(dateTimeStr);
  }

  // 从Date对象中提取小时和分钟
  const hours = date.getHours();
  const minutes = date.getMinutes();
  return hours * 60 + minutes;
};

const minutesToTime = (minutes) => {
  // 处理边界情况：1440分钟应该是23:59:59而不是24:00:00
  if (minutes >= 1440) {
    minutes = 1439; // 23:59
  }
  if (minutes < 0) {
    minutes = 0;
  }
  const today = calendarTime.value;
  const currentDate = today.toISOString().split('T')[0]; // 格式: YYYY-MM-DD
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  const timeStr = `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:00`;
  return `${currentDate} ${timeStr}`;
};

const getEventStyle = (event) => {
  // // 使用新的时间转换函数
  // const startMinutes = timeToMinutes(event.time[0]);
  // const endMinutes = timeToMinutes(event.time[1]);
  // const duration = endMinutes - startMinutes;
  //
  // // 按照1152px总高度计算（24小时 = 1152px，每分钟 = 0.8px）
  // const pixelsPerMinute = 1152 / 1440; // 1152px / 1440分钟 = 0.8px/分钟
  // topDragAreaTop.value = startMinutes * pixelsPerMinute; // 从00:00开始的像素位置
  // topDragAreaHeight.value = duration * pixelsPerMinute; // 持续时间对应的像素高度
  const { top, height } = calculateTimePosition(instrumentBookingCreateForm.value.time[0], instrumentBookingCreateForm.value.time[1], calendarTime.value)
  // console.log(calculateTimePosition(instrumentBookingCreateForm.value.time[0], instrumentBookingCreateForm.value.time[1], calendarTime.value))
  // topDragAreaTop.value = top
  // topDragAreaHeight.value = height
  return {
    top: `${ topDragAreaTop.value }px`,
    height: `${ topDragAreaHeight.value }px`,
    position: 'absolute',
    left: '2px',
    right: '2px'
  };
};

</script>



<style scoped>


.otherBookingTime {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
}
.otherBookingBtn {
  margin-top: 32px;
  display: flex;
  justify-content: flex-end;
  padding-right: 24px;
  padding-bottom: 20px;
}
.otherBookTime .el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 20px !important;
}
.otherBookingTimeLeft {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgb(252, 235, 218);
  border: 8px solid rgb(250, 243, 237);
  display: flex;
  justify-content: center;
  align-items: center;
}
.otherBookingTimeRight {
  width: 288px;
  margin-left: 20px;

}
.otherBookingTimeRight p{
  margin-bottom: 0;
  margin-top: 4px;
}
.otherBookingTimeRight p:not(:first-of-type) {
  font-size: 14px;
  text-align: left;
  font-weight: 400;
}
.instrumentScheduleOut {
  width: 340px;
  height: 584px;
}
.instrumentScheduleOut {
  width: 340px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.errorColor .el-range-input {
  color: rgb(246, 121, 86) !important;
}
.timeBox {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-around;
}
.instrumentScheduleOut-header {
  display: flex;
  align-items: center;
  padding-top: 3px;
  padding-bottom: 9px;
  padding-left: 12px;
}
.instrumentScheduleOut-container {
  width: 342px;
  flex: 1;
  border-top:1px solid rgb(220, 223, 230);
  max-height: 546px;
  overflow: scroll; /* 允许滚动 */
}
.instrumentScheduleAlready {
  position: absolute;
  left: 60px;
  top:20px;
  padding-left: 8px;
  padding-top: 4px;
  color: rgb(156, 156, 166);
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  width: 277px;
  height:100px;
  background: rgba(208, 208, 217, 0.15);
  border: 1px solid rgb(208, 208, 217);
  border-radius: 2px;
}
.instrumentScheduleNowArea {
  position: absolute;
  left: 60px;
  padding-left: 8px;
  padding-top: 4px;
  color: rgb(156, 156, 166);
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  width: 272px;
  z-index: 100;
  cursor: pointer;
}
.safeArea {
  border: 1px solid rgb(115, 102, 255);
  background: rgba(115, 102, 255, 0.15);
  color: rgb(115, 102, 255);
}
.errorArea {
  border: 1px solid rgb(246, 121, 86) !important;
  background: rgba(247, 131, 98, 0.15) !important;
}
.errorAreaCircle {
  border: 1px solid rgb(246, 121, 86) !important;
  background: white !important;
}
.instrumentScheduleNowArea-circle1 {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid rgb(115, 102, 255);
  background: white;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0); /* 使用transform调整位置使其完全居中 */
  top: -6px;
  cursor: ns-resize;
}
.instrumentScheduleNowArea-circle2 {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid rgb(115, 102, 255);
  background: white;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0); /* 使用transform调整位置使其完全居中 */
  bottom: -6px;
  cursor: ns-resize;
}
.instrumentScheduleNowArea-topDragArea {
  height: 20px;
  cursor: ns-resize; /* 鼠标指针显示为上下拖动 */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.instrumentScheduleNowArea-bottomDragArea {
  cursor: ns-resize; /* 鼠标指针显示为上下拖动 */
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 20px;
}
.instrumentScheduleOut-container::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}
.instrumentScheduleIns {
  width: 340px;
  height: 1152px;
  display: flex;
  flex-direction: column;
}
.instrumentScheduleIns-now {
  position: relative;
  left: 57px;
  display: flex;
  align-items: center;
  height: 0;
  z-index: 999;
}
.instrumentScheduleIns-nowCircle {
  width: 7px;
  height: 7px;
  background: rgb(255, 119, 51);
  border-radius: 50%;  /* 使其变为圆形 */
  padding-right: -2px;
}
.instrumentScheduleIns-nowLine {
  height: 2px;  /* 线条的厚度 */
  background-color: rgb(255, 119, 51);
  flex-grow: 1;  /* 让线条延伸占满剩余空间 */
}
.instrumentScheduleIns-item {
  width: 340px;
  height: 48px;
  display: flex;
  flex-direction: row;
}

.instrumentScheduleIns-itemLeft{
  width: 60px;
  height: 48px;
}
.instrumentScheduleIns-itemRight {
  flex: 1;
  height: 48px;
  border-bottom: 1px solid rgb(220, 223, 230);
  border-left:  1px solid rgb(220, 223, 230);
}
.instrumentScheduleIns-item:last-child .instrumentScheduleIns-itemRight {
  border-top: none !important;
}

.instrumentBookingCreateRow {
  display: flex;
  flex-direction: row;
  justify-content: flex-start !important;
  padding: 0 24px 0 !important;
}


#isInstrumentBookingCreateDialogDiv :deep(.el-dialog__body) {
  padding: 0 !important;
}
#instrumentCreateBtn :deep(.el-form-item__content) {
  display: flex !important;
  justify-content: flex-end;
}
#isInstrumentBookingCreateDialogDiv  :deep(.el-dialog__header) {
  padding: 24px 24px 20px !important;
}
#instrumentCreateBtn {
  margin-top: 32px;
  margin-bottom: 20px;
}
#isInstrumentBookingCreateDialogDiv :deep(.el-input__inner:focus) {
  border: none !important;
  box-shadow: none !important;
}
#isInstrumentBookingConfigDialogForm :deep(.el-select__input:focus) {
  border: none !important;
  box-shadow: none !important;
}
#isInstrumentBookingCreateDialogDiv :deep(.el-input__wrapper .el-input__inner) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}
#isInstrumentBookingCreateDialogDiv :deep(.el-dialog) {
  padding: 0 !important;
}
#isInstrumentBookingCreateDialogDiv :deep(.el-autocomplete__popper) {
  position: absolute;
  z-index: 1000000 !important;
}

/* 事件容器 - 覆盖在时间网格上 */
.instrumentBookingNowDragContainer {
  position: absolute;
  top: 0;
  left: 59px;
  right: 0;
  height: 1152px; /* 24小时 * 60分钟 = 1440像素 */
  pointer-events: none; /* 允许点击穿透到背景网格 */
}

.instrumentBookingNowDragContainerItem {
  padding: 4px 8px;
  color: white;
  font-size: 12px;
  cursor: move;
  display: flex;
  flex-direction: column;
  min-height: 20px;
  border: 1px solid rgb(115, 102, 255);
  transition: transform 0.1s, box-shadow 0.1s;
  user-select: none;
  pointer-events: auto; /* 重新启用事件的鼠标事件 */
}


.instrumentBookingNowDragContainerItem.dragging {
  z-index: 1000;
  transition: none; /* 拖拽时禁用过渡动画 */
}
/* 拖拽手柄样式 */
.resize-handle {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  background: rgba(255,255,255,0.9);
  border: 1px solid rgb(115, 102, 255);
  border-radius: 50%;
  cursor: ns-resize;
  opacity: 1; /* 常驻显示 */
  z-index: 10;
}
.resize-handle-top {
  top: -4px; /* 向上偏移，让圆圈在事件边缘 */
}
.resize-handle-bottom {
  bottom: -4px; /* 向下偏移，让圆圈在事件边缘 */
}
.instrumentBookingNowDragContainerItem:hover .resize-handle {
  opacity: 1;
}

/* 事件内容区域调整 */
.event-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2px 0;
  position: relative;
}

.event-title {
  font-weight: 400;
  margin-bottom: 2px;
  color: rgb(115, 102, 255);
  font-size: 12px;
  line-height: 18px;
}

.event-time {
  font-size: 10px;
  opacity: 0.9;
}
</style>

<style>
.otherBookTime .el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 20px !important;
}
.instrumentBookingCreateTime .el-input__inner:focus {
  border: none !important;
  box-shadow: none !important;
}
.instrumentBookingCreateTime .el-input__inner {
  border: none !important;
}
.instrumentBookingCreateTime .el-date-table__row td {
  border: none !important;
}
.instrumentBookingCreateTime .today .el-date-table-cell__text{
  color: rgb(115, 102, 254) !important;
}
.instrumentBookingCreateTime .end-date .el-date-table-cell__text {
  background: rgb(115, 102, 255)!important;
  color: white !important;
}
.instrumentBookingCreateTime .start-date .el-date-table-cell__text{
  background: rgb(115, 102, 255)!important;
  color: white !important;
}
.instrumentBookingCreateTime .in-range .el-date-table-cell{
  background: rgba(115, 102, 255,.1)!important;
}


</style>