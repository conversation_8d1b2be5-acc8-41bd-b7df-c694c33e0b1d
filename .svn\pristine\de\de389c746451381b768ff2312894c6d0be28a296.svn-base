/*eln.png后面的版本号。没有用less处理。而是用自已写的gulp插件来实现。*/

$version=666666;
html, 1200px;

}

html.fullscreen {
    overflow: hidden;
}

.nav-bar-height47 {
    height: 47px;
}

input[type=password], input[type=text], input[type=number] {
    /*width: 130px; 有地方父元素设置宽度 100% 填充, 这里不建议设置默认宽度, 会覆盖 */
    border-radius: 3px;
    height: 28px;
}

/*.modal-content input[type=text]{*/
/*    height:25px;*/
/*}*/

/* add by wy 2023/3/31 引入自定义字体 */
@font-face {
    font-family: 'integle';
    src: url('/fonts/integle.eot'); /* IE9 Compat Modes */
    src: url('/fonts/integle.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */ url('/fonts/integle.woff') format('woff'), /* Modern Browsers */ url('/fonts/integle.ttf') format('truetype'), /* Safari, Android, iOS */ url('/fonts/integle.svg#integle') format('svg'); /* Legacy iOS */
}

/*新增的标签弹出文本框的样式*/
.invisible_div {
    width: auto;
    height: auto;
    position: fixed;
    z-index: -100;
    display: none;
}

.visible_div {
    margin-top: 8px;
    visibility: visible;

    background: #fff;
    left: -22px;
    top: 29px;
    color: #666;
    padding: 8px;
    text-align: left;
    border-radius: 7px;
    box-shadow: 0 0 5px #ccc;
    /*margin-top: 10px;*/
}

/*新增的标签弹出文本框的样式结束*/


input[readonly]::-webkit-input-placeholder {
    /*color: #000;*/
    color: #757575 !important;
    font-size: 13px;
}

.list_title td {
    position: relative;
}

.eln-container {
    padding: 0px 0px 0px 0px;
    margin-top: 60px;
    overflow: hidden;
}

.shadow {
    box-shadow: 0px 0px 4px #afafaf;
    -moz-box-shadow: 0px 0px 4px #afafaf;
    -webkit-box-shadow: 0px 0px 4px #afafaf;
    -ms--box-shadow: 0px 0px 4px #afafaf;
}

.user_ico {
    font-family: 'integle' !important;
}

.loginout_ico {
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url("../image/eln.png?v=$version") -578px -30px;
    vertical-align: middle;
    margin-top: -3px;
}

footer {
    color: #333;
    line-height: 36px;
    width: 100%;
    bottom: 0px;
    background: #f8f8f8;
    height: 36px;
    z-index: 1;
    padding: 0px 20px;
    border-top: 1px solid #d0cfcf;
}

.layout_left {
    width: 260px;
    z-index: 1002;
}

.center {
    text-align: center;
}

/*选择layout_left的所有子元素create_exp_btn*/
/*around_create_exp_btn*/
.around_create_exp_btn > .create_exp_btn {
    line-height: 32px;
    background: #1388FF;
    color: #FFFFFF;
    margin: 10px 25px 10px 40px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    position: relative;
    z-index: 101;
}

.around_create_exp_btn > .create_exp_btn:hover {
    background: #0073e8;
}


.left_nav {
    background: #fff;
    white-space: nowrap;
    box-shadow: 0px 0px 10px #ccc;
    -moz-box-shadow: 0px 0px 10px #ccc;
    -webkit-box-shadow: 0px 0px 10px #ccc;
    padding-bottom: 36px;
}

.follow_tip {
    position: absolute;
    z-index: 500001;
    background: #fff;
    border: 1px solid #ccc;
    box-shadow: 0px 0px 10px #a5a5a5;
    -moz-box-shadow: 0px 0px 10px #a5a5a5;
    -webkit-box-shadow: 0px 0px 10px #a5a5a5;
    padding: 6px 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
}

.record_book a {
    padding: 3px;
}

.top_nav {
    height: 60px;
    background: #333544;
    width: 100%;
    top: 0px;
    z-index: 999;
    line-height: 60px;
    padding: 0 20px 0 0;
    min-width: 1200px;
}

.top_nav .fl {
    line-height: 43px;
}

.top_nav .right_opts a {
    padding: 0px 5px;
    color: #fff;
    display: inline-block;
}

.top_nav .right_opts a:hover {
    background: none;
    color: #fff;
}

.layout_left .exp_part {
    border-top: none;
}

.exp_part .part_title {
    line-height: 46px;
    padding-left: 15px;
    min-width: 170px;
    overflow: hidden;
    position: relative;
    border-bottom: 1px solid #f2f2f2;
}

.exp_part .exp_list {
    display: none;
    line-height: 28px;
}

.exp_part .exp_list a,
.exp_part .exp_list .a {
    padding-left: 14px;
}

.exp_part .exp_list .follow {
    padding-left: 0px;
}

.exp_part .exp_list .exp_href {
    padding-left: 25px;
    padding-right: 27px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.exp_href.on,
.mudule_manage_btn.on {
    background: #f2f2f2;
}

.right_opts .exp_href.on {
    background: none;
}

/*.exp_part .open .exp_list {*/
/*    display: block;*/
/*    padding-right: 12px;*/
/*}*/

.exp_part .exp_book_list .open .exp_list {
    display: block;
    padding-right: 12px;
}

.exp_part .box {
    display: none;
    padding: 4px 0px 4px 16px;
}


.navs-wrap .exp_book_list .right_ico {
    position: absolute;
}


.exp_part .exp_book_list .box {
    display: none;
    padding: 0px 0px 4px 0px;
}

.exp_part.open .box {
    display: block;
}

.exp_part .share_list_eln {
    padding: 5px 10px;
}

.exp_part .share_list_eln a,
.exp_part .share_list_eln .a {
    line-height: 29px;
    padding-left: 16px;
}

.book_info {
    width: 24px;
    height: 28px;
    background: url("../image/eln.png?v=$version") -456px -45px;
    margin-right: 4px;
}

.exp_part.open .part_con {
    /*padding: 6px 0px;*/
    padding-top: 6px;
    padding-bottom: 6px;
    border-bottom: 1px solid #f2f2f2;
}

.exp_part .switch_icon {
    width: 28px;
    text-align: center;
}

.part_title .switch_icon,
.part_title .mudule_manage_btn {
    position: absolute;
    top: 4px;
    right: 10px;
}

.part_title .switch_icon {
    width: 100%;
    text-align: right;
    right: 6px;
    padding-right: 10px;
}

.table-condensed {
    border-bottom: none;
}

.part_title:hover {
    background-color: #f5f5f5;
}

.exp_part .switch_icon:after {
    content: '';
    display: inline-block;
    background: url("../image/eln.png?v=$version") -179px -229px;
    width: 17px;
    height: 18px;
    vertical-align: top;
    margin-top: 10px;
}

.open .switch_icon:after {
    background-position: -160px -228px;
}

.exp_list .user_exp_list {
    display: none;
}

.user.open .user_exp_list {
    display: block;
    padding-left: 10px;
}

.user.open .user_exp_list a {
    padding-left: 20px;
}

.exp_part a.block,
.exp_part .a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.exp_part a.block:hover,
.exp_part .book_outer_box:hover,
.exp_part a.block.on,
.exp_part .book_outer_box.on {
    background: #f2f2f2;
}

.exp_href label {
    cursor: pointer;
}

.exp_part a span.exp_href {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    padding-left: 12px;
}

.part_con .share_list_eln span.exp_href {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
}

.exp_href {
    cursor: pointer;
}

.exp_part a .book_name {
    text-indent: 0px;
}

td:first-child {
    /*border-left: none;*/
}

tr:last-child td {
    /*border-bottom: none;*/
}

.sub_template_outer_html tr:last-child td {
    border: 1px solid #ddd;
}

.modul_line .modul_part {
    position: relative;
}

.modul_line .modul_box {
    position: relative;
    background: #fff;
    clear: both;
}

.ui-draggable-dragging {
    z-index: 99999;
}

.modul_box > .border_all {
    border-color: #ccc;
    border-top-color: #ddd;
}

.positionpart.modul_part .modul_box {
    padding: 0px;
}

.positionpart.modul_part {
    opacity: .7;
    cursor: move;
}

.ui-draggable-dragging {
    cursor: move;
}

.drag_box {
    background: #fff;
}

.drag_box .over {
    border: 1px dashed #000;
}

.modul_part.middle {
    width: 69%;
}

.modul_part.small {
    width: 29%;
}

.modul_part.middle {
    float: left;
    clear: none;
}

.modul_part.small {
    float: right;
    clear: none;
}

.modul_line {
    clear: both;
    margin: 0px 2px 30px 2px;
}

.modul_line:after {
    clear: both;
}

.modul_line:before,
.modul_line:after {
    display: table;
    content: " ";
}

/* begin tag css */
.exp_conetnt:not(.active) {
    display: none;
}

.tag_bar {
    position: fixed;
    padding: 22px 10px 0 10px;
    width: calc(100% - 260px);
    top: 60px;
    /*padding-left: 260px;*/
    z-index: 101;
    background: #f5f5f5;
}

.exp_title .tag {
    text-align: center;
    line-height: 32px;
    background: #eaeaea;
    float: left;
    position: relative;
    padding: 0 15px;
    bottom: -1px;
    color: #3c4043;
    height: 32px;
    max-width: 150px;
    min-width: 40px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    flex: 1;
}

.exp_title .tag:hover {
    cursor: pointer;
    background: #e5e5e5;
}

.exp_title .tag.on {
    background: #fff;
    z-index: 11;
}

.exp_title .tag.on:before, .tag.on:after {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    top: 22px;
    pointer-events: none;
    background-color: transparent;
}

.exp_title .tag.on:before {
    background-image: radial-gradient(
            circle farthest-side at 0 0,
            transparent 100%,
            #fff 0
    );
    left: -10px;
}

.exp_title .tag.on:after {
    background-image: radial-gradient(
            circle farthest-side at 100% 0,
            transparent 100%,
            #fff 0
    );
    right: -10px;
}

.exp_title .tag.on:hover {
    background: #fff;
}

.exp_title .tag .name {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    width: calc(100% - 20px);
    text-overflow: ellipsis;
    position: absolute;
    left: 0;
    padding: 0 0 0 20px;
}

.exp_title .tag .tag_close {
    background: url("../image/tag_close.svg") no-repeat center;
    width: 20px;
    height: 32px;
    position: absolute;
    top: 0px;
    right: 3px;
    opacity: 0.5;
}

.exp_title .tag .tag_close:hover {
    background: url("../image/tag_close_hover.svg") no-repeat center;
}

/* end tag css */

.exp_title {
    /*padding-left: 275px;*/
    /*padding: 22px 10px 0 270px;*/
    /*position: fixed;*/
    /*top: 60px;*/
    /*height: 67px;*/
    /*background: #f5f5f5;*/
    border-bottom: 1px solid #f5f5f5;
    /*width: 100%;*/
    /*z-index: 101 !important;*/
    /*// modified by hkk 2019/10/24 91-101*/
    /*left: 260px;*/
    -webkit-transform: translateZ(0);
    display: flex;
    display: -webkit-flex;
}


.exp_title .a {
    position: relative;
    padding: 0 16px 0 8px;
    text-align: center;
}

.exp_title a .close {
    position: absolute;
    right: -4px;
    background: url(/image/eln.png) -57px top;
    top: 4px;
    width: 11px;
    height: 11px;
    z-index: 40;
    opacity: .4;
}

.exp_title a.on .close {
    background: url(/image/eln.png) -44px top;
    opacity: 1;
}


.max .tag_bar {
    /*left: 0;*/
    /*padding-left: 0px;*/
    width: 100%;
    /*padding-right: 0;*/
}

/*.exp_title:after {*/
/*    content: '';*/
/*    width: 25px;*/
/*    height: 100%;*/
/*    position: absolute;*/
/*    left: -25px;*/
/*    top: 1px;*/
/*    background: #f5f5f5;*/
/*}*/

.tool_data_box {
    position: fixed;
    /*padding-left: 260px;*/
    /*padding-right: 260px;*/
    /*left: 0px;*/
    top: 115px;

    width: calc(100% - 260px);
    min-width: 1150px;
    /*width: 100%;*/
    /*changed by xieyuxiang 工具栏右边不留空格*/
    z-index: 100;
    -webkit-transform: translateZ(0);
}

.add_module_body {
    width: 100%;
    background: #fff;
    /*padding-left: 260px;*/
    padding-right: 8px;
    left: 0px;
}

.exp_title a {
    line-height: 32px;
    background: url("../image/eln_title_bg.png") repeat-x;
    float: left;
    position: relative;
    border-top: 1px solid #e0e0e0;
    margin: 0px 17px;
    padding: 0px 8px;
    bottom: -1px;
    color: #999;
    z-index: 10;
    height: 32px;
    width: 143px;
}

.exp_title .a-line {
    display: inline-block;
}

.exp_title a:hover {
    color: #0079d1;
}

.exp_title a:after,
.exp_title a:before {
    content: '';
    display: block;
    position: absolute;
    background: url("../image/eln.png?v=$version");
    width: 22px;
    height: 33px;
    position: absolute;
    top: -1px;
}

.exp_title a:after {
    background-position: -299px -222px;
    right: -20px;
}

.exp_title a:before {
    background-position: -323px -222px;
    left: -22px;
}

.exp_title a.on:before {
    background-position: -251px -222px;
}

.exp_title a.on:after {
    background-position: -275px -222px;
    z-index: 17;
}

.exp_title a:hover {
    cursor: pointer;
}

.exp_title a.on {
    background: #fff;
    color: #1976D2;
    z-index: 100;
}

.tool_nav {
    line-height: 0px;
    /*overflow: hidden;*/
    width: 100%;
    z-index: 91;
    background: #fff;
    /*padding: 8px 0px 7px 30px;*/
    /*changed by xieyuxiang 2022.8.11 修改样式，更紧凑*/
    padding: 3px 0px 3px 20px;
    border-bottom: 1px solid #e5e5e5;
    box-shadow: 0px 0px 3px #ddd;
}

.tool_nav.main,
.tool_nav.list {
    /*border-bottom: 0px;*/
}

.float.tool_nav {
    box-shadow: 0px 0px 3px #aaa;
}

.tool_nav a {
    display: inline-block;
    padding: 0px 5px 0px 5px;
    text-align: center;
    line-height: 40px;
    height: 40px;
    vertical-align: top;
    text-align: center;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    color: #222;
    border: 1px solid transparent;
}

.tool_nav .share_exp_list {
    vertical-align: top;
    display: inline-block;
    margin-top: 5px;
    padding: 0px 16px;
}

.share_exp_list select {
    width: 85px;
    padding: 3px;
    border: 1px solid #eee;
    border-radius: 3px;
    color: #555;
}

.tool_nav .share_exp_list:hover {
    background: none;
}

.tool_nav a.edit {
    background-color: #ffecb3;
}

.tool_nav a.edit:hover {
    background-color: #FDDE80;
    border: 1px solid #FDDE80;
}

.tool_nav a:hover {
    text-decoration: none;
    background: #fbfbfb;
    border: 1px solid #f3f3f3;
}

.tool_nav .ico {
    display: inline-block;
    width: 32px;
    height: 24px;
    background: url("../image/eln.png?v=$version");
    vertical-align: middle;
}

.tool_nav .edit {
    background-position: -43px -53px;
}

.tool_nav .save {
    background-position: -139px -55px;
}

.tool_nav .del {
    background-position: -75px -50px;
}

.tool_nav .savemodule {
    background-position: -236px -54px;
}

.tool_nav .lock {
    background-position: -331px -54px;
}

.tool_nav .history {
    background-position: -363px -54px;
}

.tool_nav .pdf {
    background-position: -267px -54px;
}

.tool_nav .word {
    background-position: -299px -54px;
}

.tool_nav .print {
    background-position: -203px -55px;
}

.tool_nav .copy {
    background-position: -107px -54px;
}

.tool_nav .ico.ai {
    background: url('../image/svg/ai-chat.svg')  no-repeat center;
}

.tool_nav .filtering {
    display: inline-block;
    width: 32px;
    height: 24px;
    /*background: url("../image/eln.png");*/
    vertical-align: middle;
    background: url("../image/filter_icon.png") no-repeat;
}

.tool_nav .log {
    background-position: -171px -55px;
}

.tool_nav .revoke {
    background-position: -394px -54px;
}

.tool_nav .share {
    background-position: -75px -82px;
}

.tool_nav .create_time {
    background-position: -107px -84px;
}

.tool_nav .edit_time {
    background-position: -139px -85px;
}

.tool_nav .order_page {
    background-position: -171px -84px;
}

.tool_nav .module {
    background-position: -305px -84px;
}

.tool_nav .on .create_time {
    /*background-position: -203px -79px;*/
}

.tool_nav .on .edit_time {
    /*background-position: -235px -79px;*/
}

.tool_nav .on .order_page {
    /*background-position: -267px -79px;*/
}

.tool_nav .agree {
    background-position: -442px -78px;
}

.tool_nav .refuse {
    background-position: -474px -78px;
}

.tool_nav .quick_label {
    background-position: -165px -471px
}

.tool_nav .set_route {
    background-position: -298px -471px
}

.tool_nav .create_next {
    background-position: -325px -471px
}

.tool_nav .reaction_router {
    background-position: -360px -471px
}

.tool_nav .new_icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    vertical-align: middle;
}

.tool_nav .turn_page {
    padding: 0;
}

.tool_nav .prev {
    background: url("../image/svg/page-prev.png");
    background-size: 24px 24px;
}

.tool_nav .next {
    background: url("../image/svg/page-next.png");
    background-size: 24px 24px;
}

.tool_nav.history .edit,
.history [data-type="save"],
.history [data-type="savemasodule"],
.history [data-type="revoke"],
.history [data-type="lock"] {
    display: none;
}

.exp_info {
    /*padding: 16px 8px 16px 0px;*/
    /*changed by xieyuxiang 缩小间距*/
    /*min-height: 120px;*/
    /* add by hkk 2019/3/28指定高度防止有项目框时天气栏被隐藏  10/15 改为min-height*/
}

.weather-box {
    /*add by hkk 2019/3/28 天气预审栏样式*/
    float: right;
}

.icon_info_part {
    background: url("../image/weather-grey.svg");
    height: 32px;
    width: 32px;
    cursor: pointer;
    margin-top: 4px;
    /*position: relative;*/
    /*left: 130px;*/
}

.icon_info_part:hover {
    background: url("../image/weather-deepgrey.svg");
}

.icon_info_part.active {
    background: url("../image/weather-blue.svg");
    height: 32px;
    width: 32px;
    cursor: pointer;
    margin-top: 4px;
    /*position: relative;*/
    /*left: 130px;*/
}

/*.icon_info_part.active:hover {*/
/*    background: url("../image/weather-deepblue.svg");*/
/*}*/

.exp_info .info_part {
    /*padding: 4px 0;*/
    /*changed by xieyuxiang 缩小间距*/
    padding: 0px 0px 4px 0px;

    vertical-align: middle;
    line-height: 32px;
}

.exp_info .info_part span.iblock {
    vertical-align: middle;
    line-height: 32px;
    /*bugFix 36615 by lzs*/
    /*max-width: 170px;*/
    text-overflow: ellipsis;
    overflow: hidden;
}

.show_module_list .add_module {
    display: none;
}

.add_module.add .btn_ {
    color: #0ea7eb;
}

.show_module_list .show {
    display: inline-block;
}

.add_module {
    text-align: center;
    width: 120px;
    position: relative;
    padding: 0px 4px;
    cursor: pointer;
    margin: 12px 0px;
    vertical-align: top;
    word-break: break-word;
}

.show_module_list {
    padding: 5px 0px;
}

.show_module_list .open {
    background-color: #f8f8f8;
}

.show_module_list .add_module .icon {
    height: 59px;
}

.add_module .icon {
    display: inline-block;
    width: 70px;
    height: 74px;
    background: #fdfdfd;
    padding-top: 5px;
    position: static;
    margin-bottom: 5px;
    border-radius: 10px;
    border: 1px solid #f1f1f1;
}

.add_module.add .icon {
    border: none;
}

.add_module .icon:after {
    content: '';
    display: block;
    width: 60px;
    height: 60px;
    background: url("../image/eln.png?v=$version") no-repeat;
    margin: 0 auto;
}

.add_module .btn-primary {
    display: block;
    line-height: 22px;
    color: #fff;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    padding: 4px 10px;
    color: #fff;
    background-color: #428bca;
}

.add_module .btn-primary:hover {
    background-color: #3071a9;
}

.add.add_module .icon,
.add.add_module .icon:hover {
    background: #38b4f2;
}

.add_module .disable.btn-primary,
.add_module .disable.btn-primary:hover {
    color: #fff;
    background-color: #ccc;
}

.add_module .gray {
    color: #666;
    cursor: default;
    padding: 0px;
}

#footer .gray-link {
    color: #999 !important;
}

#footer .gray-link:hover {
    color: #1388ff !important;
    text-decoration: none;
}

.add_module .chendraw:after {
    background-position: left -278px;
}

.add.add_module .chendraw:after {
    background-position: left -338px;
}

.add_module .reaction_details:after {
    background-position: -463px -398px;
}

.add.add_module .reaction_details:after {
    background-position: -463px -464px;
}

.add_module .tlc:after {
    background-position: -528px -398px;
}

.add.add_module .tlc:after {
    background-position: -528px -464px;
}

.add_module .protocol:after {
    background-position: -120px -278px;
}

.add.add_module .protocol:after {
    background-position: -120px -338px;
}

.add_module .discuss:after {
    background-position: -180px -278px;
}

.add.add_module .discuss:after {
    background-position: -180px -338px;
}

.add_module .results:after {
    background-position: -240px -276px;
}

.add.add_module .results:after {
    background-position: -240px -336px;
}

.add_module .spreadsheet:after {
    background-position: -360px -278px;
}

.add.add_module .spreadsheet:after {
    background-position: -360px -338px;
}

.add_module .discussion:after {
    background-position: -600px -278px;
}

.add.add_module .discussion:after {
    background-position: -600px -338px;
}

.add_module .file:after {
    background-position: -539px -278px;
}

.add.add_module .file:after {
    background-position: -539px -338px;
}

.add_module .image:after {
    background-position: -480px -275px;
}

.add.add_module .image:after {
    background-position: -480px -334px;
}

.add_module .notes:after {
    background-position: -420px -277px;
}

.add.add_module .notes:after {
    background-position: -420px -335px;
}

.add_module .abstract:after {
    background-position: -60px -276px;
}

.add.add_module .abstract:after {
    background-position: -60px -336px;
}

.add_module .tinymce:after {
    background-position: -297px -278px;
}

.add.add_module .tinymce:after {
    background-position: -297px -338px;
}

.add_module .remark:after {
    background-position: -665px -279px;
}

.add.add_module .remark:after {
    background-position: -665px -339px;
}

.add_module .define:after {
    background-position: -68px -398px;
}

.add.add_module .define:after {
    background-position: -1px -398px;
}

.editor_iframe_content {
    width: 100%;
}

.module_list {
    padding: 0px 18px;
    margin-left: 16px;
    box-shadow: 0px 10px 15px #eee;
    -moz-box-shadow: 0px 10px 15px #eee;
    -webkit-box-shadow: 0px 10px 15px #eee;
    position: relative;
    z-index: 9999;
}

.add_module_body .close_module {
    position: absolute;
    right: 16px;
    background: url("/image/eln.png?v=$version") -57px top;
    top: 7px;
    width: 11px;
    height: 11px;
    z-index: 10000;
    cursor: pointer;
}

.add_module_body .close_module:hover {
    background: url("/image/eln.png?v=$version") -44px top;
}

.add_module_body .close_module_body {
    position: absolute;
    right: 26px;
    /*right: 286px;*/
    bottom: 16px;
    z-index: 10000;
}

.add_module .choose {
    position: absolute;
    width: 26px;
    height: 27px;
    background: url("../image/eln.png?v=$version") left -62px;
    right: 5px;
    top: -5px;
    display: none;
    left: inherit;
}

.more .choose {
    background: url("../image/eln.png?v=$version") left -89px;
    color: #fff;
    line-height: 26px;
}

.add .choose,
.choose.on {
    display: block;
}

.module_detail {
    display: none;
}

.modul_content.file_modlue,
.modul_content.comment_modlue {
    padding: 15px 24px;
}

.modul_head {
    background: #f4f4f4;
    line-height: 46px;
    height: 46px;
    background: #f8f8f8;
    font-size: 13px;
}

.modul_head .def_title {
    display: inline-block;
    max-width: 600px;
    font-weight: 700;
    color: #555;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 45px;
    min-width: 200px;

}

.modul_head .no-editable-module-title {
    display: inline-block;
    max-width: 600px;
    font-weight: 700;
    color: #555;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.modul_head .title_input {
    height: 33px;
    text-align: center;
    display: none;
    width: 300px;
    margin-top: 4px;
    line-height: 25px;
}

.modul_head .text {
    font-weight: normal;
}

.modul_head .fr {
    line-height: 0px;
    font-weight: normal;
}

.modul_fr_btn {
    width: 40px;
    height: 46px;
    padding: 0px 4px;
    position: relative;
    display: inline-block;
}

.modul_resize {
    display: none;
}

.modul_fr_btn:after {
    content: '';
    display: block;
    margin: 0 auto;
}

.modul_fr_btn:hover {
    background: #E7EDF9;
}

.modul_fr_btn:focus,
.modul_fr_btn:active {
    background: #C4D1E8;
}

.modul_resize:after {
    background: url("../image/eln.png?v=$version") left -34px;
    width: 26px;
    height: 13px;
    margin-top: 10px;
}

.small .modul_resize:after {
    background: url("../image/eln.png?v=$version") -655px top;
}

.modul_opts:after {
    background: url("../image/eln.png?v=$version") -19px -48px;
    width: 12px;
    height: 8px;
    margin-top: 22px;
}

/* 对模块操作的dropdown的top定位,改为通过js计算手动赋值 bug#34337 mod dx */
.modul_opts .opt_box {
    position: absolute;
    display: none;
    right: 0;
    z-index: 10;
    background-color: #fff;
    top: 46px;
    border: 1px solid #ddd;
    text-align: center;
    padding: 5px;
    line-height: 24px;
    font-size: 13px;
    white-space: nowrap;
}

.modul_opts .opt_box:after {
    content: '';
    display: block;
    width: 36px;
    height: 34px;
    position: absolute;
    z-index: 1;
    top: -34px;
    left: 22px;
}

/*
所有实验模块操作弹出框都会用到,先不注释,
修改为通过js控制
@link frontend/web/js/components/exp_modules/mod_common.js
*/
.modul_opts:hover .opt_box {
    /*display: block;*/
}

.modul_opts .opt_box a {
    display: block;
    /* padding-left: 10px; */
}

.modul_opts .opt_box a.hide {
    display: none;
}

.modul_opts .opt_box a:hover {
    background-color: #eee;
}

.file_list {
    padding: 10px 0px;
    margin-top: 5px;
}

.file_list .file_part {
    width: 50%;
    float: left;
    padding: 5px 10px;
}

.file_list .file_part a,
.file_list .file_part span {
    padding: 0px 5px;
    vertical-align: middle;
}

.file_list .file_part span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 85%;
}

.file_part .name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.file_part a,
.file_part span {
    vertical-align: middle;
}

.modul_box .comment_box {
    padding-top: 3px;
}

.comment_box .buttom {
    background: #f7f7f7;
    text-align: right;
    border: 1px solid #dcdcdc;
    border-top: none;
    color: #888;
}

.comment_box .submit_comment {
    background: #0fa7f0;
    height: 32px;
    width: 100px;
    border-left: 1px solid #e4e4e4;
    color: #fff;
}

.comment_box .conut_box {
    padding-right: 15px;
}

.modul_box > .border_all {
    border-radius: 3px;
}

.gray {
    color: #999;
}

.comment_modlue {
    word-wrap: break-word;
    word-break: break-all;
}
.comment_modlue .angle_input .comment_at_block {
    background: #eeeeee;
    border-radius: 3px;
    margin: 0 2px;
    user-select: text;
    -webkit-user-modify: read-only;
}

.comment_modlue .angle_input:empty:before{
    content: attr(placeholder);
}

.comment_modlue .angle_input img{
    vertical-align: baseline;
    max-width: 100%;
    width: auto;
}

.comment_modlue .comment_part {
    padding: 12px 0px;
}


.comment_modlue .comment_part .comment_at_block{
    background: #eeeeee;
    border-radius: 3px;
    margin: 0 2px;
}

.comment_modlue .comment_part .text img{
    vertical-align: baseline;
    max-width: 100%;
    width: auto;
}
.comment_modlue .comment_part:last-child {
    border: none;
}

.comment_part .user_name {
    color: #6cbff4;
}

.comment_part .user_face {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -o-border-radius: 50%;
    -moz-border-radius: 50%;
    overflow: hidden;
}

.comment_part .text_box {
    margin: -2px 0px 0px 50px
}

.comment_modlue .hd {
    padding: 10px 0px;
    font-size: 15px;
}

.comment_part .text {
    padding-top: 5px;
    color: #333;
}

.describe_part textarea {
    width: 500px;
    height: 100px;
    display: inline-block;
}

.describe_part {
    padding: 4px 0px;
}

.modul_box .describe {
    background-color: #F0F7FD;
    padding: 11px 0px;
    margin-bottom: 20px;
}

.name_td .dropdown_container {
    width: 23px;
    float: left;
}

.name_td input.text {
    margin-left: 30px;
    width: auto;
}

.name_td .dropdown-toggle {
    position: absolute;
    display: block;
    left: 6px;
    top: 2px;
    width: 24px;
    height: 24px;
    border-radius: 12px;
    /*background: url("../image/eln.png?v=$version");*/
    color: #fff;
    line-height: 24px;
    text-align: center;
    cursor: pointer;
}

.chendraw_data .title .titleText {
    display: inline-block;
    margin-left: 6px;
}

.name_td .dropdown-toggle.substrates {
    background-color: #1388ff;
}

.name_td .dropdown-toggle.catalysts {
    background-color: #0056ad;
}

.name_td .dropdown-toggle.product {
    background-color: #ff6c00;
}

.name_td .dropdown-toggle.solvent {
    background-color: #1abd4e;
}

.name_td .dropdown-toggle.condition {
    background-color: #a9b7c0;
}

.name_td .dropdown-toggle .num {
    /*// -webkit-transform: scale(0.75);
    // font-size: 10px;
    display: block;
    // line-height: 36px;
    line-height: 24px;
    margin-left: 10px;
    vertical-align: top;
    !* margin-top: 6px; *!
    margin-top: 0.5px;*/
}

.name_td .substrates_pic {
    background-position: -463px top;
/ / background: #4388cc;
/ / border-radius: 50 %;
/ / color: #fff;
/ / line-height: 24 px;
}

.tr_ .dropdown_menu {
    top: auto;
    bottom: 100%;
}

.dropdown_menu .change_type:hover {
    background: #eee;
}

.product .change_li {
    display: none;
}

.name_td li .type {
    top: 0px;
    background: #fff;
    width: 100px;
    right: -100px;
    border: 1px solid #ddd;
    display: none;
}

.name_td li:hover .type {
    display: block;
}

.name_td .solvent_pic {
    background-position: -512px top;
}

.name_td .catalysts_pic {
    background-position: -488px top;
}

.name_td .product_pic {
    background-position: -537px top;
}

.data_table .title {
    font-weight: bold;
    position: relative;
}

.add_ico_btn:after {
    content: '';
    display: inline-block;
    background: url("../image/eln.png?v=$version");
    width: 10px;
    height: 10px;
}
.btn-ico-add:after {
    content: '';
    display: inline-block;
    background: url("../image/eln.png?v=$version");
    width: 10px;
    height: 10px;
}

.create_exp_body .add_ico_btn:after {
    /*deleted by xieyuxiang 2022.9.8 在新建实验中有个按钮要居中*/
    /*margin-bottom: 5px;*/
}

.create_exp_body .border_all {
    margin-top: 10px;
}

.data_table .tr_bg {
    background-color: #f0f7fd;
    font-weight: 700;
}

.data_table .tr_bg .normal {
    font-weight: normal;
}

.data_table .tr_bg td {
    padding: 6px;
    background-color: #f0f7fd;
}

.data_table .tr_bg select {
    /*background: transparent; 删除*/
}

.data_table .tr_bg td {
    border-color: #E5F2FD;
}

.data_table .before_catalysts,
.data_table .before_solvent {
    display: none;
}

.choose_additives .btn-primary {
    margin: 0px 8px;
    width: 100px;
    text-indent: 0px;
}

.add_stoichiometry,
.product_add {
    height: 20px;
    width: 40px;
    display: inline-block;
    position: absolute;
}

.add_stoichiometry:hover .choose_additives {
    display: block;
    background: #fff;
    box-shadow: 0px 0px 5px #666;
    top: 17px;
    left: 24px;
    z-index: 10001;
}

.add_chemical_icon {
    position: absolute;
    width: 10px;
    height: 10px;
    left: 100px;
    top: 50%;
    margin-top: -5px;
    cursor: pointer;
    background: url(../image/eln.png?v=55555555);
}

.add_condition_icon {
    position: absolute;
    width: 10px;
    height: 10px;
    left: 120px;
    top: 50%;
    margin-top: -5px;
    cursor: pointer;
    background: url(../image/svg/arrow-up.svg);
}

.choose_additives .tit {
    line-height: 32px;
    font-weight: normal;
}

.list_title {
    background: #f8f8f8;
    line-height: 50px;
    background-clip: padding-box;
}

.file_modlue .file_name {
    vertical-align: top;
    line-height: 28px;
    height: 28px;
}

.file_modlue .file_name input {
    width: 100%;
}

.exp_detial .exp_share {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../image/exp_share_active.svg");
    vertical-align: middle;
}

.exp_detial .exp_share:hover {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../image/exp_share_active_hover.svg");
    vertical-align: middle;
}

.exp_share.disable {
    background: url("../image/exp_share.svg");
}

.exp_share.disable:hover {
    background: url("../image/exp_share_hover.svg");
}

.excel_module tr td {
    text-align: left;
}

.excel_module {
    position: relative;
}

.record_name_body .record_input {
    width: 270px;
}

.record_name_body {
    line-height: 28px;
}

.exp_info .info_part input,
.exp_info .info_part select {
    width: 170px;
    height: 28px;
    padding: 0 8px;
    line-height: 28px;
}

.exp_info .body_left {
    line-height: 32px;
    width: auto;
    min-width: 80px;
    max-width: 80px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.add_book_btn {
    vertical-align: top;
    display: inline-block;
}

.add_book_btn,
.create_group {
    border: 1px solid #ddd;
    border-radius: 2px;
    display: inline-block;
    width: 130px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    vertical-align: middle;
    position: relative;
    top: -2px;
}

.add_book_btn:hover,
.create_group:hover {
    text-decoration: none !important;
    background-color: #eee;
}

.add_book_btn:before {
    content: '';
    width: 21px;
    height: 21px;
    display: inline-block;
    background: url("../image/eln.png?v=$version") -29px -85px;
    margin-right: 4px;
    vertical-align: middle;
}

.exp_list_table {
    background: #fff;
    /*margin-top: 20px;*/
    /*changed by xieyuxiang 上边距缩小*/

    overflow-y: auto;

}

.list_title td {
    /*border-top: none;*/
}

.exp_list_table tr td {
    border-color: #e4e4e4;
    min-width: 100px;
}

.exp_list_table tr td:first-child {
    min-width: 50px;
}

tr.exp_detial:hover {
    background: #e7f4ff;
}

.exp_conetnt {
    background: #fff;
    /*padding-left: 260px;*/
    /*padding-right: 260px;*/
    margin-top: 104px;
}

.layout_right {
    margin-left: 260px;
}

.max .exp_conetnt {
    /*padding-left: 0;*/
    /*padding-right: 0;*/
}

.max .tool_data_box {
    /*padding-left: 0;*/
    width: calc(100% - 25px);
    /*padding-right: 0;*/
}

.max .exp_title {
    padding-left: 35px;
}

.exp_conetnt.search {
    margin-top: 52px;
}

.choose label {
    display: block;
}

.search_input_text {
    display: none;
}

.editor_ico {
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url("../image/eln.png?v=$version") -465px -222px;
    position: absolute;
    right: 0px;
    margin: 14px 12px 0px 0px;
}

.editor_ico:hover {
    background: url("../image/eln.png?v=$version") -465px -254px;
}

.more_menu_ico {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("../image/ico/more_menu_ico_grey.svg?v=$version");
    position: absolute;
    right: 5px;
    top: 6px;
    /*margin: 14px 12px 0px 0px;*/
}

.part_title .more_menu_ico {
    top: 15px;
    right: 15px;
}

.more_menu_ico:hover {
    background: url("../image/ico/more_menu_ico.svg?v=$version");
}

.part_title .type_ico {
    display: inline-block;
    width: 22px;
    height: 22px;
    vertical-align: middle;
    margin-right: 4px;
    margin-top: -2px;
}

.part_title .book_ico {
    background: url("../image/eln.png?v=$version") -377px -222px;
}

.part_title .group_ico {
    background: url("../image/eln.png?v=$version") -507px -29px;
}

.part_title .share_ico {
    background: url("../image/eln.png?v=$version") -399px -222px;
}

.part_title .reaction_ico {
    background: url("../image/eln.png?v=$version") -421px -222px;
}

.part_title .del_ico {
    background: url("../image/eln.png?v=$version") -443px -222px;
}

.part_title .file_ico {
    background: url("../image/eln.png?v=$version") -378px -254px;
}

.part_title .setting_ico {
    background: url("../image/eln.png?v=$version") -353px -254px;
}

.part_title .help_ico {
    background: url("../image/eln.png?v=$version") -433px -255px;
}

.create_ico {
    display: inline-block;
    width: 22px;
    height: 22px;
    vertical-align: middle;
    margin-right: 8px;
    background: url("../image/eln.png?v=$version") -134px -474px
}

.exp_box_title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    padding: 2px 0px 2px 25px;
    color: #1976D2;
}

.right_ico {
    display: inline-block;
    width: 8px;
    height: 10px;
    left: 10px;
    top: 7px;
    background: url("../image/eln.png?v=$version") -461px -33px;
    position: absolute;
}

.change_li .right_ico {
    right: 9px;
    top: 8px;
    left: auto;
}

.open.box .exp_box_title .right_ico {
    background: url("../image/eln.png?v=$version") -478px -32px;
}


/*.moverLine {
    width: 15px;
    height: 230px;
    position: absolute;
    right: -15px;
    cursor: pointer;
    background: #ddd;
    top: 50%;
    margin-top: -115px;
    overflow: hidden;
    border-radius: 0 65px 65px 0;
    -moz-border-radius: 0 65px 65px 0;
    -webkit-border-radius: 0 65px 65px 0;
    line-height: 230px;
    z-index: 2;
    display: none;
    z-index: 1000;
}*/


/*modified by hkk 2019/10/21 左侧收起展开箭头*/

.moverLine {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 225px;
    cursor: pointer;
    top: 16px;
    overflow: hidden;
    z-index: 1000;
    border-right: 2px solid #1388ff;
    border-top: 2px solid #1388ff;
    -webkit-transform: rotate(-135deg);
    border-left: 2px solid transparent;
    border-bottom: 2px solid transparent;
}

.around_create_exp_btn .moverLine {
    border-right: 2px solid #1388ff00;
    border-top: 2px solid #1388ff00;
}

.around_create_exp_btn:hover .moverLine {
    border-right: 2px solid #1388ff;
    border-top: 2px solid #1388ff;
}

.max .around_create_exp_btn .moverLine {
    border-right: 2px solid #1388ff;
    border-top: 2px solid #1388ff;
}

.layout_left:hover .moverLine,
.max .layout_left .moverLine {
    /*display: block;*/
}

.max .layout_left .moverLine {
    right: -15px;
    -webkit-transform: rotate(45deg);
    /*add by hkk 2019/10/21*/
}


/*add by hkk 2019/10/21*/

.max .add_module_body {
    /*padding-left: 0px;*/
    padding-right: 0;
}

.max .add_module_body .close_module_body {
    right: 26px;
}

/*table.JColResizer th{
    border-right: 1px solid #ddd;
}*/


/*
.moverLine:hover {
    background: #428BCA;
}

.moverLine .arrow {
    width: 4px;
    height: 8px;
    display: inline-block;
    background: url("../image/eln.png?v=$version") -535px -28px;
    margin-left: 6px;
}

.moverLine:hover .arrow {
    background: url("../image/eln.png?v=$version") -535px -36px;
}
*/

.main.max .layout_left {
    width: 0px;
    padding: 5px;
}

.main.max .create_exp_btn,
.main.max .left_nav {
    visibility: hidden;
}

.main.max .layout_right {
    margin-left: 25px;
}

.max .moverLine .arrow {
    background: url("../image/eln.png?v=$version") -539px -28px;
}

.max .moverLine:hover .arrow {
    background: url("../image/eln.png?v=$version") -539px -36px;
}

.group_exp_ico {
    width: 16px;
    height: 16px;
    background: url("../image/eln.png?v=$version") -485px -52px;
    vertical-align: middle;
    margin-left: 5px;
    position: absolute;
    right: 7px;
    top: 3px;
    display: none;
}

.box:hover .group_exp_ico {
    display: inline-block;
}

.select_box .select_part {
    display: none;
    background-color: #f8f8f8;
    border: 2px solid #bebfc2;
    top: 0px;
    left: 0px;
    padding: 5px 0px 20px 0px;
    width: 125%;
    z-index: 9999;
    border-radius: 6px;
}

.select_box .option_list {
    padding: 5px 10px;
    max-width: 400px;
    max-height: 200px;
    overflow: auto;
}

.select_box .create_book {
    padding: 5px 10px;
}

.create_book .create_group:before {
    display: inline-block;
    content: '';
    width: 20px;
    height: 20px;
    background: url("../image/eln.png?v=$version") -691px top;
    vertical-align: middle;
    margin-right: 4px;
}

.create_book .angle_input {
    /*changed by xieyuxiang 2022.9.8 新建记录本中，下拉框的加载存在先是这个210px然后又变成其他宽度，不太好看，出现了其他bug可以跟我商量再修改*/
    width: 200px;
}

.select_box .book_part:hover {
    background: #428BCA;
    color: #fff;
}

.select_box .book_part {
    line-height: 24px;
    padding-left: 8px;
    cursor: default;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 100%;
}

.select_box.angle_input {
    padding: 0px 8px;
    line-height: 28px;
}

.create_exp_body .show_input {
    width: 190px;
    vertical-align: middle;
}

.create_exp_body .angle_input {
    height: 30px;
}

.create_exp_body .angle_input.choose_book_select,
.create_exp_body .angle_input.show_input,
.transfer_module_body .angle_input {
    /*changed by xieyuxiang 2022.9.8 新建记录本中，下拉框的加载存在先是这个210px然后又变成其他宽度，不太好看，出现了其他bug可以跟我商量再修改*/
    width: 200px;
}

.v_hidden {
    visibility: hidden;
}

.create_exp_body .input_part {
    padding: 9px 0;
}

.create_exp_body .body_left {
    line-height: 28px;
}

.create_book .input_part {
    white-space: nowrap
}

.create_book .body_left {
    width: 170px;
}

.save_name_modal .body_left {
    width: 117px;
}

.create_book .fr.iblock {
    padding-right: 62px;
}

.create_book .block {
    text-align: center;
    display: block;
}

.create_exp_modal .blue {
    font-size: 13px;
}

.create_exp_modal .modal-footer {
    line-height: 28px;
}

.create_book .angle_input.iblock {
    /*changed by xieyuxiang 2022.9.8 新建记录本中，下拉框的加载存在先是这个210px然后又变成其他宽度，不太好看，出现了其他bug可以跟我商量再修改*/
    width: 200px;
    white-space: inherit;
}

.create_exp_body .tip_text {
    padding: 12px 0px 0px 143px;
}

.create_book .tip_text {
    padding: 5px 0 0 98px;
}

.show_input .def_val {
    overflow: hidden;
    height: 28px;
}

.info_part.project_progress {
    display: none;
}

.bg_white {
    background: #fff;
}

.detail_title {
    line-height: 40px;
    padding: 0px 30px;
    margin-top: 5px;
}

.detail_list_box .detail_title {
    position: relative;
    z-index: 101;
}

.detail_list_box .btn_box {
    padding: 10px;
    border-left: 1px solid #e6e6e6;
    border-right: 1px solid #e6e6e6;
}

.detail_list_box .btn_box .btn-primary {
    margin: 0px 5px;
}

.share_user_list {
    height: 200px;
    overflow: auto;
    padding: 5px 10px;
}

.share_user_list .user_ico,
.share_user_list .group_ico {
    display: inline-block;
    width: 26px;
    height: 21px;
    background: url("../image/eln.png?v=$version");
    vertical-align: middle;
}

.share_user_list .user_ico {
    background-position: -347px -83px;
}

.share_user_list .group_ico {
    background-position: -383px -83px;
}

.share_user_part {
    padding: 5px;
}

.exp_list_table .load_more_btn,
.exp_list_table .load_more_recycle,
.exp_list_table .load_more_search,
.load_more_recycle {
    background: #f8f8f8;
    cursor: pointer;
}

.load_more_btn td {
    height: 50px;
}

.load_more_btn td,
.load_more_recycle td,
.load_more_search td,
.load_more_recycle td {
    padding: 8px;
}

.exp_list_table .load_more_btn:hover,
.exp_list_table .load_more_recycle:hover,
.exp_list_table .load_more_search:hover,
.load_more_recycle:hover {
    background-color: #DFF0FF;
}

.exp_list_table .load_more_btn:active,
.exp_list_table .load_more_recycle:active,
.exp_list_table .load_more_search:active {
    background-color: #D7ECFF;
}

.review .angle_input {
    width: 400px;
}

.pop_modal .review {
    min-height: 80px;
}

.sign_modal_body input[type="text"],
.sign_modal_body input[type="password"],
.sign_modal_body .signer_names,
.sign_modal_body select.angle_input {
    width: 400px;
}

.copy_search {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

.top_nav .s_open:before {
    content: '';
    width: 20px;
    height: 18px;
    background: url("../image/eln.png?v=$version") -260px -434px;
    /*    -webkit-transition-duration: .3s;
        transition-duration: .3s;
        -moz-transition-duration: .3s;*/
    cursor: pointer;
    margin-top: -2px !important;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
}

.top_nav .s_close {
    /*float: right;*/
    color: #fff;
    padding: 0px 10px;
    cursor: pointer;
    /*margin-top: 6px;*/
}

.top_nav .s_open,
.top_nav .s_close:hover {
    opacity: 1;
    position: relative;
    /*z-index: 10000;*/
    color: #fff;
}

.top_search_box {
    /*visibility: hidden;*/
    /*    transition-duration: 1s;
        -moz-transition-duration: 1s;*/
    position: relative;
    left: 50px;
    opacity: 1;
    position: absolute;
    width: 890px;
    left: 50%;
    margin-left: -445px;
    top: 0px;
    height: 60px;
    line-height: 60px;
    /*padding-top: 10px;*/
}

.top_search_box input {
    background: transparent;
    border: 0;
    color: #000000;
    box-shadow: none;
    width: 88%;
    vertical-align: middle;
}

.top_search_box.active {
    visibility: visible;
/ / background: transparent;
    display: inline-block;
    opacity: 1;
    /*margin-left: -445px;*/
    background: #333544;
    z-index: 1;
}

.top_search_box.active input {
    padding: 3px 8px;
}

.top_search_box input:focus {
    box-shadow: none;
    outline: none;
}

.group_info_body table tr {
    height: 44px;
}

.group_info_body table td {
    border: none;
}

.group_info_body table td:first-child {
    width: 30%;
    text-align: right;
}

.group_info_body table td:last-child {
    text-align: left;
    padding-left: 20px;
}

.jsDraw_pop {
    position: fixed;
    top: 60px;
    z-index: 100;
    line-height: 0px;
    z-index: 10001;
    height: 540px;
    left: 50%;
    margin-left: -445px;
    border: 1px solid #c3c3c3;
}

.search_eln_btn {
    right: 1px;
    display: inline-block;
    top: 68px;
    color: #1976D2;
    background: #f8f8f8;
    width: 62px;
    line-height: 22px;
    text-align: center;
    font-size: 20px;
    padding: 108px 20px;
    border-top-left-radius: 60px;
    border-bottom-left-radius: 60px;
    display: none;
}

.search_eln_btn:hover {
    background: #eee;
}

.search_eln_btn.disabled {
    color: #ccc !important;
}

.jsDraw_pop .btn_box {
    line-height: 1.42857143;
}

.jsDraw_pop iframe {
    margin: 0 auto;
    /*border: 1px solid #c3c3c3;*/
}

.jsDraw_pop .btn_box button {
    margin: 0px 8px;
    border-radius: 3px;
}

.exp_detial .status_ico {
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: middle;
}

.refuse.status_ico.draft {
    background: url("../image/eln.png?v=$version") -608px -55px no-repeat;
}

.status_ico.draft {
    background: url("../image/draft.svg") no-repeat;
}

.status_ico.signed {
    background: url("../image/signed.svg") no-repeat;
}

.status_ico.waiting {
    background: url("../image/waiting.svg") no-repeat;
}

.status_ico.refused {
    background: url("../image/refused.svg") no-repeat;
}

.status_ico.countersigned {
    background: url("../image/countersigned.svg") no-repeat;
}

.status_ico.wait-pretrial {
    background: url(../image/wait_pretrial.svg) no-repeat;
    width: 20px;
    height: 20px;
}

.status_ico.pass-pretrial {
    background: url("../image/pass_pretrial.svg") no-repeat;
}

.status_ico.refuse-pretrial {
    background: url("../image/refuse_pretrial.svg") no-repeat;
}

.route_ico {
    /*position:absolute;*/
    background: url(../image/svg/route_link.svg);
    width: 22px;
    height: 22px;
    /*right: 5px;*/
    /*bottom: 5px;*/
    cursor: pointer;
    vertical-align: middle;
    display: inline-block;
}

.favorites_ico {
    background: url("../image/favorites_ico.svg") no-repeat;
    width: 22px;
    height: 22px;
    cursor: pointer;
    vertical-align: middle;
    display: inline-block;
}

.favorites_ico:hover {
    background: url("../image/favorites_ico_hover.svg") no-repeat;
    width: 22px;
    height: 22px;
    cursor: pointer;
    vertical-align: middle;
    display: inline-block;
}

.reminder_ico {
    background: url("../image/reminder_ico.svg") no-repeat;
    width: 22px;
    height: 22px;
    cursor: pointer;
    vertical-align: middle;
    display: inline-block;
}

.reminder_ico:hover {
    background: url("../image/reminder_ico_hover.svg") no-repeat;
    width: 22px;
    height: 22px;
    cursor: pointer;
    vertical-align: middle;
    display: inline-block;
}

.exp_list_ico {
    /*position: absolute;*/
    /*bottom: 5px;*/
    /*right: 5px;*/
    float: right;
}

.exp_list_ico i:hover {
    /*border: 1px solid #ccc;
    border-radius: 4px;*/
}

.route_ico:hover {
    background: url(../image/svg/route_link_hover.svg);
}

.route_ico.active {
    background: url(../image/svg/route_link_active.svg);
}

.route_ico.active:hover {
    background: url(../image/svg/route_link_active_hover.svg);
}

.single_history .history-desc {
    max-width: 680px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

.open_exp_history {
    cursor: pointer;
}

.static_td:hover .tip {
    display: block;
    width: 300px;
    position: absolute;
    background: #fff;
    box-shadow: 0px 0px 3px #555;
    left: -22px;
    top: 29px;
    color: #666;
    padding: 10px;
    text-align: left;
    border-radius: 2px;
    box-shadow: 0 0 5px #ccc;
    z-index: 10;
}

.static_td .tip:before {
    content: '';
    display: block;
    position: absolute;
    width: 17px;
    height: 10px;
    background: url("../image/eln.png?v=$version") -438px -12px no-repeat;
    top: -10px;
    left: 24px;
}

.static_td:hover .tip_new {
    display: block;
    width: 300px;
    background: #fff;
    box-shadow: 0px 0px 3px #555;
    left: -22px;
    top: 29px;
    color: #666;
    padding: 10px;
    text-align: left;
    border-radius: 2px;
    box-shadow: 0 0 5px #ccc;
    z-index: -1;
    position: fixed;
    margin-top: 10px;
}

.static_td .tip_new:before {
    content: '';
    display: block;
    position: absolute;
    width: 17px;
    height: 10px;
    background: url("../image/eln.png?v=$version") -438px -12px no-repeat;
    top: -10px;
    left: 24px;
}

.ref_file_part .file_name {
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    max-width: 100px;
    display: inline-block;
    vertical-align: middle;
}

.generate_certificate {
    border-radius: 5px;
    font-size: 15px;
    margin-bottom: 10px;
    width: 180px;
    margin: 0 auto;
    -webkit-box-shadow: inset 0 -4px 0 #2a6496;
    box-shadow: inset 0 -4px 0 #2a6496;
    padding: 0px;
    line-height: 36px;
    height: 40px;
    margin-bottom: 10px;
}

.add_literature_part,
.delete_literature_part,
.add_literature_part_next {
    font-size: 18px;
    cursor: pointer;
    color: #000;
}

.readonly .show_module {
    display: none;
}

.editor_iframe {
    display: block;
}

.book_name,
.nowrap {
    white-space: nowrap;
}

.open_comment,
.like_btn {
    display: inline-block;
    width: 26px;
    height: 24px;
}

.open_comment {
    background: url("../image/eln.png?v=$version") -672px -80px;
}

.open_comment:hover {
    background-position: -518px -221px;
}

.like_btn {
    background: url("../image/eln.png?v=$version") -639px -80px;
}

.like_btn .like_count {
    right: -12px;
    top: -6px;
    /*font-size: 10px;
    -webkit-transform: scale(.83,.83);*/
}

.like_btn:hover {
    background-position: -485px -221px;
}

.store_show .store_save,
.store_show .store_close {
    padding: 0px 5px;
    cursor: pointer;
}

.group_get_user {
    padding-right: 20px;
    position: relative;
}

.modal .save_name_modal_body {
    padding: 60px;
}

.save_modal_name {
    width: 330px;
}

.store_show {
    padding: 0px 10px;
}

.store_show p {
    line-height: 22px;
}

.exp_detial {
    cursor: pointer;
}

.exp-abbr-info {
    max-height: 150px;
    line-height: 25px;
    overflow: hidden;
    text-align: left;
}

.exp-abbr-info.center {
    text-align: center;
}

.exp-abbr-info img {
    max-width: 500px;
    max-height: 130px;
}

.exp-abbr-info img.left {
    float: left;
    max-width: 100px;
    max-height: 100px;
    margin-right: 10px;
}

.weather-ico {
    background: url("../image/eln.png?v=$version") -612px -221px;
    width: 27px;
    height: 56px;
    display: inline-block;
    position: absolute;
    left: 0px;
    top: 3px;
}

.temperature_ico {
    background: url("../image/eln.png?v=$version") -651px -221px;
    width: 25px;
    height: 18px;
    display: inline-block;
    vertical-align: top;
}

.weather {
    right: 24px;
    top: 7px;
    color: #9b9b9b;
    padding-left: 34px;
    padding-top: 3px;
    line-height: 18px;
}

.exp_condition [name="tep"] {
    width: 95px;
}

.exp_condition [name="all_time"] {
    width: 95px;
}

.exp_condition [name="pressure"] {
    width: 95px;
}

.open_comment,
.like_btn {
    margin: 0px 4px;
    cursor: pointer;
}

.like_list {
    padding: 4px 0px 4px 10px;
}

.like_ico {
    width: 15px;
    height: 14px;
    background: url("../image/eln.png?v=$version") -656px -40px;
}

.like_part {
    padding: 0px 5px;
}

select.analisis_select {
    height: 220px;
    margin-bottom: 10px;
}

.delete_file {
    cursor: pointer;
}

.info_part input.save_modal_name,
.info_part textarea.save_modal_describe {
    width: 350px;
}

.exp_info .mudole_info_part {
    padding: 8px 0px;
}

.share_exp_body .group_list {
    max-height: 200px;
    overflow-y: auto;
}

.share_exp_body .group_list .input_group {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.exp_content {
    padding-bottom: 20px;
}

.add_analisis {
    cursor: pointer;
}

.add_analisis:hover {
    color: #259;
}

.add_analisis a {
    text-decoration: underline;
}

.my_exp_detial .name {
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100%;
    white-space: nowrap;
    vertical-align: top;
}

.my_exp_list {
    text-overflow: ellipsis;
    max-width: 100%;
    white-space: nowrap;
    vertical-align: middle;
    line-height: 0px !important;
}

.my_exp_list .a-line {
    line-height: 32px;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    text-overflow: ellipsis;
    position: absolute;
    left: 0;
    padding: 0 15px;
}

.blue {
    color: #259;
}

.excel_module {
    overflow: hidden;
}

.jsDraw_pop iframe {
    height: 100%;
}

.jsDraw_pop .btn_box {
    position: absolute;
    bottom: 50px;
    right: 15px;
    z-index: 1000001;
}

.a_lock_click {
    display: none !important;
}

.data_table input[type="text"],
.data_table input[type="number"] {
    border: 1px solid transparent;
    box-shadow: none;
    -webkit-box-shadow: none;
    text-align: center;
}

.data_table .name_td input[type="text"],
.data_table .name_td input[type="number"] {
    text-align: left;
}

.data_table input[property="batch_num"] {
    min-width: 50px;
    text-align: left;
    /*add by hkk 2019/3/21 左对齐和新增的下拉列表对齐一致*/
    width: calc(100% - 25px);
}

.data_table input[name="product_batch_num"] {
    width: 100%;
}

.data_table input:focus {
    border: 1px solid #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}

.data_table input[readonly],
.data_table input[readonly]:focus {
    border: 1px solid transparent !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    background: none !important;
}

.chendraw_data .data_table {
    margin-bottom: 0px;
}

.chendraw_data {
    font-size: 12px;
}

.exp_menu_opts {
    width: 24px;
    height: 24px;
    background: url("../image/eln.png?v=$version") -54px -105px;
    top: 2px;
    right: 0px;
    display: none;
    cursor: pointer;
}

.exp_menu_opts:hover {
    background: url("../image/eln.png?v=$version") -82px -105px;
}

.book_outer_box:hover .exp_menu_opts {
    display: inline-block;
}

.record_book {
    display: none;
    box-shadow: 0px 0px 5px #666;
    padding: 7px 0px;
    position: absolute;
    z-index: 10001;
    background: #fff;
    right: 0px;
    top: 29px;
    /* width: 125px; */
}

.exp_part .record_book a {
    line-height: 20px;
    padding: 0 5px;
    color: #333;
}

.record_book a:hover {
    color: #418bca;
}

.record_book em {
    position: absolute;
    top: -6px;
    z-index: 10002;
    right: 8px;
}

.record_book .black {
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #bbb;
    top: -7px;
}

.record_book .white {
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #fff;
    z-index: 10003;
}

.search_supplier {
    display: inline-block;
    width: 23px;
    height: 23px;
    background: #4388cc;
    border-radius: 50%;
    line-height: 23px;
    text-align: center;
    color: #fff;
    cursor: pointer;
}

.save_define_btn:hover .question_mark {
    display: block;
}

.search_inventory_btn:hover .question_mark {
    display: block;
}

.name1_th:hover .question_mark {
    display: block;
}

.biological_table td,
.literature_table td {
    position: relative;
}

.biological_table td input,
.literature_table td input {
    width: 100%;
}

.biologyData .reference_name {
    /* margin-left: 25px;  modified by hkk 2019/10/12*/
    /*width: 90%;*/
    /*margin-left: 20%;*/
}

.reference_name_td .dropdown {
    position: absolute;
    top: 7px;
}

.modul_part.add {
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    color: #999;
    border-color: #66afe9;
    background: #A6D6FD;
    z-index: 100;
}

.modul_line.hidden_,
.modul_line.hidden_:hover {
    height: 0px;
    border: none;
    box-shadow: none;
    -webkit-box-shadow: none;
    padding: 0px;
}

.right_opts .set_select .dropdown-menu .block {
    color: #333;
    margin: 0px;
    padding: 0px 12px;
    cursor: pointer;
    width: 100%;
}

.right_opts .set_select .dropdown-menu .block:hover {
    background: #eee;
}

.set_select:hover .dropdown-menu {
    line-height: 24px;
    top: 40px;
    border-radius: 3px;
    left: -24px;
    display: block;
    padding: 4px 0px;
}

.top_nav .dropdown-menu:before {
    content: '';
    display: block;
    position: absolute;
    width: 22px;
    height: 13px;
    background: url("../image/eln.png?v=$version") -438px -13px no-repeat;
    top: -10px;
    left: 24px;
}

.right_opts .right_part {
    margin: 0px 18px;
    cursor: pointer;
}

.exp_data_attribute {
    width: 220px;
}

.first_tip {
    background: rgba(0, 0, 0, .5);
    z-index: 10000;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
}

.clouds {
    background: url(../image/tip.png) left top no-repeat;
    width: 676px;
    height: 227px;
    padding: 87px 0px 0px 198px;
    font-size: 14px;
}

.clouds_en {
    background: url(../image/tip_en.png) left top no-repeat;
    width: 676px;
    height: 227px;
    padding: 87px 0px 0px 198px;
    font-size: 14px;
}

.small_detail_title {
    margin-top: -44px;
}

@media only screen and (max-width: 1580px) {
    .exp_data_attribute {
        width: 190px;
    }

    .exp_list_table tr td.book {
        width: 170px;
    }
}

@media screen and (max-width: 1366px) {
    .exp_conetnt,
    .tool_data_box {
    }

    /*.exp_title {*/
    /*    padding-left: 270px;*/
    /*    !*left: 260px;*!*/
    /*}*/
    .top_search_box {
    / / width: 840 px;
    }

    .add_module_body {
        /*padding-left: 240px;*/
    }

    .temperature_ico {
        background: url("../image/eln.png?v=$version") -639px -221px;
        width: 12px;
        height: 12px;
    }

    .file_list .file_part span {
        max-width: 55%;
    }

    .exp-abbr-info img {
        max-width: 490px;
    }

    div.belong_to {
        min-width: 80px;
    }
}

@media only screen and (max-width: 1280px) {
    .exp-abbr-info img {
        max-width: 460px;
    }

    .tool_nav a {
        display: inline-block;
        padding: 0px 13px 0px 8px;
    }

    .name_td input.text {
        width: 100px;
    }
}


/*pad*/

.copy_module_box .body_left {
    width: 138px;
}

.record_name_body .body_left {
    width: 118px;
}

.eln-container > .main {
    min-height: 867px;
}

@media only screen and (max-width: 1024px) {
    marquee {
        display: none;
    }

    body {
        font-size: 12px;
    }

    .eln-container {
        padding: 0px 0px 0px 0px;
    }

    .top_nav {
        top: 0px;
    }

    .main .layout_left {
        height: 100%;
        z-index: 1002;
    }

    .main .layout_right,
    .main.max .layout_right {
        min-width: 750px;
    }

    .pad_hide {
        display: none;
    }

    .exp_data_attribute {
        width: 90px;
    }

    .exp-abbr-info img {
        max-width: 350px;
    }

    .main .create_exp_btn,
    .main .left_nav {
        /*visibility: hidden;*/
    }

    .main .create_exp_btn,
    .main .left_nav {
        /*visibility: hidden;*/
    }

    .main .layout_left {
        /*width: 260px !important;*/
        padding: 0;
    }

    .moverLine .arrow {
        background: url("../image/eln.png?v=$version") -539px -28px;
    }

    .max.main .create_exp_btn,
    .max.main .left_nav {
        /*visibility: visible;*/
    }

    .max.main .create_exp_btn,
    .max.main .left_nav {
        /*visibility: visible;*/
    }

    .max.main .layout_left {
        /*width: 260px !important;*/
        padding: 5px;
    }

    .max.moverLine .arrow {
        background: url("../image/eln.png?v=$version") -535px -28px;
    }

    .chendraw_data {
        overflow-x: auto;
    }

    .file_list .file_part span {
        max-width: 40%;
    }
}

@media only screen and (max-width: 768px) {
    .exp_data_attribute {
        width: 90px;
    }

    .pad_exp_data_con {
    }

    .exp-abbr-info img {
        max-width: 300px;
    }
}

tr.data_part input,
tr.data_part select,
tr.data_part input[readonly],
tr.data_part select[readonly] {
    border: 1px solid transparent;
    box-shadow: none;
    -webkit-box-shadow: none;
    background: none;
}

.invitation_user_select {
    max-width: 325px;
}

.set_permissions {
    right: 109px;
    background: #fff;
    z-index: 100001;
    top: 0px;
    display: none;
}

.block.relative:hover .set_permissions {
    display: block;
}

.right_opts .dropdown-menu {
    min-width: 110px;
    width: 110px;
    font-size: 12px;
}

.question_mark {
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    background: #ffb681;
    cursor: pointer;
}

.wo_sort_default {
    background: url("../image/wo_sort_default.svg") center no-repeat;
    height: 14px;
    width: 14px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.wo_sort_desc {
    background: url("../image/wo_sort_desc.svg") center no-repeat;
    height: 14px;
    width: 14px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.wo_sort_asc {
    background: url("../image/wo_sort_asc.svg") center no-repeat;
    height: 14px;
    width: 14px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.letter_btn:hover,
.letter_btn.on {
    background: #eee;
    color: #25659C;
}

.tool_exp_letter {
    line-height: 26px;
    padding: 0 10px;
    color: #fff;
    border: 1px solid #0AF;
    margin: 3px 7px 0 0;
    color: #0AF;
    border-radius: 3px;
}

.tool_exp_letter:hover {
    background: #E6F7FF;
}

.letter_btn {
    width: 38px;
    line-height: 25px;
    display: inline-block;
    color: #0AF;
    font-weight: bold;
    font-size: 16px;
}

.letter_exp_list {
    padding: 10px 0px;
    line-height: 28px;
}

.letter_exp_list a {
    padding-left: 15px;
    color: #333;
}

.letter_exp_list a:hover {
    color: #0AF;
}

.delete_analisis {
    margin: 0px 5px;
    display: inline-block;
    cursor: pointer;
    position: relative;
}

.delete_analisis .analisis_list,
.delete_analisis .delete_file {
    display: none;
}

.delete_analisis:hover .delete_file {
    display: block;
    position: absolute;
    top: -6px;
    color: red;
    left: -4px;
}

.analisis_ico {
    display: block;
    width: 16px;
    height: 16px;
    background: url("../image/eln.png?v=$version") -683px -182px;
}

.analisis_ico:hover {
    background: url("../image/eln.png?v=$version") -701px -182px;
}

.show_big_img {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .7);
    top: 0px;
    left: 0px;
    z-index: 100092;
    display: flex;
    justify-content: center;
}

.show_big_img .img_box {
    padding: 30px;
    position: relative;
}

.img_box img {
    max-height: 100%;
    max-width: 100%;
    vertical-align: middle;
}

.show_big_img .img_box img {
    position: absolute;
    max-height: 90%;
    max-width: 90%;
    margin: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.show_big_img .btn_box {
    top: 50%;
    width: 100%;
    overflow: hidden;
    height: 71px;
    margin-top: -35px;
    padding: 0px 30px;
}

.show_big_img .img_change_btn {
    display: inline-block;
    width: 36px;
    height: 71px;
    background: url("../image/eln.png?v=$version");
}

.btn_box .prev_btn {
    background-position: 0 -175px;
}

.btn_box .prev_btn:hover {
    background-position: -36px -175px;
}

.btn_box .next_btn {
    background-position: -72px -175px;
}

.btn_box .next_btn:hover {
    background-position: -108px -175px;
}

.close_img_btn {
    top: -5px;
    color: #fff;
    right: 18px;
    font-size: 60px;
    opacity: .7;
    z-index: 1;
}

.close_img_btn:hover {
    opacity: 1;
    color: #991212;
}

.img_part .img_box {
    padding: 20px;
    height: 142px;
    margin: 0 auto;
}

.img_part .img_bottom {
    padding: 3px 0px;
}

.file_list .img_part {
    display: inline-block;
    line-height: 34px;
    margin: 5px;
    width: 300px;
    height: 210px;
    position: relative;
    float: left;
}

.img_part .upload_form {
    margin-top: 83px;
}

.file_list .file_btn_box {
    text-align: center;
    line-height: 130px;
}

.file_list .file_up_drag {
    width: 100%;
    height: 100%;
}

.file_list .img_part .file_name textarea {
    position: absolute;
    height: 28px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0px 10px;
    width: 100%;
    line-height: 20px;
}

.file_list .img_part .file_name textarea:focus {
    height: 60px;
    white-space: normal;
    text-overflow: initial;
    overflow: auto;
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    padding-right: 24px;
}

.file_list .img_part .nowrap:hover {
    padding-right: 30px;
}

.img_part .file_name.editable:after {
    content: '';
    display: inline-block;
    width: 24px;
    height: 28px;
    background: #fff url("../image/eln.png?v=$version") -208px -225px;
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 10;
}

.file_up_box [class*='_ico'] {
    font-size: 13px;
    display: inline-block;
    height: 23px;
    line-height: 21px;
    border: 1px solid #f2f2f2;
    margin-right: 0px;
    border-radius: 3px;
    -moz-border-radius: 3px;
    padding: 0px 1px;
    color: #333;
}

.file_up_box [class*='_ico']:before {
    content: '';
    display: inline-block;
    width: 17px;
    height: 17px;
    background: url("../image/eln.png?v=$version");
    vertical-align: middle;
    margin-top: -3px;
}

.literature_table .file_up_box [class*='_ico'] {
    border: none;
    vertical-align: middle;
}

.literature_table .file_up_box [class*='_ico']:before {
    margin-top: 0px;
}

.file_up_box [class*='_ico']:hover {
    border-color: #9fdcf8;
    text-decoration: none;
    color: #25adf1;
}

.file_up_box .download_ico:before {
    background-position: -113px -129px;
}

.file_up_box .download_ico:hover:before {
    background-position: -113px -147px;
}

.file_up_box .view_ico:before {
    background-position: -79px -128px;
}

.file_up_box .view_ico:hover:before {
    background-position: -79px -146px;
}

.file_up_box .del_ico:before {
    background-position: -96px -129px;
}

.file_up_box .del_ico:hover:before {
    background-position: -96px -147px;
}

.file_up_box .extract_ico:before {
    background-image: url("../image/extraction.svg");
    background-size: 14px 14px;
    display: inline-block;
    width: 14px;
    height: 14px;
    cursor: pointer;
}

.file_up_box .rotate_ico {
    cursor: pointer;
}

.file_up_box .rotate_ico:before {
    background-image: url('../image/rotateImg.png');
    background-position: -20px 3px;
    background-repeat: no-repeat;
    background-size: 36px 52px;
}

.file_up_box .rotate_ico:hover:before {
    background-position: -20px -36px;
}

.file_up_box .see_ico:before {
    background-image: url("../image/online_view.svg");
    background-size: 14px 14px;
    display: inline-block;
    width: 14px;
    height: 14px;
}

.file_up_box .see_ico:hover:before {
    background-image: url("../image/online_view_hover.svg");
}

.file_up_box .local_preview_ico:before {
    background-image: url("../image/local_view.svg");
    background-size: 14px 14px;
    display: inline-block;
    width: 14px;
    height: 14px;
}

.file_up_box .local_preview_ico:hover:before {
    background-image: url("../image/local_view_hover.svg");
    cursor: pointer;
}

.file_up_box .local_edit_ico:before {
    background-image: url("../image/local_edit.svg");
    background-size: 14px 14px;
    display: inline-block;
    width: 14px;
    height: 14px;
}

.file_up_box .local_edit_ico:hover:before {
    background-image: url("../image/local_edit_hover.svg");
    cursor: pointer;
}

.chendraw .btn_box {
    padding: 15px;
}

.chendraw .btn_box input {
    height: 28px;
    padding: 5px 8px;
}

.user_module_list {
    border: 1px solid #e6e6e6;
}

.user_module_list td {
    padding: 8px;
}

.exp_list_table tr:last-child td,
.detail_list_box tr:last-child td {
    border-bottom: 1px solid #e6e6e6;
}

.literature_table .literature_page {
    /*width: 150px;*/
}

.literature_table .yead_sel {
    /* width: 100px;*/
}

.data_part td input,
.data_part td {
    text-align: center;
}

.define_module .data_part th input,
.define_module .data_part th {
    text-align: center;
    color: #222;
    box-sizing: border-box;
    line-height: 18px;
}

.define_module th {
    padding: 4px 6px;
    border-bottom: 1px solid #ddd;
}

input[isinput="1"] {
    color: #00f !important;
}

#yii-debug-toolbar {
    display: none;
}

.dis_create_exp {
    padding-top: 5px !important;
    line-height: 28px;
    text-align: center;
}

.create_exp_body .tip {
    color: #287DEF
}

.modul_box .describe_con {
    padding-left: 10px;
}

.share_exp_body input {
    height: 34px;
    padding: 6px 8px;
}

.literature_table {
    /*bugFix 36394 by lzs */
    /*min-width: 900px;*/
    width: auto;
    min-width: 100%;
}

.biological_table {
    min-width: 700px;
}

.reference_doc_module,
.abstract_module {
    /*overflow-x: auto;*/
}

.title_input:focus {
    background: #fff;
}

.loadmore_btn {
    line-height: 36px;
}

.quick_find_modal_body .choose_quick_btn {
    margin: 3px;
    width: 125px;
    height: 125px;
    text-align: center;
    line-height: 125px;
    vertical-align: top;
}

.quick_find_modal_body .choose_quick_btn:hover {
    border-color: #0ea7eb;
}

.quick_find_modal_body img {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
}

.quick_detial table {
    border-left: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}

.quick_detial .name {
    word-wrap: break-word;
}

.quick_edit_modal_body {
    padding-top: 15px;
}

.quick_detial img {
    max-width: 100%;
    max-height: 160px;
}

.filter_select_box {
    background: #fff;
    z-index: 100;
    width: 860px;
    border: 1px solid #ddd;
    box-shadow: 0px 1px 3px #999;
    border-top: none;
    padding: 3px 0px;
    display: none;
    max-height: 360px;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 50px;
}

.filter_select_box.top_position {
    bottom: 34px;
    top: inherit;
    box-shadow: 0px -3px 3px #ddd;
}

.filter_select_box.bottom_position {
    top: 34px;
    bottom: inherit;
}

.filter_select_box .empty {
    padding: 5px 14px;
}

.filter_select_box .ico {
    display: inline-block;
    width: 18px;
    height: 22px;
    margin: 0px 8px;
}

.filter_select_box .ico {
    background: url("../image/eln.png?v=$version");
}

.filter_select_box .edit_quick {
    background-position: -290px 5px;
}

.filter_select_box .edit_quick:hover {
    background-position: -332px 5px;
}

.filter_select_box .delete_quick {
    background-position: -272px 5px;
}

.filter_select_box .delete_quick:hover {
    background-position: -314px 5px;
}

.filter_select_box li {
    padding: 0px 5px;
    line-height: 22px;
    width: 122px;
    display: inline-block;
    vertical-align: top;
    min-height: 156px;
}

.filter_select_box li.draw {
    display: inline-block;
    border: 1px solid #ddd;
    margin: 10px;
}

.filter_select_box .search_img_box {
    width: 110px;
    height: 110px;
}

.search_img_box img {
    max-width: 100%;
    max-height: 100%;
}

.filter_select_box li:hover {
    background: #f8f8f8;
    cursor: pointer;
}

.draw_click {
    right: 0px;
    bottom: 0px;
    display: none;
}

.exp_data_box {
    position: relative;
    padding: 15px 15px 0;
}

.exp_data_box.no-tool-bar {
    margin-top: -47px;
}

.exp_data_box .no-data {
    text-align: center;
    height: 100px;
    line-height: 100px;
    margin-top: 20px;
    border: 1px solid #e4e4e4;
    border-radius: 3px;
}

.msg {
    z-index: -1;
    position: relative;
}

.msg:after {
    content: '';
    position: absolute;
    display: block;
    width: 8px;
    height: 8px;
    background: red;
    z-index: 10000000;
    top: 0px;
    right: -14px;
    border-radius: 50%;
    animation: my_new_am 1.5s infinite;
    -moz-animation: my_new_am 1.5s infinite;
    -webkit-animation: my_new_am 1.5 infinite;
    -o-animation: my_new_am 1.5s infinite;
}

.user_ico.msg:after {
    top: -2px;
    right: 1px;
}

.user_ico.msg:before {
    content: '\e93b';
    vertical-align: middle;
    margin-top: -3px;
    margin-right: 2px;
    font-size: 21px;
    color: #fff;
}

@keyframes my_new_am {
    0% {
        background: none;
    }
    50% {
        background: red;
    }
    100% {
        background: none;
    }
}

@-moz-keyframes my_new_am {
    0% {
        background: none;
    }
    50% {
        background: red;
    }
    100% {
        background: none;
    }
}

@-webkit-keyframes my_new_am {
    0% {
        background: none;
    }
    50% {
        background: red;
    }
    100% {
        background: none;
    }
}

@-o-keyframes my_new_am {
    0% {
        background: none;
    }
    50% {
        background: red;
    }
    100% {
        background: none;
    }
}

.sub_temp_pop {
    color: #444;
    line-height: 24px;
}

.sub_temp_pop .sub_temp_con {
    margin-left: 82px;
}

.msg_ico {
    display: inline-block;
    background: url("../image/eln.png?v=$version") -603px -33px;
    width: 16px;
    height: 12px;
}

.history_sign.tool_nav {
    display: none;
}

.copy_book_list select {
    max-width: 300px;
    border: none;
}

.copy_book_list1 select {
    max-width: 300px;
    border: none;
}


.sub_temp_modal table {
    width: 100% !important;
    table-layout: auto !important;
}

.sub_temp_modal table td {
    word-wrap: break-word;
    word-break: break-all;
    width: auto !important;
    white-space: normal !important;
}

.sub_temp_modal img {
    max-width: 100%;
}

#yii-debug-toolbar-min {
    display: none;
}

.preview_file {
    cursor: pointer;
}

.js-search-bio {
    position: absolute;
    z-index: 9999;
    border: 1px solid #e5e5e5;
    border-top: none;
    background: #fff;
}

.js-search-bio li {
    line-height: 28px;
}

.js-search-bio li:hover {
    cursor: pointer;
    background: #e5e5e5;
}

.nowrap {
    white-space: nowrap;
}

.w70 {
    width: 70px;
}

.biology-word-table {
    border: 1px solid #ddd;
}

.biology-word-table tr:last-child td {
    border-bottom: 1px solid #ddd;
}

.biology-word-table td:last-child {
    border-right: 1px solid #ddd;
}

.biology-word-table input[type=text] {
    width: 100%;
    min-width: 20px;
}

.biology-word-table span[name] {
    line-height: 1.5em;
}

.biology-word-table tr[data-id]:hover {
    /*background: #e7f4ff;*/
}

.biology-word-table td img {
    max-width: 100%;
}

.biology-btn,
.ref-btn,
.indrawword-btn,
.add-line-btn {
    cursor: pointer;
}

.dictionary_add_one_icon {
    background-image: url("../image/eln.png");
    background-position: -174px -544px;
    background-size: 900px 724px;
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.dictionary_import_icon {
    background-image: url("../image/dictionary_import.svg");
    background-size: 20px 20px;
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.dictionary_import_history_icon {
    background-image: url("../image/dictionary_import_history.svg");
    background-size: 21px 21px;
    display: inline-block;
    width: 21px;
    height: 21px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.dictionary_export_icon {
    background-image: url("../image/wo_icos/wo_export_btn.svg");
    background-size: 21px 21px;
    display: inline-block;
    width: 21px;
    height: 21px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.dictionary_share_icon {
    background-image: url("../image/eln.png");
    background-position: -399px -222px;
    display: inline-block;
    width: 22px;
    height: 22px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.dictionary_show_hide_column_icon {
    background-image: url("../image/hide_show_column.svg");
    background-size: 20px 20px;
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.dictionary_batch_delete_icon {
    background-image: url("../image/eln.png");
    background-position: -443px -222px;
    display: inline-block;
    width: 22px;
    height: 22px;
    vertical-align: bottom;
    margin-right: 10px;
    cursor: pointer;
}

.share_exp_body .input-group {
    margin: 10px 0;
}

.share_exp_body .input-group .fs-wrap input {
    height: unset;
}

.share_exp_body .input-group .fs-wrap {
    width: 260px;
}

.share_exp_body .input-group .fs-dropdown {
    width: 260px;
}

.mb10 {
    margin-bottom: 10px;
}

.mb15 {
    margin-bottom: 15px;
}

.mb20 {
    margin-bottom: 20px;
}

.mr10 {
    margin-right: 10px;
}

.bio-top {
    margin-top: -50px;
    /*line-height: 0;*/
    /*overflow: hidden;*/
    width: 100%;
    z-index: 91;
    background: #fff;
    padding: 8px 0 7px 30px;
    border-bottom: 1px solid #e5e5e5;
    /*box-shadow: 0 0 3px #ddd;*/
    position: relative;
    /*height: 48px;*/
    height: 38px;
    /*    changed by xieyuxiang 高度过高*/
}

.bio-top .right-btns a {
    padding: 0 16px 0 10px;
    line-height: 30px;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    color: #222;
    display: inline-block;
    vertical-align: middle;
}

.bio-top .right-btns a:hover {
    text-decoration: none;
    background: #fbfbfb;
    border: 1px solid #f3f3f3;
}

.bio-top .right-btns i {
    margin: 0 5px;
}

.add_bio_btn {
    font-size: 18px;
    margin-left: 20px;
}

.referenceData .reference_name_td {
    padding-left: 50px;
}

.reference_name_td .dropdown {
    position: absolute;
    top: 50%;
    left: 10px;
    margin-top: -11px;
}

.jsDraw_pop_word {
    width: 880px;
    /*height: 540px;*/
}

.jsDraw_pop_wrap {
    width: 940px;
}

.box.switch_box.nav_second {
    padding-left: 5px;
}

.nav_second .exp_list .exp_href {
    color: #000;
}

.bio-time-td {
    min-width: 100px;
}

.bio-time-td.small {
    width: 100px;
}

.hide-input {
    display: none;
}

.dropdown_menu .disabled a {
    color: #aaa !important;
    background: #e5e5e5 !important;
    cursor: not-allowed;
}

.select_reference_form,
.select_define_form {
    padding-top: 10px;
}

.select_reference_form li,
.select_define_form li {
    line-height: 26px;
}

.select_reference_title,
.select_define_title {
    line-height: 26px;
    padding-left: 20px;
}

.small-do {
    width: 80px;
}

.middle-do {
    width: 180px;
}

.biology-word-table .word-ico {
    font-size: 13px;
    display: inline-block;
    height: 23px;
    width: 23px;
    line-height: 21px;
    border: 1px solid #f2f2f2;
    margin-right: 3px;
    border-radius: 3px;
    -moz-border-radius: 3px;
    /*     padding: 0 7px; */
}

.biology-word-table .word-ico {
    width: 23px;
}

.biology-word-table .word-ico:hover {
    border-color: #9fdcf8;
    text-decoration: none;
    color: #25adf1;
}

.word-ico:after {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 22px;
    width: 22px;
    background: url("../image/eln.png?v=$version");
    -moz-transform: scale(0.9, 0.9);
    -webkit-transform: scale(0.9, 0.9);
    -o-transform: scale(0.9, 0.9);
    transform: scale(0.9, 0.9);
}

.word-ico.word-edit-ico:after {
    background-position: -462px -252px;
}

.word-ico.word-share-ico:after {
    background-position: -400px -223px;
    width: 20px;
}

.word-ico.word-del-ico:after {
    background-position: -443px -222px;
}

.word-ico.word-save-ico:after {
    background-position: -144px -54px;
}


/*table-ico*/
/*此部分是模板列表中的小图标*/
table .table-ico {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin: 2.5px 2.5px 2.5px 2.5px;
    padding: 4px;
    cursor: pointer;

    background-repeat: no-repeat;
    background-size: 20px 20px;
    background-position: center;
}

/*查看编辑*/
.table-ico.look-ico {
    background-image: url("../image/table_ico/look_ico.svg");
}

.table-ico.look-ico:hover {
    background-image: url("../image/table_ico/look_ico_hover.svg");
}

/*分享*/
/*分享按钮调用的是群内权限的两个图标*/
.table-ico.share-ico {
    background-image: url("../image/svg/group_auth_share.svg");
}

.table-ico.share-ico:hover {
    background-image: url("../image/svg/group_auth_share_hover.svg");
}

/*痕迹*/
.table-ico.view-temp-history {
    background-image: url("../image/table_ico/view_temp_history.svg");
}

.table-ico.view-temp-history:hover {
    background-image: url("../image/table_ico/view_temp_history_hover.svg");
}

/*设置企业模板*/
.table-ico.company-ico {
    background-image: url("../image/table_ico/company_ico.svg");
}

.table-ico.company-ico:hover {
    background-image: url("../image/table_ico/company_ico_hover.svg");
}

/*取消企业模板*/
.table-ico.cancel-company-ico {
    background-image: url("../image/table_ico/cancel_company_ico.svg");
}

.table-ico.cancel-company-ico:hover {
    background-image: url("../image/table_ico/cancel_company_ico_hover.svg");
}

/*模板可见*/
.temp_show {
    background-image: url("../image/table_ico/temp_show.svg");
}

.temp_show:hover {
    background-image: url("../image/table_ico/temp_show_hover.svg");
}

/*模板不可见*/
.temp_hide {
    background-image: url("../image/table_ico/temp_hide.svg");
}

.temp_hide:hover {
    background-image: url("../image/table_ico/temp_hide_hover.svg");
}

/*放入回收站*/
.table-ico.file-ico {
    background-image: url("../image/table_ico/file_ico.svg");
}

.table-ico.file-ico:hover {
    background-image: url("../image/table_ico/file_ico_hover.svg");
}

/*转移模板*/
.transfer_ico {
    background-image: url("../image/table_ico/transfer_ico.svg");
}

.transfer_ico:hover {
    background-image: url("../image/table_ico/transfer_ico_hover.svg");
}

/*提交模板审核*/
.table-ico.submit-temp-audit {
    background-image: url("../image/table_ico/submit_temp_audit.svg");
}

.table-ico.submit-temp-audit:hover {
    background-image: url("../image/table_ico/submit_temp_audit_hover.svg");
}

/*取消模板审核*/
.table-ico.cancel-temp-audit {
    background-image: url("../image/table_ico/cancel_temp_audit.svg");
}

.table-ico.cancel-temp-audit:hover {
    background-image: url("../image/table_ico/cancel_temp_audit_hover.svg");
}

/*从回收站复原*/
.table-ico.back-ico {
    background-image: url("../image/table_ico/back_ico.svg");
}

.table-ico.back-ico:hover {
    background-image: url("../image/table_ico/back_ico_hover.svg");
}


/*.temp_show:after, .temp_hide:after {*/
/*    background-size: 20px 20px;*/
/*    height: 22px;*/
/*    width: 20px;*/
/*}*/


.plug_btn {
    border: 1px solid #ddd;
    border-radius: 2px;
    line-height: 30px;
    padding: 0 18px;
    font-size: 14px;
    cursor: pointer;
    transition-duration: .2s;
    -moz-transition-duration: .2s;
    -webkit-transition-duration: .2s;
    -o-transition-duration: .2s;
    min-width: 80px;
    background: 0 0;
}

.plug_btn.plug_btn_default {
    border-color: #1388ff;
    color: #1388ff
}

.plug_btn.plug_btn_default:hover {
    background: rgba(19, 136, 255, .15)
}

.file_tip {
    white-space: normal;
    z-index: 999;
}

.minw100 {
    min-width: 100px;
}

.top-word-select {
    /*float: left;*/
    display: inline-block;
    vertical-align: bottom;
    position: absolute;
    bottom: 0;
}

.top-word-select a {
    color: #333;
    display: inline-block;
    text-decoration: none !important;
    line-height: 30px;
    vertical-align: bottom;
}

.top-word-select a:not(last-child) {
    margin-right: 10px;
}

.top-word-select .on {
    position: relative;
    color: #1388ff;
    border-bottom: 2px solid #1388ff;
}

.top-word-select .on:after {
    content: '';
    position: absolute;
    border-bottom: 1px solid #1388ff;
    bottom: -1px;
    display: inline-block;
    width: 100%;
    left: 0
}


/*打印设置页面20161020-ls*/

.print-setting {
    margin-left: 20px;
    min-width: 1030px;
    margin-top: -30px;
}

@media only screen and (max-width: 1556px) {
    .en .print-setting {
        /*margin-top: -50px;*/
    }
}

.setting-con {
    margin-top: 30px;
}

.setting-left,
.setting-right {
    display: inline-block;
    vertical-align: top;
}

.setting-left {
    width: 396px;
}

.setting-right {
    margin-left: 20px;
}

.setting-title {
    font-size: 18px;
    color: #333;
}

.setting-con {
    color: #666;
}

.setting-name {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 25px;
}

.foot-con {
    border: 1px solid #eeeeee;
    width: 396px;
    padding: 0 40px;
    padding-top: 8px;
    /*transition: height .3s;*/
}

.logo-wrap {
    width: 200px;
    height: 140px;
    border: 1px solid #eeeeee;
    padding: 10px 15px;
    vertical-align: top;
    text-align: center;
    overflow: hidden;
}

.logo-wrap .img-wrap {
    height: 120px;
    width: 100%;
    /*display: table;*/
    text-align: center;
    line-height: 120px;
    overflow: hidden;
}

.logo-wrap img {
    vertical-align: middle;
}

.setting-logo-btn {
    position: absolute;
    width: 170px;
    height: 100%;
    opacity: .7;
    background-color: black;
    top: 130px;
    left: 0;
    transition: top .3s;
    cursor: pointer;
}

.logo-wrap:not(.loading):hover .setting-logo-btn {
    top: 0;
}

.logo-wrap .upload_form {
    top: 100%;
    /*margin-top: -15px;*/
    width: 100%;
    left: 0;
    text-align: center;
    transition: top .3s;
    color: white;
    font-size: 14px;
    line-height: 30px;
    cursor: pointer;
}

.logo-wrap:not(.loading):hover .upload_form {
    top: 50%;
    margin-top: -15px;
}

.logo-txt {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    text-align: center;
    width: 100%;
    color: #fff;
    margin-top: -10px;
    line-height: 20px;
    font-size: 14px;
    cursor: pointer;
}

.logo-items {
    margin-left: 20px;
}

.logo-item.big {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
    line-height: 20px;
}

.logo-item {
    line-height: 25px;
}
.mt5 {
    margin-top: 5px;
}

.mt10 {
    margin-top: 10px;
}

.mt16 {
    margin-top: 16px;
}

.foot-setting {
    margin-top: 30px;
}

.foot-name {
    color: #1976d2;
    margin: 20px 0;
}

.foot-name.big {
    margin: 24px 0;
}

.foot-con {
    font-size: 14px;
    /*padding-top: 8px;*/
}

.foot-con input[type=text] {
    font-size: 13px;
    line-height: 32px;
    height: 32px;
}

.foot-con input[type=text]::-webkit-input-placeholder {
    color: #dcdcdc;
}

.foot-con .form-group {
    margin-bottom: 10px;
}

.form-group.btn-wrap {
    margin-top: 35px;
    margin-bottom: 45px;
    text-align: center;
}

.foot-con label {
    width: 90px;
    text-align: right;
    display: inline-block;
    /*margin-bottom: 10px;*/
}

.foot-setting .ml40 {
    margin-left: 40px;
}

.print-ex {
    width: 606px;
    height: 676px;
    border: 1px solid #eeeeee;
    padding: 20px 30px;
}

.print-setting .blue {
    color: #1976d2;
}

.small-print-wrap {
    width: 70px;
    height: 45px;
    overflow: hidden;
}

.small-print-wrap img {
    max-width: 100%;
}


/*.print-logo-img{
    max-width: 100%;
    /*max-height: 45px;*/


/*
}
*/

.print-con-img {
    width: 100%;
    margin-top: 20px;
}

.setting-color-ul {
    margin-top: 10px;
}

.setting-color-ul li {
    width: 100%;
    border-bottom: 5px solid #fafafa;
    margin-bottom: 17px;
}

.print-con-foot {
    border-top: 1px solid #eee;
    line-height: 50px;
    margin-top: 265px;
}

.print-con-foot .foot-item:not(last-child) {
    margin-right: 20px;
}

.foot-con .errorMsg {
    color: red;
    position: relative;
    left: 100px;
}

.print-tips {
    color: #aaa;
    padding-left: 100px;
    white-space: nowrap;
}

.head-setting img {
    max-width: 100%;
    /*max-height: 100%;*/
}

.print-group-choose {
    line-height: 30px;
}

.group-left {
    /*width: 100px;*/
    /*text-align: right;*/
    color: #666;
}

.print-groups {
    width: 900px;
}

.print-groups,
.group-left {
    display: inline-block;
    vertical-align: top;
}

.print-groups {
    font-size: 0;
}

.print-groups > li {
    display: inline-block;
    width: 100px;
    line-height: 25px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    margin-right: 10px;
}

#cropper {
    white-space: nowrap;
    vertical-align: top;
    margin-top: 10px;
}

#cropper .avatar-preview {
    overflow: hidden;
    height: 52px;
    width: 131px;
    vertical-align: top;
    margin-left: 10px;
}

.cut_container {
    width: 400px;
    height: 400px;
    overflow: hidden;
    vertical-align: top;
}

.cut_logo_modal .modal-content {
    width: 596px;
}

.print-setting .hide {
    display: none;
}

.cut-tip {
    color: #555;
    display: inline-block;
    vertical-align: inherit;
    vertical-align: top;
    line-height: 1.5;
    margin-top: 5px;
}

.print-group-tabs {
    border-bottom: 1px solid #e5e5e5;
}

.print-group-tab {
    position: relative;
    line-height: 30px;
    display: inline-block;
    padding: 0 10px;
    cursor: pointer;
}

.print-group-tab.on {
    border-bottom: 1px solid #1388ff;
    margin-bottom: -1px;
    cursor: default;
}

.tepl-ul {
    position: absolute;
    line-height: 24px;
    display: none;
    right: 0;
    top: 30px;
    z-index: 1;
    min-width: 100%;
    text-align: center;
    background: #fff;
    border: 1px solid #e5e5e5;
    padding: 2px;
}

.tepl-li {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tepl-li.on {
    background-color: #eee;
    cursor: default;
}

.tepl-li:hover {
    background-color: #e5e5e5;
}

.print-tepl-btn:after {
    content: '';
    display: inline-block;
    height: 24px;
    width: 24px;
    background: url("../image/eln.png?v=$version") -490px -255px;
    vertical-align: middle;
    cursor: pointer;
}

.print-tepl-btn {
    font-size: 13px;
    display: inline-block;
    height: 24px;
    width: 24px;
    line-height: 21px;
    border: 1px solid #f2f2f2;
    margin-right: 3px;
    border-radius: 3px;
}

.print-tepl-btn:hover {
    border-color: #9fdcf8;
    text-decoration: none;
    color: #25adf1;
}

.trangle-print {
    display: none;
}

.trangle-print:before {
    content: "";
    position: absolute;
    top: 17px;
    right: 19px;
    width: 0px;
    height: 0px;
    border: 7px solid transparent;
    border-bottom-color: #e5e5e5;
    z-index: 2;
}

.trangle-print:after {
    content: "";
    position: absolute;
    top: 20px;
    right: 20px;
    width: 0px;
    height: 0px;
    border: 6px solid transparent;
    border-bottom-color: #FFF;
    z-index: 2;
}

.tepl-ul .disabled {
    cursor: not-allowed;
}

.navs-con {
    display: none;
}

.navs-con.open {
    display: block;
}

.text-b {
    font-weight: 600;
}

.navs-wrap {
    /*padding-left: 10px;*/
}

.navs-wrap .right_ico {
    position: static;
    margin-right: 6px;
}

.ml5 {
    margin-left: 5px;
}

.ml10 {
    margin-left: 10px;
}

.ml20 {
    margin-left: 20px;
}

.ml30 {
    margin-left: 30px;
}

.ml40 {
    margin-left: 40px;
}

.ml50 {
    margin-left: 50px;
}

.ml60 {
    margin-left: 60px;
}

.pl5 {
    padding-left: 5px
}

.pl8 {
    padding-left: 8px
}

.pl10 {
    padding-left: 10px
}

.pl20 {
    padding-left: 20px
}

.pl30 {
    padding-left: 30px
}

.ml16 {
    margin-left: 16px;
}

.mr16 {
    margin-right: 16px;
}

.navs-wrap .exp_href.pl57 {
    padding-left: 57px;
}

.navs-wrap .exp_href.on,
.navs-wrap .color666 a.on,
.navs-wrap .color666 .navs-switch.on {
    color: #1976D2;
}

.navs-wrap .color666 .navs-switch,
.navs-wrap .color666 a {
    color: #666;
}

.navs-wrap .exp_href.member-blue {
    color: #1976D2
}

.navs-wrap.pl0 {
    padding-left: 0;
}

.group-left-ico {
    display: inline-block;
    border-left: 1px dotted #333;
    width: 13px;
    height: 11px;
    position: relative;
    margin-right: 5px;
    vertical-align: top;
}

a > .group-left-ico {
    height: 14px;
}

.group-left-line {
    width: 100%;
    border-bottom: 1px dotted #333;
    position: absolute;
    bottom: 0;
}

.share_exp_select_wrap {
    margin-top: 5px;
    padding: 0 16px;
    vertical-align: top;
    display: inline-block;
}

.share_exp_select_wrap a {
    vertical-align: top;
    display: inline-block;
}

.share_exp_select_wrap select {
    width: 85px;
    padding: 3px;
    border: 1px solid #eee;
    border-radius: 3px;
    color: #555;
}

/*.navs-switch.open i {*/
/*    background: url("../image/eln.png?v=$version") -478px -32px;*/
/*}*/

.navs-switch.open .right_ico {
    background: url("../image/eln.png?v=$version") -478px -32px;
}

.navs-switch {
    line-height: 25px;
    padding-left: 16px;
    padding-top: 2px;
    padding-bottom: 2px;
}

.navs-switch .set_top_button {
    display: none;
}

.navs-switch:hover .set_top_button {
    display: inline-block;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(-50%, -50%);
}

.navs-switch .set_top_button.is_top{
    height: 16px;
    width: 16px;
    background: url("/image/svg/cancle_top.svg") no-repeat
}

.navs-switch .set_top_button.is_down{
    height: 16px;
    width: 16px;
    background: url("/image/set_top.png") no-repeat;
}

.navs-wrap .changeBlack {
    padding-left: 42px;
}

.del-cols {
    background: url("../image/eln.png?v=$version") -44px top;
    opacity: 1;
    display: inline-block;
    position: absolute;
    right: 2px;
    top: 4px;
    width: 11px;
    height: 11px;
    z-index: 40;
    cursor: pointer;
    display: none;
}

.del-cols:hover {
    background: url("/image/eln.png?v=$version") -44px -11px;
    width: 14px;
    height: 14px;
    top: 2px;
    right: 0px;
}

.del-cols-warning {
    background: url("/image/eln.png?v=$version") -625px 0px;
    width: 30px;
    height: 30px;
}

.del-cols-txt {
    padding-left: 60px;
}

.del-cols-txt p {
    line-height: 30px;
}

.defineData .reference_name_td {
    padding-left: 50px;
}

.add_col {
    cursor: pointer;
}

.defineData .add_col:after {
    content: '';
    background: url("/image/eln.png?v=$version") -140px -412px;
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: middle;
}

.defineData .add_literature_part:after {
    content: '';
    background: url("/image/eln.png?v=$version") -139px -435px;
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: middle;
    margin-top: 1px;
}

.add_bio_btn[type=addView],
.add_literature_part .add_info {
    content: '';
    background: url("/image/eln.png?v=$version") -139px -435px;
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: middle;
}

.l-user-ico {
    display: inline-block;
    vertical-align: middle;
    height: 10px;
    width: 10px;
    background: url("/image/eln.png?v=$version") -166px -414px;
    margin-right: 5px;
}

.allcbx-wrap {
    margin: 15px 25px 0;
}

.defineData .add_literature_part:after {
    content: '';
    background: url("/image/eln.png?v=$version") -139px -435px;
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: middle;
    margin-top: 1px;
}

@media only screen and (max-width: 1556px) {
    .en .exp_conetnt {
        /*margin-top: 130px;*/
    }
}

@media only screen and (max-width: 1366px) {
    .right_opts .right_part {
        margin: 0px 10px;
    }

    .en .exp_conetnt {
        margin-top: 130px;
    }
}

.modul_head .modul-head-tip {
    color: #1976D2;
    line-height: auto;
    position: absolute;
    right: 45px;
}


/* 评论新增功能*/

.call-wrap {
    /*border: 1px solid #e5e5e5;*/
    /*padding: 10px;*/
    /*display: inline-block;*/
    /*position: absolute;*/
    /*top: 0;*/
    /*background: #fff;*/
    /*display: none;*/
    /*z-index: 1;*/
    /*min-width: 400px;*/
}

.search-wrap {
    border: 1px solid #e5e5e5;
    padding: 5px;
    min-height: 100px;
}

.search-ul {
    max-height: 150px;
    overflow: auto;
}

.search-ul li {
    line-height: 26px;
    text-indent: 5px;
}

.search-ul li:hover {
    background: #ccc;
}

.at_user_span {
    color: blue;
    position: relative;
    cursor: pointer;
}

.direct-input-wrap {
    margin-top: 10px;
}

.at_user_span .share {
    position: absolute;
    right: -40px;
    top: -32px;
    border: 1px solid #ccc;
    display: inline-block;
    white-space: nowrap;
    padding: 5px;
    display: none;
    background: #fff;
    cursor: pointer;
}

.at_user_span:hover .share {
    display: inline-block;
}

.at_user_span .share:before {
    content: '';
    width: 18px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    background: url("../image/eln.png?v=$version") -81px -82px;
    margin-right: 5px;
}

.at_user_span[share-disabled] .share:before {
    background: url("../image/eln.png?v=$version") -422px -82px;
    opacity: .6;
}

.at_user_span .share_txt_cancel {
    display: none;
}

.at_user_span[share-disabled] .share_txt_cancel {
    display: inline-block;
}

.at_user_span .share_txt {
    display: inline-block;
}

.at_user_span[share-disabled] .share_txt {
    display: none;
}


/* 评论新增功能 */


/*设置页面的新功能*/

.top-group-tabs {
    border-bottom: 1px solid #e5e5e5;
    line-height: 40px;
}

.top-group-tab {
    position: relative;
    /* line-height: 30px; */
    display: inline-block;
    padding: 0 10px;
    cursor: pointer;
    font-size: 12px;
}

.top-group-tab.on:not(.default){
    border-bottom: 2px solid #1388ff;
    margin-bottom: -2px;
    cursor: default;
    color: #1388ff;
    font-size: 14px;
}

.mt_40 {
    margin-top: -40px;
}

.pd_l_r_15 {
    padding-left: 15px;
    padding-right: 15px;
}

.line-tab-box {
    border-bottom: 1px solid #1388ff
}

.line-tab-box .tab-hd {
    display: inline-block;
    text-align: center;
    padding: 0 10px;
    line-height: 40px;
    margin-left: 4px;
    border: 1px solid #e5e5e5;
    margin-bottom: -1px;
    border-bottom: none !important;
    color: #333;
    cursor: pointer
}

.line-tab-box .tab-hd:first-child {
    margin-left: 10px
}

.line-tab-box .tab-hd.active {
    padding-bottom: 1px;
    border: 1px solid #1388ff;
    background: #FFF;
    color: #1388ff
}

.line-tab-box .tab-hd:hover {
    background: #f9f9f9;
    color: #1388ff
}

.line-tab-box .active:hover {
    background: #FFF;
    color: #1388ff
}

.print-tab-con {
    display: none;
}

.print-tab-con.active {
    display: block;
}

.mt20 {
    margin-top: 20px;
}

.mt40 {
    margin-top: 40px;
}

.logo-item.spec {
    color: #1388ff;
    line-height: 36px;
    font-size: 16px;
}

.print-foot-title {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.foot-con {
    padding: 0;
    border: none;
}

.foot-setting {
    margin-top: 10px;
}

.logo-empty-box {
    width: 300px;
    height: 180px;
    border: 1px solid #e5e5e5;
    display: block;
}

.print-ex {
    height: auto;
    width: auto;
}

.print-con-foot {
    margin-top: 0;
}

.lh32 {
    line-height: 32px;
}

.lh32 .btn {
    height: 32px;
}

.blue-btn {
    background: #1388FF;
    color: #fff;
    border-radius: 3px;
}

a[href].blue-btn:hover {
    background: #1A81E8;
    color: #fff;
    text-decoration: none;
}

.logo-wrap,
.print-ex {
    border: none;
}

#go_to_top_arraw {
    width: 60px;
    height: 60px;
    background: #f3f6f8;
    position: fixed;
    bottom: 20px;
    right: 10px;
    transition: all ease 0.5s;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    z-index: 1010;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=$ version);
}

#go_to_top_arraw:hover {
    background: #1388ff;
}

#go_to_top_arraw:hover i {
    background: url(../image/ico.png) -231px -399px
}

#go_to_top_arraw i {
    width: 32px;
    height: 18px;
    background: url("../image/ico.png") -185px -380px;
}

.img_tool_rotate {
    bottom: 10%;
    width: 400px;
    height: 50px;
    overflow: hidden;
    padding: 13px 38px;
    background-color: #0f0f0f;
    opacity: 0.3;
    border-radius: 6px;
    margin-top: -10px;
}


/*上传图片，图片预览设置被优化了*/
/*.img_tool_rotate .fl span {
    background-position: -4px 0;
}

.img_tool_rotate .fl:hover span {
    background-position: -4px -80px;
}

.img_tool_rotate .fr span {
    background-position: -46px 0;
}

.img_tool_rotate .fr:hover span {
    background-position: -46px -80px;
}*/

.img_tool_rotate span.enlarge, .img_tool_rotate span.shrink .img_tool_rotate span.counterclockwise.img_tool_rotate span.clockwise{
    background-position: unset;
    background-size: contain;

}

.img_tool_rotate span.enlarge {
    background-image: url('../image/enlarge.svg');
}

.img_tool_rotate span.shrink {
    background-image: url('../image/shrink.svg');
}
.img_tool_rotate span.counterclockwise {
    background-image: url('../image/counterclockwise.svg');
}
.img_tool_rotate span.clockwise {
    background-image: url('../image/clockwise.svg');
}

.img_tool_rotate span.enlarge:hover {
    background-image: url('../image/enlarge_hover.svg');
}

.img_tool_rotate span.shrink:hover {
    background-image: url('../image/shrink_hover.svg');
}
.img_tool_rotate span.counterclockwise:hover {
    background-image: url('../image/counterclockwise_hover.svg');
}
.img_tool_rotate span.clockwise:hover {
    background-image: url('../image/clockwise_hover.svg');
}



.img_tool_rotate span {
    width: 24px;
    height: 24px;
    color: #fff;
    line-height: 24px;
    text-align: center;

}
.file_up_box .rotate_ico:before {
    background-image: url('../image/rotateImg.png');
    background-position: -20px 3px;
    background-repeat: no-repeat;
    background-size: 36px 52px;
}


.img_tool_rotate .fl:hover span.enlarge, .img_tool_rotate .fr:hover span.shrink, .img_tool_rotate .fl:hover span.counterclockwise, .img_tool_rotate .fr:hover span.clockwise {
    background-position: unset;
    background-size: contain;
}

a.setting-nav {
    width: 210px;
    color: #222;
}

.setting-nav.on {
    background: #fff;
}

.modal h4 {
    font-size: 18px;
    color: #000;
    font-weight: lighter;
}

.modal h4 span {
    font-size: 16px;
    color: #3499ff;
    margin-left: 12px;
}

.sp-small-input {
    width: 110px;
}

.modul_part .tip-box {
    text-indent: 0;
    line-height: 16px;
    cursor: pointer;
    position: absolute;
    right: 115px;
    top: 18px;
}

.modul_part .tip-box:hover {
    z-index: 90;
}

.modul_part .tip-box .tip-con {
    z-index: 89;
}

.modul_part .modul_fr_btn .shadow {
    z-index: 90;
}

.tip-box:before {
    content: '';
    width: 60px;
    height: 15px;
    position: absolute;
    bottom: -10px;
    left: -15px;
}

i.tip-ico {
    width: 16px;
    height: 16px;
    background: #ccc;
    line-height: 16px;
    font-size: 12px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    margin-left: 5px;
    display: block;
    text-align: center;
}

i.tip-ico:after {
    box-sizing: border-box;
    content: '?';
    color: #fff;
}

.tip-box .tip-con {
    max-width: 230px;
    font-size: 13px;
    position: absolute;
    width: 250px;
    border: 1px solid #ddd;
    box-shadow: 0 0 8px rgba(0, 0, 0, .2);
    left: -15px;
    padding: 8px 12px;
    line-height: 20px;
    top: 29px;
    display: none;
    background: #fff;
    text-align: left;
    word-break: break-all;
}

.tip-con i {
    width: 12px;
    height: 12px;
    display: block;
    background: #fff;
    transform: rotate(45deg);
    position: absolute;
    left: 20px;
    top: -6px;
    box-shadow: -5px -5px 8px rgba(0, 0, 0, 0.13);
}


/* .tip-con:before,
     .tip-con:after {
         content: '';
         position: absolute;
         border-bottom: 6px solid #fff;
         border-right: 6px solid transparent;
         border-left: 6px solid transparent;
         top: -6px;
         z-index: 12;
         left: 21px;
         display: block;
     }
     .tip-con:before {
         border-bottom-color: #ddd;
         z-index: 11;
         top: -7px
     }
*/

.tip-con p {
    padding: 3px 0;
}


/*begin indraw模块的提示问号标记样式add by hkk 2019/2/26*/

#questionSign0 {
    left: 0px;
    position: absolute;
    display: inline-block;
}

#questionSign {
    max-width: 500px;
    width: 500px;
}

#questionSign i {
    left: 25px !important;
}


/*end*/


/*begin 实验天气栏样式add by hkk 2019/2/27*/

.ico-weather {
    background: url(../image/weather.png);
}

.ico-temperature {
    background: url(../image/temperature.png);
}

.ico-humidity {
    background: url(../image/humidity.png);
}

.new-weather-bar {
    display: inline;
    float: right;
    height: 120px;
    margin-left: 50px;
}

.new-weather {
    margin-right: 35px;
    display: inline-block;
    width: 95px;
    height: 108px;
    background: url(../image/weather.png);
    position: relative;
    padding-top: 60px;
    text-align: center;
}

div.new-weather:hover .new-weather-select {
    border: 0.1px solid white;
}

.new-weather-select {
    border: none;
    /*width: 46px;*/
    max-width: 85px;
    height: 30px;
    background-color: transparent;
    font-size: 22px;
    color: white;
    margin-left: 10px;
    font-family: "\9ED1\4F53", "\5FAE\8F6F\96C5\9ED1";
}

.new-weather-select > option {
    color: black;
    font: 14px '\5FAE\8F6F\96C5\9ED1';;
}

.new-temperature {
    margin-right: 30px;
    display: inline-block;
    width: 95px;
    height: 108px;
    background: url(../image/temperature.png);
    position: relative;
    padding-top: 60px;
    text-align: center;
}

div.new-temperature:hover #new-tem-input {
    border: 0.1px solid white;
}

#new-tem-input {
    background-color: transparent;
    border: none;
    color: white;
    box-shadow: none;
    font-size: 22px;
    padding-right: 5px;
    margin-left: 15px;
    width: 45px;
    text-align: center;
}

.new-humidity {
    display: inline-block;
    width: 95px;
    height: 108px;
    background: url(../image/humidity.png);
    position: relative;
    padding-top: 60px;
    text-align: center;
}

div.new-humidity:hover #new-humidity-input {
    border: 0.1px solid white;
}

#new-humidity-input {
    background-color: transparent;
    border: none;
    color: white;
    box-shadow: none;
    font-size: 22px;
    padding-right: 5px;
    margin-left: 15px;
    width: 45px;
    text-align: center;
}


/*end*/


/*begin 实验结论评价样式 add by hkk 2019/2/28*/

.lab-score {
    width: 80px;
    height: 20px;
    display: inline-block;
    line-height: 28px;
}

.lab-score li {
    display: inline-block;
    margin-top: 5px;
    float: left;
    width: 16px;
    height: 15px;
    list-style: none;
    background-image: url(../image/star-hollow.png);
}

.lab-score:hover li {
    background-image: url(../image/star-yellow.png);
}

.lab-score li:hover ~ li {
    background-image: url(../image/star-hollow.png);
}

#score-result {
    display: none;
    width: 50px;
}


/*实验列表栏样式*/

.lab-table-result {
    text-align: left;
}

.lab-table-score {
    width: 80px;
    height: 20px;
    display: inline-block;
    margin-left: -5px;
}

.lab-table-score li {
    display: inline-block;
    margin-top: 7px;
    float: left;
    width: 16px;
    height: 15px;
    list-style: none;
    background-image: url(../image/star-hollow.png);
}


/* add by hkk 2019/3/7 增加物料表手输入颜色，调整radio框的样式*/

.userInputData {
    color: #0000ff
}
/* BUG#1556修复标记物料表用户输入值的css由于优先级不足导致被覆盖的问题 */
.exp_conetnt .userInputData {
    color: #0000ff;
}

.catalysts_equivalent,
.substrates_equivalent {
    display: inline;
    width: calc(100% - 25px);
}

input[property="batch_num"] {
}


/*end*/


/*add by hkk 2019/3/21 更改物料表batchNo 样式*/

.substrates_batch_num,
.catalysts_batch_num,
.product_batch_num,
.solvent_batch_num {
    display: inline;
    width: 130px;
    padding-left: 2px;
    padding-right: 2px;
}

.batchNoSign:hover {
    cursor: pointer;
    background: url(../image/route_link_hover.png);
}

.batchNoSign {
    /*add by hkk 2019/4/8*/
    background: url(../image/route_link.png);
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: middle;
}

.openInscada {
    background: url(../image/svg/openInscada.svg);
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: middle;
}

.openInscada:hover {
    background: url(../image/svg/openInscada_hover.svg);
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: middle;
}

.batchNoList {
    position: absolute;
    width: 150px;
    background: white;
    border: 1px solid #e5e5e5;
    /*add by hkk 2019/3/27*/
    border-radius: 4px;
    /*add by hkk 2019/3/27*/
    z-index: 1;
    /*add by hkk 2019/3/27*/
    text-align: left;
    /*add by hkk 2019/3/27*/
}

.batchNoList li {
    line-height: 30px;
    /*add by hkk 2019/3/27*/
    border-radius: 4px;
    /*add by hkk 2019/3/27*/
    padding-left: 10px;
    /*add by hkk 2019/3/27*/
}

.batchNoList li:hover {
    background-color: #e5e5e5;
    /*add by hkk 2019/3/27*/
    cursor: pointer;
}


/*end */

#improve_file {
    padding: 5px 20px;
    text-align: center;
    background: #fff;
    right: 40px;
    top: 50px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    display: none;
}

#improve_file h6 {
    font-weight: normal;
    font-size: 12px;
}

#improve_file i {
    width: 0;
    height: 0;
    top: -12px;
    left: 40px;
    border-left: 10px solid transparent;
    border-top: 6px solid transparent;
    border-bottom: 10px solid #fff;
    border-right: 10px solid transparent;
}

#improve_file p {
    width: 100%;
    display: flex;
    display: -webkit-flex;
    margin-top: -14px;
    justify-content: space-between;
}

#improve_file p a {
    font-size: 12px;
    width: 40%;
    color: #000;
    cursor: pointer;
    padding: 0;
}

#improve_file p a:first-child {
    color: #1388ff;
}

.eln_setting_href.on .eln_setting_bg {
    position: absolute;
    width: 262px;
    display: inline-block;
    height: 44px;
    background: #666;
    left: -42px;
    opacity: 0.1;
}

.part_title:hover .eln_setting_href .eln_setting_bg {
    background: transparent;
}

.eln_setting_href.on {
    background: transparent;
}

.add_info {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}


/*分页*/

.page-box {
    overflow: hidden;
    line-height: 30px;
    text-align: right;
}

.page-box .first.current,
.page-box .current.last {
    opacity: .7;
}

.page-box a[href],
.page-box span {
    display: inline-block;
    background: #fcfcfc;
    background: -moz-linear-gradient(top, #fff, #fafafa);
    background: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#fafafa));
    background: -o-linear-gradient(top, #fff, #fafafa);
    border: 1px solid #e5e5e5;
    padding: 0 12px;
    color: #666666;
}

.page-box span.page-btn {
    background: #1388ff;
    color: #fff;
    border: 1px solid #1388ff;
}

.page-box a.page-btn,
.page-box span.page-btn,
.page-box a.next {
    border-left: none;
}

.page-box a[href]:hover {
    color: #fff;
    background: #1388ff;
    border-color: #1388ff;
    text-decoration: none;
}

.page-box a[href].prev,
.page-box .prev {
    padding: 0 24px;
}

.page-box .go-to {
    border: none;
    margin-right: -12px;
}

.page-box .first {
    margin-right: 6px;
}

.page-box .last {
    margin-left: 6px;
}

.page-box .show-title {
    margin-left: 10px;
    border: none;
    padding: 0;
    background: none;
}

.page-box input {
    border: 1px solid #e5e5e5;
    padding: 8px 0px;
    width: 110px;
    text-align: center;
    text-indent: 0px;
}

.page-box select {
    outline: 0;
    height: 32px;
    vertical-align: middle;
    border: 1px solid #e5e5e5;
}

.page-box .pagevalue {
    width: 70px;
    text-indent: 0px;
    vertical-align: middle;
    height: 32px;
    padding: 0;
    line-height: 30px;
}

.page-box .current.prev,
.page-box .current.next {
    cursor: not-allowed;
    color: #aaa;
    opacity: .8;
}

.page-box span {
    margin-top: 10px;
    margin-bottom: 10px;
}

.page_box a[href]:hover {
    text-decoration: none;
}

.page_box {
    overflow: hidden;
    line-height: 30px;
    text-align: right;
}

.page_box .first.current,
.page_box .current.last {
    opacity: .7;
}

.page_box a[href],
.page_box span {
    display: inline-block;
    background: #fcfcfc;
    background: -moz-linear-gradient(top, #fff, #fafafa);
    background: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#fafafa));
    background: -o-linear-gradient(top, #fff, #fafafa);
    border: 1px solid #e5e5e5;
    padding: 0 12px;
    color: #666666;
}

.page_box span.page-btn {
    background: #1388ff;
    color: #fff;
    border: 1px solid #1388ff;
}

.page_box a.page-btn,
.page_box span.page-btn,
.page_box a.next {
    border-left: none;
}

.page_box a[href]:hover {
    color: #fff;
    background: #83c1ff;
    border-color: #83c1ff;
}

.page_box a[href].prev,
.page_box .prev {
    padding: 0 15px;
}

.page_box .go-to {
    border: none;
    margin-right: -12px;
    background: none;
}

.page_box .first {
    margin-right: 6px;
}

.page_box .last {
    margin-left: 6px;
}

.page_box .show-title {
    margin-left: 10px;
    border: none;
    padding: 0;
    background: none;
}

.page_box input {
    border: 1px solid #e5e5e5;
    padding: 8px 0px;
    width: 110px;
    text-align: center;
    text-indent: 0px;
}

.page_box select {
    outline: 0;
    height: 29px;
    vertical-align: middle;
}

.page_box .pagevalue {
    width: 50px;
    text-indent: 0px;
    vertical-align: middle;
    line-height: normal;
}

.page_box .current.prev,
.page_box .current.next {
    cursor: not-allowed;
    color: #aaa;
    opacity: .8;
}

.page_box span {
    margin-top: 10px;
    margin-bottom: 10px;
}

.page_box a:hover {
    text-decoration: none;
}

.small-page-box {
    text-align: center !important;
}

.small-page-box .first,
.small-page-box .last,
.small-page-box .show-title,
.small-page-box .pager-select,
.small-page-box .pagevalue,
.small-page-box .go-to {
    display: none !important;
}

.small-page-box a[href],
.small-page-box span {
    padding: 0 6px;
}


/*这是为了不被置顶遮住分页*/

.page_box {
    padding-bottom: 30px;
}

.small-page-box {
    padding-bottom: 0;
}


/*分页*/

.define_module tr:nth-of-type(2) {
    border-top: 2px solid #ddd \9 \0/ IE9;
    /* ie 9*/
}

.search_nav_book {
    width: 125px;
    margin-left: 5px;
    vertical-align: middle;
    line-height: normal;
    border-radius: 13px;
    padding-left: 25px !important;
}

.en .search_nav_book {
    width: 105px;
    border-radius: 13px;
}

.left_nav .search_nav_book_icon {
    position: absolute;
    left: 10px;
    top: -9px;
    /* margin: -8px 138px 0 0;
     width: 23px !important;
    height: 23px !important;*/
    width: 18px !important;
    height: 18px !important;
    /* background: url(../image/ico.png) -335px 0;
    cursor: pointer;*/
    vertical-align: middle;
    /* transform:scale(0.8); */
}

.left_nav .search_nav_book_icon2 {
    position: absolute;
    right: 0;
    top: 0;
    margin: 5px 39px 0 0;
    /* width: 23px !important;
    height: 23px !important;*/
    width: 18px !important;
    height: 18px !important;
    /* background: url(../image/ico.png) -335px 0; */
    cursor: pointer;
    vertical-align: middle;
    /* transform:scale(0.8); */
}

.left_nav .search_nav_project_icon1 {
    position: absolute;
    width: 18px;
    height: 18px;
    margin: 4px 0 0 13px;
    z-index: 1
}

.left_nav .search_nav_project {
    position: absolute;
    width: 125px;
    height: 26px;
    margin-left: 7px;
    vertical-align: middle;
    line-height: normal;
    border-radius: 13px;
    padding: 2px 8px 2px 25px;
}

.left_nav .search_nav_project_icon2 {
    position: absolute;
    width: 18px;
    height: 18px;
    right: 0;
    margin: 4px 5px 0 0;
}

.img_remark {
    position: absolute;
    top: 138px;
    left: 0;
    background: #000;
    width: 100%;
    height: 100%;
    opacity: .4;
    padding: 0 15px;
    color: #fff;
    height: 32px;
    line-height: 32px;
    transition: .1s;
}

.img_remark:hover {
    top: 0;
    height: 170px;
    padding: 15px;
    line-height: 24px;
}

.img_remark .remark {
    height: 100%;
    width: 100%;
    border: none;
    background: transparent;
    color: #fff;
    overflow: hidden;
}

.img_remark .remark:focus {
    outline: 0
}

@media all and (min-width: 0\0
) and (min-resolution: .001dpcm) {
    /*ie9隐藏*/
    .hide-in-ie9 {
        display: none !important;
    }
}

.indraw-line,
.tlc-line,
.ueditor-line,
.xsheet-line {
    position: relative;
    background-color: #F0F0EE;
    border-top: 1px solid #CCCCCC;
    font-size: 0;
    line-height: 0;
    overflow: hidden;
    text-align: center;
    cursor: s-resize;
}

.indraw-line span,
.tlc-line span,
.ueditor-line span,
.xsheet-line span {
    background-position: -0px -754px;
    width: 15px;
    height: 11px;
    /*background-image: 	url('../kindeditor/themes/default/default.png');*/
    display: inline-block;
    vertical-align: middle;
    zoom: 1;
}

span.left-nav-settop {
    height: 16px;
    width: 13px;
    background: url("../image/eln.png?v=$version") -266px -413px;
    position: absolute;
    right: 3px;
    top: 50%;
    margin-top: -8px;
    display: none;
}

/*.share_list_eln.box [data-type="shareMe"] {*/
/*    position: relative;*/
/*}*/

.share_list_eln.box [data-type="shareMe"]:hover .left-nav-settop {
    display: inline-block;
}

.tool_btn.disabled {
    opacity: 0.5;
}

td .upload_form {
    vertical-align: middle;
}


/*美化的checkbox。大号。开始*/

.beauty-checkbox-big {
    display: none;
}

.beauty-checkbox-big + label {
    position: relative;
    display: inline-block;
    padding-left: 25px;
    cursor: pointer;

}

.exp-list-cols .col-item .beauty-checkbox-big + label {
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.beauty-checkbox-big + label:after {
    content: '';
    position: absolute;
    display: inline-block;
    height: 18px;
    width: 18px;
    /*background: url("../image/eln.png?v=$version");*/
    /*background-position: -185px -411px;*/
    background: url("../image/checkbox/checkbox-unchecked-abled.svg");
    left: 0;
    top: 50%;
    margin-top: -9px;
}

.beauty-checkbox-big.indeterminated + label:after {
    background: url("../image/checkbox/checkbox-indeterminated-abled.svg");
}

/*如果之后换用indeterminate属性，则启用此项*/
/*.beauty-checkbox-big：indeterminate+label:after{*/
/*    background: url("../image/checkbox/checkbox-indeterminated-abled.svg");*/
/*}*/
/*disable的checkbox使用自带的disable，不额外使用其他图标*/

.beauty-checkbox-big:checked + label:after {
    background: url("../image/checkbox/checkbox-checked-abled.svg");
}

/*eln_setting.js 2573 调用*/
.beauty-checkbox-big.some + label:after {
    /*background-position: -210px -411px;*/
    /*之前是雪碧图，现在改为单个背景文件*/
    background: url("../image/checkbox/checkbox-indeterminated-abled.svg");
}


/*美化的checkbox。大号。开始*/


/* eln设置　*/

.eln_setting_btn.disabled {
    cursor: not-allowed;
}

.eln_setting_btn.disabled .blue-btn:hover {
    background: #ccc;
    color: #333;
    cursor: not-allowed;
}

.eln_setting_btn.disabled .down-box {
    background: #ccc;
}

.eln_setting_btn.disabled > .blue-btn {
    background: #ccc;
    color: #333;
}

.opt-more .group-opt-btn.disabled {
    cursor: not-allowed;
    color: #ccc;
}

.opt-more .group-opt-btn.disabled:hover {
    cursor: not-allowed;
    color: #ccc;
}

.eln_setting_btn > a.blue-btn {
    /*line-height: 22px;*/
    /*    deleted by xieyuxiang2022.8.31 限制了行高会导致该按钮与旁边的按钮高度不一致（模板管理）*/
}

ul.analisis_select {
    height: 220px;
    overflow: auto;
}

ul.analisis_select li {
    user-select: none;
}

ul.analisis_select li.choosed {
    background: #ccc;
}


/*分享新弹窗 */

.line-tab-ul {
    border-bottom: 1px solid #e5e5e5;
    position: relative;
    margin-top: 16px;
    line-height: 40px;
}

.line-tab-li {
    position: relative;
    /* line-height: 30px; */
    display: inline-block;
    padding: 0 30px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1em;
    padding-bottom: 16px;
}

.line-tab-li.on {
    border-bottom: 2px solid #198bfe;
    margin-bottom: -2px;
    cursor: default;
    color: #198bfe;
/ / font-size: 14 px;
}

/
/
body > .fixed {
/ / position: static;
    /*请删除请删除*/
/ /
}

.new-share-wrap {
    height: 500px;
    width: 100%;
    background: #fff;
    position: absolute;
    z-index: 99999;
    top: 0;
    display: none;
}

.share-btn-wrap {
    position: absolute;
    right: 0;
    bottom: 10px;
}

.new-share-wrap .share-search-input {
    height: 32px;
    width: 260px;
    display: inline-block;
    vertical-align: bottom;
}

.share-add-btn {
    height: 32px;
    width: 32px;
    background: #198bfe;
    vertical-align: bottom;
}

.share-add-btn:before {
    content: '';
    height: 18px;
    width: 18px;
    display: inline-block;
    background: url("../image/eln.png?v=$version") -178px -434px;
/ / position: relative;
/ / top: 4 px;
}

.font0 {
    font-size: 0;
}

.new-share-wrap {
/ / border: 1 px solid #e5e5e5;
}

.new-share-con {
    margin-top: 20px;
    border: 1px solid #e5e5e5;
}

.share-table {
    font-size: 16px;
    color: #333;
}

.share-table tr:nth-child(even) td {
    background: #f9fafb;
}

.share-table td {
    border: none;
    padding: 12px 0;
    text-align: left;
}

.share-table .share-btn-td {
    width: 120px;
    text-align: center;
}

.share-table .share-name-td {
    text-align: left;
    padding-left: 20px;
}

.share-name-td .text {
    vertical-align: middle;
}

.new-share-i {
    vertical-align: middle;
    display: inline-block;
    height: 20px;
    width: 20px;
    background: url("../image/eln.png?v=$version") -203px -433px;
    margin-right: 10px;
}

.new-share-i.shared {
    background-position: -230px -433px;
}

.new-share-btn {
    color: #1388ff;
}

.new-share-btn.disabled {
    color: #666;
}


/*语言设置*/

.language-box {
    display: inline-block;
    position: relative;
    color: #fff;
}

.language-box .global-ico {
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url(/image/language.png) -77px -24px;
    vertical-align: middle
}

.language-box span {
    vertical-align: middle;
}

.language-box .user-down-ico {
    display: inline-block;
    border: 6px solid transparent;
    border-bottom-width: 0;
    border-top-color: #fff;
    cursor: pointer;
    vertical-align: middle;
    margin: -2px 0 0 2px
}

.language-box .language-selector {
    display: none;
    position: absolute;
    width: 210px;
    height: auto;
    left: 0;
    top: 50px;
    color: #444;
    border: 1px solid #bbb;
    border-radius: 5px;
    background: #fff;
    z-index: 9999;
    line-height: 35px;
    padding: 14px
}

.language-box .language-selector li {
    cursor: pointer;
}

.language-box .language-selector li .radio-ico {
    width: 18px;
    height: 18px;
    background: url(/image/language.png) -74px -2px;
    vertical-align: middle
}

.language-box .language-selector li .radio-ico.active,
.language-box .language-selector li:hover .radio-ico {
    background-position: -98px -2px
}

.language-box .language-selector li:hover {
    color: #e47911
}

.language-box .language-selector:before {
    content: '';
    position: absolute;
    top: -10px;
    left: 40px;
    border: 10px solid transparent;
    border-top-width: 0;
    border-bottom-color: #fff;
    border-left-width: 9px;
    border-right-width: 9px
}

.language-box .language-selector:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 20px;
    top: -20px
}

.language-box:hover .language-selector {
    display: block
}


/*更新通知*/

.update-notice {
    color: #333;
    padding: 0 20px;
}

.update-notice .title {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
}

.update-notice p {
    font-size: 16px;
    line-height: 1.5em;
    margin-bottom: 5px;
}


/*更新通知*/

.warn-ico {
    display: inline-block;
    height: 67px;
    width: 64px;
    background: url("../image/eln.png?v=$version") -324px -403px;
    vertical-align: middle;
}

.warn-modal-wrap {
    text-align: center;
}

.show-important {
    display: block !important;
    visibility: visible !important;
}

.placeholder {
    color: #757575 !important;
}

.app-icon-td .ineln-icon {
    width: 35px;
    height: 35px;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    vertical-align: middle;
    display: inline-block;
    background: url("../image/app-icos.png") -97px 0;
}

.app-icon-td .ineln-icon.not-open {
    opacity: .5;
}

@font-face {
    font-family: 'eln';
    src: url('fonts/eln.eot?xr1o1y');
    src: url('fonts/eln.eot?xr1o1y#iefix') format('embedded-opentype'), url('fonts/eln.ttf?xr1o1y') format('truetype'), url('fonts/eln.woff?xr1o1y') format('woff'), url('fonts/eln.svg?xr1o1y#eln') format('svg');
    font-weight: normal;
    font-style: normal;
}

.font-ico {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'eln' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.font-ico.task-infomation-ico:before {
    content: "\e900";
    color: #aaa;
    font-size: 16px;
    display: inline-block;
    vertical-align: middle;
/ / position: relative;
/ / top: - 2 px;
    cursor: pointer;
}

.task-info:hover .font-ico.task-infomation-ico:before {
    color: #0c9dfa;
}

.task-info-con {
    position: absolute;
    top: 22px;
    left: 0;
    background: #fff;
    width: 255px;
    z-index: 100;
    border: 1px solid #e5e5e5;
    padding: 0 15px 12px;
    box-shadow: 0 3px 3px #ddd;
    display: none;
}

.task-info-con .title {
    font-size: 14px;
    line-height: 1em;
    font-weight: 600;
    padding-top: 14px;
    padding-bottom: 8px;
}

.task-info-con li {
    width: 100%;
}

.task-info-con .con {
    color: #333;
    line-height: 1.5em;
    max-height: 200px;
    overflow: auto;
}

.task-info-con .prop {
    color: #666;
    width: 60px;
    float: left;
    text-align: right;
}

.task-info .txt {
    margin-left: 60px;
    word-break: break-all;
}

.task-info {
    margin-left: 13px;
}

.task-info:hover .task-info-con {
    display: block;
}

.v-middle {
    vertical-align: middle;
}

.select-beautify {
    position: relative;
    padding: 0;
    text-indent: 0
}

.select-beautify .select-text {
    line-height: 28px;
    text-indent: 5px;
    position: absolute;
    z-index: 9;
    right: 20px;
    left: 0;
    top: 0;
    display: block;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.select-beautify select {
    vertical-align: middle;
    opacity: 0;
    position: relative;
    z-index: 10;
    min-width: 32px;
    border: none;
    padding-right: 30px;
    height: 28px;
    line-height: 28px;
    width: 100%
}

.select-beautify:after {
    content: '';
    border: 6px solid transparent;
    border-color: #888 transparent transparent;
    border-left-width: 5px;
    border-right-width: 5px;
    width: 0;
    height: 0;
    z-index: 2;
    top: 12px;
    right: 6px;
    position: absolute
}

.search-filter-box .search-part label {
    vertical-align: top;
    line-height: 32px
}

.field-filter .field-box .part input,
.form-box .search-part {
    vertical-align: middle;
}

.search-part {
    margin: 10px 0;
}

.search-part label {
    padding-right: 2px;
}

.select-beautify {
    height: 28px;
    line-height: 28px;
    border: 1px solid #ddd;
    font-size: 14px;
    transition-duration: .3s;
    -moz-transition-duration: .3s;
    -webkit-transition-duration: .3s;
    -o-transition-duration: .3s
}

.border {
    border: 1px solid #e5e5e5;
}

.pad15 {
    padding: 15px;
}

.top-filter-box {
    border: 1px solid #e5e5e5;
    margin: 0 0 15px;
    padding: 0 15px;
}

.top-filter-box .big-input {
    width: 200px;
    border-radius: 3px;
}

.top-filter-box .search-part:not(:last-child) {
    margin-right: 20px;
}

.top-filter-box .btn {
    border: 1px solid #ddd;
    border-radius: 2px;
    line-height: 32px;
    padding: 0 18px;
    font-size: 14px;
    cursor: pointer;
    transition-duration: .2s;
    -moz-transition-duration: .2s;
    -webkit-transition-duration: .2s;
    -o-transition-duration: .2s;
    min-width: 80px;
}

.top-filter-box .btn {
    width: 100px;
}

.top-filter-box .blue-btn {
    background: #1388ff;
    color: #fff;
    border-color: #1388ff;
}

.top-filter-box .height32 {
    height: 32px !important;
    line-height: 32px;
}

.top-filter-box .blue-btn:hover {
    background: #006fdf;
}

.top-filter-box .blue-btn:active {
    background: #0055ac;
    box-shadow: 0 0 5px rgba(255, 255, 255, .4) inset;
}

.top-filter-box .mr10 {
    margin-right: 10px !important;
}

.page-error {
    padding: 20px;
}

.page-error p {
    font-size: 16px;
    color: #666;
}


/*字体文件*/

.search-hard-ico:before {
    content: "\e903";
    color: #fff;
    font-size: 18px;
    vertical-align: middle;
    margin-left: 5px;
    position: relative;
}

.ask-ico:before {
    content: "\e901";
    /*color: #1388FF;*/
    color: #9b9b9b;
    font-size: 16px;
}

.around_create_exp_btn .ask-ico:before {
    color: #1388FF00;
}

.around_create_exp_btn:hover .ask-ico:before {
    color: #1388FF;
}

.max .around_create_exp_btn:hover .ask-ico:before {
    color: #1388FF00;
}

.date-picker-ico:before {
    content: "\e902";
    color: #148dff;
}


/*字体文件*/

.top-search-hard {
    color: #fff;
    /*margin-left: 6px;*/
    cursor: pointer;
    display: inline-block;
}

.hover-show-info {
    position: relative;
    font-size: 18px;
    vertical-align: middle;
    top: -2px;
    margin-left: 7px;
}

.hover-show-info > div {
    position: absolute;
    font-size: 14px;
}

.hover-show-info > div {
    font-size: 14px;
    position: absolute;
    border: 1px solid #dddbdb;
    color: #666;
    top: -11px;
    left: 31px;
    box-sizing: border-box;
    border-radius: 5px;
    background: #fff;
    white-space: nowrap;
    line-height: 38px;
    padding: 0 10px;
    display: none;
}

.hover-show-info:hover > div {
    display: block;
}

.hover-show-info > div:before {
    content: '';
    display: block;
    height: 0;
    width: 0;
    position: absolute;
    border: 10px solid transparent;
    left: -20px;
    top: 10px;
    border-right-color: #ccc;
}

.hover-show-info > div:after {
    content: '';
    display: block;
    height: 0;
    width: 0;
    position: absolute;
    visibility: visible;
    border: 9px solid transparent;
    border-right-color: #fff;
    visibility: visible;
    left: -18px;
    top: 11px;
}

.high_search_modal .angle_input {
    height: 38px;
    width: 280px;
}

.high_search_modal .angle_input[readonly] {
    border: 1px solid #dcdcdc;
}

.high_search_modal .time_input {
    width: 140px;
}

.high_search_modal .time_to {
    width: 30px;
    display: inline-block;
    text-align: center;
}

.high_search_modal .date-picker-ico {
    font-size: 24px;
    margin-left: -34px;
    top: 4px;
    position: relative;
}

.high_search_modal .date-picker-wrap {
    display: inline-block;
    font-size: 0;
}

.high_search_modal .date-picker-wrap input {
    font-size: 14px;
}

.high_search_modal .input_part {
    padding: 9px 0;
    display: inline-block;
    width: 450px;
}

.high_search_modal .body_left {
    line-height: 38px;
}

.search_modal {
    top: 0px;
    display: block;
    position: absolute;
    left: 25px;
    z-index: 100040;
}

.comment-modal-dialog {
    width: 480px;
    height: 345px;
    margin: 0 auto 30px;
}

.click_search_indraw {
    display: inline-block;
    width: 20px;
    height: 22px;
    background: url(../image/indraw-ico.png?v=$version);
    /*margin-right: 12px;*/
    vertical-align: middle;
    cursor: pointer;
}

.click_search_indraw:hover {
    background: url(../image/indraw-ico-blue.png);
}

.top_nav .user_info_url {
    max-width: 60px;
    overflow-x: hidden;
    white-space: nowrap;
    display: block;
    float: right;
    text-overflow: ellipsis;
}

.jsDraw_pop iframe {
    width: 890px;
}


/*头部的效果*/

.en .top-search-hard {
    margin: 0 15px;
}

@media screen and (max-width: 1340px) {
    .top_nav .top_search_box {
        width: 620px;
        margin-left: -320px;
    }

    .jsDraw_pop iframe {
        width: 770px;
    }

    .jsDraw_pop {
        margin-left: -320px;
    }

    .top_search_box input {
        width: 80%;
    }

    .en .top_nav .top_search_box {
        width: 600px;
        margin-left: -320px;
    }

    .en .jsDraw_pop iframe {
        width: 770px;
    }

    .en .jsDraw_pop {
        margin-left: -320px;
    }

    .en .top_search_box input {
        width: 80%;
    }
}

@media screen and (min-width: 1340px) {
    .top_nav .top_search_box {
        width: 770px;
        margin-left: -400px;
    }

    .jsDraw_pop iframe {
        width: 770px;
    }

    .jsDraw_pop {
        margin-left: -400px;
    }

    .top_search_box input {
        width: 84%;
    }

    .en .top_nav .top_search_box {
        width: 770px;
        margin-left: -400px;
    }

    .en .jsDraw_pop iframe {
        width: 770px;
    }

    .en .jsDraw_pop {
        margin-left: -400px;
    }

    .en .top_search_box input {
        width: 82%;
    }
}

@media screen and (min-width: 1900px) {
    .top_nav .top_search_box {
        width: 1000px;
        margin-left: -500px;
    }

    .jsDraw_pop iframe {
        width: 1000px;
    }

    .jsDraw_pop {
        margin-left: -500px;
    }

    .top_search_box input {
        width: 88%;
    }

    .en .top_nav .top_search_box {
        width: 900px;
        margin-left: -450px;
    }

    .en .jsDraw_pop iframe {
        width: 900px;
    }

    .en .jsDraw_pop {
        margin-left: -450px;
    }

    .en .top_search_box input {
        width: 85%;
    }
}

.top-logo-wrap {
    height: 60px;
    display: block;
}

.top-logo-wrap img {
    max-width: 120px;
}

.set-module-ico:before {
    content: "\e904";
    color: #148dff;
    font-size: 24px;
    cursor: pointer;
}

.add_to_schedule {
    width: 16px;
    height: 16px;
    background: url("../image/add_to_schedule_default.svg");
    background-size: 16px 16px;
    display: block;
    cursor: pointer;
    margin-top: 6px;
}

.experiment_reminder_setting #add_to_schedule:disabled + label .add_to_schedule,
.instrument_reminder_setting .add_to_schedule_checkbox:disabled + label .add_to_schedule,
.collaboration_reminder_setting #add_to_schedule:disabled + label .add_to_schedule {
    width: 16px;
    height: 16px;
    background: url("../image/add_to_schedule_disabled.svg");
    background-size: 16px 16px;
    display: block;
    cursor: pointer;
    margin-top: 6px;
}

.add_to_schedule_checkbox:checked + label .add_to_schedule {
    background: url("../image/add_to_schedule_select.svg");
    background-size: 16px 16px;
}

.set-sign-ico {
    height: 24px;
    background: url("../image/sign.svg") no-repeat center;
    background-size: 21px;
    cursor: pointer;
}

.add-ico:before, .add-node-ico:before, .add-setting-btn:before {
    content: "\e907";
    color: #1296db;
    font-size: 24px;
    cursor: pointer;
    vertical-align: middle;
}

.del-node-ico:before, .del-setting-btn:before {
    content: "\e908";
    color: #d81e06;
    font-size: 24px;
    cursor: pointer;
    vertical-align: middle;
}


/*view-box*/

.data-box-ineln .view-info-box {
    line-height: 40px;
}

.data-box-ineln .data-box {
    width: 350px;
}

.data-box-ineln .view-checked-box {
    position: relative;
}

.data-box-ineln .view-checked-box input[type=checkbox] {
    width: auto;
    height: auto;
}

.data-box-ineln .view-checked-box {
    position: relative;
}

.data-box-ineln .view-checked-box .clear-ico {
    display: none;
    cursor: pointer;
    position: absolute;
    width: 20px;
    height: 26px;
    line-height: 26px;
    right: 0;
    top: 0;
}

.data-box-ineln .view-checked-box .clear-ico:before {
    content: '\e906';
    font-size: 12px;
    color: #999;
}

.data-box-ineln .view-checked-box .pr34 {
    padding-right: 34px;
}

.data-box-ineln .view-checked-box .name-box {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.data-box-ineln .view-checked-box .top-set {
    top: 31px;
}

.data-box-ineln .view-checked-box .top-set:after {
    top: -20px;
    border: 1px solid transparent;
    content: "";
    width: 0px;
    height: 0px;
    position: absolute;
}

.data-box-ineln .view-checked-box .top-set:before {
    top: -24px;
    border: 1px solid transparent;
    content: "";
    width: 0px;
    height: 0px;
    position: absolute;
}

.data-box-ineln .view-checked-box .top-set:before {
    border-bottom-color: #61AFF1;
    border-width: 12px;
    left: 10%;
    margin-left: -12px;
}

.data-box-ineln .view-checked-box .top-set:after {
    border-bottom-color: #fff;
    border-width: 10px;
    left: 10%;
    margin-left: -10px;
}

.data-box-ineln .view-checked-box .bottom-set {
    bottom: 100%;
}

.data-box-ineln .view-checked-box .bottom-set:after {
    bottom: -20px;
    border: 1px solid transparent;
    content: "";
    width: 0px;
    height: 0px;
    position: absolute;
}

.data-box-ineln .view-checked-box .bottom-set:before {
    bottom: -24px;
    border: 1px solid transparent;
    content: "";
    width: 0px;
    height: 0px;
    position: absolute;
}

.data-box-ineln .view-checked-box .bottom-set:before {
    border-top-color: #61AFF1;
    border-width: 12px;
    left: 10%;
    margin-left: -12px;
}

.data-box-ineln .view-checked-box .bottom-set:after {
    border-top-color: #fff;
    border-width: 10px;
    left: 10%;
    margin-left: -10px;
}

.data-box-ineln .view-checked-box .checked-box {
    border: 1px solid #61AFF1;
    width: 100%;
    text-align: left;
    position: absolute;
    border-radius: 2px;
    padding: 5px 10px;
    line-height: 18px;
    left: 0px;
    background-color: #fff;
    z-index: 80;
}

.data-box-ineln .view-checked-box .checked-box .checked-list-box {
    max-height: 90px;
    overflow-y: auto;
}

.data-box-ineln .view-checked-box .checked-box .checked {
    float: left;
    margin-right: 5px;
    max-width: 180px;
    line-height: 24px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.data-box-ineln .view-checked-box .check-box {
    position: absolute;
    left: 0px;
    /*width: 100%;*/
    width: 300px;
    overflow-y: auto;
    background: #FFFFFF;
    border: 1px solid #66afe9;
    border-radius: 3px;
    padding: 5px 0;
    z-index: 11;
    box-shadow: rgb(0 0 0 / 8%) 0px 1px 1px inset, rgb(102 175 233 / 60%) 0px 0px 8px;
}

.data-box-ineln .view-checked-box .check-box.top {
    top: 30px;
}

.data-box-ineln .view-checked-box .check-box.bottom {
    bottom: 30px;
}

.data-box-ineln .view-checked-box .check-one-box {
    width: 100%;
    position: absolute;
    display: none;
    max-height: 180px;
    overflow-y: auto;
    z-index: 5001;
    background: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 0 10px #a5a5a5;
    -moz-box-shadow: 0 0 10px #a5a5a5;
    -webkit-box-shadow: 0 0 10px #a5a5a5;
}

.data-box-ineln .view-checked-box .check-one-box div {
    padding: 0px 10px;
    white-space: nowrap;
    color: #666;
}

.data-box-ineln .view-checked-box .check-one-box div:hover {
    background: #f1f1f1;
    color: #333;
}

.data-box-ineln .view-info-box .view-checked-box .top {
    top: 44px;
}

.data-box-ineln .view-info-box .view-checked-box .bottom {
    bottom: 44px;
}

.data-box-ineln .check-search-box {
    line-height: normal;
}

.data-box-ineln .check-search-box .part-right {
    display: table;
    /*width: 220px;*/
    /*changed by xieyuxiang 2022.8.10 扩大空间以让搜索框往左移动一些*/
    width: 260px;
    float: right;
}

.data-box-ineln .check-search-box .part-right .search-check-key {
    line-height: 24px;
    width: 230px;
    height: 24px;
    padding: 1px 24px 1px 0;
    display: inline-block;
    text-indent: 8px;
    margin-left: 10px;
    /*position: absolute;*/
    /*right: 10px;*/
    /*changed by xieyuxiang 2022.8.10 修改布局让组件贴于左方*/

}

.data-box-ineln .check-search-box .part-right .search-check-btn {
    padding: 0;
    height: 24px;
    line-height: 24px;
    display: inline-block;
    width: 57px;
    text-align: center;
    background: #3499ff;
    color: #fff;
}

.data-box-ineln .check-search-box .part-right .search-ico {
    width: 24px;
    display: table-cell;
    position: absolute;
    right: 10px;
    border-radius: 0 3px 3px 0
}

.data-box-ineln .check-list-box {
    max-height: 131px;
    min-height: 24px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.data-box-ineln .check-list-box .check {
    cursor: pointer;
    padding: 0px 10px;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 24px;
}

.data-box-ineln .check-list-box .check .check-name {
    cursor: pointer;
    width: 290px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
}

.data-box-ineln .line-height32 {
    line-height: 32px;
}

.data-box-ineln .EN .search-btn-box input {
    width: 305px;
}

.data-box-ineln .search-btn-box.display-table input {
    width: 320px;
    height: 32px;
    padding: 5px;
    font-size: 14px;
    display: table-cell;
}

.data-box-ineln .search-btn-box.display-table .btn {
    border: 0;
    display: table-cell;
}

.data-box-ineln .search-btn-box .btn {
    height: 32px;
    width: 80px;
    padding: 6px 0px;
    text-align: center;
    color: #333;
    line-height: 18px;
    background: #3499ff;
    color: #fff;
    background: #1388ff;
    border-color: #1388ff;
    border-radius: 0 !important;
}

.data-box-ineln .search-btn-box .btn:hover {
    background: #006fdf;
}

.data-box-ineln .view-checked-box .search-ico:before {
    content: "\e905";
    vertical-align: top;
    font-size: 18px;
}

.data-box-ineln .check-list-box .check {
    cursor: pointer;
    padding: 0 10px;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 24px;
}

.data-box-ineln .view-checked-box .check-search-box {
    padding: 0 10px;
    line-height: 25px;
}

.data-box-ineln input.check_department {
    border: 1px solid #dcdcdc;
}

.copy_module_box .data-box-ineln {
    margin-left: -4px;
}

.save_name_modal_body .data-box {
    width: 330px;
}

.en .mudole_info_part .body_left {
    min-width: 80px;
}

.modal-body .mudole_info_part .hover-show-info {
    position: relative;
    left: 0;
    top: 3px;
}

.data-box-ineln .no-group-wrap {
    padding: 10px;
}

.en .save_name_modal_body .body_left {
    min-width: auto;
}

.save_name_modal_body .mudole_info_part i.hover-show-info {
    position: relative;
    left: 0;
    top: 0;
}

#export-canvas-wrap {
    position: fixed;
    left: 30px;
    bottom: 60px;
    display: none;
    z-index: 99999;
    /*background: #fff;*/
}

.export-report-progress{
    position: fixed;
    left: 30px;
    bottom: 30px;
    display: none;
    z-index: 99999;
    width: 150px;
}

.details-switch {
    position: absolute;
    left: 100px;
    top: 10px;
    width: 10px;
    height: 7px;
    cursor: pointer;
    background: url(../image/svg/collapse-down.svg?v=4);
}

.details-switch.show {
    background: url(../image/svg/collapse-up.svg?v=4);
}

.tool_nav .set_require {
    width: 28px;
    height: 24px;
    background: url(../image/set_require.png);
    background-repeat: no-repeat;
}

.tool_nav .set_struct_data {
    width: 28px;
    height: 24px;
    background: url(../image/set_struct_data.png);
    background-repeat: no-repeat;
}

.tool_nav .set_revision {
    width: 28px;
    height: 24px;
    background: url(../image/set_revision.png);
    background-repeat: no-repeat;
}

.more-exp-tools-wrap {
    line-height: 47px;
    padding: 0 13px 0 8px;
    cursor: pointer;
}

.more-exp-tools-ico {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    background: url(/image/eln.png) -160px -228px;
    vertical-align: middle;
}

.more-exp-tool-list {
    display: none;
    top: 40px;
    left: -5px;
    background: #ffffff;
    border: 1px solid #cccccc;
    box-shadow: 0 1px 3px #aaaaaa;
}

@media screen and (max-width: 1424px) {
    .more-exp-tool-list {
        left: 0;
        right: auto;
    }
}

.more-exp-tools-wrap:hover .more-exp-tool-list {
    display: block;
}

.more-exp-tool-list li {
    padding: 0 10px;
    white-space: nowrap;
}

.more-exp-tool-list li:hover {
    background: #f2f2f2;
}


/*begin add by hkk 2019/3/27 实验页面更改新的input选择框样式*/

.new-input-select-list {
    position: absolute;
    display: block;
    border: 1px solid #e5e5e5;
    width: 100px;
    border-radius: 4px;
    background-color: white;
    z-index: 91;
    /*begin add by hkk 2019/3/28 */
    overflow-y: auto;
    text-align: center;

}

.new-input-select-list li {
    line-height: 30px;
    border-radius: 4px;
}

.new-input-select-list li:hover {
    background-color: #e5e5e5;
    cursor: default;
}

.weather-box .new-input-select-list li {
    height: 24px;
    line-height: 24px; /*mod dx,防止ul出现滚动条的问题*/
}

.details_tr > td:nth-child(3) .new-input-select-list li {
    text-align: left;
    padding-left: 10px;
}


/*end*/

.exp_menu_opts_for_check {
    width: 24px;
    height: 24px;
    background: url("../image/eln.png?v=$version") -54px -105px;
    top: 2px;
    right: 0px;
    display: none;
    cursor: pointer;
}

.exp_menu_opts_for_check:hover {
    background: url("../image/eln.png?v=$version") -82px -105px;
}

.book_outer_box:hover .exp_menu_opts_for_check {
    display: inline-block;
}


/*end*/


/*begin add by hkk 2019/4/4 eln顶部栏英文状态样式更改*/

.en .right_opts .right_part {
    margin: 0 10px;
}

.en .top_nav .right_opts a {
    padding: 0;
}

.en .top-search-hard {
    margin: 0;
}


/*end*/


/*add by hkk 2019/4/8 BatchNo输入提示*/

.substrates_batch_num::-webkit-input-placeholder {
    color: #ccc;
    font-size: 10px;
}

.catalysts_batch_num::-webkit-input-placeholder {
    color: #ccc;
    font-size: 10px;
}


/*add by hkk 2019/4/8 顶部搜索栏输入提示*/

.search_text::-webkit-input-placeholder {
    color: #999999;
}


/*add by hkk 2019/4/9 删除化合物图标*/

.deleteSign {
    background: url(../image/svg/clear.svg);
    margin-top: -6px;
}


/* begin add by 表格*/

.customTableData table {
    text-align: initial;
}

.handsontable .htAutocompleteArrow {
    color: #ccc !important;
}

.handsontable td.htNoWrap {
    white-space: pre !important;
}

.new_search_field,
.search_field {
    height: 28px;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin: 10px 5px;
    padding: 0 4px;
}

.ht_clone_top,
.ht_clone_left,
.ht_clone_top_left_corner {
    z-index: 80 !important;
}

.htCore td.customClass {
    color: #f8f8ff;
    background: #1E90FF;
}

.custom_table_toolbar {
    width: 100%;
    height: 40px;
    padding-left: 15px;
}

.custom_table_toolbar_btn:hover,
.custom_table_toolbar_btn.active {
    background: rgba(0, 0, 0, 0.08);
}

.custom_table_toolbar_btn.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.custom_table_toolbar_btn {
    flex: 0 0 auto;
    display: inline-block;
    float: left;
    border: 1px solid transparent;
    height: 26px;
    line-height: 26px;
    min-width: 26px;
    margin: 6px 1px 0;
    padding: 0;
    text-align: center;
    border-radius: 2px;
}

.custom_table_icon {
    width: 18px;
    height: 18px;
    margin: 1px 1px 2px 1px;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    overflow: hidden;
    position: relative;
    display: inline-block;
}

.custom_table_icon .custom_table_icon_img {
    background-image: url(../image/handsontable_sprite.svg);
    position: absolute;
    background-repeat: no-repeat;
    width: 262px;
    height: 444px;
    opacity: 0.56;
}

.date .custom_table_icon .custom_table_icon_img,
.time .custom_table_icon .custom_table_icon_img,
.dropdown .custom_table_icon .custom_table_icon_img,
.checkbox .custom_table_icon .custom_table_icon_img,
.radio .custom_table_icon .custom_table_icon_img,
.conversion .custom_table_icon .custom_table_icon_img,
.upload_file .custom_table_icon .custom_table_icon_img,
.import_csv .custom_table_icon .custom_table_icon_img,
.export_csv .custom_table_icon .custom_table_icon_img {
    position: absolute;
    width: 18px;
    height: 18px;
    top: 0;
    left: 0;
}

.date .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_date.svg) center no-repeat;
    background-size: 18px;
    opacity: .9;
}

.time .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_time.svg) center no-repeat;
    background-size: 18px;
    opacity: .8;
}

.dropdown .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_dropdown.svg) center no-repeat;
    background-size: 18px;
    opacity: 1;
}

.checkbox .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_checkbox.svg) center no-repeat;
    background-size: 18px;
    opacity: 1;
}

.radio .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_radio.svg) center no-repeat;
    background-size: 18px;
    opacity: 1;
}

.conversion .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_conversion.svg) center no-repeat;
    background-size: 18px;
    opacity: .75;
}

.upload_file .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_uploadFile.svg) center no-repeat;
    background-size: 18px;
    opacity: .75;
}

.import_csv .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_importCSV.svg) center no-repeat;
    background-size: 14px;
    opacity: .55;
}

.export_csv .custom_table_icon .custom_table_icon_img {
    background: url(../image/handsontable_exportCSV.svg) center no-repeat;
    background-size: 13px;
    opacity: .55;
}

.custom_table_icon span.currentColor {
    width: 100%;
    height: 4px;
    display: inline-block;
}

#border-box {
    display: none;
    position: absolute;
    top: 26px;
    left: 5px;
    z-index: 9999999;
    background: #fff;
    width: 162px;
    padding: 5px;
    box-shadow: 1px 2px 5px 2px rgba(51, 51, 51, 0.15)
}

.border-btn {
    margin: 0
}

.table-color-palette-cell {
    width: 15px;
    height: 15px
}

.font_color,
.font_size,
.fill_color {
    position: relative
}

.table-color-palette {
    position: absolute;
    z-index: 999999999;
    display: none;
    background-color: #fff;
    box-shadow: 1px 2px 5px 2px rgba(51, 51, 51, 0.15);
}

.table-color-palette table td {
    border: none;
    padding: 3px;
}

.table-dropdown-content {
    position: absolute;
    top: 10px;
    left: 0;
    z-index: 99999999;
}

.custom-modal-mask {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    height: 100%;
    z-index: 9999998;
    background: rgba(0, 0, 0, 0.65);
}

#custom-modal-box {
    display: none;
    width: 430px;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    position: fixed;
    top: 30%;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 9999999;
    background: #fff;
}

#custom-modal-box .modal-header {
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    margin: 0;
    color: rgba(0, 0, 0, 0.85);
}

#custom-modal-box .modal-header .close {
    float: right;
    text-decoration: none;
    opacity: .6;
    color: #000;
    font-size: 16px;
    line-height: 18px;
    margin: 0;
}

#custom-modal-box .modal-header .close:hover {
    opacity: .9;
}

#custom-modal-box .modal-body {
    padding: 24px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
}

#custom-modal-box .modal-body button {
    border-radius: 3px;
    border: none;
    background-color: dodgerblue;
    color: #fff;
    padding: 5px 10px;
    text-align: center;
    cursor: pointer;
    margin-bottom: 5px;
}

#custom-modal-box .modal-body .child {
    margin: 5px 0;
}

#custom-modal-box .modal-body .child label {
    line-height: 30px;
}

#custom-modal-box .modal-body .child input {
    height: 30px;
    width: 250px;
    border-radius: 3px;
    border: 1px solid #e4e4e4;
}

#custom-modal-box .modal-footer {
    padding: 10px 16px;
    text-align: right;
    border-top: 1px solid #e8e8e8;
    border-radius: 0 0 4px 4px;
}

#custom-modal-box .modal-footer button,
#custom-modal-box .modal-body .child span {
    line-height: 1.499;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    cursor: pointer;
    -webkit-transition: all .3s cubic-bezier(.645, .045, .355, 1);
    transition: all .3s cubic-bezier(.645, .045, .355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    border-color: #d9d9d9;
}

#custom-modal-box .modal-body .child span {
    height: 30px;
    line-height: 1.799;
    margin-left: 10px;
}

#custom-modal-box .modal-body .child span:hover {
    text-decoration: none;
    color: #fff;
    background-color: #ff4d4f;
    border-color: #ff4d4f;
}

#custom-modal-box .modal-footer button:hover,
#custom-modal-box .modal-footer button:focus {
    color: #40a9ff;
    background-color: #fff;
    border-color: #40a9ff;
}

#custom-modal-box .modal-footer button.submit {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.table-dropdown-content {
    overflow: auto;
    background: #fff;
    box-shadow: 1px 2px 5px 2px rgba(51, 51, 51, 0.15);
    position: absolute;
    width: 55px;
    top: 29px;
    height: 238px;
    font-size: 13px;
}

.table-dropdown-content .table-item {
    cursor: pointer;
    outline: none;
    height: 26px;
    color: rgba(0, 0, 0, 0.9);
}

.table-dropdown-content .table-item:hover {
    background: rgba(0, 0, 0, 0.08);
}

.resizeHandsontableDom {
    width: 100%;
    height: 12px;
    cursor: s-resize;
    margin: 0;
    background-color: #F0F0EE;
    border-top: 1px solid #CCC;
}

.border-dropdown-content {
    display: none;
    width: auto;
    position: absolute;
    background: #fff;
    box-shadow: 1px 2px 5px 2px rgba(51, 51, 51, 0.15);
}

.border-item.state {
    padding-left: 35px;
    position: relative;
}

.border-item {
    user-select: none;
    background: 0;
    border: 1px solid transparent;
    outline: none;
    height: 26px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 26px;
    list-style: none;
    padding: 2px 10px;
    cursor: default;
    text-align: left;
}

.border-item:hover,
.border-item.active {
    background: rgba(0, 0, 0, 0.08);
}

.border-item.state.active:before {
    content: '';
    position: absolute;
    width: 5px;
    height: 12px;
    color: #4b89ff;
    border-bottom: 2px solid;
    border-right: 2px solid;
    left: 15px;
    top: 6px;
    transform-origin: center;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
}

.custom_table,
.new_custom_table {
    width: 100% !important;
}

.custom_table tr:last-child td {
    border-bottom: 1px solid #ccc;
}

.htContextMenu table.htCore {
    border: none;
    text-align: left;
}

.htContextMenu table tbody tr td {
    padding: 5px 12px 3px;
}

.htContextMenu table tbody tr td .htItemWrapper {
    padding: 2px 6px;
}

.htContextMenu table tbody tr td.htSeparator .htItemWrapper {
    padding: 0;
}

.htContextMenu .ht_master .wtHolder {
    height: 330px !important;
}

.htContextMenu .wtHider {
    height: 330px !important;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 1px 2px 5px 2px rgba(51, 51, 51, 0.15);
}

.custom_table_toolbar-ul {
    /*hkk*/
    position: absolute;
    z-index: 999;
    left: inherit;
    top: 84px;
    background: white;
    list-style: none;
    text-align: left;
    padding-left: 0px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    font-size: 13px;
    width: 130px;
    display: none;
}

.custom_table_toolbar-ul li:hover {
    /*hkk*/
    background-color: #e5e5e5;
    cursor: pointer;
}

.htContextMenu .wtHider::-webkit-scrollbar {
    width: 8px;
    background-color: rgba(237, 237, 237, 1);
}

.htContextMenu .wtHider::-webkit-scrollbar-track {
    background-color: rgba(237, 237, 237, 1);
}

.htContextMenu .wtHider::-webkit-scrollbar-thumb {
    background: #ccc;
}

.formula .table-dropdown-content {
    height: 130px;
    text-align: left;
}

.formula .table-dropdown-content div {
    height: 26px;
    padding: 0 10px;
}

.formula .table-dropdown-content div:hover {
    background: rgba(0, 0, 0, .08);
}

.handsontable span.cornerHeader {
    width: 100%;
    height: 100%;
    line-height: unset !important;
}


/* end add by 表格*/

.name_td input[property="name"] {
    width: calc(100% - 30px);
}

#wms-result-wrap {
    max-height: 600px;
    overflow-y: auto;
}

#wms-result-wrap table {
    border: 1px solid #ddd;
}

#wms-result-wrap table thead {
    background: #eee;
}

#wms-result-wrap table th {
    font-weight: bold;
}

#wms-result-wrap table td {
    border: none;
}

#wms-result-wrap .add-to-batch-num {
    cursor: pointer;
}

.paddingTop3 {
    padding-top: 3px;
    margin-left: 0px;
}

.paddingTop6 {
    padding-top: 6px;
}

.paddingTop10 {
    padding-top: 10px;
}

.paddingTop20 {
    padding-top: 20px;
}

.marginLeft10 {
    margin-left: 10px;
}

.marginLeft20 {
    margin-left: 20px;
}

.marginRight10 {
    margin-right: 10px;
}

.marginRight20 {
    margin-right: 20px;
}

.marginRight30 {
    margin-right: 30px;
}

.define-table-input-style {
    display: inline;
    width: 90%;
}

input.define-table-input-style {
    padding: 2px 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text_align_r {
    text-align: right;
}

.detail_add_file_img {
    background: url(../image/eln.png) no-repeat;
    opacity: .75;
    width: 10px;
    height: 10px;
}


/*add by hkk 自定义物料表列 2019/9/5*/

.addColumnSign:hover {
    cursor: pointer;
    background: url(../image/route_link_hover.png);
}

.addColumnSign {
    background: url(../image/route_link.png);
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px
}

input.extra_column_input {
    background-color: transparent;
    border: 1px solid #dcdcdc;
    box-shadow: none;
    width: 80px;
    border-radius: 4px;
    height: 24px;
}

input.extra_column_input:hover {
    background-color: white;
    border: 1px solid #dcdcdc;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

.deleteColumnSign {
    background: url(../image/delete_2019.svg);
    width: 24px;
    height: 24px;
    cursor: pointer;
    background-size: 24px 24px;
    vertical-align: middle;
    margin-left: 0;
    display: inline-block;
}

.attach_dictionary_options {
    position: absolute;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    background-color: white;
}

.attach_dictionary_options ul {
    height: 300px;
    overflow-y: scroll;
}

.attach_dictionary_options li {
    text-align: left;
    min-width: 100px;
    max-width: 170px;
    cursor: pointer;
    -moz-user-select: none;
    /*火狐*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -ms-user-select: none;
    /*IE10*/
    -khtml-user-select: none;
    /*早期浏览器*/
    user-select: none;
    padding-left: 10px;
}

.attach_dictionary_options .dict_title {
    background-color: #ddd;
    border-bottom: 1px solid #e5e5e5;
    text-align: left;
    padding-left: 12px;
}

.attach_dictionary_options li:hover {
    border-radius: 4px;
    background-color: #ccc;
}

.attach_dictionary_options .chosenDictionaryValue {
    background-color: #0fa7ee;;
    border-radius: 4px;
}

.attach_dictionary_options .chosenDictionaryValue:hover {
    background-color: #0fa7ee;;
    border-radius: 4px;
}

.addedColumnValue {
    width: 100%;
    border-radius: 3px;
}

.addSelectSign {
    display: inline-block;
}

.add_column_icon {
    display: inline-block;
    width: 10px;
    height: 10px;
    left: 100px;
    cursor: pointer;
    margin-left: 10px;
    background: url(../image/eln.png?v=55555555);
}

.columnTitleStyle {
    font-size: 14px;
    font-weight: bold;
}

.divSelector {
    margin-bottom: 10px;
}

.new-define-select-list {
    position: absolute;
    display: block;
    border: 1px solid #e5e5e5;
    width: 100px;
    border-radius: 4px;
    background-color: white;
    /*z-index: 999;*/
    /*changed by xieyuxiang 2022.10.25 bug28970 plug：eln自定义项，滑动时界面错位了*/
    z-index: 91;
    /*begin add by hkk 2019/3/28 */
    overflow-y: auto;
    text-align: center;
}

.new-define-select-list li {
    line-height: 30px;
    border-radius: 4px;
}

.new-define-select-list li:hover {
    background-color: #e5e5e5;
    cursor: default;
}

.suspend_dict_value {
    display: none;
    position: absolute;
    background-color: #ddd;
    border-radius: 5px;
    padding: 5px;
    white-space: nowrap;
}

.defineTriangleSign {
    height: 0;
    width: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    border-color: transparent #ddd transparent transparent;
    border-style: solid;
    border-width: 10px 15px 10px 10px;
    left: -25px;
    position: absolute;
}

.overflowAuto {
    overflow: auto;
}

.hide_show_ico {
    display: inline-block;
    width: 32px;
    height: 18px;
    background: url(../image/hide_show_column.svg);
    vertical-align: middle;
    background-size: 32px 18px;
}

.sync_inventory_ico {
    display: inline-block;
    width: 32px;
    height: 18px;
    background: url(../image/warehouse.svg);
    vertical-align: middle;
    background-size: 32px 18px;
}

.warehouse_sync_ico {
    background: url(../image/warehouse_sync.svg);
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.warehouse_sync_input {
    display: inline;
    width: calc(100% - 25px) !important;
    min-width: 50px;
    text-align: left;
}

.instrument-data-fields-box {
    width: 16px;
    height: 16px;
    background: url(../image/svg/instrument_field_setting.svg);
    margin-top: -20px;
}

.inscada-filter-box {
    text-align: right;
    padding-right: 16px;
}

.detect-inscada {
    background: url(../image/svg/instrument_detect.svg);
    width: 20px;
    height: 20px;
    position: absolute;
    left: 385px;
    margin-top: 3px;
}

.marginLeft5 {
    margin-left: 5px;
}

.search-inscada-box {
    margin-left: 10px;
    height: 25px;
    margin-bottom: 15px;
}

.search-inscada-box.popup {
    margin-left: 10px;
    margin-bottom: 15px;
    position: absolute;
    z-index: 10;
    background: white;
    border: #ccc 1px solid;
    left: 0px;
    width: 380px;
    /*height: 234px;*/
    /*仪器数据搜索框高度需要和内容保持同高, bug#10378 */
    height: fit-content;
    border-radius: 4px;
}

.instrument-filter {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: #EBEDF0 url(../image/svg/inscada_filter.svg);
    vertical-align: middle;
    margin-top: -3px;
}

.instrument-filter-cancel {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url(../image/svg/inscada_filter.svg);
    vertical-align: middle;
    margin-top: -3px;
}

.refresh-inscada-data {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(../image/svg/refresh-inscada-data.svg);
    vertical-align: middle;
    margin-top: -3px;
}

.instrument-filter-border {
    position: absolute;
    top: auto;
    bottom: 15px;
}

.instrument-data-input {
    color: #1388FF !important;
}

.instrument-data-input.modified {
    color: unset !important;
}

.instrument-data-modal {
    /*width: 95%;*/
    overflow: visible !important;
}

.instrument-data-modal .modal-content {
    /*width: 1120px;*/
    width: 420px;/*实验-插入仪器数据弹框增加详细数据列,增加弹框宽度以防止表格列宽度被挤压缩小,2023.06.29 mod dx*/
    height: 600px;
}

.instrument-data-modal .modal-header {
    padding: 12px 25px 15px 35px;
}

.instrument-data-modal .modal-dialog {
    width: 100%;
}

.instrument-data-modal table {
    text-align: left;
}

.inscada_data_page_box {
    margin: 10px 0;
}

.instrument-data-modal .project-view-tbody tr {
    border-bottom: 1px solid #EBEEF5;
}

.instrument-data-modal .project-view-tbody tr:hover td {
    background-color: #F0F2F5;
}

.delete_2019_border {
    display: inline-block;
    position: absolute;
    left: 134px;
    bottom: 19px;
    top: auto;
    z-index: 101;
}

.delete_2019 {
    width: 25px;
    height: 25px;
    background: url(../image/delete_2019.svg);
}


.instrument-select-item {
    height: 25px;
    line-height: 25px;
    cursor: pointer;
    background-color: white;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.instrument-select-item:hover {
    cursor: pointer;
    background-color: #cccccc;
}

/*.instrument-select-item td, .instrument-select-title th {*/
/*    width: 600px;*/
/*    height: 30px;*/
/*    border: 1px solid #CCC;*/
/*}*/
.instrument-select-button-num {
    width: 75px;
    height: 25px;
    display: inline-block;
    float: left;
    background-color: #FFFFFF;
    border: 1px solid gray;
    border-radius: 4px 0 0 4px;
}

.instrument-select-button-file {
    width: 75px;
    height: 25px;
    display: inline-block;
    background-color: #FFFFFF;
    border: 1px solid gray;
    border-left: 0;
    border-radius: 0 4px 4px 0;
}

.instrument-select-button:hover {
    background-color: #f7f8f8;
}

.instrument-data-modal .modal-content {
    background: #FFF;
    opacity: 0.9;
}

.instrument-select-search {
    border: 1px solid #DDD;
}

/*.instrument-select:hover {*/
/*    cursor: pointer;*/
/*}*/
.instrument-select {
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    width: 160px;
    height: 25px;
    margin-right: 2px;
    position: relative;
    z-index: 100;
    padding-left: 18px;
}

.instrument-select-detail {
    display: inline-block;
    margin-right: 2px;
    height: 16px;
    width: 16px;
    position: absolute;
    left: 4px;
    bottom: 20px;
    top: auto;
    background: url(../image/svg/instrument_detail.svg);
    cursor: pointer;
    z-index: 101;
}

.instrument-blue-btn {
    background-color: #1388FF;
    color: white;
    border-radius: 4px;
    height: 25px;
}


.instrument-select-wrap {
    position: relative;
    padding-bottom: 15px;
}

/*.instrument-select-warp-click{*/
/*    z-index:10;*/
/*    border: 1px solid black;*/
/*}*/
.instrument-select-box {
    /*margin-top: 15px;*/
    background-color: white;
    position: absolute;
    z-index: 10;
    border: 1px solid #dcdcdc;
    border-top: none;
    /*box-shadow: -5px 0 5px -5px rgba(0,0,0,0.4),*/
    /*            0 -5px 5px -5px rgba(0,0,0,0.4),*/
    /*            5px 0 5px -5px rgba(0,0,0,0.4);*/
    border-radius: 0 0 4px 4px;
    /*padding:10px 0 0 20px;*/
    /*border-image: -webkit-linear-gradient(left, #AAA 0%,#AAA 70%, white 70%, white 100%) 1;*/
    width: 160px;
}

.over-input {
    width: 160px;
    border: 1px solid #dcdcdc;
    border-bottom: none;
    padding-left: 18px;
    border-radius: 4px 4px 0 0;
    /*box-shadow: -5px 0 5px -5px rgba(0,0,0,0.4),*/
    /*            0 -5px 5px -5px rgba(0,0,0,0.4),*/
    /*            5px 0 5px -5px rgba(0,0,0,0.4);*/

}


.bottom1 {
    bottom: 1px;
}

.w40 {
    width: 40px;
}

.w50 {
    width: 50px;
}

.w80 {
    width: 80px;
}

.w115 {
    width: 115px;
}

.w137 {
    width: 137px;
}

.w150 {
    width: 150px;
}

.w160 {
    width: 160px;
}

.w170 {
    width: 170px;
}

.w185 {
    width: 185px;
}

.w200 {
    width: 200px;
}

.w260 {
    width: 260px;
}

.w85 {
    width: 85px;
}

.w83 {
    width: 83px;
}

.w35 {
    width: 35px;
}

.w124 {
    width: 124px;
}

.w130 {
    width: 130px;
}

.w114 {
    width: 114px;
}

.w118 {
    width: 118px;
}

.w163 {
    width: 163px;
}

.w103 {
    width: 103px;
}

.w300 {
    width: 300px;
}

.inscada-filter-btn {
    display: inline-block;
    width: 60px;
    height: 32px;
    border-radius: 4px;
}

.inscada_table_content-popup td[data-field='recipient'] {
    max-width: 160px;
}

.inscada_popup_overflow_style {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.inscada_table_content-popup td[data-field=timestamp] {
    color: #1388FF;
    cursor: pointer;
}

.inscada_table_content-popup td[data-field=timestamp]:hover {
    cursor: pointer;
    text-decoration: underline;
}

.search-inscada-style {
    background-color: #1388FF;
    color: #fff;
}

.inscada-filter-reset {
    background-color: unset;
    border: 1px solid #DCDFE6;
}

.instrument-select-button-outside {
    position: relative;
    display: inline-block;
    padding: 15px 0 0 270px;
    border: 1px solid black;
    border-bottom: white;
    border-radius: 4px 4px 0 0;
}

.btn-checked {
    border-color: #1388FF;
}

.instrument-select-list {
    top: 40px;
    /*border-radius: 0 4px 4px 4px;*/
    /*margin: 0 25px;*/
}

.filler {
    width: 180px;
    height: 40px;
    display: inline-block;
    border-bottom: 1px solid black;
    position: absolute;
}

.instrument-data-detail th {
    line-height: 34px;
}

.instrument-data-detail td {
    padding: 0;
    border: 0;
}

.w114 div {
    white-space: nowrap;
}

.instrument-data-fields,
.inscada-setting-btn,
.inscada-show-hide-fields {
    line-height: 25px;
    width: 100px;
}

.instrument-data-fields,
.inscada-show-hide-fields {
    border-left: 1px solid #EBEEF5;
}

.instrument-fields-chkbox {
    float: right;
    margin-top: 6px !important;
}

.instrument-data-fields div:hover,
.inscada-setting-btn div:hover,
.inscada-show-hide-fields div:hover {
    background-color: #F5F7FA;
    cursor: pointer;
}

.inscada-setting-active {
    border-right: 1px solid #1388FF;
}

.inscada-data-config {
    position: absolute;
    display: flex;
    background-color: white;
    padding: 10px;
    z-index: 2;
    border: 1px solid #EBEEF5;
    left: -5px;
    top: 40px;
    border-radius: 4px;
}


.collapse_material_ico {
    display: inline-block;
    width: 24px;
    height: 12px;
    background: url(../image/svg/collapse-down.svg?v=4);
    vertical-align: middle;
    background-size: 24px 12px;
}

.collapse_material_ico.show {
    background: url(../image/svg/collapse-up.svg?v=4);
    background-size: 24px 12px;
}

.colResizeTool td {
    overflow: hidden;
}

.colResizeTool .name_td {
    overflow: visible;
}

.inscada-show {
    background: url(../image/svg/inscada_show.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    float: right;
    margin-top: 5px;
}

.inscada-hide {
    background: url(../image/svg/inscada_hide.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    float: right;
    margin-top: 5px;
}

.instrument-data-detail td:last-child {
    position: sticky;
    right: 0; /* 将最后一列固定在右侧 */
    background-color: #fff;
}

.instrument-data-detail th:last-child {
    position: sticky;
    right: -1px; /* 将最后一列固定在右侧 */
    background-color: #eee;
    z-index: 1; /* 提高 z-index，确保覆盖在表格内容上 */
    text-align: center;
}

.instrument-data-detail td:first-child {
    position: sticky;
    left: 0; /* 将最后一列固定在右侧 */
    background-color: #fff;
}

.instrument-data-detail th:first-child {
    position: sticky;
    left: 1px; /* 将最后一列固定在右侧 */
    background-color: #eee;
    z-index: 1; /* 提高 z-index，确保覆盖在表格内容上 */
}

/*end by hkk 2019/9/5*/


/*add by hkk 2019/10/15 实验头部自定义项样式*/

.set_define_item {
    border: 1px solid #fff;
    border-radius: 3px;
    cursor: pointer;
    font-size: 20px;
    /*margin-left: 20px;*/
    line-height: 30px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    height: 25px;
}

.set_define_item:hover {
    background-color: #fbfbfb;
    border: 1px solid #f3f3f3;
}

.addDefineItem {
    margin-top: 10px;
}

.added_define_item_part {
    /*margin-right: 20px;*/
    line-height: 32px;
}

.click-menu {
    z-index: 110;
    width: 130px;
    background: white;
    border-radius: 5px;
    text-align: left;
    line-height: 27px;
    left: 0;
    top: 0;
    padding: 5px 0;
}

.click-menu li:hover {
    background: #eee;
}

.click-menu li {
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    color: black;
    padding-left: 10px;
}


/*end by hkk 2019/10/15 实验头部自定义项样式*/


/*begin add by hkk 2019/11/1 仪器库样式*/

/*.
先不对仪器弹窗进行高度限制
addInstrumentHtml {
    max-height: 500px;
    overflow-y: auto;
    position: relative;
}*/

.instrument_hide {
    display: none;
}

.addInstrumentCheck li,
.addInstrumentHtml li {
    margin-left: 10px;
    margin-bottom: 10px;
}

.addInstrumentCheck input {
    width: 300px;
}

.addInstrumentHtml input {
    width: 250px;
}

.addInstrumentHtml label {
    width: 130px;
    display: inline-block;
    text-align: right;
}


.addInstrumentCheck label {
    width: 80px;
    display: inline-block;
    text-align: right;
}

.instrument_check_record_check label,
.instrument_operate_record_check label,
.instrument_repair_record_check label,
.addRepairRecordUl label {
    width: 105px;
    display: inline-block;
    text-align: right;
    vertical-align: middle;
    margin-right: 10px;
}

.instrument_check_record_check .check_reason label,
.instrument_operate_record_check .check_reason label,
.instrument_repair_record_check .check_reason label {
    vertical-align: top;
}

.instrument_check_record_check .check_result label,
.instrument_operate_record_check .check_result label,
.instrument_repair_record_check .check_result span {
    margin-right: 20px;
}

.instrument_check_record_check li,
.instrument_operate_record_check li,
.instrument_repair_record_check li,
.addRepairRecordUl li {
    margin-left: 5px;
    margin-bottom: 10px;
}

.instrument_check_record_check textarea,
.instrument_operate_record_check textarea,
.instrument_repair_record_check textarea {
    width: 300px;
    height: 100px;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ddd
}

.instrument_check_record_check input[type='text'],
.instrument_check_record_check input[type='password'],
.instrument_operate_record_check input[type='text'],
.instrument_operate_record_check input[type='password'],
.instrument_repair_record_check input[type='password'],
.instrument_repair_record_check input[type='text'] {
    width: 300px;
}

.instrument_repair_record_ico_1 {
    background: url(../image/allow.svg);
    width: 23px;
    height: 23px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
}


.instrument_repair_record_ico_2 {
    background: url(../image/refuse.svg);
    width: 18px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
}


.addRepairRecordUl input {
    width: 300px;
}

.addRepairRecordUl li.required label:before {
    content: '*';
    color: orangered;
    margin-right: 2px;
}

.addInstrumentHtml label.required:before {
    content: '*';
    color: orangered;
    margin-right: 2px;
}

.create_book label.required:before {
    content: '*';
    color: orangered;
    margin-right: 2px;
}

.review label.required:before {
    content: '*';
    color: orangered;
    margin-right: 2px;
}

.addInstrumentHtml input:disabled {
    background-color: #eee;
}

.addInstrumentHtml select:disabled {
    background-color: #eee;
}

.more_filter_sign {
    border: 1px solid blue
}

.instrument_book_page .modal-body th,
.instrument_history_page .modal-body th {
    background: #eee;
    border: 1px solid #ddd;
}

.instrument_book_page .modal-body td,
.instrument_history_page .modal-body td {
    line-height: 30px;
}

.instrument_book_page .instrument_book input {
    text-indent: 5px;
    line-height: 18px;
    padding: 6px 0;
    border: 1px solid #e5e5e5;
    outline: 0;
}

input.datetimepicker:focus {
    border-color: #1388ff;
    box-shadow: 0 0 3px rgba(19, 136, 255, .5);
}


.instrument_cancel_record_reason,
.instrument_undo_check_reason,
#book_instrument_remark,
#repair_instrument {
    width: 400px;
    height: 100px;
    outline: none;
    padding: 4px 8px;
    color: #666;
    border: 1px solid #dcdcdc;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    border-radius: 5px;
}

.instrument_cancel_record_reason:focus,
.instrument_undo_check_reason:focus,
#book_instrument_remark:focus,
#repair_instrument:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}

.time_to {
    margin: 0 4px;
}

@media (min-width: 1045px) {
    .modal-dialog-970 {
        width: 970px;
    }

    .modal-dialog-840 {
        width: 840px;
    }

    .modal-dialog-1045 {
        width: 1045px;
    }
}

.instruments_pop_table .trBlue {
    background: #e1f0ff;
}


.instrument_record_page .instrument_file,
.instrument_add_check_record_page .instrument_file,
.instrument_begin_operate_record_page .instrument_file,
.instrument_add_repair_record_page .instrument_file,
.instrument_check_page .instrument_file,
.instrument_page .instrument_file {
    display: inline-block;
    border: 2px solid #e5e5e5;
    width: 250px;
    background: white;
    vertical-align: top;
    min-height: 100px;
    height: auto;
    max-height: 150px;
    overflow-y: auto;
}


.instrument_record_page .instrument_file,
.instrument_add_check_record_page .instrument_file,
.instrument_begin_operate_record_page .instrument_file,
.instrument_add_repair_record_page .instrument_file,
.instrument_check_page .instrument_file {
    width: 300px;
}

/*instrument_file输入框的样式*/
.instrument-run-file-box {
    display: inline-block;
    border: 2px solid #e5e5e5;
    width: 300px;
    background: white;
    vertical-align: top;
    min-height: 100px;
    height: auto;
    max-height: 150px;
    overflow-y: auto;
}

/*instrument_file输入框的样式-end*/

.instruments_table tr {
    cursor: pointer;
}


.instruments_operate_record_table tr.lightRedBackground,
.instruments_repair_record_table tr.lightRedBackground,
.instruments_check_record_table tr.lightRedBackground {
    background: #fff2f1;
}

.instruments_operate_record_table tr.lightBlueBackground,
.instruments_repair_record_table tr.lightBlueBackground,
.instruments_check_record_table tr.lightBlueBackground {
    background: #E7F4FF;
}

.instruments_operate_record_table tr:hover,
.instruments_repair_record_table tr:hover,
.instruments_check_record_table tr:hover,
.instruments_table tr:hover {
    background: #e7f4ff;
}

.instruments_table td.status-0 {
    background-color: #D9D9D9;
}

.instruments_table td.status-1 {
    background-color: #C7EFCE;
}

.instruments_table td.status-2 {
    background-color: #FDE9D9;
}

.instruments_table td.status-3 {
    background-color: #FEEB9B;
}

.instruments_table td.status-4 {
    background-color: #FEC8CE;
}

.instruments_table td {
    height: 50px !important;
}

.instruments_table td[data-checkSituation='1'],
.instruments_table td[data-checkSituation='0'] {
    color: green;
}


.instrument_add_check_record_page .single_detail_file:hover,
.instrument_begin_operate_record_page .single_detail_file:hover,
.instrument_add_repair_record_page .single_detail_file:hover,
.instrument_check_page .single_detail_file:hover,
.instrument_page .single_detail_file:hover {
    background: #e5e5e5;
    border-radius: 4px;
}

.instrument_add_check_record_page .single_detail_file,
.instrument_begin_operate_record_page .single_detail_file,
.instrument_add_repair_record_page .single_detail_file {
    padding: 5px;
}

.instrument_page .disabled_style {
    background: #eee;
}

.duplicatedModule {
    margin-right: 20px;
}

.duplicatedModules {
    margin-top: 10px;
}

.enter_InProject_sign {
    background: url(../image/link_out.svg);
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.enter_InProject_sign:hover {
    cursor: pointer;
    background: url(../image/link_out_hover.svg);
}

.inscada-sign-ico{
    background: url(../image/svg/inscada_sign.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-edit-ico {
    background: url(../image/eln.png) -462px -252px;
    width: 23px;
    height: 23px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.inscada-edit-ico {
    background: url(../image/svg/inscada_edit.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-receive-ico {
    background: url(../image/svg/import-new.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}

.inscada-receive-ico {
    background: url(../image/svg/inscada_receive.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-instant-receive-ico {
    background: url(../image/svg/instrument_instant_receive.svg) no-repeat;
    width: 23px;
    height: 23px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-download-ico {
    background: url(../image/svg/instrument_file_download.svg) no-repeat;
    width: 23px;
    height: 23px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-end-ico {
    background: url(../image/instrument_end.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-undo-ico {
    background: url(../image/instrument_undo.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-recover-ico {
    background: url(../image/instrument_recover.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-delete-ico {
    background: url(../image/eln.png) -443px -222px;
    width: 23px;
    height: 23px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.inscada-delete-ico {
    background: url(../image/svg/inscada_delete.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-repair-ico {
    background: url(../image/svg/instrument_repair.svg) no-repeat;
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-repair-ico:hover {
    background: url(../image/svg/instrument_repair_hover.svg) no-repeat;
}

.instrument-repair-ico.disabled {
    background: url(../image/svg/instrument_repair_disabled.svg) no-repeat;
}

.instrument-book-ico {
    background: url(../image/svg/instrument_book_new.svg) no-repeat;
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-book-ico.disabled {
    background: url(../image/svg/instrument_book_new_disabled.svg) no-repeat;
}

.instrument-book-ico:hover {
    background: url(../image/svg/instrument_book_new_hover.svg) no-repeat;
}

.instrument-history-ico {
    background: url(../image/instrument_history.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.inscada-history-ico {
    background: url(../image/svg/instrument_history.svg) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.history-ico {
    background: url(../image/svg/history.svg) no-repeat;
    width: 25px;
    height: 25px;
    display: inline-block;
    vertical-align: middle;
    /*margin: 2px;*/
    cursor: pointer;
}

.history-ico:after {
    content: '';
}

.instrument-reminder-ico {
    background: url(../image/svg/instrument_reminder.svg) no-repeat;
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-reminder-ico:hover {
    background: url(../image/svg/instrument_reminder_hover.svg) no-repeat;
}

.instrument-check-ico {
    background: url(../image/svg/instrument_check.svg) no-repeat;
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-check-ico:hover {
    background: url(../image/svg/instrument_check_hover.svg) no-repeat;
}

.instrument-repair-record-ico {
    background: url(../image/svg/instrument_repair_record.svg) no-repeat;
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-repair-record-ico:hover {
    background: url(../image/svg/instrument_repair_record_hover.svg) no-repeat;
}

.instrument-usage-statistics-ico {
    background: url(../image/static_time.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-setting-ico {
    background: url(../image/instrument_setting.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument-running-record-ico {
    background: url(../image/svg/instrument_running_new.svg) no-repeat;
    width: 30px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-running-record-ico:hover {
    background: url(../image/svg/instrument_running_new_hover.svg) no-repeat;
}

.instrument-inscada-ico {
    background: url(../image/svg/instrument_inscada.svg) no-repeat;
    width: 30px;
    height: 30px;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    border: 5px solid rgba(255, 255, 255, 0);
    cursor: pointer;
}

.instrument-inscada-ico:hover {
    background: url(../image/svg/instrument_inscada_hover.svg) no-repeat;
}

i.instrument-move-down {
    background: url(../image/instrument_move_in.svg) no-repeat;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px !important;
    cursor: pointer;
    left: 0px;
    top: -3px;
    position: relative;
    width: 20px;
    float: right;
    margin-right: 15px;
    margin-top: 6px;
}


i.instrument-move-up {
    background: url(../image/instrument_move_out.svg) no-repeat;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px !important;
    cursor: pointer;
    left: 0px;
    top: -2px;
    position: relative;
    width: 20px;
    float: right;
    margin-right: 15px;
    margin-top: 6px;
}

i.instrument-move-up:hover,
i.instrument-move-down:hover {
    border-radius: 3px;
    border: 1px solid #ccc;
    height: 22px;
    width: 22px;
}

.instrumentsFieldsConfig li.col-item,
.instrumentShowModalContents li.col-item {
    height: 40px;
}

.instrumentShowModalContents li.col-item i {
    display: none;
}


.instrumentShowModalContents li.col-item:hover i {
    display: inline-block;
}


.instrumentShowModalContents li.col-item label span {
    max-width: 90px;
    display: inline-block;
    vertical-align: bottom;
    overflow: hidden;
    text-overflow: ellipsis;
}


.instrument-check-ico-no-need {
    background: url(../image/svg/instrument_check_no_need.svg) no-repeat;
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: not-allowed;
    opacity: 0.5;
}

.instrument-operation-style .no-need {
    cursor: not-allowed;
    opacity: 0.5;
}

.gray {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);

    filter: grayscale(100%);

    filter: gray;
    cursor: not-allowed;
    opacity: 0.5;
}

.batch-add-instruments-tip {
    border: 1px solid #eee;
    color: red;
    margin-top: 20px;
    padding: 5px;
}

.batch-add-instruments-tip-1 {
    width: 40px;
    height: 50px;
    float: left;
}

.no_instruments_result {
    text-align: left;
    width: 460px;
    margin: 0 auto;
}

.no_instruments_result1 {
    text-align: center;
}

.no_instruments_result2 {
    margin-left: 60px;
}


.instrument-setting-ico:hover,
.instrument-usage-statistics-ico:hover,
.trace_detail_icon:hover,
.instrument-undo-ico:hover,
.instrument-end-ico:hover, {
    border: 1px solid #ccc;
    border-radius: 4px;
}

.instrument-operation-style {
    margin: 0 auto;
    max-width: 160px;
}

.trace_detail_icon {
    background: url(../image/eln.svg);
    width: 20px;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.instrument_reminder_setting .reminder_period_input, .collaboration_reminder_setting .reminder_period_input {
    width: 42px;
    text-align: center;
    margin: 0 5px;
    border-radius: 3px;
}

.instrument_reminder_setting .reminder_content, .collaboration_reminder_setting .reminder_content {
    width: 500px;
    height: 200px;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    padding: 10px;
}

.instrument_reminder_setting .del-tp, .collaboration_reminder_setting .del-tp {
    position: absolute;
    display: none;
    background: url(../image/eln.png) -44px top;
    width: 11px;
    height: 11px;
    opacity: 1;
    top: -5px;
    right: -5px;
}

.instrument_reminder_setting .date-picker-ico, .collaboration_reminder_setting .date-picker-ico {
    position: absolute;
    font-size: 24px;
    right: 0;
    top: 3px;
}

.instrument_reminder_setting .tp-box, .collaboration_reminder_setting .tp-box {
    height: 40px;
    margin-right: 10px;
}

.instrument_check_page .date-picker-ico {
    position: absolute;
    font-size: 24px;
    right: 0;
    top: 3px;
}

.instrument_check_table {
    max-height: 700px;
    overflow-y: auto;
}

.instrument_check_title {
    margin-bottom: 52px;
}

.instrument_delete_content {
    width: 420px;
    height: 100px;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    padding: 10px;
    outline-color: #4d90fe;
}

.instrumentShowModalContents .title {
    font-size: 16px;
}


.instrumentShowModalContents .title.relative {
    position: relative;
}

.instrumentShowModalContents div.collapse {
    width: 15px;
    height: 15px;
    cursor: pointer;
    z-index: 1000;
    border-right: 2px solid #1388ff;
    border-top: 2px solid #1388ff;
    -webkit-transform: rotate(135deg);
    border-left: 2px solid transparent;
    border-bottom: 2px solid transparent;
    margin-left: 10px;
    vertical-align: top;
    display: inline-block;
    position: absolute;
}

.instrumentShowModalContents div.collapse:hover {
    width: 17px;
    height: 17px;
}

.instrumentShowModalContents div.collapse.fold {
    -webkit-transform: rotate(45deg);
    top: 3px;
    left: -40px;
}

.instrumentShowModalContents div.collapse.unfold {
    -webkit-transform: rotate(135deg);
    top: -1px;
    left: -40px;
}

.import-edit-history-wrap .hide_view_detail {
    position: absolute;
    left: 20%;
    top: 30%;
    background: #fff;
    width: 500px;
    overflow: auto;
    max-height: 300px;
    margin: 10px;
}

.instrument_add_check_record_page input.check-record-input {
    height: 28px;
    padding: 4px 8px;
    color: #666;
    border: 1px solid #dcdcdc;
    width: 50px;
}

.instrument_add_check_record_page input.check-record-input:hover {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}

.import-edit-history-wrap .hide_view_detail th .close {
    font-size: 24px;
    position: absolute;
    right: 10px;
    opacity: 0.4;
    cursor: pointer;
}

.import-edit-history-wrap .hide_view_detail th .close:hover {
    font-size: 26px;
    opacity: 0.8;
}

.import-edit-history-wrap .hide_view_detail th.row-title {
    width: 50px;
}

.sort-field {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    height: 34px;
    width: 24px;
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
}

.add-field {
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
}

.copy_row .disabled {
    cursor: not-allowed;
    color: #ccc;
}

.sort-caret {
    width: 0;
    height: 0px;
    border: 6px solid transparent;
    position: absolute;
    left: 5px;
}

.add-caret {
    width: 0;
    height: 0px;
    border: 6px solid transparent;
    position: absolute;
    left: -26px;
}

.sort-caret.ascending {
    border-bottom-color: #c0c4cc;
    top: 3px;
}

.add-caret.add-one {
    border-bottom-color: #c0c4cc;
    top: -5px;
}

.add-caret.sub-one {
    border-top-color: #c0c4cc;
    bottom: -5px;
}

.copy_input {
    margin: 0 5px;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    max-width: 70px;
    text-align: center;
    padding-right: 25px;
}

.sort-caret.descending {
    border-top-color: #c0c4cc;
    bottom: 5px;
}

.ascending .sort-caret.ascending {
    border-bottom-color: #409eff;
}

.descending .sort-caret.descending {
    border-top-color: #409eff;
}

.sortThDiv {
    display: inline-block;
}

/*end add by hkk 2019/11/1 仪器库样式*/

.fullScreen {
    display: block;
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 1000000;
    top: 0;
    left: 0;
}

.overflowHidden {
    overflow: hidden;
}


/*begin add by zhhj 2019/11/20 实验提醒设置样式*/

.experiment_reminder_setting .reminder_period_input {
    width: 42px;
    text-align: center;
    margin: 0 5px;
    border-radius: 3px;
}

.experiment_reminder_setting .date-picker-ico {
    position: absolute;
    font-size: 24px;
    right: 0;
    top: 3px;
}

.experiment_reminder_setting .tp-box {
    height: 40px;
    margin-right: 10px;
}

.experiment_reminder_setting .del-tp {
    position: absolute;
    display: none;
    background: url(/image/eln.png) -44px top;
    width: 11px;
    height: 11px;
    opacity: 1;
    top: -5px;
    right: -5px;
}

.tp-box:hover .del-tp {
    display: block;
}

.experiment_reminder_setting .reminder_content {
    width: 500px;
    height: 200px;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    padding: 10px;
}

.trace_detail_table tr:hover {
    background-color: #e6e6e6;
}

.exp_history_body_button {
    margin: 10px;
}

.lineHeight28 {
    line-height: 28px;
}

.height28 {
    height: 28px;
    line-height: 28px;
}


/*end add by zhhj 2019/11/20 实验提醒设置样式*/

.exp-list-cols .col-item {
    display: inline-block;
    float: left;
    line-height: 30px;
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 5px;
    cursor: pointer
}

.exp-list-cols .col-item:hover {
    background-color: #9e9e9e42;
    border-radius: 5px;
}

.company_general_setting {
    margin-left: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 30px;
}

.company_general_setting .tip-box {
    margin-left: 20px;
}

.company_general_setting .tip-box .left-side {
    position: absolute;
    line-height: 30px;
}

.company_general_setting .tip-box .right-side {
    margin-left: 30px;
}

.company_general_setting .tip-box .tip-item {
    line-height: 30px;
}

.collaboration-tips {
    background: url(/image/svg/tips.svg) no-repeat
}

.template-history {
    margin-top: -20px;
    margin-left: 20px;
}

.template-history .basic_info {
    font-size: 14px;
}

.template-history .basic_info label {
    display: inline-block;
    width: auto;
    min-width: 80px;
}

.template-history .module-item .title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
    text-decoration: underline;
}

.template-history tr {
    height: 30px;
}

.template-history tr.head {
    background-color: #f8f8f8;
    color: #000000;
}

.template-history td {
    border: 1px solid #ddd !important;
}

.ztree-box {
    position: absolute;
    display: none;
    border: 1px solid #dcdcdc;
    background-color: #ffffff;
    z-index: 99;
}

.ztree-box .search-box {
    padding: 5px 5px 0 5px;
}

.ztree-box .search-box .search-input {
    width: 100%;
}

.ztree-box ul.ztree {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: auto;
}

.set-role-ico {
    height: 24px;
    background: url("../image/svg/role.svg") no-repeat center;
    background-size: 21px;
    cursor: pointer;
}

.set-other-ico {
    height: 24px;
    background: url("../image/svg/other.svg") no-repeat center;
    background-size: 20px;
    cursor: pointer;
}

.break-inline {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.exp-link {
    clear: both;
    float: right;
}

.exp-link-icon {
    background: url(../image/link-grey.svg);
    height: 32px;
    width: 32px;
    cursor: pointer;
    margin-top: 4px;

}

.exp-link-icon:hover {
    background: url(../image/link-deepgrey.svg);
}

.exp-link-icon.active {
    background: url(../image/link-blue.svg);
    height: 32px;
    width: 32px;
    cursor: pointer;
    margin-top: 4px;

}

.exp-link-icon.active:hover {
    background: url(../image/link-deepblue.svg);
}

.exp-link-list {
    z-index: 10;
    background-color: white;
    width: 205px;
    border: 1px solid #eaebee;
    top: 40px;
    right: 2px;
    box-shadow: 0px 0px 10px rgb(225, 225, 225);
}

.exp-link-box {
    overflow-y: auto;
    max-height: 500px;
}

.weather-box .exp-link-box {
    text-indent: 7px;
}

.exp_link_modal .exp-link-box {
    width: 400px;
    background-color: white;
}

.exp-link-search {
    /*border: 1px solid #EEE;*/
    padding: 5px;
}

.exp_link_modal .exp-link-search {
    width: 400px;
    line-height: 20px;
    background-color: white;
    border: none;
}

.exp-link-item {
    padding: 5px;
}

.exp-link-item:hover {
    background-color: #EEE;
}

.exp-link-item.manual {
    /*background-color: rgba(19,136,255,.15);*/
}

.exp-link-item.manual:hover {
    background-color: #F2F2F2;
}

.exp_link_modal .exp-link-option {
    text-indent: 10px;
    width: calc(100% - 25px);
    display: inline-block;
}

.exp-link-option:hover {
    text-decoration: underline;
    cursor: pointer;
}

.add-exp-link {
    display: inline-block;
    font-size: 15px;
}

.add-exp-link-icon {
    cursor: pointer;
    position: relative;
    color: #259;
    margin-left: 5px;
}

.wms-sample-box .add-exp-link-icon {
    color: white;
}

.add-exp-link-box {
    border: 1px solid #eaebee;
    position: absolute;
    z-index: 10;
}

.wms-sample-box .add-exp-link-box {
    width: 200px;
}

.add-exp-link-item {
    background-color: white;
    padding: 5px;
    font-size: 14px;
}

.add-exp-link-item:hover {
    background-color: #EEE;
    cursor: pointer;
}

.add-link-text {
    line-height: 20px;
    padding: 5px;
    width: 600px;
    border: 1px solid #DDD;
    font-size: 16px;
}

.search-link-wrap {
    line-height: 25px;
    margin-bottom: 15px;
}

.search-link-wrap select {
    border: 1px solid #DDD;
    height: 25px;
}

.search-link-wrap input {
    border: 1px solid #DDD;
    width: 418px;
    line-height: 15px;
    padding: 5px;
    height: 28px;
    vertical-align: middle;
}

.search-link-wrap button {
    width: 80px;
    height: 28px;
    margin-left: 5px;
}

.link-search-table {
    max-height: 450px;
    overflow-y: auto;
    display: block;
}

.link-search-table tbody {
    background-color: white;
}

.batch-add-link-button {
    text-align: right;
    padding: 15px;
}

.batch-add-link-pop {
    width: 400px;
    height: 300px;
    position: absolute;
    top: 100px;
    left: 100px;
    background-color: #FFF;
    padding: 20px;
    border: 1px solid #EEE;
}

.batch-search-key {
    width: 350px;
    height: 200px;
    border: 1px solid #DDD;
    padding: 5px;
}
.batch-search-key:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}

.delete-link {
    display: none;
}

.delete-link:hover {
    cursor: pointer;
}

.exp_link_modal .delete-link {
    background: url(../image/eln.png) -443px -222px;
    width: 23px;
    height: 23px;
}

.wms-sample-box .delete-link {
    color: red;
}

.project-task-selector-box {
    position: relative;
}
.project-task-selector-box .clear-ico {
    width: 18px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 9px;
    bottom: 0;
    margin: auto;
    color: #999;
}

.project-task-selector-box .project-task-clear-btn {
    visibility: hidden;
}

.project-task-selector-box:hover .project-task-clear-btn {
    visibility: visible;
}

/* 可见人员下拉框组件 start */
.visible-user-selector-box {
    position: relative;
}

.visible-user-selector-box .visible-user-input {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    border: 1px solid #dcdcdc;
    cursor: pointer;
}

.visible-user-selector-box .visible-user-input:focus {
    border: 1px solid #66afe9;
}

.visible-user-selector-box .clear-ico {
    /*line-height: 28px;*/
    width: 18px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    color: #999;
}

.visible-user-selector-box .visible_user_clear_check_btn {
    visibility: hidden;
}

.visible-user-selector-box:hover .visible_user_clear_check_btn {
    visibility: visible;
}

.visible-user-selector-box .visible-user-search-box {
    position: absolute;
    width: 300px;
    height: 270px;
    background: #FFF;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 5px 0;
    z-index: 91;
    /*display: none;*/
}

.visible-user-selector-box .visible-user-btn-box {
    width: 299px;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 5px;
    position: absolute;
    padding-left: 10px;
    line-height: 28px;
}

.visible-user-selector-box .visible-user-btn-box .visible-user-search-input {
    width: 125px;
    margin-left: 5px;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    padding-right: 0;
}

.visible-user-selector-box .visible-user-btn-box i {
    width: 18px;
    height: 18px;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    left: 0px;
    top: 0px;
}


.visible-user-selector-box .visible-user-ztree-box {
    position: absolute;
    top: 42px;
    max-height: 225px;
    min-height: 24px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.visible-user-selector-box .visible-user-set-ico {
    background: url("/image/ico/user-grey.svg");
}

.visible-user-selector-box .visible-user-group-set-ico {
    background: url("/image/ico/user-group-grey.svg");
}

.visible-user-selector-box .visible-user-department-set-ico {
    background: url("/image/ico/user-department-grey.svg");
}

.visible-user-selector-box .active .visible-user-set-ico {
    background: url("/image/ico/user-blue.svg");
}

.visible-user-selector-box .active .visible-user-group-set-ico {
    background: url("/image/ico/user-group-blue.svg");
}

.visible-user-selector-box .active .visible-user-department-set-ico {
    background: url("/image/ico/user-department-blue.svg");
}

.visible-user-selector-box .all-collapse .all-expand-collapse-ico {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 18 18' style='enable-background:new 0 0 18 18;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;stroke:%238A8A8A;stroke-width:1.2;stroke-miterlimit:10;%7D%0A%3C/style%3E%3Cpath class='st0' d='M16.5,12L9.1,4.6C9,4.6,9,4.6,8.9,4.6L1.5,12c-0.1,0.1,0,0.3,0.1,0.3h14.7C16.5,12.3,16.6,12.1,16.5,12z'/%3E%3C/svg%3E");
}

.visible-user-selector-box .all-expand .all-expand-collapse-ico {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 18 18' style='enable-background:new 0 0 18 18;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:none;stroke:%238A8A8A;stroke-width:1.2;stroke-miterlimit:10;%7D %3C/style%3E%3Cpath class='st0' d='M1.5,5.9l7.3,7.3c0.1,0.1,0.2,0.1,0.2,0l7.3-7.3c0.1-0.1,0-0.3-0.1-0.3H1.7C1.5,5.6,1.4,5.8,1.5,5.9z'/%3E%3C/svg%3E");
}

/* 可见人员下拉框组件 end */

.padding3 {
    padding: 3px;
}

.toggleshow {
    height: 30px;
    background: url("/image/svg/arrow_hide.svg") no-repeat center;
    background-size: 15px;
    cursor: pointer;
    width: 30px;
    transform: rotate(90deg) scaleY(1)
}

.togglehide {
    height: 30px;
    background: url("/image/svg/arrow_show.svg") no-repeat center;
    background-size: 15px;
    cursor: pointer;
    width: 30px;
}


.submit_wo_ico_block {
    height: 32px;
    width: 32px;
    text-align: center;
    line-height: 32px;
}

.submit_wo_ico_block:hover {

    border: 1px solid #3499ff;
    border-radius: 3px;
    cursor: pointer;
    z-index: 1;
}


.search_wo_ico {
    background: url("../image/wo_icos/wo_search_blue.svg") center no-repeat;
    height: 20px;
    width: 20px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.filter_wo_ico {
    background: url("../image/wo_icos/wo_filter_blue.svg") center no-repeat;
    height: 21px;
    width: 20px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.display_hidden_column {
    background: url("../image/wo_icos/wo_display_hidden_column_blue.svg") center no-repeat;
    height: 20px;
    width: 20px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.wo_export_btn {
    height: 32px;
    width: 32px;
    text-align: center;
    line-height: 32px;
}

.wo_export {
    background: url("../image/wo_icos/wo_export_btn.svg") center no-repeat;
    height: 20px;
    width: 20px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.wo_export:hover {
    background: url("../image/wo_icos/wo_export_btn_hover.svg") center no-repeat;
    height: 20px;
    width: 20px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.info-box {
    position: relative;
    cursor: pointer;
}

.info-ico:before {
    content: "\e901";
    color: #CCC;
    font-size: 16px;
}

.info-box .info-content {
    position: absolute;
    font-size: 13px;
    width: 250px;
    border: 1px solid #ddd;
    box-shadow: 0 0 8px rgb(0 0 0 / 20%);
    border-radius: 5px;
    padding: 8px 12px;
    line-height: 20px;
    display: none;
    background: #fff;
    text-align: left;
    z-index: 9999;
    cursor: default;
}

.info-box:hover .info-content {
    display: block;
}

/* 设置审批人员集合 组件 start */
.user-collection-select {
    position: relative;
}

.user-collection-select .user-collection-input {
    border: 1px solid #dcdcdc;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.user-collection-select .user-collection-input:focus {
    border: 1px solid #dcdcdc;
}

.user-collection-select span {
    /*vertical-align: middle;*/
}

.user-collection-select .clear-ico {
    line-height: 28px;
    width: 18px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    color: #999;
}

.user-collection-select .user_collection_clear_check_btn {
    visibility: hidden;
}

.user-collection-select:hover .user_collection_clear_check_btn {
    visibility: visible;
}

.approval-users-chain-box .change_method_btn {
    width: 80px;
    position: absolute;
    height: 28px;
    border: 1px solid #dcdcdc;
}

.approval-users-chain-box .choose-area {
    margin-left: 80px;
}

.approval-users-chain-box .chain-box select {
    border: 1px solid #dcdcdc;
    height: 28px;
}

/* 设置审批人员集合 组件 end */

/*工单筛选页操作按钮样式*/
.wo-eln-setting-down-btn .down-box {
    background: #1281f2;
    width: 24px;
    height: 32px;
}

.wo-eln-setting-down-btn.disabled .blue-btn {
    background-color: #ccc;
}

.wo-eln-setting-down-btn.disabled .down-box {
    background: #ccc;
}

.wo-eln-setting-down-btn .opt-more {
    text-align: center;
}

/*下拉箭头样式*/
.wo-eln-setting-down-btn .down-box:after {
    content: '';
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #fff;
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    top: 19px;
    right: 13px;
}

/*-end 工单筛选页操作按钮样式*/


div.drag-and-drop-border {
    border-color: #00b7ee;
}

.company-exp-checker-ico {
    background: url("../image/ico/exp_checker.svg") center no-repeat;
    height: 16px;
    width: 16px;
    display: inline-block;
    vertical-align: text-top;
    cursor: pointer;
}

.set-compare1-ico {
    height: 40px;
    background: url("../image/svg/compare_1.svg") no-repeat center;
    /*background-size: 20px;*/
    cursor: pointer;
}

.set-compare2-ico {
    height: 40px;
    background: url("../image/svg/compare_2.svg") no-repeat center;
    background-size: 20px;
    cursor: pointer;
}

#layout_left_bar {
    position: fixed;
    left: 262px;
    top: 115px;
    width: 3px;
    height: 775px;
    opacity: 0;
    background: rgb(***********);
    z-index: 999;
    cursor: ew-resize;
}

.approval-btn-box {
    margin-bottom: 10px;
    display:flex;
    justify-content: end;
}

/*审批相关样式 start*/
.approval-btn-box a {
    display: inline-block;
    height: 32px;
    line-height: 30px;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    color: white;
    background: #1890ff;
    vertical-align: top;
    padding-right: 5px;
    margin: 0px 2px;
}

.approval-btn-box span {
    width: 32px;
    height: 30px;
    padding: 4px;
    display: inline-block;
    line-height: 30px;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    vertical-align: top;
}

.approval-btn-box a:hover {
    text-decoration: none;
    background: #096dd9;
}

.approval-btn-box .ico, .approval_operate .ico {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.approval-btn-box .ico.agree, .approval-btn-box .ico.refuse {
    position: relative;
    margin: 0 5px;
    vertical-align: text-bottom;
}

.approval-btn-box .ico.agree {
    background: url(../image/svg/check_white.svg) no-repeat center;
}

.approval-btn-box .ico.refuse {
    background: url(../image/svg/cross_white.svg) no-repeat center;
}

.approval-btn-box .ico.filter {
    background: url(../image/svg/filter_mark.svg);
}

.approval-btn-box .ico.filter:hover {
    background: url(../image/svg/filter_mark_hover.svg);
}

.approval-btn-box .ico.history {
    background: url(../image/svg/approval_history.svg);
}

.approval-btn-box .ico.history:hover {
    background: url(../image/svg/approval_history_hover.svg);
}

.approval_operate a {
    display: inline-block;
    width: 30px;
    height: 30px;
    padding: 7px;
}

.approval_operate .ico.agree {
    background: url(../image/svg/check_mark.svg);
}

.approval_operate .ico.agree:hover {
    background: url(../image/svg/check_mark_blue.svg);
}

.approval_operate .ico.refuse {
    background: url(../image/svg/cross_mark.svg);
}

.approval_operate .ico.refuse:hover {
    background: url(../image/svg/cross_mark_blue.svg);
}

.approval_operate .ico.view {
    background: url(../image/svg/approval_view.svg);
}

.approval_operate .ico.view:hover {
    background: url(../image/svg/approval_view_hover.svg);
}

/* 审批进度弹框 start */
.approval-progress-dialog {
    max-width: 780px;
    max-height: 600px;
    overflow: auto;
}

.approval-progress-dialog .horizontal-node {
    white-space: nowrap;
}

.approval-progress-dialog .horizontal-node .node {
    display: inline-flex;
    width: 100px;
    text-align: center;
    flex-direction: column;
    flex-shrink: 0;
    vertical-align: top;
}

.approval-progress-dialog .horizontal-node .node .node-icon-start,
.approval-progress-dialog .horizontal-node .node .node-icon-item,
.approval-progress-dialog .horizontal-node .node .node-icon-end {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #ddd;
    position: relative;
    margin: 0 auto;
    line-height: 30px;
    color: #fff;
}

.approval-progress-dialog .horizontal-node .node .node-icon-start {
    background-color: #1388FF;
}

.approval-progress-dialog .horizontal-node .node .node-icon-start:after,
.approval-progress-dialog .horizontal-node .node .node-icon-item:after {
    content: '';
    display: inline-block;
    width: 40px;
    height: 4px;
    background-color: inherit;
    position: absolute;
    left: 29px;
    top: 13px;
}

.approval-progress-dialog .horizontal-node .node .node-icon-end:before,
.approval-progress-dialog .horizontal-node .node .node-icon-item:before {
    content: '';
    display: inline-block;
    width: 40px;
    height: 4px;
    background-color: inherit;
    position: absolute;
    right: 29px;
    top: 13px;
}

.approval-progress-dialog .horizontal-node .node .ellipsis {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: default;
    margin: 0 auto;
}

.approval-progress-dialog .horizontal-node .node .time-display {
    height: inherit;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.approval-progress-dialog .vertical-node .node {
    color: #22C048;
    display: flex;
}

.approval-progress-dialog .vertical-node .node .node-icon {
    width: 30px;
    height: 60px;
}

.approval-progress-dialog .vertical-node .node .node-icon .step {
    width: 16px;
    height: 16px;
    border: solid 1px currentColor;
    border-radius: 100%;
    position: relative;
}

.approval-progress-dialog .vertical-node .node .node-icon .step:before {
    content: '';
    display: block;
    width: 10px;
    height: 10px;
    background-color: currentColor;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
}

/*.approval-progress-dialog .vertical-node .node .node-icon .step:after {*/
/*    content: '';*/
/*    display: block;*/
/*    width: 1px;*/
/*    height: 44px;*/
/*    background-color: currentColor;*/
/*    position: absolute;*/
/*    top: 15px;*/
/*    left: 7px;*/
/*}*/

.approval-progress-dialog .vertical-node .node .node-icon .step.last:after {
    height: 0;
}

.approval_status_remind {
    border-bottom: #ccc 1px solid;
}

.approval_status_remind:hover {
    border-bottom: #1388FF 1px solid;
}

/* 审批进度弹框 end */
/*审批相关样式 end*/
.img-instrument {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    margin-right: 10px;
}

.img-instrument-large {
    position: absolute;
    display: none;
    left: 300px;
    transform: scale(15);
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.img-instrument:hover + .img-instrument-large {
    display: inline;
}

#dropdownMenu1 {
    width: 28px;
    height: 28px;
    margin-bottom: 2px;
}

.del-ico:before {
    content: "\e908";
    color: #d81e06;
    font-size: 24px;
    cursor: pointer;
    vertical-align: middle;
}

.my-share-books {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 90%;
}

.preview-excel:hover {
    cursor: pointer;
}

.catalog-dropdown .dropdown-menu {
    border-radius: 3px;
    padding: 5px 0;
    width: 205px;
    max-height: 500px;
    overflow: auto;
    cursor: default;
}

.catalog-dropdown .dropdown-menu li {
    display: flex;
    align-items: center;
    height: 30px;
    padding: 0 5px;
}

.catalog-dropdown .dropdown-menu li.hover {
    /*font-size: 14px;*/
    background-color: #EEEEEE;
}

.catalog-dropdown .dropdown-menu li .ico-drag {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/image/svg/drag.svg");
    cursor: pointer;
}
.instruments_manage_page .ico-drag,
.instruments_operate_record_table .ico-drag,
.instruments_repair_record_table .ico-drag,
.instruments_check_record_table .ico-drag{
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("/image/svg/drag.svg");
    cursor: pointer;
}

.catalog-dropdown .dropdown-menu li span {
    display: inline-block;
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.catalog-dropdown .dropdown-menu li .ico-copy {
    display: none;
    width: 16px;
    height: 16px;
    margin-left: auto;
    background: url("/image/svg/copy.svg");
    cursor: pointer;
}

.catalog-dropdown .dropdown-menu li .ico-paste {
    display: none;
    width: 16px;
    height: 17px;
    margin-left: 5px;
    background: url("/image/svg/paste.svg");
    cursor: pointer;
}

.catalog-dropdown .dropdown-menu li .ico-paste.paste-disabled {
    background: url("/image/svg/paste-disabled.svg");
}

.catalog-dropdown .dropdown-menu li.hover .ico-copy,
.catalog-dropdown .dropdown-menu li.hover .ico-paste {
    display: inline-block;
}

.catalog-dropdown .dropdown-menu li .menu-item-content {
    display: flex;
    align-items: center;
    width: 160px;
    height: 30px;
}

/*.catalog-dropdown .dropdown-menu li .menu-item-content:hover {*/
/*    font-size: 14px;*/
/*}*/

/*.catalog-dropdown .dropdown-menu li .menu-item-content:hover .ico-copy {*/
/*    display: inline-block;*/
/*}*/

/*.catalog-dropdown .dropdown-menu li[draggable="true"] .ico-copy {*/
/*    display: none !important;*/
/*}*/

/*.catalog-dropdown .dropdown-menu li .menu-item-content:hover .ico-paste {*/
/*    display: inline-block;*/
/*}*/

/*.catalog-dropdown .dropdown-menu li[draggable="true"] .ico-paste {*/
/*    display: none !important;*/
/*}*/

.sortable-ghost {
    background-color: #EEEEEE;
    cursor: crosshair;
}

.sortable-drag {
    opacity: 0;
}

.history-action-box {
    display: none;
}

.ico-display-trigger:hover .history-action-box {
    display: inline-block;
}

.revert_icon {
    background: url("/image/svg/revert.svg");
    width: 22px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}

.revert_icon:hover {
    background: url("/image/svg/revert_hover.svg");
}

/*可见密码*/
.password-text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
}
.visible-password {
    position: absolute;
    top: 6px;
    right: 10px;
    z-index: 2;
    cursor: pointer;
}
.visible-icon {
    width:18px;
    height: 18px;
    opacity: 0.3;
    background: url(../../image/visible.svg) no-repeat;
}
.visible-icon:hover {
    opacity: 0.4;
}
.invisible-icon {
    background: url(../../image/invisible.svg) no-repeat !important;
    opacity: 0.5;
}
.invisible-icon:hover {
    opacity: 0.7;
}
.hide-important {
    display: none !important;
}

.exp-abbr-info-box img {
    max-width: 100%;
}

.textarea {
    padding: 5px;
    border-radius: 3px;
}
.textarea:focus {
    border: 1px solid #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}
.high-search-indraw{
    background: url("/image/svg/indraw.svg");
    width: 38px;
    height: 38px;
    display: inline-block;
    vertical-align: middle;
    margin: 2px;
    cursor: pointer;
}
.high-search-indraw:hover{
    background: url("/image/svg/indraw-hover.svg");
}
.w100{
    width: 100px;
}
.w120{
    width: 120px;
}
.w155{
    width: 155px;
}
.w180{
    width:180px
}
.w240{
    width: 240px
}
.w310{
    width: 310px;
}

/*起警示作用的红色border*/
.red-shadow {
    border: 1px solid red;
    box-shadow: 0 0 4px red;
    -moz-box-shadow: 0 0 4px red;
    -webkit-box-shadow: 0 0 4px red;
    -ms--box-shadow: 0 0 4px red;
}

.ico-drag-box {
    position: relative;
    display: inline-block;
}

.fixHeaderOuter .ico-drag {
    position: absolute;
    top: -13px;
    left: -12px;
}

.system-barcode {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    white-space: nowrap;
}

.instrument_ext_record_title{
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
}

.fixHeaderOuter {
    overflow: auto;
}

.fixHeaderOuter table {
    table-layout: fixed;
}

.inscada_table_content-popup {
    height: 455px;
    overflow: auto;
}

.inscada_table_content-popup .project-view-table {
    table-layout: fixed;
}

.batch-add-inscada-data-border {
    float: right;
    margin-top: -4px;
}

.batch_add_inscada_data {
    width: 50px;
    color: #fff;
}

.select-insert-direction {
    width: 20px;
    height: 20px;
}

.down-arrow {
    width: 10px;
    height: 10px;
}
.down-arrow:after {
    content: '';
    display: block;
    width: 7px;
    height: 7px;
    border-right: 1px solid #fff;
    border-top: 1px solid #fff;
    -webkit-transform: rotate(135deg); /*箭头方向可以自由切换角度*/
    transform: rotate(135deg);
}

.inscada-popup-page-box .show-title,
.inscada-popup-page-box .pager-select,
.inscada-popup-page-box .pagevalue,
.inscada-popup-page-box .page-btn,
.inscada-popup-page-box .go-to {
    display: none !important;
}

.inscada-popup-page-box .next {
    background: url("../image/svg/arrow-right.svg") no-repeat center !important;
    width: 24px;
    height: 24px;
}

.inscada-popup-page-box .next:hover {
    background: url("../image/svg/arrow-right1388ff.svg") no-repeat center !important;
}

 .inscada-popup-page-box .prev_previous {
     background: url("../image/svg/arrow-left.svg") no-repeat center !important;
     width: 24px;
     height: 24px;
 }

.inscada-popup-page-box .prev_previous:hover {
    background: url("../image/svg/arrow-left1388ff.svg") no-repeat center !important;
}

.inscada-popup-page-box .first {
    background: url("../image/svg/d-arrow-left.svg") no-repeat center !important;
    width: 24px;
    height: 24px;
}

.inscada-popup-page-box .first:hover {
    background: url("../image/svg/d-arrow-left1388ff.svg") no-repeat center !important;
}

.inscada-popup-page-box .last {
    background: url("../image/svg/d-arrow-right.svg") no-repeat center !important;
    width: 24px;
    height: 24px;
}

.inscada-popup-page-box a[href],
.inscada-popup-page-box span {
    border: none;
    display: inline;
}

.inscada-popup-tr-style {
    height: 36px;
}

.batch-add-inscada-data {
    background-color: #1388FF;
}

.batch-add-inscada-data-fields {
    display: none;
}

.batch-add-inscada-data-fields:hover {
    display: block;
}

.auto_insert_inscada_box {
    background-color: #1388FF;
    position: fixed;
    right: 30px;
    bottom: 30px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1011;
    border-radius: 30px;
    box-shadow: 0px 18px 48px 4px rgba(0, 0, 0, 0.04), 0px 12px 30px 0px rgba(0, 0, 0, 0.08);
}

.auto_insert_inscada_icon {
    width: 32px;
    height: 38px;
    padding-bottom: 6px;
}

.auto_insert_inscada .auto_insert_inscada_icon {
    background: url("../image/svg/inscada_popup_auto_insert.svg") no-repeat center;
    width: 16px;
    height: 16px;
}

.auto_insert_inscada_btn{
    background-color: unset;
    border: 1px solid #DCDFE6;
    padding: 4px 12px;
    margin-top: -3px;
}

.auto_insert_inscada_btn:hover {
    background-color: #F5F7FA;
    border-color: #C1C4CC;
}

.auto_insert_inscada {
    display: flex;
    align-items: center;
}

.pl3 {
    padding-left: 3px;
}

.auto_insert_inscada_close {
    cursor: pointer;
    font-size: 16px;
    padding: 10px;
}

.auto_insert_inscada_tip {
    color: #fff;
}

.info-text {
    position: relative;
    display: inline-block;
    text-align: right;
    width: 130px;
    cursor: pointer;
}

.info-title {
    border-bottom: solid 1px #d2d2d2;
}

.info-text:hover .info-title {
    border-bottom-color: #1593ff;
}

.info-text .link-info {
    display: none;
    position: absolute;
    border: 1px solid #e5e5e5;
    top: 25px;
    left: 90px;
    text-align: left;
    padding: 0 10px;
    line-height: 26px;
    background: #fff;
    z-index: 15;
    font-size: 14px;
    color: black;
    min-width: 350px;
    white-space: normal;
    word-break: break-word;
}

.info-text:hover .link-info {
    display: block;
}

.info-text .link-info:after {
    content: "";
    position: absolute;
    top: -12px;
    left: 6px;
    width: 0px;
    height: 0px;
    border: 6px solid transparent;
    border-bottom-color: #FFF;
}

.info-text .link-info:before {
    content: "";
    position: absolute;
    top: -15px;
    left: 6px;
    width: 0px;
    height: 0px;
    border: 7px solid transparent;
    border-bottom-color: #e5e5e5;
}

.modul_line.chendraw:nth-last-child(3) td:has(.new-input-select-list) {
    position: relative;
    overflow: visible;
}

.modul_line.chendraw:nth-last-child(3) .new-input-select-list {
    bottom: 100%;
}

.display_none {
    display: none !important;
}

.test-result-icon-success {
    vertical-align: middle;
    background: url(/image/test_success.svg);
}
.test-result-icon-fail {
    vertical-align: middle;
    background: url(/image/test_fail.svg);
}

.file_list .webuploader-dnd-over{
    border-color: #00b7ee;
}

.info-text.clear_comments_tip .link-info{
    left: 20px;
    top: 35px;
}

.info-text.clear_comments_tip{
    width: fit-content;
}

.inscada_page_data_style {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.charLimitContainer {
    position: relative;
}

.charLimitMessage {
    font-size: 12px;
    color: #999;
    position: absolute;
    bottom: 5px; /* Adjust the distance from the bottom as needed */
    right: 45px; /* Adjust the distance from the right as needed */
}

.left-border {
    width: 20px;
    height: calc(100%-75px);
    top: 55px;
    left: 0;
    bottom: 20px;
    position: absolute;
    cursor: ew-resize;
}

.right-border {
    position: absolute;
    right: 0;
    height: calc(100%-75px);
    width: 20px;
    top: 55px;
    bottom: 20px;
    cursor: ew-resize;
}

.left-under-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 20px;
    height: 20px;
    cursor: nesw-resize;
    z-index: 1;
}

.right-under-border {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    cursor: nwse-resize;
    z-index: 1;
}

.under-border {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 20px;
    cursor: ns-resize;
    width: calc(100%-40px);
    left: 20px;
    right: 20px;
}

.insert_data_direction:hover {
    color: #1388ff;
    background-color: #F0F2F5;
}

.insert_data_direction:hover .select_inscada_direction:after {
    border-right: 1px solid #1388ff;
    border-top: 1px solid #1388ff;
}

.select_inscada_direction_box {
    width: 16px;
    height: 16px;
    display: inline-block;
}

.select_inscada_direction {
    width: 16px;
    height: 16px;
}

.select_inscada_direction:after {
    content: '';
    display: block;
    width: 10px;
    height: 5px;
    border-right: 1px solid #000;
    border-top: 1px solid #000;
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
    margin-top: 8px;
}

.select-inscada-direction-style {
    width: 138px;
    text-align: center;
    left: unset;
    right: -1px;
    top: 25px;
    z-index: 2;
    background-color: #fff;
    border: 1px solid #DEDCE6;
    border-radius: 4px;
}

.inscada-download-ico {
    background: url("../image/svg/inscada_download.svg") no-repeat center;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}

.instant_receive_file_ico {
    background: url("../image/svg/instant_receive_file_ico.svg") no-repeat center;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}

.inscada_popup_input_box {
    height: 30px;
    width: 29px;
    padding-right: 7px;
}

.inscada_empty_data {
    background: url("../image/svg/image_EmptyFolder.svg") no-repeat center;
    width: 200px;
    height: 200px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}

.inscada_empty_data_box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.username_required:before,
.password_required:before {
    content: '*';
    color: #ff4500;
    margin-right: 2px;
}

.inscada-popup-num-padding {
    padding: 8px 8px 8px 35px !important;
}

/*input[type="checkbox"] {*/
/*    -webkit-appearance: none;*/
/*    -moz-appearance: none;*/
/*    appearance: none;*/
/*    width: 13px;*/
/*    height: 13px;*/
/*    border: 1px solid #666;*/
/*    background-color: #fff;*/
/*    border-radius: 3px;*/
/*    outline: none;*/
/*    position: relative;*/
/*}*/

/* 因为某些页面input框有padding，导致会变宽，先注释上*/
/*!* 设置复选框选中状态的样式 *!*/
/*input[type="checkbox"]:checked {*/
/*    background-color: #0075FF;*/
/*    border-color: #0075FF;*/
/*    color: #fff;*/
/*}*/

/*input[type="checkbox"]::before {*/
/*    content: ""; !* Empty content for pseudo-element *!*/
/*    width: 5px; !* Width of the rectangle *!*/
/*    height: 10px; !* Height of the rectangle *!*/
/*    border-bottom: 2px solid #fff;*/
/*    border-right: 2px solid #fff;*/
/*    position: absolute;*/
/*    top: -3px;*/
/*    left: 0;*/
/*    transform: rotate(40deg); !* Rotate the rectangle to form an L shape *!*/
/*    transform-origin: 0 100%; !* Set the transform origin to create the L shape *!*/
/*    box-sizing: border-box; !* Include the border in the element's dimensions *!*/
/*    opacity: 0;*/
/*}*/

/*!* 设置复选框选中状态时对号的可见性 *!*/
/*input[type="checkbox"]:checked::before {*/
/*    opacity: 1;*/
/*}*/

/*flex布局命名
1、flex
2、主轴方向
3、主轴对齐方式
4、交叉轴对齐方式
*/
.flex {
    display: flex;
}

.flex-x-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.flex-y-start {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.flex-y-end {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
}

.flex-y-end-start {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
}

.flex-x-sb {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

/**
内容截断，超出部分省略
 */
.truncate {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/*禁用edge浏览器自带密码框样式*/
input[type="password"]::-ms-reveal {
    display: none;
}
input[type="password"]::-ms-clear {
    display: none;
}
input[type="password"]::-o-clear {
    display: none;
}
