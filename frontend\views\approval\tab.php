<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/24
 * Time: 11:11
 */
$approvalType = \Yii::$app->params['approval_type'];
?>
<div class="tool_data_box">
    <style>
        .tool_nav a.approval_tab {
            border: none;
            border-radius: 0;
            background-color: rgba(0, 0, 0, 0);
            white-space: nowrap;
            overflow: hidden;
            /*max-width: 120px;*/
            padding: 0 5px;
        }

        .tool_nav .approval_tab.active {
            color: #1388ff;
            border-bottom: 2px solid #1388ff;
        }
    </style>

    <div class="tool_nav nav-bar-height47 clear">
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_sign'), @getVar($approvalCnt['sign'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['sign']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['sign']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_coauthor_sign'), @getVar($approvalCnt['coauthor_sign'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['coauthor_sign']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['coauthor_sign']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_reopen'), @getVar($approvalCnt['reopen'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['reopen']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['reopen']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_signing'), @getVar($approvalCnt['signing'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['signing']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['signing']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_pretrial'), @getVar($approvalCnt['pretrial'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['pretrial']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['pretrial']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'share_exp'), @getVar($approvalCnt['share_experiment'], 0)) ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['share']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['share']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_exp_notebook'), @getVar($approvalCnt['create_book'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['create_book']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['create_book']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_template'), @getVar($approvalCnt['template'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['template']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['template']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_module_reedit'), @getVar($approvalCnt['module_reedit'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['module_reedit']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['module_reedit']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_instrument'), @getVar($approvalCnt['instrument_check'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['instrument_check']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['instrument_check']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
        <?php $text = sprintf('%s(%s)', Yii::t('base', 'approval_work_order'), @getVar($approvalCnt['work_order'], 0)); ?>
        <a href="javascript:void(0)"
           class="block exp_href approval_tab <?= ($type == $approvalType['work_order']) ? 'active' : ''; ?>"
           data-type="<?= $dataType; ?>" type="<?= $approvalType['work_order']; ?>" title="<?= $text ?>"><?= $text ?>
        </a>
    </div>
</div>
