<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import type { IDatasheetPackProps } from '@integle/form'
import { isEmpty } from 'radash'
import type { BaseOperationToolBar } from '@/components/in-form/operation-tool-bar/interface'
import OperationButton from '@/components/in-form/operation-tool-bar/OperationButton.vue'
import OperationToolBar from '@/components/in-form/operation-tool-bar/OperationToolBar.vue'
import {Pencil,CalendarDateTime,CalendarX} from '@integle/icons'
const { t } = useI18n();
const props = defineProps<BaseOperationToolBar & {
  informData:IDatasheetPackProps
}>()
const emit = defineEmits(['editBooking', 'bookAgain', 'cancelBooking'])
enum InstrumentsBookAction {
  EditBooking = 'EditBooking',
  BookAgain = 'BookAgain',
  CancelBooking = 'CancelBooking',
}
const editBooking = {
  id: InstrumentsBookAction.EditBooking,
  icon: Pencil,
  name: t(`instrumentsBookMine.editBooking`),
  click: () => {
    emit('editBooking')
  },
}
const bookAgain = {
  id: InstrumentsBookAction.BookAgain,
  icon: CalendarDateTime,
  name: t(`instrumentsBookMine.bookAgain`),
  click: () => {
     emit('bookAgain')
  },
}
const cancelBooking = {
  id: InstrumentsBookAction.CancelBooking,
  icon: CalendarX,
  name: t(`instrumentsBookMine.cancelBooking`),
  click: () => {
     emit('cancelBooking')
  },
}
const resultArr = computed(() => {
  const actions = new Set([
    InstrumentsBookAction.EditBooking,
    InstrumentsBookAction.BookAgain,
    InstrumentsBookAction.CancelBooking,
  ])
  const data_list = props.informData?.snapshot.recordMap
  if (props.selectedIds.length === 0 || isEmpty(data_list)) {
    return []
  }
  for (const item of props.selectedIds) {
    if (data_list && ( props.selectedIds.length !== 1 || (data_list[item].data.bookingStatus === '3' || data_list[item].data.bookingStatus === '4'))) {
      actions.delete(InstrumentsBookAction.EditBooking)
    }
    if (data_list && (data_list[item].data.bookingStatus !== '1')) {
      actions.delete(InstrumentsBookAction.CancelBooking)
    }
    if (props.selectedIds.length !== 1) {
      actions.delete(InstrumentsBookAction.BookAgain)
    }
  }
  const allAction = [ editBooking, bookAgain, cancelBooking]
  const filteredActions = allAction.filter(action => actions.has(action.id))
  return filteredActions
})
</script>

<template>
  <OperationToolBar :selected-ids="selectedIds">
    <template v-for="(item, index) in resultArr" :key="index">
      <OperationButton
        :label="item.name"
        :icon="item.icon"
        @click="item.click"
      />
    </template>
  </OperationToolBar>
</template>

<style lang="scss" scoped>

</style>
