<?php
namespace frontend\models;

use frontend\services\ApprovalServer;
use frontend\services\CompanyServer;
use frontend\services\IntegleTableServer;
use frontend\services\modules\XSheetServer;
use frontend\services\TempleServer;
use frontend\services\TlcServer;
use service\models\Common;
use yii;
use yii\caching\Cache;

/**
 * This is the model class for table "template".
 *
 * @property integer $id
 * @property string $name
 * @property string $description
 * @property integer $user_id
 * @property integer $type
 * @property integer $is_system
 * @property string $create_time
 * @property string $update_time
 * @property string $email
 * @property integer $status
 */
class TemplateModel extends \frontend\core\CommonModel {

    /**
     * @inheritdoc
     */
    public static function tableName() {
        return 'template';
    }

    //模板step字段
    static public $step = [
        'is_script' => 1,   // 草稿状态
        'is_approving' => 2, // 审核中状态
        'is_agreed' => 3,    // 审核通过状态
        'is_refused' => 4,   // 审核拒绝状态
        'is_company_temp_approving' => 5,/* 普通模板转企业模板审批中仍然是普通模板 */
    ];

    /**
     * @inheritdoc
     */
    public function rules() {
        return [
            [
                [
                    'name',
                    'user_id',
//                    'transfer_id',
//                    'previous_id'
                ],
                'required'
            ],
            [
                [
                    'user_id',
                    'type',
                    'is_system',
                    'is_company',
                    'system_type',
                    'tfrom',
                    'step',
                    'transfer_id',
                    'previous_id',
                    'subtype_id'
                ],
                'integer'
            ],
            [
                [
                    'create_time',
                    'update_time'
                ],
                'safe'
            ],
            [
                [
                    'name'
                ],
                'string',
                'max' => 64
            ],
            [
                [
                    'descript',
                    'keywords',
                    'title',
                    'email'
                ],
                'string',
                'max' => 255
            ],
            [
                [
                    'content'
                ],
                'string',
                'on' => 'add-sub-temp'
            ],
            [
            [
                'define_item',  // add by hkk 2019/10/16
                'img'  // add by hkk 2019/10/16
            ],
                'string',
            ]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels() {
        return [
            'id' => 'ID',
            'name' => \Yii::t('temp', 'name'),
            'descript' => \Yii::t('temp', 'temp_descript'),
            'type' => \Yii::t('temp', 'temp_type'),
            'tfrom' => \Yii::t('temp', 'temp_from'),
            'is_company' => \Yii::t('temp', 'is_company'),
            'keywords' => \Yii::t('temp', 'keywords'),
            'title' => \Yii::t('temp', 'title'),
            'content' => \Yii::t('temp', 'temp_content'),
            'edit_user_id' => \Yii::t('base', 'update_user'),
            'create_time' => \Yii::t('base', 'create_time'),
            'update_time' => \Yii::t('base', 'update_time'),
            'user_id' => \Yii::t('base', 'creator'),
            'define_item' => 'Define Item',
            'img' => 'image',
            'step' => 'step',
            'transfer_id'=>\Yii::t('temp', 'transfer_id'),
            'previous_id'=>\Yii::t('temp', 'previous_id'),
            'subtype_id'=>\Yii::t('temp', 'subtype')
        ];
    }


    /**
     * 保存实验模板
     *
     * <AUTHOR>
     * @copyright 2016-3-1
     * @param array $data
     * @return boolean multitype:
     */
    public function addTemplate($postData, $isArray = FALSE) {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        try {
            if (!$isArray) {
                $postData = [
                    $postData
                ];
            }

            $id = 0;


            foreach ($postData as $data) {

                $_this = clone $this;
                $_this->setAttributes($data['insertData']);

                if (!$_this->cacheSave()) {
                    $transaction->rollBack();
                    return $this->fail($_this->getFirstErrors());
                }

                $id = $_this->attributes['id'];

                $this->_addOrUpdateTemp($id, $data, $transaction);

            }

            return $this->success($id);
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

/**
     * 保存实验模板
     *
     * <AUTHOR>
     * @copyright 2016-3-1
     * @param array $data
     * @return boolean multitype:
     */
    public function addTemplateByCopy($postData, $isArray = FALSE) {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();

        try {
            $expId = isset($postData['id']) ? $postData['id'] : 0;
            if (!$isArray) {
                $postData = [
                    $postData
                ];
            }

            $id = 0;

            foreach ($postData as $data) {
                $_this = clone $this;
                $_this->setAttributes($data['insertData']);

                if (!$_this->cacheSave()) {
                    $transaction->rollBack();
                    return $this->fail($_this->getFirstErrors());
                }

                $id = $_this->attributes['id'];

                $this->_addOrUpdateTempByCopy($id, $data, $transaction);

            }

            // 更新该实验的模板id (Bug#12390：实验列表的属性栏增加实验选择的模板（如果有存为模板，显示保存的模板）
            $exp = ExperimentModel::findOne($expId);
            $exp['template_id'] = $id;
            $exp->save();

            return $this->success($id);
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 添加模板适用的鹰群
     *
     * @date: 2018年7月4日 下午4:13:42
     * <AUTHOR>
     * @param integer $tempId 模板id
     * @param array $groupIds 鹰群id
     * @return boolean
     */
    private function _addTempForGroup($tempId, $groupIds){
        $tran = \Yii::$app->integle_ineln->beginTransaction();

        $tran->commit();
        return true;
    }

    /**
     * 插入子模板
     * <AUTHOR> @copyright 2016-5-7
     * @param array $data ['temp_real'=>'模板组件数据','insertData'=>'模板基本数据']
     * @return Ambigous <boolean, \service\models\ineln\multitype:, multitype:>|boolean
     */
    public function addSubTemp($data) {
        $this->setScenario('add-sub-temp');  // 设置场景为添加子模板
        //$data['insertData']['content']字段需要提前取出，因为content字段之后会从template表删除
        $content = $data['insertData']['content'];
        unset($data['insertData']['content']);

        $this->setAttributes($data['insertData']);
        if (!$this->cacheSave()) {
            return $this->fail($this->errors);
        }

        $id = $this->attributes['id'];

        // 如果Type = 2，将content保存到TemplateIntextData表
        if ($this->type == 2) {
            $intextData = new TemplateIntextData();
            $intextData->template_id = $id;
            if(isset($content))
            $intextData->content = $content;
            if (!$intextData->save()) {
                return $this->fail($intextData->errors);
            }
        }
        // 如果Type = 4，将content和img保存到TemplateIntableData表
        if ($this->type == 4) {
            $intableData = new TemplateIntableData();
            $intableData->template_id = $id;
            if(isset($content))
            $intableData->content = $content;
            if(isset($img))
            $intableData->img = $img;
            if (!$intableData->save()) {
                return $this->fail($intableData->errors);
            }
        }

        $relayArr = empty($data['temp_real']) ? NULL : $data['temp_real'];
        if (!empty($relayArr)) {
            $relayAfetRows = (new TemplateRelayModel())->addTemplateRelay($relayArr, $id);
            if(empty($relayAfetRows['data'])){
                return $this->fail(!empty($relayAfetRows['info']) ? $relayAfetRows['info'] : \Yii::t('base', 'failed'));
            }
        }

        return $this->success($id);
    }

    /**
     * 模板组件添加
     *
     * <AUTHOR> @copyright 2016-5-6
     * @param int $id
     * @param array $data
     */
    private function _addOrUpdateTempByCopy($id, $data, $transaction) {

        $chemData = [];
        if (!empty($data['chem_detail'])) {
            $chemData = [
                'chem_detail' => $data['chem_detail'],
                'chem_data' => $data['chem_data'],
                'substrate_data' => isset($data['substrate_data']) ? $data['substrate_data'] : [],
                'product_data' => isset($data['product_data']) ? $data['product_data'] : [] ,
                'details_data' => isset($data['details_data']) ? $data['details_data'] : []
            ];
        }

        $transaction->commit();
        //这个是结构式模块保存
        $chemAffectedRows = (new ChemModel())->addTempChemAll($chemData, $id);

        $excelData['excel_data'] = empty($data['excel_data']) ? NULL : [
            $data['excel_data']
        ];
        $excelData['experiment_id'] = $id;
         //EXcel表格模块保存
        $excelInsertId = (new ExcelModel())->addTempExcelByCopy($excelData);

        $this->log_info('operation_data部分');

        $operatData['editor_data'] = empty($data['operation_data']) ? NULL : $data['operation_data'];
        $operatData['experiment_id'] = $id;
        $operatData['component_id'] = self::$component['operation'];

        $editorInsertId = (new EditorModel())->addTempEditorByCopy($operatData);

        $this->log_info('discuss_data部分');

        $discussData['editor_data'] = empty($data['discuss_data']) ? NULL : $data['discuss_data'];
        $discussData['experiment_id'] = $id;
        $discussData['component_id'] = self::$component['discuss'];
        $editorInsertId = (new EditorModel())->addTempEditorByCopy($discussData);

        $this->log_info('diy_data部分');
        $diyData['editor_data'] = empty($data['diy_data']) ? NULL : $data['diy_data'];
        $diyData['experiment_id'] = $id;
        $diyData['component_id'] = self::$component['diy'];
        $editorInsertId = (new EditorModel())->addTempEditorByCopy($diyData);

        $this->log_info('lite_data部分');
        $liteData['editor_data'] = empty($data['lite_data']) ? NULL : $data['lite_data'];
        $liteData['experiment_id'] = $id;
        $liteData['component_id'] = self::$component['lite'];

        $editorInsertId = (new EditorModel())->addTempEditorByCopy($liteData);


        $this->log_info('abstract_data部分');
        $abstractData['editor_data'] = empty($data['abstract_data']) ? NULL : $data['abstract_data'];
        $abstractData['experiment_id'] = $id;
        $abstractData['component_id'] = self::$component['abstract'];
        $editorInsertId = (new EditorModel())->addTempEditorByCopy($abstractData);

        $this->log_info('upload_file_data部分');
        $fileData['upload_data'] = empty($data['upload_file_data']) ? NULL : $data['upload_file_data'];
        $fileData['experiment_id'] = $id;
        $fileData['component_id'] = self::$component['upload'];
        $fileInsertId = (new UploadModel())->addTempUploadByCopy($fileData);

        $this->log_info('upload_img_data部分');
        $imgData['upload_data'] = empty($data['upload_img_data']) ? NULL : $data['upload_img_data'];
        $imgData['experiment_id'] = $id;
        $imgData['component_id'] = self::$component['picture'];

        $imgInsertId = (new UploadModel())->addTempUploadByCopy($imgData);

        $this->log_info('wechat_pic_data部分');
        $imgData['upload_data'] = empty($data['wechat_pic_data']) ? NULL : $data['wechat_pic_data'];
        $imgData['experiment_id'] = $id;
        $imgData['component_id'] = self::$component['wechat_pic'];
        $imgInsertId = (new UploadModel())->addTempUploadByCopy($imgData);

        $this->log_info('comment_data部分');
        $commData = [
            'relay_data' => empty($data['comment_data']) ? NULL : $data['comment_data'],
            'experiment_id' => $id,
            'component_id' => self::$component['comment']
        ];

        $discussInsertId = (new ExperimentCommentModel())->addTempCommonByCopy($commData);

        $this->log_info('reference_data部分');
        $referenceData = empty($data['reference_data']) ? NULL : $data['reference_data'];

        $referenceAtfRows = (new ReferenceModel())->addTempReferenceByCopy($referenceData, $id);

        $this->log_info('biology_data部分');
        $biologyData = empty($data['biology_data']) ? NULL : $data['biology_data'];
        $biologAtfRows = (new BiologyModel())->addTempBiolByCopy($biologyData, $id);

        $this->log_info('define_data部分');
        $defineData = empty($data['define_data']) ? NULL : $data['define_data'];
        $biologAtfRows = (new DefineTableKeyModel())->addDefineKey($defineData, $id, 2);

        $this->log_info('tlc_data部分');
        if(!empty($data['tlc'])) {
            $tlcResult = (new TlcServer())->saveTemplate($data['tlc'], $id);
        }
    }


    /**
     * 模板组件添加
     *
     * <AUTHOR> @copyright 2016-5-6
     * @param int $id
     * @param array $data
     */
    private function _addOrUpdateTemp($id, $data, $transaction) {

        $chemData = [];
        if (!empty($data['chem_detail'])) {
            $chemData = [
                'chem_detail' => $data['chem_detail'],
                'chem_data' => $data['chem_data'],
                'substrate_data' => isset($data['substrate_data']) ? $data['substrate_data'] : [],
                'product_data' => isset($data['product_data']) ? $data['product_data'] : [] ,
                'details_data' => isset($data['details_data']) ? $data['details_data'] : []
            ];
        }

        $transaction->commit();
        //这个是结构式模块保存
        $chemAffectedRows = (new ChemModel())->addTempChemAll($chemData, $id);

        $excelData['excel_data'] = empty($data['excel_data']) ? NULL : [
            $data['excel_data']
        ];
        $excelData['experiment_id'] = $id;
         //EXcel表格模块保存
        $excelInsertId = (new ExcelModel())->addTempExcel($excelData);

        $this->log_info('operation_data部分');

        $operatData['editor_data'] = empty($data['operation_data']) ? NULL : $data['operation_data'];
        $operatData['experiment_id'] = $id;
        $operatData['component_id'] = self::$component['operation'];

        $editorInsertId = (new EditorModel())->addTempEditor($operatData);

        $this->log_info('discuss_data部分');

        $discussData['editor_data'] = empty($data['discuss_data']) ? NULL : $data['discuss_data'];
        $discussData['experiment_id'] = $id;
        $discussData['component_id'] = self::$component['discuss'];
        $editorInsertId = (new EditorModel())->addTempEditor($discussData);

        $this->log_info('diy_data部分');
        $diyData['editor_data'] = empty($data['diy_data']) ? NULL : $data['diy_data'];
        $diyData['experiment_id'] = $id;
        $diyData['component_id'] = self::$component['diy'];
        $editorInsertId = (new EditorModel())->addTempEditor($diyData);

        $this->log_info('lite_data部分');
        $liteData['editor_data'] = empty($data['lite_data']) ? NULL : $data['lite_data'];
        $liteData['experiment_id'] = $id;
        $liteData['component_id'] = self::$component['lite'];

        $editorInsertId = (new EditorModel())->addTempEditor($liteData);


        $this->log_info('abstract_data部分');
        $abstractData['editor_data'] = empty($data['abstract_data']) ? NULL : $data['abstract_data'];
        $abstractData['experiment_id'] = $id;
        $abstractData['component_id'] = self::$component['abstract'];
        $editorInsertId = (new EditorModel())->addTempEditor($abstractData);

        $this->log_info('upload_file_data部分');
        $fileData['upload_data'] = empty($data['upload_file_data']) ? NULL : $data['upload_file_data'];
        $fileData['experiment_id'] = $id;
        $fileData['component_id'] = self::$component['upload'];
        $fileInsertId = (new UploadModel())->addTempUpload($fileData);

        $this->log_info('upload_img_data部分');
        $imgData['upload_data'] = empty($data['upload_img_data']) ? NULL : $data['upload_img_data'];
        $imgData['experiment_id'] = $id;
        $imgData['component_id'] = self::$component['picture'];

        $imgInsertId = (new UploadModel())->addTempUpload($imgData);

        $this->log_info('wechat_pic_data部分');
        $imgData['upload_data'] = empty($data['wechat_pic_data']) ? NULL : $data['wechat_pic_data'];
        $imgData['experiment_id'] = $id;
        $imgData['component_id'] = self::$component['wechat_pic'];
        $imgInsertId = (new UploadModel())->addTempUpload($imgData);

        $this->log_info('comment_data部分');
        $commData = [
            'relay_data' => empty($data['comment_data']) ? NULL : $data['comment_data'],
            'experiment_id' => $id,
            'component_id' => self::$component['comment']
        ];

        $discussInsertId = (new ExperimentCommentModel())->addTempCommon($commData);

        $this->log_info('reference_data部分');
        $referenceData = empty($data['reference_data']) ? NULL : $data['reference_data'];

        $referenceAtfRows = (new ReferenceModel())->addTempReference($referenceData, $id);

        $this->log_info('biology_data部分');
        $biologyData = empty($data['biology_data']) ? NULL : $data['biology_data'];
        $biologAtfRows = (new BiologyModel())->addTempBiol($biologyData, $id);

        $this->log_info('define_data部分');
        $defineData = empty($data['define_data']) ? NULL : $data['define_data'];
        $biologAtfRows = (new DefineTableKeyModel())->addDefineKey($defineData, $id, 2);

        $this->log_info('tlc_data部分');
        if(!empty($data['tlc'])) {
            $tlcData = $data['tlc'];
        } else if(!empty($data['tlc_data'])) {
            $tlcData = $data['tlc_data'];
        }
        if(!empty($tlcData)) {
            $tlcResult = (new TlcServer())->saveTemplate($tlcData, $id);
        }

        $this->log_info('多功能表格');
        if(!empty($data['custom_data'])) {
            $customData = $data['custom_data'];
        }
        if(!empty($customData)) {
            foreach($customData as $cd) {
                (new IntegleTableServer())->addToTemplate($cd, $id);
            }
        }


        $this->log_info('xsheet');
        if(!empty($data['xsheet_data'])) {
            $xSheetData = $data['xsheet_data'];
        }
        if(!empty($xSheetData)) {
            foreach($xSheetData as $cd) {
                (new XSheetServer())->addToTemplate($cd, $id);
            }
        }
    }

    /**
     *
     * <AUTHOR>
     * @param id 根据主键删除模板
     */
    public function delTemp($idArr, $userId) {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        try {
            $affectedRows = $this->cacheUpdateAll([
                'status' => 0
            ], [
                'id' => $idArr,
                /* 'user_id' => $userId */
            ]);
            if (!$affectedRows) {
                $transaction->rollBack();
                return $this->fail(\Yii::t('base', 'failed'));
            }

            $recycleData = [];
            foreach ($idArr as $id) {
                $recycleData[ ] = [
                    'parentId' => $id,
                    'userId' => $userId,
                    'type' => 2
                ];
            }

            $recycleAtfRows = (new RecycleModel())->addRecycle($recycleData, TRUE);

            if (empty($recycleAtfRows['status'])) {
                $transaction->rollBack();
                return $this->fail($recycleAtfRows['info']);
            }

            $transaction->commit();
            return $this->success(\Yii::t('base', 'success'));
        } catch (Exception $e) {
            throw $e;
        }
    }

    public static function active($status = 1) {
        $this->andwhere([
            'status' => $status
        ]);
        return $this;
    }

    /**
     * 获取模板的基本信息
     *
     * <AUTHOR> @copyright 2016-3-24
     * @param int $id
     * @param number $status
     * @return Ambigous <multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function getTemp($id, $status = 1) {
        $query = $this->find()->select('id, user_id, name AS temp_name, descript AS temp_descript, keywords, title, create_time, is_system, system_type,define_item')->where([
            'id' => $id,
            'status' => $status
        ]);

        return $this->cacheQueryOne($query);
//         return $query->one();
    }

    /**
     *
     * <AUTHOR> @copyright 2016-4-18
     */
    public function getTempRelay() {
        return $this->hasMany(TemplateRelayModel::className(), [
            'template_id' => 'id'
        ])->onCondition([
            'status' => 1
        ]);
    }

    /**
     * 根据id数组获取模板
     *
     * <AUTHOR> @copyright 2016-3-28
     * @param array $idArr
     * @return Ambigous <multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function getTempByIds($idArr, $status = 1) {
//        $fields = 'TMP.id, TMP.name AS tep_name, TMP.descript AS tep_descript,TMP.define_item AS define_item'; // add kk define_item 2019/10/17
        $fields = ['TMP.id', 'TMP.name AS tep_name', 'TMP.descript AS tep_descript','TMP.define_item AS define_item',
            'TMP.type', 'TMP.is_company', 'TMP.is_system','TMP.subtype_id'
        ];

        $query = $this->find()->select($fields)->from(self::tableName() . ' AS TMP')->where([
            'TMP.id' => $idArr,
            'TMP.status' => $status
        ]);

        return $this->cacheQueryAll($query);
//         return $query->all();
    }

    /**
     * 将模板设为系统模板temp
     *
     * <AUTHOR> @copyright 2016-3-30
     * @param array $data
     * @param int $id
     * @return number
     */
    public function setSystemTemp($id) {
        return $this->cacheUpdateAll([
            'is_system' => 1
        ], [
            'id' => $id
        ]);
    }

    /**
     *
     * <AUTHOR>
     * @param unknown $user_id
     * @return Ambigous <multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function listTemplate($userId, $type = NULL, $status = 1) {
        $query = self::find()->select('id,name,create_time,update_time,type,email,user_id, type, edit_user_id,tfrom, is_system, is_company,subtype_id')
        ->from(self::tableName())->where('(user_id=:user_id OR is_system=1) AND status=:status', [
            ':user_id' => $userId,
            ':status' => $status,
//             ':type' => $type
        ]);

        if(!empty($type)){
        	$query->andWhere(['type'=>$type]);
        }

        $query = $query->orderBy('create_time DESC');
        return $this->cacheQueryAll($query);

//         $query->orderBy('create_time DESC')->asArray();
//         return $query->all();
    }


    /**
     * 获取新建实验时可以用的模板列表
     *
     * <AUTHOR> @copyright 2017-7-19
     * @param unknown $params ['group_ids'=>'鹰群ids','user_id'=>'用户id']
     * @param number $status
     * @return \common\models\Ambigous
     */
    /**
     * 获取实验可用的模板列表
     * 
     * @param array $params 包含用户ID、鹰群ID等参数
     * @param int $tempType 模板类型，默认为1（全文模板）
     * @param int $status 模板状态，默认为1（激活状态）
     * @return array 返回符合条件的模板列表
     */
    public function tempListForExp($params, $tempType=1, $status=1){
        //获取模板生效模式
        $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];

        // 定义需要查询的字段
        $fields = 'temple.id,temple.name,temple.subtype_id';

        $query = self::find()->select($fields)
            ->from(self::tableName() . ' AS temple')
            // 注释掉的连接条件包含了user_id，存在bug
            //->leftJoin(ShareModel::tableName() . ' AS share', 'share.parent_id = temple.id AND share.user_id = temple.user_id')
            // 修改后的连接条件，移除了user_id的限制
            ->leftJoin(ShareModel::tableName() . ' AS share', 'share.parent_id = temple.id')    //bug#29170，join时on user_id存在bug，先取消。 mod dx
            // 连接用户隐藏模板表，用于过滤用户隐藏的模板
            ->leftJoin(TemplateUserHide::tableName() . ' AS hide', 'temple.id = hide.temp_id AND hide.user_id = :user_id', [':user_id' => $params['user_id']])
            // 基本条件：模板状态为激活
            ->where([
                'temple.status' => self::STATUS_ACTIVE,
            ])
            // 模板类型条件：类型为3或指定的tempType
            ->andWhere([
                'or',
                [
                    'temple.type' => 3,
                ],
                [
                    'temple.type' => $tempType,
                ]
            ])
            // 复杂的权限条件，满足以下任一条件的模板可见：
            ->andWhere([
                'or',
                // 1. 分享给用户所在鹰群的模板
                [
                    'share.to_group_id' => $params['group_ids'],
                    'share.type' => 2,
                    'share.status' => self::STATUS_ACTIVE,
                    'share.share_type' => 1,
                ],
                // 2. 直接分享给用户的模板
                [
                    'share.to_user_id' => $params['user_id'],
                    'share.type' => 2,
                    'share.status' => self::STATUS_ACTIVE,
                    'share.share_type' => 1,
                ],
                // 3. 用户自己创建的模板
                [
                    'temple.user_id' => $params['user_id']
                ],
                // 4. 系统模板
                [
                    'temple.is_system' => self::STATUS_ACTIVE
                ],
                // 5. 企业模板
                [
                    'temple.is_company' => self::STATUS_ACTIVE
                ]
            ])
            // 根据参数中的step条件进行过滤（如果存在）
            ->andFilterWhere(@getVar($params['step'], []))
            // 排除用户隐藏的模板
            ->andWhere('hide.status <> 1 OR hide.status is NULL')
            // 按创建时间降序排序
            ->orderBy('temple.create_time DESC');

        // 获取企业设置，判断是否需要审核
        $settingRes = (new CompanyServer())->getCompanySetting('CREATE_EDIT_TEMPLATE');
        $setting = @getVar($settingRes['data']['CREATE_EDIT_TEMPLATE']['value'], '');
        $setting = json_decode($setting, true);
        $needApproval = !empty($setting['require_approval']);
        
        // 如果需要审核且模板生效模式为1，则只显示已通过审核的模板（step=3）
        if ($needApproval && $templateEffectMode == 1) {
            $query->andWhere([
                'temple.step' => 3,
            ]);
        }
        
        // 如果模板生效模式为2，则只显示在TemplateHistoryNew中有对应is_agreed状态的记录
        if ($templateEffectMode == 2) {
            // 使用子查询获取有效模板ID
            $subQuery = TemplateHistoryNewModel::find()
                ->select('template_id')
                ->where(['status' => TemplateHistoryNewModel::$status['is_agreed']])
                ->groupBy('template_id');
            
            // 添加条件：模板ID必须在有效模板ID列表中
            $query->andWhere(['temple.id' => $subQuery]);
        }
        
        // 使用缓存执行查询并返回结果
        return $this->cacheQueryAll($query);
    }

    /**
     * 获取子模板
     * <AUTHOR> @copyright 2016-7-19
     * @param unknown $userId
     * @param unknown $compentId
     * @param unknown $groupId
     * @param number $status
     * @param $company_id 已废弃
     * @return Ambigous <multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function subListTemp($userId, $compentId, $groupId, $status = 1, $company_id) {

        $query = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content')
        ->from(self::tableName() . ' AS TEMP')
        ->innerJoin(TemplateRelayModel::tableName() . ' AS RELAY', 'RELAY.template_id=TEMP.id AND RELAY.component_id=:component_id AND RELAY.status=:status', [
            ':status' => $status,
            ':component_id' => $compentId
        ])
        ->leftJoin(ShareModel::tableName() . ' AS SHA', 'SHA.parent_id=TEMP.id AND SHA.type=2 AND SHA.status=1')
        ->where([
            'TEMP.type' => 2,
            'TEMP.status' => $status
        ])
        ->andWhere(['OR', ['TEMP.user_id' => $userId], ['SHA.to_group_id'=>$groupId], ['SHA.to_user_id'=>$userId]])
        ->andWhere(['TEMP.step' => 3])
        ->groupBy('TEMP.id')
        ->orderBy('TEMP.id desc');
        $result_1 = $this->cacheQueryAll($query);

        //自己的模板
        $query2 = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content')
        ->from(self::tableName() . ' AS TEMP')
//        ->innerJoin(TemplateRelayModel::tableName() . ' AS RELAY', 'RELAY.template_id=TEMP.id AND RELAY.component_id=:component_id AND RELAY.status=:status', [
//            ':status' => $status,
//            ':component_id' => $compentId
//        ])
//        ->leftJoin(ShareModel::tableName() . ' AS SHA', 'SHA.parent_id=TEMP.id AND SHA.type=2 AND SHA.status=1')
        ->where([
            'TEMP.type' => 2,
            'TEMP.status' => $status ,
            'TEMP.user_id' => $userId,  //查找本用户的模板
            'TEMP.step' => 3,   //查找已通过状态的模板
        ])
        //->andWhere(['TEMP.user_id' => 0])
        ->groupBy('TEMP.id')
        ->orderBy('TEMP.id desc');
        $result_2 = $this->cacheQueryAll($query2);
        //print_r($result_2);exit;

        $query3 = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content')
        ->from(self::tableName() . ' AS TEMP')
//        ->innerJoin(TemplateRelayModel::tableName() . ' AS RELAY', 'RELAY.template_id=TEMP.id AND RELAY.component_id=:component_id AND RELAY.status=:status', [
//            ':status' => $status,
//            ':component_id' => $compentId
//        ])
//        ->leftJoin(ShareModel::tableName() . ' AS SHA', 'SHA.parent_id=TEMP.id AND SHA.type=2 AND SHA.status=1')
        ->where([
            'TEMP.is_company' => 1,
            'TEMP.type' => 2,
//            'TEMP.company_id' => $company_id ,//数据库里company_id字段筛选已经无效 mod dx
            'TEMP.status' => 1,
            'TEMP.step' => 3,
        ])
        //->andWhere(['TEMP.user_id' => 0])
        ->groupBy('TEMP.id')
        ->orderBy('TEMP.id desc');
        $result_3 = $this->cacheQueryAll($query3);

        //系统内置的方法模板
        $query4 = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content')
            ->from(self::tableName() . ' AS TEMP')
            ->where([
                'TEMP.type' => 2,
                'TEMP.step' => 3,
                'TEMP.user_id' => 0,//is_system字段筛选已经失效,修改为使用user_id=0判断 mod dx
                'TEMP.status'=> 1,
            ])->groupBy('TEMP.id')
            ->orderBy('TEMP.id desc');
        $result_4 = $this->cacheQueryAll($query4);


        $result = array_merge($result_1, $result_2, $result_3, $result_4);
        //去掉重复的数据
        $tmp_ids = [];
        foreach($result as $key=>$template){
            if (in_array($template['id'], $tmp_ids)) {
                unset($result[$key]);

            } else {
                $tmp_ids[] = $template['id'];
            }
        }
        $result = array_merge($result,[]);
        //$result=[$result];
        //print_r($result);exit;
        return $result;
//        return $query->all();
    }

    /**
     * 获取文本子模板内容
     * @param integer $id 模板ID
     * @param integer $status 模板状态，默认为1（激活状态）
     * @return array|null 返回模板数据，包含基本信息和关联的文本内容
     */
    public function getSubContent($id, $status=1){
        $query = $this->find()
            ->select([
                'TEMP.id',
                'TEMP.name',
                'TEMP.descript',
                'INTEXT.content',  // 从关联表获取content
                'TEMP.user_id',
                'TEMP.subtype_id',
                'TEMP.step'
            ])
            ->from(self::tableName() . ' AS TEMP')
            // 左连接TemplateIntextData表以获取content内容
            ->leftJoin(TemplateIntextData::tableName() . ' AS INTEXT', 'INTEXT.template_id = TEMP.id')
            ->where([
                'TEMP.id' => $id,
                'TEMP.status' => $status
            ]);

        // 使用缓存查询获取单条记录
        return $this->cacheQueryOne($query);
    }

    /**
     * 获取表格方法模板内容
     * @param integer $id 模板ID
     * @param integer $status 模板状态，默认为1（激活状态）
     * @return array|null 返回模板数据，包含基本信息和关联的表格内容
     */
    public function getInTableTemp($id, $status = 1) {
        $query = $this->find()
            ->select([
                'TEMP.id',
                'TEMP.name',
                'TEMP.descript',
                'TEMP.title',
                'TEMP.user_id',
                'TEMP.step',
                'TEMP.subtype_id',
                'INTABLE.content',  // 从关联表获取content
                'INTABLE.img'       // 从关联表获取img
            ])
            ->from(self::tableName() . ' AS TEMP')
            // 左连接TemplateIntableData表以获取content和img内容
            ->leftJoin(TemplateIntableData::tableName() . ' AS INTABLE', 'INTABLE.template_id = TEMP.id')
            ->where([
                'TEMP.id' => $id,
                'TEMP.status' => $status,
                'TEMP.type' => 4    // 表格模板类型
            ]);

        // 使用缓存查询获取单条记录
        return $this->cacheQueryOne($query);
    }


    /** 设置企业级模板 更新后按照操作类型set_company_template 和 模板子类型判断审批
     *  需要审批时，生成审批。不需要审批时，直接修改模板company状态
     * 参数 status 为1 代表设为企业模板，为0代表取消企业模板
     * <AUTHOR>
     * @param int $idArr 根据主键删除模板
     */
    public function setCompanyTemp($idArr, $userId,$status,$company_id=0) {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        try {
            //获取设为企业模板权限
            $operate_type = 'set_company_template';
            $oldTemp = TemplateModel::findOne([
                'id' => $idArr,
            ]);
            if (empty($oldTemp)) {
                throw (new yii\db\Exception('No template found by provided id!!!'));
            }
            if ($oldTemp['step'] != 3) {    //如果模板当前状态不是已通过，就返回
                return $this->fail(\Yii::t('temp', 'temp_is_approving'));
            }
            $needApproval = false;  // 企业模板修改为普通模板，不需要审批
            if ($status != 0) { //普通模板转企业模板，需要审批,获取审批设置
               // $is_company_temp = !empty($status) ? 'company' : 'normal';
               // $old_temp_type = (1 == $oldTemp['type']) ? 'full' : ((in_array($oldTemp['type'], [2,4])) ? 'function' : 'other');//根据模板type判断模板是全文模板还是方法模板
                $temp_type = $oldTemp['type'];  // 1为全文模板，2为文本方法模板，4为表格方法模板
                $temp_subtype = $oldTemp['subtype_id'];
               // $template_type = sprintf('%1$s_%2$s', $is_company_temp, $old_temp_type);
                $currUid = \Yii::$app->view->params['curr_user_id'];
                $userDetail = CompanyServer::getCurrUserDetail($currUid);
                $apvSetting = (new TempleServer())->checkTemplateApproval(null, $operate_type, $temp_type,$temp_subtype, $userDetail, 1,[]);
                //如果 指定模板类型 和 指定模板操作 都需要审批才视为需要审批
                $needApproval = !empty($apvSetting[$operate_type]);

            }
              
            if (!$needApproval) {//不需要审批,直接修改模板is_company状态
                $attributes = ['company_id' => $company_id, 'is_company' => $status];
                $affectedRows = $this->cacheUpdateAll( $attributes, [
                    'id' => $idArr,
                ]);
                if (!$affectedRows) {
                    $transaction->rollBack();
                    return $this->fail(\Yii::t('base', 'failed'));
                }
                $transaction->commit();
                return $this->success(['need_approval' => $needApproval]);
            }

            //需要审批，生成审批，修改模板状态为待审批
            $creatApprove = (new ApprovalServer())->createApproval(\Yii::$app->params['approval_type']['set_company_template'], $idArr, 0, json_encode([
                'template_id' => $idArr,
                'send_email' => !empty($apvSetting['send_remind_email']) ? 1 : 0,
                'remind_result_with_email' => !empty($apvSetting['send_remind_email']) ? 1 : 0,
                'to_company_status' => $status,
            ]), $apvSetting['approval_nodes'], []);
            if (empty($creatApprove['status'])) {
                $transaction->rollBack();
                return $this->fail($creatApprove['info'], ['tipType' => 'popContent']);
                //throw (new yii\db\Exception($creatApprove['info']));
            }

            $apvAttributes = ['step' => 5]; //step=5,模板正在企业模板审批
            $affectedRow = $this->cacheUpdateAll($apvAttributes, ['id' => $idArr]);
            if (empty($affectedRow)) {
                $transaction->rollBack();
                return $this->fail(\Yii::t('base', 'failed'));
            }
            $transaction->commit();

            return $this->success(['temp_id' => $idArr, 'need_approval' => $needApproval], \Yii::t('base', 'waiting_for_approval'));

        } catch (yii\base\Exception $e) {
            throw $e;
        }
    }


    /**
     *
     * <AUTHOR>
     * @param unknown $user_id
     * @return Ambigous <multitype:, multitype:\yii\db\ActiveRecord , array, multitype:unknown >
     */
    public function listTemplateNew($userId, $type = NULL, $status = 1,$page, $limit=NULL) {
        $query = self::find()->select('id,name,create_time,update_time,type,email,user_id, type, edit_user_id,tfrom, is_system, is_company')
        ->from(self::tableName())->where('(user_id=:user_id OR is_system=1) AND status=:status', [
            ':user_id' => $userId,
            ':status' => $status,
//             ':type' => $type
        ]);

        if(!empty($type)){
        	$query->andWhere(['type'=>$type]);
        }


        $count = $this->cacheQueryCount($query, 'id');
        if(empty($limit)){
            $limit = \Yii::$app->params['default_page_size'];
        }

        $query = $query->offset(($page-1)*$limit)->limit($limit)->orderBy('create_time DESC');

//         $result = $query->asArray()->all();
        $result = $this->cacheQueryAll($query);


        return ['result'=>$result,'count'=>$count];

//         $query->orderBy('create_time DESC')->asArray();
//         return $query->all();
    }

    /**
     * 插入子模板
     * <AUTHOR> @copyright 2016-5-7
     * @param array $data ['temp_real'=>'模板组件数据','insertData'=>'模板基本数据']
     * @return Ambigous <boolean, \service\models\ineln\multitype:, multitype:>|boolean
     */
    public function addXsTemp($data) {
        $this->setScenario('add-sub-temp');
        //var_dump($data)    ;exit;
        $this->setAttributes($data['insertData']);

        if (!$this->cacheSave()) {
            return $this->fail($this->errors);
        }

        $id = $this->attributes['id'];
        return $this->success($id);

    }

    /**
     * Notes: 只用于intable内部获取插入的方法模板，包括分享给我的，分享给我鹰群的，企业级和我自己的
     * Author: modified by hkk 2021/3/3
     * Date: 2016-7-19
     * @param $userId
     * @param $groupId
     * @param int $status
     * @param int $company_id
     * @return array
     */
    public function xsListTemp($userId, $groupId, $status = 1,$company_id=0) {

        $query = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content, TEMP.img')
            ->from(self::tableName() . ' AS TEMP')
            ->leftJoin(ShareModel::tableName() . ' AS SHA', 'SHA.parent_id=TEMP.id AND SHA.type=2 AND SHA.status=1')
            ->where([
                'TEMP.type' => 4,
                'TEMP.status' => $status
            ])
            ->andWhere(
                [
                    'OR',
                    ['TEMP.user_id' => $userId],
                    ['SHA.to_group_id' => $groupId],
                    ['SHA.to_user_id' => $userId],
                    [
                        'AND',
                        //创建的表格模板的company_id可能为1或null,因此取消企业id筛选,防止筛选的企业模板不正确 bug#34290 mod dx
                        // ['TEMP.company_id' => $company_id],
                        ['TEMP.is_company' => 1],
                    ]
                ]
            )
            ->andWhere(['TEMP.step' => 3])
            ->groupBy('TEMP.id');
        $result = $this->cacheQueryAll($query);

        //系统内置的方法模板
        $query4 = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content, TEMP.img')
                       ->from(self::tableName() . ' AS TEMP')
                       ->where([
                           'TEMP.type' => 4,
                           'TEMP.step' => 3,
                           'TEMP.user_id' => 0,//is_system字段筛选已经失效,修改为使用user_id=0判断 mod dx
                           'TEMP.status' => 1,
                       ])->groupBy('TEMP.id')
                       ->orderBy('TEMP.id desc');
        $systemTempList = $this->cacheQueryAll($query4);

        $tempIds = array_column($result, 'id');
        $tempIdsArr = array_flip($tempIds);
        foreach ($systemTempList as $temp) {
            if (!isset($tempIdsArr[$temp['id']])) {
                $result[] = $temp;
            }
        }


        return $result;

    }

    public function getXsContent($id, $status=1){
        $query = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content')
        ->from(self::tableName() . ' AS TEMP')
        ->where([
                'TEMP.id' => $id,
                'TEMP.status' => $status
                ]);

        return $this->cacheQueryOne($query);

//         return $query->one();
    }

    public function getSubTemplate($id, $status=1){
        $query = $this->find()->select('TEMP.id, TEMP.name, TEMP.descript, TEMP.content, TEMP.user_id, TEMP.step')
            ->from(self::tableName() . ' AS TEMP')
            ->where([
                'TEMP.id' => $id,
                'TEMP.status' => $status
            ]);

        return $this->cacheQueryOne($query);

//         return $query->one();
    }

}
