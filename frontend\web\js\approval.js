define(function (require) {
    var tab = require('tab');
    var module = {
        // 事件委托
        init: function () {
            $('body').on('click', '.approval-node-box [name=coapproval-checkbox]', function () {
                if ($(this).is(':checked')) {
                    $(this).next().next().show();
                } else {
                    $(this).next().next().hide();
                }
            });

            $('body').on('click', '.approval-node-box label[name=coapproval-label]', function () {
                $(this).prev().trigger('click');
            });

            $('body').on('click', '.add-node-ico', function () {
                var $box = $(this).parents('.approval-node-box');

                var cnt = $box.find('.approval-node:visible').length + 1;
                var $newNode = $box.find('.approval-node-temp .approval-node').clone();
                $newNode.find('.node-text').html(mainLang('step') + cnt);
                $newNode.appendTo($box);
            });

            $('body').on('click', '.del-node-ico', function () {
                $(this).parent().remove();
                var $box = $(this).parents('.approval-node-box');
                $box.find('.approval-node:visible').each(function (index) {
                    $(this).find('.node-text').html(mainLang('step') + (index + 1));
                });
            });

            // 筛选->查询
            $('body').on('click', '.filter-approval-list', function () {
                var type = $(this).attr('data-type');
                var filter = require('exp_list').getFilterParam();
                filter['is_form_filter'] = 1;
                var $activeTab = $('.exp_conetnt.active  .approval_tab.active');
                if ($activeTab.attr('data-type') === 'approval') {
                    var limit = $('.exp_conetnt.active .page-box .pager-select').val();
                    var page = Number($('.exp_conetnt.active .page-box .current.page-btn').text());
                    module.genApprovalListPage(type, filter, page, limit);
                } else if ($activeTab.attr('data-type') === 'approval_submitted') {
                    module.genApprovalSubmittedListPage(type, filter);
                } else if ($activeTab.attr('data-type') === 'approval_history') {
                    module.genApprovalHistoryListPage(type, filter);
                }
            });

            var users_select_input;
            // 弹框设置人员集合, 渲染 弹框
            $('body').on('click', '.user-collection-select .user-collection-input', function () {
                users_select_input = $(this);
                var users_type = users_select_input.attr('users_type');
                var users_roles = [];
                var users_ranges = [];
                if (users_select_input.attr('users_roles') !== '') {
                    users_roles = users_select_input.attr('users_roles').split(',');
                }
                if (users_select_input.attr('users_ranges') !== '') {
                    users_ranges = users_select_input.attr('users_ranges').split(',');
                }

                $.ajaxFn({
                    url: ELN_URL + '?r=company-setting/get-user-collection-popup',
                    data: {},
                    success: function (data) {
                        // console.log(data);
                        if (data.status === 1) {
                            $('.user-collection-popup').remove();
                            $('body').append(data.data.file);

                            var popup = $('.user-collection-popup');
                            // 内容选中渲染
                            // 初次打开 默认角色
                            if (!users_type) {
                                users_type = 'role_select_users';
                            }

                            // 类别选中渲染
                            popup.find('.users_type option[value='+ users_type +']').attr('selected', true);
                            // $('.user-collection-popup .users_type option[value='+ users_type +']').attr('selected', true);
                            // 切换选项
                            popup.find('.select_box').each(function () {
                                if ( users_type === $(this).attr('select_item') ) {
                                    $(this).removeClass('hidden');
                                } else {
                                    $(this).addClass('hidden');
                                }
                            });

                            // 渲染选中的 角色 + 范围
                            if (users_roles.length > 0 && users_ranges.length > 0) {
                                var item_box = popup.find('[select_item='+ users_type +']');
                                item_box.find('.users_roles option').each(function () {
                                    if (users_roles.indexOf($(this).attr('value')) !== -1) {
                                        $(this).attr('selected', true);
                                    } else {
                                        $(this).attr('selected', false);
                                    }
                                });
                                item_box.find('.users_ranges option').each(function () {
                                    if (users_ranges.indexOf($(this).attr('value')) !== -1) {
                                        $(this).attr('selected', true);
                                    } else {
                                        $(this).attr('selected', false);
                                    }
                                });
                                item_box.removeClass('hidden');
                            }

                            // fSelect 变为 下拉复选框
                            var opt = {
                                placeholder: mainLang('select'),
                                showSearch: false,
                                onNumDisplayed: false,
                                width: '180px',
                            };

                            var users_type_select = $('.user-collection-popup .users_type');
                            var users_roles_select = $('.user-collection-popup .users_roles');
                            var users_ranges_select = $('.user-collection-popup .users_ranges');

                            users_type_select.fSelect(opt);
                            users_roles_select.fSelect(opt);
                            users_ranges_select.fSelect(opt);

                            popup.modal('show');
                        }
                    }
                });
            });

            // 清空 input 框
            $('body').on('click', '.user-collection-select .user_collection_clear_check_btn', function () {
                var cur_input = $(this).parent('.user-collection-select').find('.user-collection-input');
                cur_input.attr('users_type', '');
                cur_input.attr('users_roles', '');
                cur_input.attr('users_ranges', '');
                cur_input.attr('value', '');
                cur_input.attr('title', '');
            });

            // 切换 users_type 改变选项
            $('body').on('change', '.user-collection-popup .users_type', function () {
                var cur_select_box = $(this).find('option:selected').attr('value');

                $('.user-collection-popup .select_box').each(function () {
                    if (cur_select_box === $(this).attr('select_item')) {
                        $(this).removeClass('hidden');
                    } else {
                        $(this).addClass('hidden');
                    }
                });
            });

            // 填充 input 框
            $('body').on('click', '.user-collection-popup ._conform_btn', function () {

                var popup = $(this).parents('.user-collection-popup');
                var cur_select_item = popup.find('.users-type-box .users_type option:selected').attr('value');
                var item_box = popup.find('[select_item='+ cur_select_item +']');

                var users_roles = [];
                var users_roles_name = [];
                item_box.find('.users_roles option:selected').each(function () {
                    users_roles.push($(this).attr('value'));
                    users_roles_name.push($(this).text());
                });
                var users_ranges = [];
                var users_ranges_name = [];
                item_box.find('.users_ranges option:selected').each(function () {
                    users_ranges.push($(this).attr('value'));
                    users_ranges_name.push($(this).text());
                });

                var show_name = users_roles_name + '(' + users_ranges_name + ')';

                // 未选中不生效，弹框拦截
                if (users_roles.length === 0 || users_ranges.length === 0) {
                    var pls_alert = mainLang('pls_select_role_and_range');
                    if (cur_select_item === 'superior_select_users') {
                        pls_alert = mainLang('pls_select_range');
                    }
                    $.showAlert(pls_alert);
                    return;
                }

                users_select_input.attr('users_type', cur_select_item);
                users_select_input.attr('users_roles', users_roles);
                users_select_input.attr('users_ranges', users_ranges);
                users_select_input.attr('value', show_name);
                users_select_input.attr('title', show_name);
                $.closeModal();
            });

            // 图标切换
            $('body').on('change', '.approval-users-chain-box .change_method_btn', function () {
                var obj = $(this);
                var type = obj.val();

                if (type === 'chain') {
                    obj.next().children('.chain-box').removeClass('hidden').attr('approval-visible', 'visible');
                    obj.next().children('.normal-box').addClass('hidden').attr('approval-visible', 'hidden');
                } else if (type === 'normal') {
                    obj.next().children('.chain-box').addClass('hidden').attr('approval-visible', 'hidden');
                    obj.next().children('.normal-box').removeClass('hidden').attr('approval-visible', 'visible');
                }
            });

            // 勾选 input 框显示审批组件
            $('body').on('change', '.show_users_with_approval', function () {
                var isCheck = $(this).prop('checked');
                if (isCheck) {
                    $(this).siblings('.users_with_approval_settings').removeClass('hidden');
                } else {
                    $(this).siblings('.users_with_approval_settings').addClass('hidden');
                }
            });

            // 获取痕迹列表 已迁移至tool.js showHistory()
            $('body').on('click', '.view-temp-history', function (e) {
                e.stopPropagation(); // 阻止冒泡，防止触发模板列表的点击事件，导致模板操作日志弹窗被关闭。                
                var tempId = $(this).attr('temp-id');
                var tempPower = $(this).attr('temp-power') || 0;
                var templateEffectMode = $(this).attr('effect-mode') || 1;
                var updateTime = $(this).attr('update-time') || '';
                console.log('加载模板操作日志，模板ID:', tempId, '模板生效模式:', templateEffectMode, '模板编辑权限:', tempPower, '模板更新时间:', updateTime);

                // 确保容器可见
                $('#template-history-dialog').show();
                // 加载组件
                renderComponent('/vue-ui/dist/templateHistoryDialog.js', '#template-history-dialog', {
                    templateId: tempId,
                    visible: true,
                    tempPower: tempPower,
                    templateEffectMode: templateEffectMode,
                    updateTime: updateTime,
                    onClose: function() {
                        $('#template-history-dialog').hide();
                        unrenderComponent('#template-history-dialog');
                    }
                });
            });
        },

        // 获取节点数据
        getNodes: function ($parent, $nodes, visible) {

            if(visible === 0){
                if (!$nodes) {
                    if ($parent) {
                        $nodes = $parent.find('.approval-node-box .approval-node');
                    } else {
                        $nodes = $('.approval-node-box .approval-node');
                    }
                }
            }
            else{
                if (!$nodes) {
                    if ($parent) {
                        $nodes = $parent.find('.approval-node-box .approval-node:visible');
                    } else {
                        $nodes = $('.approval-node-box .approval-node:visible');
                    }
                }
            }


            var approvalNodes = [];
            $nodes.each(function () {
                var approvalNode = {};
                var approval_user_ids = $(this).find('[name=user_ids]').attr('idbox');
                if (approval_user_ids) {
                    approvalNode.approval_user_ids = approval_user_ids.split(',');
                } else {
                    approvalNode.approval_user_ids = [];
                }

                $checkbox = $(this).find('[name="coapproval-checkbox"]');
                var coapproval_user_ids = $(this).find('[name=couser_ids]').attr('idbox');
                if ($checkbox.is(':checked') && coapproval_user_ids) {
                    approvalNode.coapproval_user_ids = coapproval_user_ids.split(',');
                } else {
                    approvalNode.coapproval_user_ids = [];
                }

                if (approvalNode.approval_user_ids.length > 0 || approvalNode.coapproval_user_ids.length > 0) {
                    approvalNodes.push(approvalNode);
                }
            });

            return approvalNodes;
        },

        // 获取节点用户集合数据
        getUsersNodes: function ($parent, $nodes, visible) {

            if(visible === 0){
                if (!$nodes) {
                    if ($parent) {
                        $nodes = $parent.find('.approval-node-box .approval-node');
                    } else {
                        $nodes = $('.approval-node-box .approval-node');
                    }
                }
            }
            else{
                if (!$nodes) {
                    if ($parent) {
                        $nodes = $parent.find('.approval-node-box .approval-node:visible');
                    } else {
                        $nodes = $('.approval-node-box .approval-node:visible');
                    }
                }
            }

            var approvalNodes = [];
            $nodes.each(function () {
                var approvalNode = {};
                var approval_users = {};
                var co_approval_users = {};

                var user_collection_input = $(this).find('.user-collection-input').first();
                var users_type, users_roles, users_ranges, users_show;
                users_type = user_collection_input.attr('users_type');
                users_roles = user_collection_input.attr('users_roles');
                users_ranges = user_collection_input.attr('users_ranges');
                users_show = user_collection_input.attr('value');

                if (users_type && users_roles && users_ranges) {
                    users_roles = users_roles.split(',');
                    users_ranges = users_ranges.split(',');
                    approval_users['users_type'] = users_type;
                    approval_users['users_roles'] = users_roles;
                    approval_users['users_ranges'] = users_ranges;
                    approval_users['users_show'] = users_show;
                } else {
                    approval_users = {};
                }
                approvalNode['approval_users'] = approval_users;

                // 合批
                var $checkbox = $(this).find('[name="coapproval-checkbox"]');
                var co_user_collection_input =  $(this).find('.user-collection-input').last();
                var co_users_type, co_users_roles, co_users_ranges, co_users_show;
                co_users_type = co_user_collection_input.attr('users_type');
                co_users_roles = co_user_collection_input.attr('users_roles');
                co_users_ranges = co_user_collection_input.attr('users_ranges');
                co_users_show = co_user_collection_input.attr('value');

                if ($checkbox.is(':checked') && co_users_type && co_users_roles && co_users_ranges) {
                    co_users_roles = co_users_roles.split(',');
                    co_users_ranges = co_users_ranges.split(',');
                    co_approval_users['co_users_type'] = co_users_type;
                    co_approval_users['co_users_roles'] = co_users_roles;
                    co_approval_users['co_users_ranges'] = co_users_ranges;
                    co_approval_users['co_users_show'] = co_users_show;
                } else {
                    co_approval_users = {};
                }
                approvalNode['co_approval_users'] = co_approval_users;

                if (Object.keys(approvalNode['approval_users']).length > 0 || Object.keys(approvalNode['co_approval_users']).length > 0) {
                    approvalNodes.push(approvalNode);
                }
            });

            return approvalNodes;
        },

        // 根据设置的 input checkBox 和 approvalDataBox 获取 approval_users_setting 设置
        dataWithApproval: function($checkBox, $nodesBox) {
            var dataWithApprovalChecked = $checkBox.prop('checked') ? 1 : 0;
            var dataWithApprovalFlag = false;
            var approvalDataBox, dataApprovalUsersType, dataApprovalUsersNodes;
            if (dataWithApprovalChecked) {
                approvalDataBox = $nodesBox;
                dataApprovalUsersType = approvalDataBox.find('[name=approval-type][approval-visible=visible]').attr('approval-type');
                if (dataApprovalUsersType === 'normal') {
                    // 正常情况
                    dataApprovalUsersNodes = this.getUsersNodes(approvalDataBox, '', 0); // 审批节点，未勾选主动分享需要审批时为空
                    dataWithApprovalFlag = dataApprovalUsersNodes.length !== 0;
                } else if (dataApprovalUsersType === 'chain') {
                    // 层级链
                    var users_ranges = approvalDataBox.find('.chain-box .users_ranges option:selected').attr('value');
                    var users_roles = approvalDataBox.find('.chain-box .users_roles option:selected').attr('value');
                    if (users_ranges && users_roles) {
                        dataApprovalUsersNodes = {
                            users_type: 'chain_select_users',
                            users_ranges: users_ranges,
                            users_roles: users_roles,
                        };
                    }
                    dataWithApprovalFlag = !!(users_ranges && users_roles);
                }
            }
            if (dataWithApprovalChecked && !dataWithApprovalFlag) {
                return false;
            }
            return {
                approval_users_type: dataApprovalUsersType,
                approval_users_nodes: dataApprovalUsersNodes,
            };
        },

        // 获取用户集合数据, 单一输入框
        getUsersSetting: function ($usersInput) {
            var usersSetting = {};
            var users_type, users_roles, users_ranges, users_show;
            users_type = $usersInput.attr('users_type');
            users_roles = $usersInput.attr('users_roles');
            users_ranges = $usersInput.attr('users_ranges');
            users_show = $usersInput.attr('value');

            if (users_type && users_roles && users_ranges) {
                users_roles = users_roles.split(',');
                users_ranges = users_ranges.split(',');
                usersSetting['users_type'] = users_type;
                usersSetting['users_roles'] = users_roles;
                usersSetting['users_ranges'] = users_ranges;
                usersSetting['users_show'] = users_show;
            }
            if ('{}' === JSON.stringify(usersSetting)) {
                return false;
            }
            return usersSetting;
        },

        /* 审核列表 begin */
        // ajax请求
        getApprovalListContent: function (type, filter, page, limit) {
            var data = {
                type: type,
                filter: filter,
                page: page || 1,
                limit: limit || getPageLimit('getApprovalListContent') || 15,
            };

            return $.ajaxFn({
                url: '/?r=approval/list',
                data: data,
                success: function (res) {
                    require('file');
                    setLimitToLocal('getApprovalListContent', data.limit);
                    $('.for_approval_count').html(res.data.approvalCnt);
                }
            }, undefined, true);
        },

        // 打开标签
        genApprovalListPage: function (type, filter, page, limit) {
            module.getApprovalListContent(type, filter, page, limit).then(function (res) {
                if (res.status === 1) {
                    var data = res.data;
                    var tagId = tab.getTag('getApprovalListContent');
                    if (tagId) {
                        tab.switchTag(tagId, data.contentHtml);
                    } else {
                        var obj = {
                            html: data.contentHtml,
                            func: 'getApprovalListContent',
                            params: [type],
                            name: data.tabName,
                            title: data.tabName,
                        };
                        tab.openTag(obj);
                    }
                    $('.for_approval_count').text(data.approvalCnt.total);
                }
            });
        },
        /* 审核列表 end */

        /* 我提交的审批 start */
        // ajax请求
        getApprovalSubmittedListContent: function (type, filter, page, limit) {
            var data = {
                type: type,
                filter: filter,
                page: page || 1,
                limit: limit || getPageLimit('getApprovalSubmittedListContent') || 15,
            };

            return $.ajaxFn({
                url: '/?r=approval/list-submitted',
                data: data,
                success: function (res) {
                    setLimitToLocal('getApprovalSubmittedListContent', data.limit);
                }
            }, undefined, true);
        },

        genApprovalSubmittedListPage: function (type, filter, page, limit) {
            module.getApprovalSubmittedListContent(type, filter, page, limit).then(function (res) {
                if (res.status === 1) {
                    var data = res.data;
                    var tagId = tab.getTag('getApprovalSubmittedListContent');
                    if (tagId) {
                        tab.switchTag(tagId, data.contentHtml);
                    } else {
                        var obj = {
                            html: data.contentHtml,
                            func: 'getApprovalSubmittedListContent',
                            params: [type],
                            name: data.tabName,
                            title: data.tabName,
                        };
                        tab.openTag(obj);
                    }
                    // $('.for_approval_submitted_count').text(data.approvalCnt.total);
                }
            });
        },
        /* 我提交的审批 end */

        /* 历史审核列表 begin */
        // ajax请求
        getApprovalHistoryListContent: function (type, filter, page, limit) {
            var data = {
                type: type,
                filter: filter,
                page: page || 1,
                limit: limit || getPageLimit('getApprovalHistoryListContent') || 15,
            };

            return $.ajaxFn({
                url: '/?r=approval/history-list',
                data: data,
                success: function (res) {
                    setLimitToLocal('getApprovalHistoryListContent', data.limit);
                }
            });
        },

        // 打开标签
        genApprovalHistoryListPage: function (type, filter, page, limit) {
            module.getApprovalHistoryListContent(type, filter, page, limit).then(function (res) {
                if (res.status === 1) {
                    var data = res.data;
                    var tagId = tab.getTag('getApprovalHistoryListContent');
                    if (tagId) {
                        tab.switchTag(tagId, data.contentHtml);
                    } else {
                        var obj = {
                            html: data.contentHtml,
                            func: 'getApprovalHistoryListContent',
                            params: [type],
                            name: data.tabName,
                            title: data.tabName,
                        };
                        tab.openTag(obj);
                    }
                }
            });
        },
        /* 历史审核列表 end */

        pageFn: function (type, cbFn) {
            var $pageBox = $('.exp_conetnt.active .page-box');
            var total = $pageBox.attr('data-num');
            var page = $pageBox.attr('data-page') || 1;
            var fnName = $('.exp_title .tag.on').attr('data-func');
            var limit = getPageLimit(fnName) || $pageBox.attr('data-limit') || 15;
            $pageBox.pagination(total, {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                items_per_page: limit || 15,
                callback: function (pageIndex) {
                    var limit = 15;
                    var $pageSelect = $pageBox.find('.pager-select');
                    if ($pageSelect.length > 0) {
                        limit = $pageSelect.val();
                    }

                    require(['approval'], function (module) {
                        var filter = require('exp_list').getFilterParam();
                        if (cbFn) {
                            cbFn(type, filter, parseInt(pageIndex) + 1, limit);
                        }
                        // module.genApprovalListPage(type, filter, parseInt(pageIndex) + 1, limit);
                    });
                }
            });
        }
    };

    module.init();

    return module;
});
