<?php
return [
    //dgh 消息部分
    'send_user' => 'Sender',
    'receive_user' => 'Recipient',
    'msg_title' => 'Message Title',
    'msg_content' => 'Message Content',
    'msg_type' => 'Message Type',
    'msg_id' => 'Message Id',

    'code_is' => 'Number is：',
    'is_exp' => ' experiment',
    'need_sign' => 'waiting for witness',
    'sign_msg' => 'Witness Message',
    'comment_msg' => 'Comment Message',
    'praise_msg' => 'Thumb up Message',
    'down_book_msg' => 'Export NoteBook Message',
    'sys_msg' => 'System Message',
    'share_msg' => 'Sharing Message',
    'for_you_code' => ' experiment',
    'doing' => 'doing',
    'for_you_comment' => 'Comment your experiment:{0}',
    'for_you_praise' => 'Thumb up your experiment:{0}',
    'for_you_down_book' => 'Export notebook number:{0} ',
    'code_is_sign' => 'Experiment page number:{0} need you to witness',
    'sign_refuse' => 'Rejected experiment: {0}',
    'sign_allow' => 'Approved experiment: {0}',
    'no_sign_day' => 'Since more than {1} day(s) passed after creation, the following InELN experiments needs to be submitted for witness. <br>experiment: {0} ',
    'exp_witness_remind_subject' => 'Witness Message',
    'exp_witness_remind_content' => 'Since more than {0} day(s) passed after creation, the following InELN experiments needs to be submitted for witness.<br>',
    'share_you' => 'Shared to',
    'cancle_share_you_exp' => 'Cancel sharing',
    'exp' => 'experiment',
    'temp' => 'Template',
    'database' => 'Entries',
    'share_database' => '{0} shared the entries ({1} component {2}) to you, please pay attention.',
    'share_database_cancel' => '{0} cancelled the entries ({1} component {2}).',
    'indraw_database' => 'inDraw',
    'material_database' => 'materials and instruments',
    'reference_database' => 'reference',
    'diy_database' => 'custom form',
    'has_exp' => 'have {0} experiments',
    'reason_is' => ' Reason ：{0}',
    'dear_sir' => 'Dear：<br/>',
    'integle_ineln' => '<br/>Integle InELN',
    'click_url' => '<br/>(If the url is invalid after click, please copy this URL and paste it to your browser：{0})',
    'sign_allow_cont' => ' approved the witness of experiment ({0})',
    'sign_refesue_cont' => ' rejected the witness of experiment ({0}). Reason:{1}',
    'msg' => 'Messages',
    'project' => 'Project',

    'sign_remind_title' => 'Witness Message',
    'sign_msg_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) Request your witness, please review,Approval process:{4}.',
    'sign_email_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) Request your witness, please review.<br/>Approval process:{4}.',
    'sign_agree_msg_content' => '{0} witnessed your experiment {1} (Title:{2}, Created by:{3}, Created time: {4}),Approval process:{6}.',
    'sign_agree_email_content' => '{0} witnessed your experiment {1} (Title:{2}, Created by:{3}, Created time: {4}).<br/>Approval process:{6}.',
    'sign_refuse_msg_content' => '{0} rejected your experiment {1}  (Title:{2}, Created by:{3}, Created time: {4}), Rejection reason:{5},Approval process:{6}.',
    'sign_refuse_email_content' => '{0} rejected your experiment {1}  (Title:{2}, Created by:{3}, Created time: {4}), Rejection reason:{5}.<br/>Approval process:{6}.',

    'pretrial_remind_title' => 'Pre-approval Message',
    'pretrial_msg_content' => 'Experiment {0}(Title:{1}, Created by:{2}, created time:{3}) applied a pre-plan approval{4}, request your approval. The reason:{5},Approval process:{6}.',
    'pretrial_email_content' => 'Experiment {0}(Title:{1}, Created by:{2}, created time:{3}) applied a pre-plan approval{4}, request your approval. The reason:{5}.<br/>Approval process:{6}.',
    'pretrial_agree_msg_content' => '{0} approved your experiment {1} pre-plan(Title:{2}, Created by:{3}, Created time:{4}),Approval process:{6}.',
    'pretrial_agree_email_content' => '{0} approved your experiment {1} pre-plan(Title:{2}, Created by:{3}, Created time:{4}).<br/>Approval process:{6}.',
    'pretrial_refuse_msg_content' => '{0} rejected your experiment {1}( Title:{2}, Created by:{3}, Created time:{4}), Rejection reason:{5},Approval process:{6}.',
    'pretrial_refuse_email_content' => '{0} rejected your experiment {1}( Title:{2}, Created by:{3}, Created time:{4}), Rejection reason:{5}.<br/>Approval process:{6}.',
    'all_setting_content' => '(All experiments need pre-plan)',
    'indraw_setting_content' => '(Hazardous chemicals are in InDraw component, need pre-plan)',
    'project_setting_content' => '(Project{0} need pre-plan)',

    'signing_remind_title' => 'Signature Message',
    'signing_msg_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) ({4}) request your sign. The reason:{5},Approval process:{6}.',
    'signing_email_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) ({4}) request your sign. The reason:{5}.<br/>Approval process:{6}.',
    'signing_agree_msg_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) ({4}) set up a signature approval, {5} approved,Approval process:{7}.',
    'signing_agree_email_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) ({4}) set up a signature approval, {5} approved.<br/>Approval process:{7}.',
    'signing_refuse_msg_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) ({4}) set up a signature approval, {5} rejected, Rejection reason:{6},Approval process:{7}.',
    'signing_refuse_email_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) ({4}) set up a signature approval, {5} rejected, Rejection reason:{6}.<br/>Approval process:{7}.',

    'reopen_remind_title' => 'Reopen Message',
    'reopen_msg_content' => '{0} is reopening the closed experiment {1}(Title:{2}, Created by:{3}, Created time:{4}), request your approval, Reason：{5}, please check it,Approval process:{6}.',
    'reopen_email_content' => '{0} is reopening the closed experiment {1}(Title:{2}, Created by:{3}, Created time:{4}), request your approval, Reason：{5}, please check it.<br/>Approval process:{6}.',
    'reopen_agree_msg_content' => 'Experiment {0}( Title:{1}, Created by: {2}, Created time: {3}) applied for a reopening approval, {4} approved,Approval process: {6}.',
    'reopen_agree_email_content' => 'Experiment {0}( Title:{1}, Created by: {2}, Created time: {3}) applied for a reopening approval, {4} approved.<br/>Approval process: {6}.',
    'reopen_refuse_msg_content' => 'Experiment {0}( Title:{1}, Created by: {2}, Created time: {3}) applied for a reopening approval, {4} refused, Rejection reason:{5},Approval process: {6}.',
    'reopen_refuse_email_content' => 'Experiment {0}( Title:{1}, Created by: {2}, Created time: {3}) applied for a reopening approval, {4} refused, Rejection reason:{5}.<br/>Approval process: {6}.',

    'node_no' => '( Node {node_no} )',
    'book_remind_title' => 'Notebook Message',
    'book_msg_content' => '{0} Notebook application: {1} needs you to approve',
    'book_msg_content_new' => '{book_creator} Notebook creation (Book Name: {book_name}, Group: {book_group_name}) needs your approval, approval Process：{approve_route}。',
    'book_email_content_new' =>
        "{book_creator}'s new notebook (Book Name：{book_name}, Group：{book_group_name}) need your approval.<br/>" .
        "Approval process: {approve_route}。<br/>",

    'book_email_content' => 'Dear user,<br/><br/>{0} Notebook creation application (book name: {1}) needs you to approve.<br/>If the link not available, please visit the website: {2}<br/><br/>Integle InELN',
    'book_agree_msg_content' => '{0} approved your creation of new notebook (Notebook Name:{1}, Group:{2},Project:{3})， Approval process: {5}.',
    'book_agree_email_content' => '{0} approved your creation of new notebook (Notebook Name:{1}, Group:{2},Project:{3}).<br/> Approval process: {5}.',
    'book_refuse_msg_content' => '{0} rejected your creation of new notebook (Notebook Name:{1}, Group:{2},Project:{3})，Rejection Reason:{4}, Approval process: {5}.',
    'book_refuse_email_content' => '{0} rejected your creation of new notebook (Notebook Name:{1}, Group:{2},Project:{3})，Rejection Reason:{4}.<br/> Approval process: {5}.',

    'template_remind_title' => 'Template Message',
    'template_msg_content' => '{0} created/modified {1} (Title:{2}, Created by:{3}, Created time:{4}), request your approval, please check it. Approval process:{5}.',
    'all_temp' => 'global template',
    'method_temp' => 'method template',
    'table_temp' => 'table template',
    'template_email_content' => '{0} created/modified {1} (Title:{2}, Created by:{3}, Created time:{4}), request your approval, please check it. <br/>Approval process:{5}.',
    'template_agree_msg_content' => '{0} approved the creation/modification of {1} (Title:{2}, Created by:{3}, Created time:{4}), please view. Approval process:{5}.',
    'template_agree_email_content' => '{0} approved the creation/modification of {1} (Title:{2}, Created by:{3}, Created time:{4}), please view. <br/>Approval process:{5}.',
    'template_refuse_msg_content' => '{0} rejected your creation/modification of {1} (Title:{2}, Created by:{3}, Created time:{4}),{6} request your approval, please view. Approval process:{5}.',
    'template_refuse_email_content' => '{0} rejected your creation/modification of {1} (Title:{2}, Created by:{3}, Created time:{4}),{6} request your approval, please view. <br/>Approval process:{5}.',

    'instrument_repair_message' => 'Instrument repair message',
    'instrument_repair_message_content' => '{0} reported a instrument repair (Instrument name: {1}，Device ID:{2}), reason:{3}.',
    'instrument_repair_email_content' => '{0} reported a instrument repair (Instrument name: {1}，Device ID:{2}), reason:{3}.',
    'instrument_record_check_message' => 'Instrument record approval message',
    'instrument_record_check_message_content' => '{0} is applying for a review of the {3} of the instrument (Instrument name:{1} Device ID: {2})，please review.  Approval process: {4}.',
    'instrument_record_check_email_content' => 'Dear user,<br/><br/>Instrument ({1},(Device ID：{2}) {3} applied for check by {0}<br/>Integle InELN',
    'instrument_record_check_agree_msg_content' => '{0} approved the {1} of the instrument (Instrument name:{2} Device ID: {3}). Approval process: {4}.',
    'instrument_record_check_refuse_msg_content' => '{0} rejected the {1} of the instrument (Instrument name:{2} Device ID: {3})，reason：{5}.Approval process: {4}.',
    'instrument_record_check_agree_email_content' => 'Dear user,<br/><br/>Instrument ({1},(Device ID：{2}) {3} approved<br/>Integle InELN',
    'instrument_record_check_refuse_email_content' => 'Dear user,<br/><br/>Instrument ({1},(Device ID：{2}) {3} refused(reason：{0})<br/>Integle InELN',

    'instrument_status_message' => 'Instrument status approval message',
    'instrument_status_change_message' => '{0} is applying to {1} the instrument (Instrument name:{2}, Device ID: {3}), please review. Approval process: {4}.',
    'instrument_status_change_agree_msg_content' => '{0} approved to {1} the instrument (Instrument name:{2}, Device ID: {3}). Approval process: {4}.',
    'instrument_status_change_refuse_msg_content' => '{0} rejected to {1} the instrument (Instrument name:{2}, Device ID: {3}), reason:{5}. Approval process: {4}.',


    'experiment_reminder_title' => 'Notification Message',
    'experiment_reminder_update_msg_content' => '{0} changed the experiment {1} (Title:{2}, Created by: {3}, Created time:{4}). Remind Content:{5}.',
    'experiment_reminder_time_point_msg_content' => 'Experiment {0}(Title:{1},Created by: {2}, Created time: {3} )Set a reminder, remind date is {4}. Remind Content:{5}.',
    'experiment_reminder_period_msg_content' => 'Experiment {0}(Title:{1},Created by: {2}, Created time: {3} )Set a periodic reminder, remind every {4} days. Remind Content:{5}.',
    'experiment_reminder_email_content' => 'Dear user,<br/><br/>Notification of {0}:<br/>{1}<br/>If the link is not available, please visit the website: {2}<br/><br/>Integle InELN',

    'xsheet_condition_email_content' => 'There is a reminder from Experiment {0}(Title:{1}, Created by:{2},Created time:{3}) component Intable {4}, please check it.',
    'xsheet_condition_email_title' => 'InTable Date reminder message',

    'Instrument_expiry_email_content' => 'Instrument ({0},Device ID：{1}) check expires in {2} days,please schedule a check <br/><br/>',
    'Instrument_expiry_email_title' => 'Instrument check expiry reminder',

    'instrument_reminder_title' => 'Instrument Notification Message',
    'instrument_reminder_period_msg_content' => 'Instrument (Instrument name:{0}，Device ID:{1}) Set a periodic reminder, remind every {2} days. Remind Content:{3}.',
    'instrument_reminder_time_point_msg_content' => 'Instrument (Instrument name:{0}，Device ID:{1}) Set a reminder, remind date is {2}. Remind Content:{3}.',
    'instrument_reminder_email_content' => 'Dear user,<br/><br/>Notification of Instrument{0}:<br/>{1}<br/><br/>Integle InELN',
    'instrument_booking_title' => 'Instrument reservation',
    'collaboration_message_title'=>'Work order notification message',

    'collaboration_content_for_create_cc' =>'{0} Dear user,<br/><br/>As a participant, please know the new situation of the work order WO{1},work order ID: <a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_create_assigned' =>'{0} assigned the work order <a href="{1}">WO{2}</a> to you (Created by:{3}, Created time:{4}).',

    'collaboration_content_for_confirm'=>'{0} Confirmed the work order <a href="{1}">WO{2}</a> (Created by: {3}, Created time:{4}){5}.',
    'collaboration_content_for_confirm_cc' =>'{0} Hello!<br/><br/>As a participant, please know the new situation of the ticket R{1}, work order ID: <a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_confirm_assigned' =>'{0} assigned the work order <a href="{2}">WO{1}</a> to you (Created by:{3}, Created time:{4}){5}.',
    'batch_collaboration_content_for_confirm_assigned' =>
        '{user_name} assigned you to work order{wo_ids} .',
    'wo_Ids_with_link' => '<a href="{wo_link_url}">WO{wo_id}</a>(Created by:{wo_creator}, Created time:{wo_create_time})',

    'collaboration_content_for_assign_cc'=>'As a participant, please know the new situation of the work order R{1}, work order ID：<a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_assign_assigned'=>'{0} Hello! <br/><br/>User {1} has assigned you to work on a work order, work order ID: <a href="{2}">WO{3}</a>(Click on ID to see details).',

    'collaboration_content_for_resolve'=>'{0} solved the work order <a href="{2}">WO{1}</a> (Created by: {3}, Created time:{4}){5}.',
    'batch_collaboration_content_for_resolve' =>
        '{operate_username} solved your work order{wo_ids} .',

    'collaboration_content_for_resolve_cc'=>'As a participant, please know the new situation of the work order R{1}, work order ：<a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_resolve_assigned'=>'{0} Hello! <br/><br/>The ticket assigned to you by user {1} has been resolved, work order : <a href="{2}">WO{3}</a>(Click on ID to see details).',

    'collaboration_content_for_close'=>'User {0} closed your work order <a href="{1}">WO{2}</a>(Created by: {3}, Created time:{4}){5}.',
    'batch_collaboration_content_for_close' =>
        '{operate_username} closed your work order {wo_ids}</a> .',

    'collaboration_content_for_close_cc'=>'As a participant, please know the new situation of the work order R{1}, work order: <a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_close_assigned'=>'User {1} the work order assigned to you has been closed, work order ID: <a href="{2}">WO{3}</a>（Click for detail）。',

    'collaboration_content_for_active_cc'=>'As a participant, please know the new situation of the work order R{1}, work order: <a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_active_assigned'=>'The work order assigned to you by user {0} has been activated, work order: <a href="{1}">WO{2}</a>(Click on ID to see details).',

    'collaboration_content_for_edit_cc'=>'As a participant, please know the new situation of the work order R{1}, work order: <a href="{2}">WO{1}</a>(Click on ID to see details).',
    'collaboration_content_for_edit_assigned'=>'User {0} edit your work order <a href="{1}">WO{2}</a>(Created by: {3}, Created time:{4}){5}.',
    'collaboration_detail'=>',Content:{0}',
    'notebook_msg'=>'Notebook message',
    'notebook_msg_content'=>'{0} changed your group of notebook(Name:<a href="{2}">{1}</a>,Serial number:{3}). The original group was {4}，the present group is {5}，Changed reason :{6}.',
    'dear'=>'Dear',
    'notebook_msg_content_for_master'=>'{0} changed {1} group of notebook(Name:<a href="{3}">{2}</a>,Serial number:{4}). The original group was {5}，the present group is {6}，Changed reason :{7}.',
    'new_notebook_msg_content_for_master' => '{username} changed the {book_code}({book_name}) from {group_name} to {to_group_name}, reason: {reason}.',

    'module_reedit_remind_title' => 'Module Re-edit Message',
    'module_reedit_msg_content' => '{0} Experiment {1} needs you to approve, Reason:{2}',
    'module_reedit_email_content' => 'Dear user,<br/><br/>{0}Experiment {1} needs you to approve. Reason:{2}.<br/>If the link not available, please visit the website: {3}<br/><br/>Integle InELN',
    'module_reedit_agree_msg_content' => '{0} re-edit approved experiment: {1}.',
    'module_reedit_agree_email_content' => 'Dear user,<br/><br/>Your experiment {0} re-edit approved by {1}.<br/>If the link not available, please visit the website: {2}<br/><br/>Integle InELN',
    'module_reedit_refuse_msg_content' => '{0} re-edit rejected experiment: {1}. Reason:{2}.',
    'module_reedit_refuse_email_content' => 'Dear user,<br/><br/>Your experiment {0} re-edit rejected by {1}. Reason:{2}<br/>If the link not available, please visit the website: {3}<br/><br/>Integle InELN',

    'comment_msg_title' => 'Comment Message',
    'add_comment_msg_content' => '{0} remarked the experiment {1} Component {2} (Name:{3}, Created by:{4}, Created time:{5}), Remark content:{6}',
    'del_comment_msg_content' => '{0}Removed annotation/comments in experiment {1}{2} modules',
    'clear_comment_msg_content' => '{0} cleared annotation/comment in experiment {1}',

    'share_remind_title' => 'Experiment Sharing Message',
    'share_remind_content' => '{0} is sharing the experiment {1}, request your approval, Approval process:{2}.',
    'share_remind_email_content' => '{0} is sharing the experiment {1}, request your approval. <br/>Approval process:{2}.',
    'experiment_info' => '(Title:{0}, Created by:{1}, Created time:{2})',

    'share_result_title' => 'Experiment Sharing Message',
    'share_agree_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}), Applied for a sharing approval, {4} approved, Approval process:{6}.',
    'share_refuse_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}), Applied for a sharing approval, {4} refused, Rejection reason:{5}, Approval process:{6}.',
    'share_agree_email_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}), Applied for a sharing approval, {4} approved. <br/> Approval process:{6}.',
    'share_refuse_email_content' => 'Experiment {0} (Title:{1}, Created by:{2}, Created time:{3}), Applied for a sharing approval, {4} refused, Rejection reason:{5}. <br/>Approval process:{6}.',

    // 修改记录本名称消息翻译
    'update_book_name_content' => '{0} is applying for changing the name of the notebook (Name:{1},Group:{2}) , from {3} to {4}，please review. Approval process: {5}.',
    //修改记录本邮件消息翻译
    'update_book_name_email_content' =>
        'Dear user, <br/><br/>{0} is updating notebook name: ({1} -> {2}) need your approval.<br/> (If the link not available, please visit the website: {3})<br/><br/>Integle InELN',
    //审批结果邮件内容
    'update_book_name_agree_msg_content' =>  '{0} approved your application for changing the name of the notebook (Name:{1},Group:{2}) from {3} to {4}，Approval process: {5}.',
    'update_book_name_refuse_msg_content' => '{0} rejected your application for changing the name of the notebook (Name:{1},Group:{2}) from {3} to {4}，Rejection Reason:{6}. Approval process: {5}.',
    'update_book_name_agree_email_content' =>
        'Dear user, <br/><br/>your notebook name update approval ({0} -> {1}) was approved by {2}.<br/> (If the link not available, please visit the website: {3})<br/><br/>Integle InELN',
    'update_book_name_refuse_email_content' =>
        'Dear user, <br/><br/>Your notebook name update approval ({0} -> {1}) was rejected by {2}.<br/> (If the link not available, please visit the website: {3})<br/><br/>Integle InELN',

    'cbg_msg_content' => '{0} is moving notebook {1} (notebook {2}) from {3} to {4}, {5}please <a href="{6}">approve</a>',
    'cbg_remark' => 'reason:{0} ',

    'book_move_approving' => 'The notebook has change book group approval not finished, please finish the approval first',
    'change_remark' => ',reason:{0}',
    'change_book_group_agree_msg_content' =>
        "{0} approved your application for changing {1}'s notebook(Name:{2},Number:{3})group from {4} to {5}，Approval process: {7}.",
    'change_book_group_refuse_msg_content' =>
        "{0} rejected your application for changing {1}'s notebook(Name:{2},Number:{3}) group from {4} to {5}，Rejection Reason:{6},Approval process: {7}.",
    'change_book_group_content' =>
        "{0} is applying for changing {1}'s notebook (Name:{2},Number:{3}) group from {4} to {5}{6},please review,Approval process:{7}.",
    'change_book_group_agree_email_content' =>
        "{0} approved your application for changing {1}'s notebook(Name:{2},Number:{3})group from {4} to {5}.<br/>Approval process: {7}.",
    'change_book_group_refuse_email_content' =>
        "{0} rejected your application for changing {1}'s notebook(Name:{2},Number:{3}) group from {4} to {5}，Rejection Reason:{6}.<br/>Approval process: {7}.",
    'change_book_group_email_content' =>
        "{0} is applying for changing {1}'s notebook (Name:{2},Number:{3}) group from {4} to {5}{6},please review.<br/>Approval process:{7}.",
    'temp_share_remind_title' => 'Template Sharing Message',
    'temp_share_remind_content' => '{0} shared {1} (Title:{2}, Created by:{3}, Created time:{4}), request your approval, please view. Approval process: {5}',
    'temp_share_remind_content_email' => '{0} shared {1} (Title:{2}, Created by:{3}, Created time:{4}), request your approval, please view.<br/> Approval process: {5}',

    'temp_share_result_title' => 'Template Sharing Message',
    'temp_share_agree_content' => '{0} approved the sharing of {1} (Title:{2}, Created by:{3}, Created time:{4}),please view.Approval process: {5}',
    'temp_share_refuse_content' => '{0} rejected your sharing of {1} (Title:{2}, Created by:{3}, Created time:{4}),{6}please view.Approval process: {5}',
    'temp_share_agree_email_content' => '{0} approved the sharing of {1} (Title:{2}, Created by:{3}, Created time:{4}),please view.<br/>Approval process: {5}',
    'temp_share_refuse_email_content' => '{0} rejected your sharing of {1} (Title:{2}, Created by:{3}, Created time:{4}),{6}please view.<br/>Approval process: {5}',
    'temp_refuse_reason' => 'Rejection reason:{0}，',
    'share_msg_1' => 'Sharing Message',

    'exp_share_msg' => '{0} shared the experiment {1}(Title:{2}, Created by:{3}, Created time:{4})to you, please pay attention, thank you!',

    'temp_share_remind_email_title' => 'Template share remind email',
    'remind_temp_share_receiver_temp' => 'Template Name: <a href="{temp_link}" target="_blank">{temp_name}</a>, Template Creator: {temp_creator}，Template Create time: {temp_create_time}',
    'temp_share_remind_email_content' => '{share_creator} {temps} {to_you}, {pls_check}',
    'remind_share_creator_name' => '{share_creator_name} shared',
    'to_you' => ' to you',
    'pls_receive' => 'please check.',

    'co_auth_book_msg_title' => 'Notebook co-authoring remind message',
    'co_auth_book_detail' => 'Book Name: {book_name}, Book Creator: {book_creator}, Create Time: {book_create_time}',
    'book_code_with_link' => '<a href="{book_link}" target="_blank">{book_code}</a>',
    'remind_message_content' => '{remind_creator} invited you to {operation_name} book {book_code} {book_detail_str}. ',
    'co_auth_book' => 'Co-author',

    'wo_remind_content' => '{0}{1}<a href="{2}">WO{3}</a>({4}, created by: {5}, Receiver：{6}), please <a href="' . ELN_URL . '?route=approval&type=14  " target="_blank">dispose</a> it in time. Approval process: {7}.',
    'wo_agree_content' => '{0} approved to {1} <a href="{2}">WO{3}</a>({4}, created by: {5}, Receiver：{6}). Approval process: {7}.',
    'wo_refuse_content' => '{0} rejected to {1} <a href="{2}">WO{3}</a>({4}, created by: {5}, Receiver：{6}),Rejection Reason:{8}. Approval process: {7}.',

    'coauthor_sign_msg_title' => 'Notebook coauthor_sign remind message',
    'coauthor_sign_remind_content' => 'Co-authored experiment {0} (Title:{1}, Created by:{2}, Created time:{3}) , request your witness, please review the co-edit components. Approval process:  {4} .',
    'coauthor_sign_agree_content' => '{0} approved the co-edit components in the co-authored experiment  {1} (Title:{2}, Created by:{3}, Created time: {4}). Approval process: {5} .',
    'coauthor_sign_refuse_content' => '{0} rejected  the co-edit components in the co-authored experiment  {1} (Title:{2}, Created by:{3}, Created time: {4}). Reason:{6}. Approval process: {5} .',
];
