<div class="modal fade audit_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <?php if ($publish == 1): ?>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('temp', 'publish_template'); ?></h4>
                <?php endif; ?>
                <?php if ($publish == 0): ?>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('temp', 'approval_flow'); ?></h4>
                <?php endif; ?>
            </div>
            <div class="modal-body">
                <?php if ($publish == 1): ?>
                <!-- 发布模板部分 -->
                <div class="dialog-tip" style="margin-bottom: 10px; margin-top: 10px;"><?php echo Yii::t('temp', 'publish_tip'); ?></div>
                <div class="publish-container text-center">
                    <div class="input_part" style="margin-bottom: 15px;">
                        <label class="iblock vertical-top" style="width: 70px; line-height: 30px; text-align: right;"><?php echo Yii::t('temp', 'version'); ?></label>
                        <input type="text" class="angle_input iblock pop_input_con" 
                            style="width: 350px;" id="template_version" 
                            value="V<?php echo isset($maxInnerVersion) ? (intval($maxInnerVersion) + 1) : '1'; ?>.0">
                    </div>
                    <div class="input_part" style="margin-bottom: 15px;">
                        <label class="iblock vertical-top" style="width: 70px; line-height: 30px; text-align: right;"><?php echo Yii::t('temp', 'effect_date'); ?></label>
                        <div class="date-select-container" style="display: inline-block; width: 350px;">
                            <select id="effect_date_type" style="width: 100px; height: 30px; margin-right: 10px;">
                                <option value="immediate"><?php echo Yii::t('temp', 'immediate'); ?></option>
                                <option value="custom"><?php echo Yii::t('temp', 'custom'); ?></option>
                            </select>
                            <input type="text" id="effect_date" class="datepicker" style="width: 230px; height: 30px; display: none;" 
                                value="<?php echo date('Y-m-d', strtotime('+1 day')); ?>" readonly>
                        </div>
                    </div>
                </div>
                
                <hr style="margin: 10px 0; border: 0; border-top: 1px solid #e5e5e5;">
                
                <h4 style="margin: 10px 0;  text-align: left;"><?php echo Yii::t('temp', 'approval_flow'); ?></h4>
                
                <?php endif; ?>

                <?php if (!empty($approvalNodes)) { ?>
                    <div class="approval-container text-center">
                        <div class="approval-nodes-wrapper">
                            <?php
                                foreach ($approvalNodes as $key => $node) {
                                    if(isset($node['approval_user_ids']) && !empty($node['approval_user_ids'])){
                                        // 不再过滤当前用户
                                        $approvalUserIds = $node['approval_user_ids'];
                            ?>

                            <div class="input_part approval-node-box">
                                <span><?=Yii::t('views/applysetting', 'step') . ($key + 1);?></span>

                                <!-- 所有审批人统一使用多选框 -->
                                <select class="angle_input iblock signer_select approval_select" multiple="multiple">
                                    <?php foreach ($approvalUserIds as $uid) { ?>
                                    <option value="<?php echo $uid; ?>" selected><?php echo \frontend\core\CommonServer::displayUserName($approvalUsers[$uid]);?></option>
                                    <?php } ?>
                                </select>
                                
                                <?php if($key === 0 && empty($node['coapproval_user_ids'])) { ?>
                                    <!-- <i class="ask-ico font-ico hover-show-info"><div style="left:32px;line-height: 25px;z-index: 1;"><?php echo Yii::t('sign', 'approval_users');?></div></i> -->
                                <?php } ?>

                                <?php 
                                // 协审人部分
                                if (!empty($node['coapproval_user_ids'])) { 
                                    // 不再过滤当前用户
                                    $coApprovalUserIds = $node['coapproval_user_ids'];
                                ?>
                                    <span> & </span>
                                    
                                    <!-- 所有协审人统一使用多选框 -->
                                    <select class="angle_input iblock signer_select coapproval_select" multiple="multiple">
                                        <?php foreach ($coApprovalUserIds as $uid) { ?>
                                        <option value="<?php echo $uid; ?>" selected><?php echo \frontend\core\CommonServer::displayUserName($approvalUsers[$uid]);?></option>
                                        <?php } ?>
                                    </select>
                                    
                                    <?php if($key === 0) { ?>
                                        <!-- <i class="ask-ico font-ico hover-show-info"><div style="left:32px;line-height: 25px;z-index: 1;"><?php echo Yii::t('sign', 'approval_users');?></div></i> -->
                                    <?php } ?>
                                <?php } ?>
                            </div>

                            <?php
                                }
                            }?>
                        </div>
                    </div>
                <?php } else { ?>
                <div class="text-center approval-info">
                    <span><?php echo Yii::t('temp', 'approver'); ?>：</span>
                    <?php if(!empty($approverNames)): ?>
                        <div class="approver-list">
                            <?php
                            $length = count($approverNames);
                            foreach($approverNames as $key => $n):
                            ?>
                                <span class="approver-name"><?php echo $n; ?></span>
                                <?php if($key != $length - 1): ?>
                                <span class="approver-arrow">-></span>
                                <?php endif;?>
                            <?php endforeach;?>
                        </div>
                    <?php endif;?>
                </div>
                <?php } ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('base', 'cancel'); ?></button>
                <?php if($publish == 0) { ?>                
                    <button type="button" class="btn btn-primary <?= Yii::$app->request->get('reload') ? 'reload' : '' ?>"
                            id="temp_audit_submit"
                            data-id="<?php echo $tempId; ?>"><?php echo Yii::t('base', 'ok'); ?></button>
                <?php } else { ?>
                    <button type="button" class="btn btn-primary <?= Yii::$app->request->get('reload') ? 'reload' : '' ?>"
                            id="publish_audit_submit"
                            data-id="<?php echo $tempId; ?>"><?php echo Yii::t('base', 'ok'); ?></button>                       
                <?php } ?>                
            </div>
        </div>
    </div>
</div>

<!-- 在页面底部添加以下脚本 -->
<script>
$(document).ready(function() {
    // 初始化多选框
    $('.signer_select').each(function() {
        $(this).fSelect({
            placeholder: '<?php echo Yii::t('base', 'select'); ?>',
            numDisplayed: 3,
            overflowText: '{n} <?php echo Yii::t('base', 'selected'); ?>',
            noResultsText: '<?php echo Yii::t('base', 'search_no_results'); ?>',
            searchText: '<?php echo Yii::t('base', 'search'); ?>',
            showSearch: true,
            selectAll: true
        });
    });
    
    // 确保禁用的多选框在提交时也能获取到值
    $('#temp_audit_submit, #publish_audit_submit').on('click', function() {
        // 临时启用所有禁用的多选框，以便能获取其值
        $('.signer_select[disabled]').prop('disabled', false);
    });
    
    <?php if ($publish == 1): ?>
    // 添加日期选择类型切换事件
    $('#effect_date_type').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#effect_date').show();
        } else {
            $('#effect_date').hide();
        }
    });
    <?php endif; ?>
});
</script>

<style>
/* 确保单个输入框和多选框长度一致 */
.single-approver .angle_input,
.single-coapprover .angle_input,
.signer_select {
    width: 200px !important;
    display: inline-block;
    vertical-align: middle;
}

/* 确保审批人和协审人在同一行 */
.approval-node-box {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 10px;
}

.approval-node-box > span,
.approval-node-box > div,
.approval-node-box > select,
.approval-node-box > i {
    margin-right: 5px;
    white-space: nowrap;
}

/* 调整多选框的样式 */
.fs-wrap {
    display: inline-block;
    vertical-align: middle;
    width: 200px !important;
}

/* 整体居中布局 */
.approval-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
}

.approval-nodes-wrapper {
    display: inline-block;
    text-align: left;
}

/* 审批人信息样式 */
.approval-info {
    padding: 20px 0;
}

.approver-list {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    max-width: 500px;
    max-height: 500px;
    overflow-y: auto;
    margin-left: 10px;
}

.approver-name {
    line-height: 30px;
}

.approver-arrow {
    width: 30px;
    line-height: 30px;
    text-align: center;
    color: #00a0e9;
    font-weight: bold;
}

/* 模态框样式优化 */
.audit_modal .modal-dialog {
    width: 600px;
    margin: 30px auto;
}

.audit_modal .modal-content {
    border-radius: 6px;
}

.audit_modal .modal-header {
    border-bottom: 1px solid #e5e5e5;
}

.audit_modal .modal-footer {
    border-top: 1px solid #e5e5e5;
    text-align: center;
}
</style>
