define(function (require) {

  // 全选
  $('body').on('click', '#template_checkbox_all', function () {
    var $table = $(this).parents('table');
    if ($(this).prop('checked')) {
      $('.template-checkbox', $table).prop('checked', true);
    } else {
      $('.template-checkbox', $table).prop('checked', false);
    }
  });

  // 每一行的checkbox
  $('body').on('click', '.template-checkbox', function () {
    var checkboxes = $(this).closest('table').find('.template-checkbox');
    $('.exp_conetnt.active #template_checkbox_all').prop('checked', checkboxes.length == checkboxes.filter(':checked').length);
  });

// 加载模板类型管理器
  $("body").on('click', '#load-template-type-btn', function() {
      console.log('加载模板类型管理');
      // 加载组件
      renderComponent('/vue-ui/dist/templateTypeManager.js', '#template-type-manager', {
          close: function() {
              unrenderComponent('#template-type-manager');
              $('#template-type-manager').hide();
              console.log('模板类型管理器已关闭');
          }
      });
      $('#template-type-manager').show();
  });

  // 批量分享
  $('body').on('click', '#batch_share_template', function () {
    var tempIdArr = [];
    $('.exp_conetnt.active .user_module_list .template-checkbox:checked').each(function () {
      tempIdArr.push($(this).parents('tr').data('id'));
    });

    require(['share_exp'], function (share) {
      share(tempIdArr, $(this), 'module');
    });
  });

  // 提交审核确认
  $('body').on('click', '.submit-temp-audit', function () {
    templateApi.submitAuditConfirm($(this).attr('temp-id'));
  });

  // 提交发布审核确认
  $('body').on('click', '.submit-publish-audit', function () {
    templateApi.submitPublishConfirm($(this).attr('temp-id'), null, null, $(this).data('display-version'));
  });

  // 提交审核
  $('body').on('click', '#temp_audit_submit', function () {
    var that = this;
    var tempId = $(this).attr('data-id');
    var $btn = $('.submit-temp-audit[temp-id=' + tempId + ']');
    templateApi.submitAudit(tempId, function (res) {
      if ($(that).hasClass('reload') && $('.exp_title .tag.on')) { //模板详情页，提交审批后刷新
        require('tab').reloadActiveTag();
      }
      var step = res.data.step;
      if (step == 2) {
        $btn.removeClass('submit-temp-audit').addClass('cancel-temp-audit');
        $btn.prop("title", mainLang('cancel_temp_audit'));
      } else if (step == 3) {
        $btn.remove();
      }
    });
  });

  // 提交发布审核
  $('body').on('click', '#publish_audit_submit', function () {
    var that = this;
    var tempId = $(this).attr('data-id');
    var $btn = $('.submit-publish-audit[temp-id=' + tempId + ']');
    templateApi.submitPublishAudit(tempId, function (res) {
      if ($(that).hasClass('reload') && $('.exp_title .tag.on')) { //模板详情页，提交审批后刷新
        require('tab').reloadActiveTag();
      }
      var step = res.data.step;
      if (step == 2) {
        $btn.removeClass('submit-publish-audit').addClass('cancel-publish-audit');
        $btn.prop("title", mainLang('cancel_publish_audit'));
      } else if (step == 3) {
        $btn.remove();
      }
    });
  });


  // 撤销审核
  $('body').on('click', '.cancel-temp-audit', function () {
    var $btn = $(this);
    templateApi.cancelAudit($(this).attr('temp-id'), function (res) {
      var step = res.data.step;
      if (step == 1) {
        $btn.removeClass('cancel-temp-audit').addClass('submit-temp-audit');
        $btn.prop("title", mainLang('submit_temp_audit'));
      } else if (step == 3) {
        $btn.siblings('.set_company_temp').show().prop("title", mainLang('set_is_company'));//显示设为企业模板按钮
        $btn.remove();
      }
    });
  });


  // 获取模板痕迹列表功能 已迁移至approval.js

  // 打开某条痕迹
  $('body').on('click', '.open_temp_history', function () {
    //templateApi.openHistory($(this).attr('data-id'));
    require('get_html').genExpTempHisPage($(this).attr('data-id'));
    $.closeModal();
    $.closeLoading();
  });


                   
  var templateApi = {
    // 提交审核确认
    submitAuditConfirm: function (tempId, isAfterSave = false, reload = 0) {
      $.ajaxFn({
        type: 'GET',
        url: '?r=template/submit-audit-view',
        data: {
          temp_id: tempId,
          after_save: isAfterSave ? 1 : 0,
          reload: reload ? 1 : 0
        },
        success: function (res) {

          if (res.status == 1) {
            $('body').append(res.data);
            $('.audit_modal').modal('show');
          }

        }
      });
    },

    // 提交发布确认
    submitPublishConfirm: function (tempId, isAfterSave = false, reload = 0, displayVersion = null) {
      // 检查是否有待生效或审核中的版本
      var needConfirm = false;
      var confirmMessage = mainLang('confirm_to_publish_template');
      
      if (displayVersion && (displayVersion.status == '2' || displayVersion.status == '5')) {
        // 状态为待生效(2)或审核中(5)，显示特殊确认框
        needConfirm = true;
        if (displayVersion.status == '2') {
            confirmMessage = mainLang('version_pending_audit_warning', [displayVersion.user_version]);
        } else if (displayVersion.status == '5') {
            confirmMessage = mainLang('version_pending_effect_warning', [displayVersion.user_version]);
        }
      }
      
      // 处理发布流程
      var proceedToPublish = function() {
        $.ajaxFn({
          type: 'GET',
          url: '?r=template/submit-publish-audit-view',
          data: {
            temp_id: tempId,
            after_save: isAfterSave ? 1 : 0,
            reload: reload ? 1 : 0
          },
          success: function (res) {
            if (res.status == 1) {
              $('body').append(res.data);
              $('.audit_modal').modal('show');
            }
          }
        });
      };
      
      // 根据情况决定是否显示确认框
      if (needConfirm) {
        $.popContent(confirmMessage, mainLang('publish'), proceedToPublish);
      } else {
        // 没有待生效版本，直接调用发布流程
        proceedToPublish();
      }
    },


    // 提交审核
    submitAudit: function (tempId, cbFn) {
      // 获取用户选择的审批节点
      var approvalNodes = [];
      var hasError = false;
      var errorMsg = '';
      
      $('.audit_modal .approval-node-box').each(function(index) {
        var node = {};
        
        // 获取审批人
        var $approvalSelect = $(this).find('.approval_select');
        var approvalUserIds = $approvalSelect.val() || [];
        
        // 检查是否至少选择了一位审批人
        if (approvalUserIds.length === 0) {
          hasError = true;
          errorMsg = mainLang('pls_select_node_approval', [index + 1]);
          return false; // 跳出循环
        }
        node.approval_user_ids = approvalUserIds;
        
        // 获取协审人
        var $coapprovalSelect = $(this).find('.coapproval_select');
        if ($coapprovalSelect.length > 0) {
          var coapprovalUserIds = $coapprovalSelect.val() || [];
          
          // 如果有协审人选择框，则必须选择至少一位协审人
          if (coapprovalUserIds.length === 0) {
            hasError = true;
            errorMsg = mainLang('pls_select_node_coapproval', [index + 1]);
            return false; // 跳出循环
          }
          node.coapproval_user_ids = coapprovalUserIds;
        } else {
          node.coapproval_user_ids = [];
        }
        
        approvalNodes.push(node);
      });
      
      // 如果有错误，显示错误信息并返回
      if (hasError) {
        $.showAlert(errorMsg);
        return false;
      }
      
      // 如果没有选择任何审批节点，也显示错误
      if (approvalNodes.length === 0) {
        $.showAlert(mainLang('pls_set_approval_node'));
        return false;
      }
      
      // 提交数据
      $.ajaxFn({
        type: 'POST',
        url: '?r=template/submit-audit',
        data: {
          temp_id: tempId,
          approval_nodes: approvalNodes
        },
        success: function (res) {
          if (res.status == 1) {
            $('.audit_modal').modal('hide');
            if (cbFn) {
              cbFn(res);
            }
            $.showAlert(mainLang('submitted_for_review'));
          } else {
            $.showAlert(res.info || mainLang('operation_failed'));
          }
        },
        error: function() {
          $.showAlert(mainLang('network_error'));
        }
      });
    },

    // 撤销审核
    cancelAudit: function (tempId, cbFn) {
      $.popContent(mainLang('confirm_cancel_temp_audit'), mainLang('cancel_audit'), function () {
        $.ajaxFn({
          url: ELN_URL + '?r=template/cancel-submit',
          data: {
            temp_id: tempId
          },
          success: function (res) {
            if (res.status == 1) {
              if (cbFn) {
                cbFn(res);
              }
              $.showAlert(mainLang('success'));
              $('.pop_modal').modal('hide');
            }
          }
        });
      });
    },

    // 启动编辑
    startEdit: function (tempId, cbFn) {
      $.popContent(mainLang('confirm_start_temp_edit'), mainLang('start_edit'), function () {
        $.ajaxFn({
          url: ELN_URL + '?r=template/start-edit',
          data: {
            temp_id: tempId
          },
          success: function (res) {
            if (res.status == 1) {
              if (cbFn) {
                cbFn(res);
              }
              $.showAlert(mainLang('success'));
              $('.pop_modal').modal('hide');
            }
          }
        });
      });
    },


    // 打开某条痕迹
    openHistory: function (hisId) {

      $.ajaxFn({
        type: 'get',
        url: ELN_URL + '?r=template/view-history',
        data: {
          his_id: hisId
        },
        success: function (res) {
          if (res.status == 1) {
            var template = res.data.template;
            var history = res.data.history;
            var pageHtml = history.content;
            if ($.trim(pageHtml) == '') {
              pageHtml = '<div class="tool_data_box relative"></div><div class="exp_data_box clear readonly"><div class="relative"></div></div>';
            }

            $('.layout_right_box .exp_conetnt').html(pageHtml);

            $('.exp_title a.on').removeClass('on');
            var findTab = $('.my_exp_detial.temp_history[data-his="' + hisId + '"]');
            if (findTab.length !== 0) {
              findTab.addClass('on');
            } else {
              var tabName = mainLang('history') + ':' + template.name + '(version:' + history.version + ')';
              var newTab = '<a class="iblock on my_exp_detial temp_history" data-his="' + hisId + '" title="' + tabName + '"><span class="name">' + tabName + '</span><span class="close"></span></a>'
              $('.exp_title').append(newTab);
            }

            handleExpTitle();
            $.closeModal();
            $.closeLoading();
          }
        }
      });
    },

    // 提交发布审核
    submitPublishAudit: function (tempId, cbFn) {
      // 获取用户选择的审批节点
      var approvalNodes = [];
      var hasError = false;
      var errorMsg = '';
      
      $('.audit_modal .approval-node-box').each(function(index) {
        var node = {};
        
        // 获取审批人
        var $approvalSelect = $(this).find('.approval_select');
        var approvalUserIds = $approvalSelect.val() || [];
        
        // 检查是否至少选择了一位审批人
        if (approvalUserIds.length === 0) {
          hasError = true;
          errorMsg = mainLang('pls_select_node_approval', [index + 1]);
          return false; // 跳出循环
        }
        node.approval_user_ids = approvalUserIds;
        
        // 获取协审人
        var $coapprovalSelect = $(this).find('.coapproval_select');
        if ($coapprovalSelect.length > 0) {
          var coapprovalUserIds = $coapprovalSelect.val() || [];
          
          // 如果有协审人选择框，则必须选择至少一位协审人
          if (coapprovalUserIds.length === 0) {
            hasError = true;
            errorMsg = mainLang('pls_select_node_coapproval', [index + 1]);
            return false; // 跳出循环
          }
          node.coapproval_user_ids = coapprovalUserIds;
        } else {
          node.coapproval_user_ids = [];
        }
        
        approvalNodes.push(node);
      });
      
      // 如果有错误，显示错误信息并返回
      if (hasError) {
        $.showAlert(errorMsg);
        return false;
      }
      
      // 如果没有选择任何审批节点，也显示错误
      if (approvalNodes.length === 0) {
        $.showAlert(mainLang('pls_set_approval_node'));
        return false;
      }
      
      // 获取版本号和生效日期信息
      var template_version = $('#template_version').val().trim();
      var effect_date_type = $('#effect_date_type').val();
      var effect_date = effect_date_type === 'immediate' ? '' : $('#effect_date').val();
      
      // 验证版本号和生效日期
      if (template_version == '') {
        $.showAlert(mainLang('pls_input_version'));
        return false;
      }
      
      if (effect_date_type === 'custom' && effect_date === '') {
        $.showAlert(mainLang('pls_select_effect_date'));
        return false;
      }
      
      // 提交数据
      $.ajaxFn({
        type: 'POST',
        url: '?r=template/submit-publish-audit',
        data: {
          temp_id: tempId,
          approval_nodes: approvalNodes,
          version: template_version,
          effect_date: effect_date,
          effect_date_type: effect_date_type
        },
        success: function (res) {
          if (res.status == 1) {
            $('.audit_modal').modal('hide');
            if (cbFn) {
              cbFn(res);
            }
            $.showAlert(mainLang('submitted_for_review'));
          } else {
            $.showAlert(res.info || mainLang('operation_failed'));
          }
        },
        error: function() {
          $.showAlert(mainLang('network_error'));
        }
      });
    },
  };

  return templateApi;
});
