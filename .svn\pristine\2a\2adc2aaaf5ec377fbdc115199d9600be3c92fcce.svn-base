<template>
  <div id="isInstrumentBookingConfigDialogDialogDiv">
    <ElDialog v-model="isInstrumentBookingConfigDialog" :title="$t('InstrumentBookingConfigDialog.title')" width="480" id="isInstrumentBookingConfigDialogDialog">
      <ElForm label-position="top" :rules="validateForm" :model="instrumentBookingForm" id="isInstrumentBookingConfigDialogForm">
        <ElFormItem class="instrumentBookingFormName">
          <ElTooltip :content="instrumentBookingForm.chooseName?.join('、')" placement="top" effect="light">
            <p class="isInstrumentBookingConfigDialog_firstP" >{{ instrumentBookingForm.chooseName?.join('、') }}</p>
          </ElTooltip>
        </ElFormItem>
        <ElFormItem  :label="$t('InstrumentBookingConfigDialog.available_slots')">
          <template v-for="(timeItem, index) in instrumentBookingForm.available_slots" :key="index" >
            <div class="demo-time-range">

              <el-select v-model="instrumentBookingForm.available_slots[index][0]" :placeholder="$t('InstrumentBookingConfigDialog.start_time')" @change="handleStartChange(index)" style="min-width: 190px;">
                <el-option
                    v-for="item in timeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
              <span>-</span>
              <el-select v-model="instrumentBookingForm.available_slots[index][1]" :placeholder="$t('InstrumentBookingConfigDialog.end_time')" :disabled="!instrumentBookingForm.available_slots[index][0]" style="min-width: 190px;">
                <el-option
                    v-for="item in endTimeOptions[index]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
              <template v-if="index === 0">
                <ElIcon
                    :size="12" color="rgb(106, 106, 115)"
                    @click="addInstrumentAvailableSlots"
                    style="cursor: pointer;margin-left: 10px;">
                  <Plus />
                </ElIcon>
              </template>
              <template v-else-if="instrumentBookingForm.available_slots.length - 1 === index">
                <ElIcon
                    :size="12" color="rgb(106, 106, 115)"
                    @click="minusInstrumentAvailableSlots"
                    style="cursor: pointer;margin-left: 10px;">
                  <Minus />
                </ElIcon>
              </template>
              <template v-else>
                <div style="min-width: 22px;margin-top: 11px;"></div>
              </template>

            </div>

          </template>
        </ElFormItem>
        <hr style="border-color:rgba(235, 238, 245, .2);margin-top: -2px;">
        <ElFormItem  :label="$t('InstrumentBookingConfigDialog.max_advance_day')">
          <el-radio-group v-model="isMaxAdvanceDay">
            <el-radio :label="false">{{ t('InstrumentBookingConfigDialog.noLimited') }}</el-radio>
            <el-radio :label="true">{{ t('InstrumentBookingConfigDialog.limited') }}</el-radio>
          </el-radio-group>
          <template v-if="isMaxAdvanceDay">
            <ElInput class="instrumentFormItemInput" type="number" style="width: 80px;margin: 0 8px;" v-model="instrumentBookingForm.max_advance_day"></ElInput>天
          </template>
        </ElFormItem>
        <ElFormItem  class="instrumentFormItem" :label="$t('InstrumentBookingConfigDialog.min_advance')">
          <ElRadioGroup v-model="isMinAdvance">
            <ElRadio :label="false">{{ t('InstrumentBookingConfigDialog.noLimited') }}</ElRadio>
            <ElRadio :label="true">{{ t('InstrumentBookingConfigDialog.limited') }}</ElRadio>
          </ElRadioGroup>
          <template v-if="isMinAdvance">
            <ElInput class="instrumentFormItemInput"  type="number" style="width: 80px;margin: 0 8px;" v-model="instrumentBookingForm.min_advance.value"></ElInput>
            <ElSelect class="instrumentFormItemSelect" v-model="instrumentBookingForm.min_advance.unit" :placeholder="$t('InstrumentBookingConfigDialog.select')" style="width: 120px">
              <ElOption
                  v-for="item in advanceOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </ElOption>
            </ElSelect>
          </template>
        </ElFormItem>
        <ElFormItem :label="$t('InstrumentBookingConfigDialog.max_booking_duration')">
          <ElRadioGroup v-model="isMaxBookingDuration">
            <ElRadio :label="false" name="isMaxBookingDurationFalse" >{{ t('InstrumentBookingConfigDialog.noLimited') }}</ElRadio>
            <ElRadio :label="true" name="isMaxBookingDurationTrue">{{ t('InstrumentBookingConfigDialog.limited') }}</ElRadio>
          </ElRadioGroup>
          <template v-if="isMaxBookingDuration">
            <ElInput class="instrumentFormItemInput" type="number" style="width: 80px;margin: 0 8px;" v-model="instrumentBookingForm.max_booking_duration.value"></ElInput>
            <el-select class="instrumentFormItemSelect" v-model="instrumentBookingForm.max_booking_duration.unit"  :placeholder="$t('InstrumentBookingConfigDialog.select')" style="width: 120px"
            >
              <el-option
                  v-for="item in advanceOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </template>
        </ElFormItem>
        <ElFormItem id="instrument-btn">
          <ElButton @click="cancelForm()">{{ t('InstrumentBookingConfigDialog.cancel') }}</ElButton>
          <ElButton type="primary" @click="submitForm()">{{ t('InstrumentBookingConfigDialog.sure') }}</ElButton>
        </ElFormItem>
      </ElForm>
    </ElDialog>
  </div>
</template>

<script setup>
import 'element-plus/dist/index.css' // 引入 Element UI 的 CSS
import {
  ElMessage , ElButton,
  ElForm,
  ElDialog,
  ElSelect,
  ElInput,
  ElFormItem,
  ElTooltip,
  ElIcon,
  ElRadioGroup,
  ElRadio,
  ElOption,
} from 'element-plus' // 按需引入组件

import { ref, watch } from "vue";
import { Plus, Minus } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const isInstrumentBookingConfigDialog = ref(true)
const isMaxAdvanceDay = ref(false)
const isMinAdvance = ref(false)
const advanceOption = ref([
  {value: 'min', label: t('InstrumentBookingConfigDialog.min')},
  {value: 'hour', label: t('InstrumentBookingConfigDialog.hour')},
  {value: 'day', label: t('InstrumentBookingConfigDialog.day')},
])
const isMaxBookingDuration = ref(false)
const { instrument_booking_config_chooseIds, instrument_booking_config_chooseName, cancel} = defineProps({
  instrument_booking_config_chooseIds: {
    type: Array,
    default: []
  },
  instrument_booking_config_chooseName: {
    type: Array,
    default: []
  },
  cancel: {
    type: Function,
    default: null
  }
});


const instrumentBookingForm = ref({
  chooseName: instrument_booking_config_chooseName,
  instrumentIds: instrument_booking_config_chooseIds,
  available_slots: [['00:00','23:59']],
  max_advance_day: 1,
  min_advance: { value: '1', unit: ''},
  max_booking_duration: { value: '1', unit: ''}
})



// 处理选择时间
const generateTimeOptions = () => {
  const options = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeValue = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      options.push({
        value: timeValue,
        label: timeValue
      })
    }
  }
  // 添加23:59选项
  options.push({
    value: '23:59',
    label: '23:59'
  })
  return options
}
const timeOptions = generateTimeOptions()
const endTimeOptions = ref([ timeOptions ])

// 计算结束时间选项
const getEndTimeOptions = (startTime) => {
  if (!startTime) return []

  const startIndex = timeOptions.findIndex(option => option.value === startTime)
  if (startIndex === -1) return []


  console.log(timeOptions.slice(startIndex + 1))
  return timeOptions.slice(startIndex + 1)
}

// 处理开始时间变化
const handleStartChange = index => {
  console.log(instrumentBookingForm.value.available_slots, index)
  endTimeOptions.value[index] = getEndTimeOptions(instrumentBookingForm.value.available_slots[index][0])
}


// 错误信息存储
const errors = ref('')

// 校验逻辑
const validateForm = () => {
  const form = instrumentBookingForm.value
  let errors = ''
  const isNumber = (value) =>{
    return !(/^\d+(\.\d+)?$/.test(value)) && Number(value) <= 0
  }
  // 校验 max_advance_day
  if (isMaxAdvanceDay.value) {
    if (isNumber(form.max_advance_day)) {
      errors = t('InstrumentBookingConfigDialog.tips1')
    }
  }

  // 校验 min_advance
  if (isMinAdvance.value) {
    const ma = form.min_advance
    if (!ma.unit?.trim()) {
      errors = t('InstrumentBookingConfigDialog.tips2')
    }
    if (isNumber(ma.value)) {
      errors = t('InstrumentBookingConfigDialog.tips3')
    }
  }

  // 校验 max_booking_duration
  if (isMaxBookingDuration.value) {
    const mbd = form.max_booking_duration
    if (!mbd.unit?.trim()) {
      errors = t('InstrumentBookingConfigDialog.tips2')
    }
    if (isNumber(mbd.value)) {
      errors = t('InstrumentBookingConfigDialog.tips4')
    }
  }

  // 记录修改前的长度
  let originalLength = instrumentBookingForm.value.available_slots.length;

// 使用 filter 删除第0个和第1个元素相等的项
  instrumentBookingForm.value.available_slots = instrumentBookingForm.value.available_slots.filter(item => item[0] !== item[1]);
  errors = originalLength !== instrumentBookingForm.value.available_slots.length ? t('InstrumentBookingConfigDialog.tips5'): errors
  console.log(errors)
  if(errors.length > 0) {
    // eslint-disable-next-line no-undef
    ElMessage({
      showClose: true,
      message: errors,
      type: 'error',
      offset: window.innerHeight / 8
    })
  }
  return errors
}




const checkArray = (arr) => {
  // 遍历数组并检查是否有空字符串
  const hasEmptyString = arr.some(subArray => subArray.includes(''));

  if(hasEmptyString) {
    ElMessage({
      showClose: true,
      message: t('InstrumentBookingConfigDialog.tips6'),
      type: 'warning',
      offset: window.innerHeight / 8
    })
  }
  // 如果存在空字符串，返回 false，否则返回 true
  return !hasEmptyString;
}


const hasConflict = (timeArray) => {
  // 1. 转换时间段，将 '00:00' 处理为 '23:59'
  const timeSegments = timeArray.map(segment => {
    const startTime = convertToMinutes(segment[0]);
    let endTime = convertToMinutes(segment[1]);

    // 如果结束时间是 '00:00'，把它转为 23:59
    if (segment[1] === '00:00') {
      endTime = 23 * 60 + 59; // 23:59 的分钟数
    }

    return [startTime, endTime];
  });

  // 2. 按起始时间排序
  timeSegments.sort((a, b) => a[0] - b[0]);

  // 3. 检查是否有重叠
  for (let i = 0; i < timeSegments.length - 1; i++) {
    const [startA, endA] = timeSegments[i];
    const [startB, endB] = timeSegments[i + 1];

    // 如果当前时间段的结束时间大于下一个时间段的起始时间，说明有冲突
    if (endA > startB) {
      ElMessage({
        showClose: true,
        message: t('InstrumentBookingConfigDialog.tips7'),
        type: 'error',
        offset: window.innerHeight / 8
      })
      return false; // 有冲突
    }
  }

  return true; // 没有冲突
}

// 辅助函数：将时间字符串（如 '00:00'）转换为分钟数
const convertToMinutes = (timeStr) => {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}



// 添加一个新的时间范围
const addInstrumentAvailableSlots = () => {
  instrumentBookingForm.value.available_slots.push(['23:30', '23:30']); // 默认值为00:00-00:00
};
const minusInstrumentAvailableSlots = () => {
  instrumentBookingForm.value.available_slots.pop()
}


const submitForm = () => {
  console.log(instrumentBookingForm.value.available_slots)
  // instrumentBookingForm.value.available_slots = updateInstrumentTime(instrumentBookingForm.value.available_slots)
  if(!validateForm().length && hasConflict(instrumentBookingForm.value.available_slots) && checkArray(instrumentBookingForm.value.available_slots)) {
    const { instrumentIds, available_slots, max_advance_day, min_advance, max_booking_duration } = instrumentBookingForm.value;
    $.ajaxFn({
      url: ELN_URL + '?r=instrument/set-instrument-booking-config',
      data: {
        instrumentIds,
        available_slots ,
        max_advance_day: isMaxAdvanceDay ? max_advance_day : null,
        min_advance: isMinAdvance ? min_advance: { value: '', unit: ''},
        max_booking_duration: isMaxBookingDuration ? max_booking_duration : { value: '1', unit: ''},
      },
      type: 'POST',
      success: function(data) {
        ElMessage({
          showClose: true,
          message: t('InstrumentBookingConfigDialog.tips8'),
          type: 'success',
          offset: window.innerHeight / 8
        })
        require('tab').reloadActiveTag(); // 刷新页面
        cancelForm()
      }
    })

    return
  }
}

const cancelForm = () => {
  isInstrumentBookingConfigDialog.value = false
  cancel()
}

</script>



<style scoped>
.isInstrumentBookingConfigDialog_firstP {
  white-space: nowrap;      /* 禁止换行 */
  overflow: hidden;         /* 隐藏溢出内容 */
  text-overflow: ellipsis;  /* 显示省略符号 */
  width: 70%;              /* 必须设置宽度 */
  display: inline-block;    /* 或 block 根据需求选择 */
  font-size: 14px;
  color: rgb(106, 106, 115);
  margin: 0;

}
.demo-time-range {
  max-width: 400px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  margin: 6px 0;
}
.demo-time-range span {
  margin: 0 8px;
}
#instrument-btn :deep(.el-form-item__content) {
  display: flex !important;
  justify-content: flex-end !important;
}
#isInstrumentBookingConfigDialogDialog :deep(.el-input__inner:focus) {
  border: none !important;
  box-shadow: none !important;
}
#isInstrumentBookingConfigDialogDialogDiv :deep(.el-input__wrapper .el-input__inner) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}
#isInstrumentBookingConfigDialogDialogDiv :deep(.el-dialog__body) {
  margin-top: -6px !important;
}
#instrument-btn {
  margin-bottom: 0 ;
}

</style>
