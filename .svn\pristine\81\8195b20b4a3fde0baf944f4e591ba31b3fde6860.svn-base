<?php
return [
    //'title' => 'Integle InELN/Internet health care data industry standard platform',
    'title' => PAGE_TITLE_EN, //dgh
    'integle' => 'Integle',
    'integle_name' => 'Shanghai Integle Informatics',
    'browser_low' => 'Your browser version is too low, you can download the Google browser in order to better browse the site, confirm download?',  //dgh
    'integle_icp' => 'Shanghai Integle Informatics, Ltd. All Rights <a href="http://www.miitbeian.gov.cn" style="color:#333;text-decoration: none;" target="_blank">Reserved ICP Shanghai ********</a>',
    'ai' => '+AI',
    'exceed_experiment_message'=> 'The current request may time out. It is recommended to select no more than 5 experiments',
	'select_all' => 'All',
	'confirm' => 'Confirm', //dgh
	'ok' => 'OK',
	'cancle' => 'Cancel',
	'close' => 'Close',
	'tips' => 'Tips',
	'save' => 'Save',
	'delete' => 'Delete',
	'download' => 'Download',
	'setting' => 'Setting',
	'carry out' => 'Fold', //dgh 在模块的左上角，把模块变到最小
	'pack up' => 'Unfold', //dgh
	'module_rename' => 'Rename',
	'big' => 'ZoomIn',  //放大
	'show_big' => 'View',
	'small' => 'ZoomOut', //缩小
	'look' => 'View',
    'view_or_edit' => 'View/Edit',
	'review' => 'Preview',
    'recordFieldConfig' => 'Record template config',
    'fieldConfig'=>'Field Config',
    'checkTimeSetting'=>'Approval&Time Config',
	'copy' => 'Duplicate',
	'copy_2' => 'Duplicate',
	'action' => 'Operation', //dgh
	'add' => 'Add',
	'print' => 'PDF Preview',
	'log' => 'Access Log', //dgh
	'history' => 'Audit Trail',
    'hide_temp' => 'Hide template',
    'show_temp' => 'Show template',

	'success' => 'Operation completed',
	'success_1' => 'The experiment has been pre-approved!', //dgh
	'failed' => 'Operation Failed',
	'failed_1' => 'The experiment has not been pre-approve!',  //dgh

    'success_with_approval' => 'Saved successfully! Please submit for approval from "Template List"',
    'save_success' => 'Saved successfully!',

	'search' => 'Search',
    'search_results' => 'Search Results',
	'input_keywords' => 'Keywords, page number for search', //dgh   Modified by wzb 20200528

	'my_exp_list' => 'My Experiments',  //首页Tab
	'recode' => 'Records',
	'load_more' => 'Load More',

	'create' => 'Create',
    'exp_creator' => 'Creator', //Modified by wzb 20200528
	'exp_code' => 'Notebook Page',  //首页列表
	'exp_code_batch' => 'Page Number',
	'exp_content' => 'Scheme/Title',
    'book' => 'My Notebooks',
    'rem_book' => 'Removed Notebooks',
	'exp' => 'Experiment',
	'creator' => 'Created By ',
	'update_user' => 'Modified By',
	'yield' => 'Yield',
	'own_to_book' => 'Notebook',
	'attributes' => 'Basic Info.',
	'refused_reason' => 'Refused Reason',
    'pretrial_refused_reason' => 'Pretrial refused reason',
	'status' => 'Status',
	'type' => 'Type',  //dgh where
	'content' => 'Content',
	'name' => 'Name',
	'email' => 'Email',
	'group' => 'Group',
	'user' => 'User',
    'select' => 'please select',
    'select all' => 'All',

	'exp_title' => 'Title',
	'keywords' => 'Keywords',
	'mark' => 'Remarks',
	'temp' => 'Template',
	'temp_title' => 'Template Name',
	'temp_desc' => 'Description',
	'project_name' => 'Project',
	'task_name'=>'Task',
	'project_progress' => 'Project Progress',  //dgh
	'create_temp' => 'Create Template',
	'create_temp_1' => 'Default template name',
	'create_weather' => 'Created weather',
    'create_weather_sunny' => 'Sunny',   //add by hkk 2019/3/1  新增天气温度栏 Sunny, cloudy, rainy, snowy, foggy.
    'create_weather_cloudy' => 'Cloudy',
    'create_weather_rainy' => 'Rainy',
    'create_weather_snowy' => 'Snowy',
    'create_weather_overcast' => 'Overcast',

    'weather' => 'Weather',   //add by hkk 2019/3/28
    'temperature' => 'Temperature',   //add by hkk 2019/3/28
    'humidity' => 'Humidity',   //add by hkk 2019/3/28

	'select_project_progress' => 'Please select the project progress',  //dgh
	'select_project_name' => 'Select Project',
	'select_task_name' => 'Select Task',

	'step' => 'Status',
	'step_1' => 'Draf',
	'step_2_5' => 'Closed',
	'step_2' => 'Witnessed',
	'step_3' => 'waiting for Witness',  //dgh
	'step_4' => 'Rejected',
	'step_5' => 'Witnessed',
	'step_6' => 'Unpublished',
	'step_7' => 'Approval in process',  //dgh审核中
	'step_8' => 'Published',
    'step_9' => 'Have analysis',
    'step_10' => 'Have undone analysis',
    'step_11' => 'Unassigned to project ',
    'step_12' => 'All',
	'pretrial_status_1' => 'Pre-approval in progress',
	'pretrial_status_2' => 'Pre-approval has been agreed',
	'pretrial_status_3' => 'Pre-approval has been rejected',
	'reopen_status_1' => 'Re-opening in progress',
	'reopen_status_2' => 'Re-opening has been agreed',
	'wait_pretrial' => 'Approval in process', //
    'witness_remind_diff_day' => 'Overdue and not submitted',

	'sign_allow' => 'Witnessed',
	'sign_refuse' => 'Rejected',
	'cancle_sign' => 'Cancel Witness',

	'refuse' => 'Rejected',
	'allow' => 'Approved',

	'create_time' => 'Created',
	'update_time' => 'Modified',

    'experiment_result' =>'Results',//add by hkk 2019/3/4
    'experiment_star' =>'Star',  //add by hkk 2019/3/4  by dgh 20190720
    'experiment_conclusion' =>'Conclusion',  //add by hkk 2019/3/4

    'conclusion_succeeded' =>'Successful',  //add by hkk 2019/3/4  //dgh?
    'conclusion_unsuccessful' =>'Failed',  //add by hkk 2019/3/4
    'conclusion_abandoned' =>'Aborted',  //add by hkk 2019/3/4

	'shared' => 'Shared',
    'no_share' => 'Non-shared',

	'memu' => 'Menu',

	'share' => 'Share',
    'batch_share' => 'Batch Share',
	'sign' => 'Witness',
	'review_sign' => 'Witness',
	'move_recycle' => 'Move to recycle',
	'save_as_temp' => 'Save As Template',
	'search_by_abc' => 'Search by first letter',

	'no_ajax' => 'Illegal request, Non-Ajax', //dgh? 不是Ajax请求
	'params_empty' => 'Pass parameter is empty',  //dgh?传递参数为空
	'params_error' => 'Parameter Error',  //dgh 参数错误
	'input_search' => 'Please enter for search',
	'no_update' => 'No change has been made',  //dgh未做任何修改

	'select_group' => 'Select Group',
    'select_department' => 'Select Department',
	'select_user' => 'Select User',
    'admin_modify_notice' => 'Oops! One user you selected includes the system administrator who has all permissions by default, and cannot be modified.',
    'lost_params' => 'Lack of necessary parameters',  //dgh'缺少必要参数'
	'data_empty' => 'Empty Data',  //dgh'数据为空'
	'no experiment' => 'No experiment found',  //dgh? 没有实验？
	'search_result' => 'Search results',
	'no_exist1' => 'Account',
	'no_exist2' => 'Doesn\'t exist', //dgh
	'exist' => 'Exist',
	'exp_code_order' => 'Ordered by Page Number',

	'create_time_order' => 'Sorted by Creation',
	'update_time_order' => 'Sorted by Modification',
	'add_his_failed' => 'Adding autidt trail failed, please check the data.', //添加痕迹记录失败,请检查数据
	'rotate'=>'Rotate',
    'excel_box_tips' => 'Export PDF/Word file cannot display the spreadsheet', //已经不用了
    'input_plan_day' => 'Please enter the number of days.',

    'search book' => 'Name',  //左侧搜索记录本
    'Technical support' => ' Provide Technical Support',
    'ICP reserve' => 'Shanghai ICP No. ********',
    'system prompt' => 'Tips',
    'refresh tips'=>'Oops, Your account has been logged out. Please refresh and log in again.', //dgh? 请注意！您的账号已退出登录，请刷新页面
    'refresh now'=>'Refresh now',
    'refresh' => 'Refresh',
    'refresh later' =>'Refresh later',
    'tmp_power' => 'Template Access',
    'export_materials_data_tip'=>'The function of exporting details of reaction data has not been enabled yet, please contact Integle service staff.',

    'move up' => 'Up',
    'move down'=>'Down',

    // ********
    'high search'=>'Advanced Search',
    'group select'=>'Select Group',
    'please select groups'=>'Select Groups to which this template belongs.',
    'select　ok groups'=> "1.The template you create here does not belong to this Group, but is used to mark that you have been granted the permission to create a new template in this Group.
                            <br>2.The newly created template here can be used by others only if you share it with this Group or other Groups, or other members, or set it as an enterprise template. If you don't share it, others won't be able to use the template you create.", //dgh
    'no group'=>'You need access to create template, please contact Group Owner/ELN Admin.',
    'set_require'=>'Set Required Fields',  //dgh设置必填项
    'set_struct_data'=>'Set Data Structuring Fields',

    'editor_id'=>'ueditor'.mt_rand().mt_rand().mt_rand(),

    //实验预审
    'exp_pretrial_check' => 'Pre-approve',
	'pretrial_allow' => 'Pre-approved', //dgh审核通过
	'pretrial_refuse' => 'Pre-approval denied',
	'cancel_pretrial' => 'Withdraw Pre-approval',
	'exp_reopen_check' => 'Re-open',  //重开
	'reopen_allow' => ' Re-open Approved', //同意重开
	'reopen_refuse' => 'Re-open Rejected',
	'cancel_reopen' => 'Withdraw Re-open',
    'signing_allow' => 'Sign Approved',
    'signing_refuse' => 'Sign Rejected',
    'module_reedit_allow' => '模块重新编辑通过',
    'module_reedit_refuse' => '模块重新编辑拒绝',
	'set_is_company' => 'Set as an enterprise template',//dgh?设为企业模板
    'cancel_is_company' => 'Cancel enterprise template', //dgh
    'set_quick_tab' => 'Set Quick Tab',
    'set_pre_post_exp' => 'Set Pre/Post Experiments',
	'pre_post_exp' => 'Pre/Post Experiments',
    'reaction_route' => 'Generate Reaction Scheme',
    'create_next_reaction' => 'Create Next Reaction',
    'set_quick_tab_tips'=>'Set quick tab for faster applying the structure as Reactant/Reagent/Solvent/Product',
    'display_condition_on_indraw'=>'Display Condition under the reaction arrow',
    'display_yield_on_indraw'=>'Display Yield under the reaction arrow',

    //企业词库
    'enterprise_dictionary_01'=>'The Excel for Dropdown List includes dropdown list in InDraw/Text editoroptions, please follow the method as below to modify or create options', //此段已经不用了
    'enterprise_dictionary_02'=>'Download Dropdown List Template(“New” is the latest updated template, download “Original” to get the initial template)',
    'enterprise_dictionary_03'=>'Modify, delete or create options in Excel are supported',
    'enterprise_dictionary_04'=>'Note: Please don’t modify the column of Name',
    'enterprise_dictionary_05'=>'After modification, upload the Excel here and submit to finish update.',
    'enterprise_dictionary_06'=>'Select File',
    'enterprise_dictionary_07'=>'Submit',
    'enterprise_dictionary_08'=>'Dropdown List',
    'enterprise_dictionary_09'=>'New',
    'enterprise_dictionary_10'=>'Original',

    //此段在使用中
    'enterprise_dictionary_11'=>'Function Description:',
    'enterprise_dictionary_12'=>'In Text Editor, TLC, InDraw, sub-templates, etc., you can customize the content of the drop-down list.',
    'enterprise_dictionary_13'=>'For example, in the text editor, you can customize the dropdown list in the category of reagents, instruments, consumables, etc.', //dgh
    'enterprise_dictionary_14'=>'For example, in InDraw,  you can customize the dropdown list in the category of Resource, Temperature, Risk Accessment, etc.',
    'enterprise_dictionary_15'=>'For example, in module TLC, you can customize the dropdown list in the category of Eluent, Ratio, Color developing reagent, etc.',
    'enterprise_dictionary_16'=>'For example, in sub-template of Text editor, you can customize the dropdown list in the category of Eluent, Volume, Drying agent, etc.',
    'enterprise_dictionary_17'=>'Dropdown List configuration: ',

    'enterprise_dictionary_18'=>'1.	Download the Drowdown List (New) excel file currently in use ',
    'enterprise_dictionary_19'=>'&nbsp;&nbsp;&nbsp;Or download the Dropdown List (Original) excel file.',
    'enterprise_dictionary_20'=>'2.	Edit the excel file',
    'enterprise_dictionary_21'=>'In the Excel file your downloaded, feel free to modify the contents.', //dgh
    'enterprise_dictionary_22'=>'3. Upload Dropdown List excel file',

    'enterprise_dictionary_23'=>'For example, in My Workspace-Work Order-Add work order, you can customize the dropdown list in the category of Work order category, Priority level.',
    'enterprise_dictionary_24'=>'For example, in My Workspace-Work Order-Solve, you can customize the dropdown list in the category of Solution.',
    'enterprise_dictionary_25'=>'For example, in experiment details, you can customize the dropdown list in the category of Title, keywords.',

    'error_for_share_auth'=>'No permission of Sharing Setting，please contact Group Owner',
    'error_for_export_auth'=>'Export setting access denied, please contact System Admin./Group Owner/Group ELN Admin',
    'pretrialSetNoAuthTip'=>'No permission of Pre-approve Setting，please contact Group Owner',
    'signSetNoAuthTip'=>'No permission of Sign Setting，please contact Group Owner',
    'witnessSetNoAuthTip'=>'No permission of Witness Setting，please contact Group Owner',
    'otherSetNoAuthTip'=>'No permission of Other Setting，please contact Group Owner',
    'roleSetNoAuthTip'=>'No permission of Role Assignment，please contact Group Owner',
    //InDraw
    'add_reactant'=>'Add Reactant', /*add by hkk 2019/3/25*/
    'add_reagent'=>'Add Reagent', /*add by hkk 2019/3/25*/
    'add_solvent'=>'Add Solvent', /*add by hkk 2019/3/25*/
    'add_condition'=>'Add Condition', /*add by hkk 2019/3/25*/
    'add_product'=>'Add Product', /*add by hkk 2019/3/25*/

    //add by wy 2023/3/17
    'reactant'=>'Reactant',
    'batch_no'=>'Batch No',
    'reagent'=>'Reagent',
    'solvent'=>'Solvent',
    'condition'=>'Condition',
    'product'=>'Product',
    'details'=>'Details',
    'eq'=>'Eq',
    'mass'=>'Mass',
    'source'=>'Source',
    'indraw_price'=>'Price/g(ml)',
    'total_price'=>'Total Price',
    'ratio'=>'Ratio',
    'reaction_time'=>'Reaction Time',
    'pressure'=>'Pressure',
    'protection_gas'=>'Protection Gas',
    'heating'=>'Heating',
    'comment'=>'Comment',
    'salt'=>'Salt',
    'salt_eq'=>'Salt Eq',
    'theo_mass'=>'Theo Mass',
    'actual_mass'=>'Actual Mass',
    'sample_id'=>'Sample ID',
    'indraw_barcode'=>'Barcode',
    'compound_no'=>'Compound No.',
    'mass_yield'=>'Mass Yield',
    'risk_assessment'=>'Risk Assessment',
    'files'=>'Files',
    'copy_lines'=>'Copy lines',
    'past_lines'=>'Past lines',

    //multi-function table
    'custom_table' => 'Multi-function Table',
    'exportCSV' => 'Export CSV',
    'importCSV' => 'Import CSV',
    'clearFormat' => 'Clear format',
    'fontSize' => 'Font size',
    'textColor' => 'Font color',
    'textBold' => 'Bold',
    'textItalic' => 'Italic',
    'textStrikethrough' => 'Strikethrough',
    'fillColor' => 'Fill color',
    'borders' => 'Border line',
    'borderAll' => 'All border',
    'borderOnlyLeft' => 'Left border',
    'borderOnlyRight' => 'Right border',
    'borderOnlyTop' => 'Top border',
    'borderOnlyBottom' => 'Bottom border',
    'borderOuter' => 'Outer border',
    'borderClear' => 'Clear Borders',
    'borderColor' => 'Border color',
    'borderWidth' => 'Border width',
    'horizontalLeft' => 'Align left',
    'horizontalCenter' => 'Horizontally centered',
    'horizontalRight' => 'Align right',
    'verticalTop' => 'Top alignment',
    'verticalMiddle' => 'Center alignment',
    'verticalBottom' => 'Bottom alignment',
    'toggleTableHeader' => 'Hide serial number', //dgh
    'addDate' => 'Add date',
    'addTime' => 'Add time',
    'addDropdown' => 'Dropdown list',
    'addCheckbox' => 'Multiple options',
    'addRadio' => 'Single option',
    'conversionData' => 'Invert row & column',
    'uploadFile' => 'Insert file',
    'formula' => 'Formula',

    'sortActionClick' => 'Click on the letter to change the sorting order',
    'customTableSearchRemind' => 'Please enter for search',
    'show_toolbar' => 'Show toolbar',
    'hide_toolbar' => 'Hide toolbar',

    'modalOption' => 'Option ',
    'modalSetting' => 'Set ',
    'addOptionAction' => 'Add Option',
    'modalCancel' => 'Cancel',
    'modalSubmit' => 'Submit',
    'dropdown' => 'Dropdown',
    'checkbox' => 'Checkbox',
    'radio' => 'Radio',
    'arrangement' => 'Horizontal arrangement',

    'contextMenuProhibitEditing' => 'Disable editing',
    'contextMenuUnFreezeRow' => 'Unfreeze row',
    'contextMenuFreezeRow' => 'Freeze row',
    'contextMenuRequiredField' => 'Required field',
    'contextMenuDataStructured' => 'Select for structuring',

    'error_for_prev_approve'=>'You need access to set pre-approval, please ask Group Owner/Admin for help. ', //dgh
    'error_for_reopen'=>'You need have access to re-open after witness, please ask Group Owner/Admin for help.', //dgh

    //data structuring
    'set_struct_data_tips'=>'Set data structuring fields is used for data extraction within the same <br>template. You use the function in My Workspace——Data Structuring', //dgh?
    'advanced_permission_setting'=>'Advanced Access Control',
    'decimal_point_setting_of_materials'=>'InDraw Decimal Point',
    'setting_of_materials'=>'Material table Setting',
    'setting_name_of_materials'=>'CN/EN IUPAC setting',
    'export_materials'=>'Export data of reaction materials',
    'cn_iupac'=>'CN IUPAC',
    'en_iupac'=>'EN IUPAC',

    'role'=>'Role',
    'set_check_user'=>'Set InELN Auditor', //dgh
    'enterprise_permission_setting'=>'Advanced Access Control',
    'set_check_user_tips1'=>'The selected experiment auditors can view all experiments in selected groups',
    'set_check_user_tips2'=>'(View from "My Workspace"-"Experiment Inspection")',
    'inspector'=>'Auditor',
    'view_ermission'=>'View Permission',
    'edit_ermission'=>'Edit Permission',
    'enterprise_setting'=>'ELN Setting',
    'group_setting'=>'User Access Control',
    'new_group_setting' => 'User Group Settings',
    'permission_setting' => 'Permission Settings',
    'group_setting_tips'=>'If you need to modify the Group owner and ELN admin. permissions, you can edit in "If you need to modify the group owner and ELN administrator permissions, you can edit in "Advanced Access Control"-"User Access Control".',
    'use_static'=>'Performance',
    'company_dict' => 'Dropdown List',
    'company_dict_tips' => 'User can edit Dropdown List if selected',
    'sys_log' => 'InELN Log',
    'company_log' => 'InELN Log',
    'login_log' => 'Login Log',
    'share_log' => 'Share Log',
    'view_log' => 'View Log',
    'setting_log' => 'Setting Log',
    'struct_share_setting' => 'Data Structuring-Views&Report(Template)sharing Permissions',
    'struct_share_setting_tip' => 'Users can share Views,Reports or Report Templates to authorized Groups or Users',
    'file_export_log' => 'File export Log',
    'change_permission_log' => 'Access Change Log',
    'enterprise_template_setting' => 'Enterprise Template Setting',
    'enterprise_template_setting_tips' => 'If checked, you can set a template as an enterprise template which can be used throughout your company, in sections such as Templates Shared to Me/Templates I Shared/My templates.',
    'enterprise_template_setting_tips2' => 'This permission determines whether you can enter "InELN Administration"-"Advanced access control". If the view permission is checked, you can enter this page; If the edit permission is checked, you can modify the permissions in this page.',

    //InDraw提示
    'indraw_hint_1' => 'InELN integrates InDraw, which can communicate with ChemDraw, InDraw Client and Microsoft Word/PPT.',   //add by hkk 2019/4/3
    'indraw_hint_2' => 'You can download InDraw Client through "http://www.integle.com/static/indraw".',   //add by hkk 2019/4/3
    'indraw_hint_3' => '1. Copy and paste between InDraw and ChemDraw',   //add by hkk 2019/4/3
    'indraw_hint_4' => 'From InDraw to ChemDraw: Copy structures in InDrawl using "Ctrl D", then in ChemDraw, press "Ctrl+Shift+Alt+P" or right click -> paste special -> "MOL/CDXML Text" .',
    'indraw_hint_5' => 'From ChemDraw to InDraw:  Select structures in ChemDraw, press "Ctrl D" or right click ->molecule->copy as->CDXML Text , then in InDraw, press "Ctrl+V" or right click -> paste special -> "MOL/CDXML Text" .',
    'indraw_hint_6' => 'You can also import CDX file or CDXML file into InDraw through InDraw toolbar button.',
    'indraw_hint_7' => '2. Copy and paste the structure of InDraw between multiple web pages or multiple browsers',
    'indraw_hint_8' => 'Press Ctrl+C (or Ctrl+D) to copy, Ctrl+V to paste in Windows computers;Press Command+C or Command+D to copy, Command+V to paste in Apple computers.',
    'indraw_hint_9' => '3. Copy and paste between InDraw and InDraw Client.',
    // 'indraw_hint_10' => 'From InDraw Client to InDraw: Select structures in InDraw Client , press "Ctrl+D" (or right click-> copy as -> CDXML ) , then in InDraw, press "Ctrl+V"；',
    'indraw_hint_10' => "From InDraw Client to InDraw: " .
                        'Select structures in InDraw Client , press "Ctrl+D" (or right click-> copy as -> CDXML ) , then in InDraw, press "Ctrl+V"；' . '<br/>' .
                        "Press \"Ctrl+C\" (or right click-> copy as -> Int/CDX ) , then in InDraw, press \"Ctrl+V\"；",
    'indraw_hint_11' => 'From InDraw to InDraw Client: Select structures in InDraw, press "Ctrl+D" (or right click-> copy as -> CDXML )，then in InDraw Client, press "Ctrl+V" (or right click -> paste special -> CDXML Text).',
    'indraw_hint_12' => '4. Copy and paste among InDraw, InDraw Client, Microsoft Word/PPT, and ChemDraw',
    'indraw_hint_13' => 'You can copy structures through "Ctrl+C" in InDraw Clent, then paste into Microsoft Word/PPT through "Ctrl+V" to yield a vector picture;The pasted structures in Word/PPT, can be chosen and copy through "Ctrl+C", then can be paste into ChemDraw through "Ctrl+V" in a re-editable way.',



    'group_setting_permission' => 'User Access Control',
    'share_setting' => 'Share setting (By single and batch)',
//    'export_setting' => 'Export setting (By single and batch)',
    'close_setting' => 'Witness setting (By single and batch)',
    'pre_setting' => 'Pre-approve setting (By single and batch)',
    'template_setting' => 'Template setting (By single and batch)',
    'sign_setting' => 'Sign setting (By single and batch)',
    'group_member_sign_setting' => 'sign setting',
    'work_order_setting' => 'Work order setting (By single and batch)',
    'group_page_setting' => 'PDF print setting',
    'group_close_setting' => 'Witness setting of group',
    'reopen_setting' => 'Re-open setting',
    'group_require_setting' => 'Require setting',
    'group_create_notebook_setting' => 'Create notebook setting',
    'witness_setting' => 'Witness setting',
    'pretrial_setting' => 'Pretrial setting',
    'signing_setting' => 'Signing setting',
    'collaboration_setting' => 'Collaboration setting',

    'view_setting_pages' => 'View permissions',
    'view_setting_pages_tips' => 'View Setting in "Advanced Access Ctrol" ',
    'edit_setting_pages' => 'Edit Setting',
    'edit_setting_pages_tips' => 'Edit Setting in "Advanced Access Ctrol"',
    'group_setting_permission_tips' => 'Edit configuration in "Advanced Access Ctrol"',
    'decimail_tips'=>'The setting is applicable to InDraw component, without restriction to the value from manual input', //dgh?
    'iupac_show_tips'=>'Here you can set the ‘Name’ column of the InDraw structure is automatically displayed the CN or EN IUPAC, excluding the material through search or input manually',
    'parameter_name'=>'Fields from InDraw',
    'decimal_number'=>'Decimal number place', //dgh
    'reset'=>'Reset',

    //实验预审
    'all_experiments_need_to_be_pre_approved'=>'All experiments need to be pre-approved',
    'please_choose_approver'=>'Please choose a reviewer.',
    'pre_approved_tips1'=>'Experiments including hazardous chemicals need to be pre-approved',
    'hazardous'=>'Hazardous',
    'precursor'=>'Precursor',
    'explosive'=>'Explosive',
    'hypertoxic'=>'Hypertoxic',
    'pre_approved_tips2'=>'Experiments including hazardous chemicals in InDraw need to pre-approved',
    'please_choose_projects'=>'Please choose projects',
    'pls_select_project' => 'Please select project',
    'search_no_result'=>'No result found',
    'pre_approved_tips3'=>'Re-open a Witnessed experiment requires approval.',
    'searchbook1'=>'Search Notebook',
    'export'=>'Export',
    'groups'=>'Groups',
    'notebooks'=>'Notebooks',
    'experiments'=>'Experiments',
    'closed_experiments'=>'Witnessed Experiments',
    'successful_experiments'=>'Successful Experiments', //dgh
    'recent_login'=>'Recent Login', //dgh最近登录时间

    //数据结构化
    'structdata_tips1'=>'Please enter notebook page numbers', //dgh
    'input_exp_num'=>'Page number',
    'generate_structured_data'=>'Data structuring',
    'history_data'=>'My Records',
    'structdata_tips2'=>'Data structuring has to be done within the same template. And structuring data fields can be set in "My Workspace" - "Templates" - "View/Edit"', //dgh

    'filter'=>'Filter',  //dgh参数筛选
    'filter2'=>'Parameter Filter',//dgh
    'login_eln_log'=>'Login log',
    'exp_share_log'=>'Share log',
    'exp_view_log'=>'View log',
    'exp_export_log'=>'Export log',
    'auth_change_log'=>'Access change log',
    'login_ip'=>'Login IP',
    'login_time'=>'Login Time',
    'login_status'=>'Login Status',
    'exp_num_search'=>'Page number',
    'share_type_search'=>'Sharing Type',
    'share_type_search_1'=>'Willing to share',//主动分享dgh
    'share_type_search_2'=>'Being shared to',//被动分享dgh
    'project_search'=>'Project',
    'group_search'=>'Group',
    'view_time'=>'View time',
    'share_time_search'=>'Time',
    'share_exp_num'=>'Page number',
    'notebook'=>'Notebook',
    'to_share_group'=>'To Group',
    'to_share_user'=>'To User',
    'exp_num'=>'Batch no.',
    'author'=>'Author',
    'id_address'=>'IP Address',
    'exp_book_num'=>'Page Number',
    'export_type'=>'Export Type',
    'operation_type'=>'Operation type',
    'export_detail'=>'File Exported Details',
    'operate_time'=>'Time',
    'share_operate_user' => 'User',
    'set_detail'=>'Setting Details',
    'system_master'=>'System Administrator',


    'BatchNo_hint'=>'Separated by ";" in English context',
    'sys_master'=>'Group Owner/ELN Admin.',
    'group_user' => 'Group Member',
    'ip_address' => 'IP Address',

      'single_export' => 'Export single experiments',
    'batch_export' => 'Export batch experiments',
    'whole_export' => 'Export all experiments',
    'single_to_pdf' => 'Export single pdf experiments',
    'single_to_word' => 'Export single word experiments',

    'creat_exp_1'=>'Create Experiment/Notebook',
    'share_type_search_3'=>'Cancel sharing',
    'no_power'=>'Access denied, please contact System Admin./Group Owner/Group ELN Admin',

    'company_setting' => 'Advanced Access Control',
    'static_time' => 'Time',


    'historic_no' => 'No.',
    'historic_time' => 'List creation time',
    'historic_exp_no' => 'Search Page Number',
    'historic_list' => 'View List',
    'historic_use_static_title' => 'Title',

    'add_file' => 'Add file',
    'add_picture' => 'Add picture',
    'columns'=>'Columns',

	'upload_hint_1' => 'Upload no more than 200 files at a time, the total size does not exceed 500M.',
	'upload_hint_2' => 'If the network speed is slow, the upload time will exceed 2 minutes, which may cause the upload to fail.',

	'toolbar_hide' => 'Hide',
    'toolbar_show' => 'Show',

    'eln_master'=>'ELN Manager',
    'group_master'=>'Group Master',
    'hide_show_column'=>'Hide/Show Columns',
    'sync_inventory'=>'Simultaneous deduction from InWMS',
    'collapse_material'=>'Hide stoichiometry. The stoichiometry will not appear in the exported PDF or Word.',
    'collapse_material2'=>'Show stoichiometry. The stoichiometry will appear in the exported PDF or Word.',

    'collapse_tip1'=>'Hide. The Details will not appear in the exported PDF or Word.',
    'collapse_tip2'=>'Show. The Details will appear in the exported PDF or Word.',

    'approval_sign_create_time' => 'Witness application time',
    'approval_sign' => 'Witness',
    'approval_coauthor_sign' => 'Co-author',
    'approval_reopen' => 'Re-open',
    'approval_signing' => 'Sign',
    'approval_pretrial' => 'Pre-approval',
    'approval_book' => 'Create notebook',
    'approval_exp_notebook' => 'Notebook',
	'approval_template' => 'Template',
	'approval_module_reedit' => 'Module re-edit',
    'approval_instrument' => 'Instrument',
    'approval_work_order' => 'Work order',
	'approval_result' => 'Approval result',
	'approval_agree' => 'Approve',
	'approval_refuse' => 'Reject',
    '_Approved' => 'Approved',
    '_Rejected' => 'Rejected',
    'approval_waiting' => 'Waiting for approval',
    'approval_ing' => 'Approval in process',
    'approval_die' => 'Canceled',
    'approval_view' => 'View approval progress',
    'approval_dialog_title_agree' => 'Approve',
    'approval_dialog_title_refuse' => 'Reject',
    'approval_dialog_title_view' => 'View approval progress',
	'approval_history' => 'Approval history',
    'submit_reason' => 'Submit Reason',
    'approval_submit_time' => 'Application time',

    /*begin记录本仪器库相关翻译*/
    'book_manage'=>'Notebook Manage',
    'book_manage_number'=>'Prefix of Notebook No. Setting',
    'book_manage_file'=>'Notebook File',
    'book_code'=>'Notebook number',
    'book_name'=>'Notebook name',
    'group_name'=>'Group name',
    'page_number'=>'Used page/total pages',
    'page_use'=>'The notebook usage rate',
    'exp_last_time'=>'The latest experiment creation time',
    'book_create_time'=>'The new notebook application time',
    'instruments_manage'=>'Instrument Manage', //Instruments Manage, Modified by wzb
    'instruments'=>'Instruments',
    'instrument_name_excel'=>'Instrument name(Required)',
    'instrument_id_excel' => 'Instrument id(unique，unchangeable)',
    'instrument_status_excel'=>'Status(normal,suspend use,scrap,repairing,deleted)',
    'instrument_check_excel'=>'Calibration(No need for calibration, need calibration)',
    'instrument_group_excel'=> 'Group(input group name,separated by semicolons if many,blank represents none,if both Group and Department are none, all groups and all departments are visible)',
    'instrument_department_excel'=> 'Department(input department name,separated by semicolons if many,blank represents none,if both Group and Department are none, all groups and all departments are visible)',
    'instrument_response_excel'=> 'Response person(input user name)',
    'instrument_charge_excel'=> 'In Charge Person(input user name)',
    'instrument_maintainer_excel'=> 'Maintainer(input user name)',
    'instrument'=>'Instruments',
    'batch_number'=>'Device ID', // modified by hkk 2020/4/23  原是批号
    'specification'=>'Spec',
    'model'=>'Serial No.', // modified by hkk 2020/4/23  原是货号/型号
    'manufacturer_tips'=>'Manufa..', // modified by hkk 2020/4/23 制造商 Manufacturer
    'manufacturer'=>'Manufacturer',
    'supplier'=>'Supplier',
    'check_situation'=>'Calibration Situation',
    'position'=>'Location',
    'file'=>'Files',
    'create_person'=>'Created by',
    'create_by_tips'=>'Create..',
    'response_person_1'=>'Head',
    'maintainer'=>'Maintainer',
//    'person_in_charge'=>'In Charge Person', // 该翻译调整为 Repairer
    'operation'=>'Operation',
    'start_time'=>'Start time',
    'end_time'=>'End time',
    'related_experiment'=>'Related experiment',
    'batchAdd'=>'Batch Add',
    'chooseConfigInstrument'=>'Select config instrument',
    'add_from_instruments' => 'Add an instrument',
    'instrument_name' => 'Instrument Info',
    'instrument_operate' => 'operate instrument',
    'instrument_category' => 'Category',
    'open_time_tip' => 'Need this parameter when calculate instrument usage, default 24h per day',
    'open_time' => 'Open time',
    'instrument_book' => 'Book the instrument',
    'operation_details' => 'Operation details',
    'instrument_trace_input' => 'Please input keywords',
    'instrument_book_graph' => 'Appointment statistics',
    'instrument_usage_graph' => 'Usage statistic',
    'instrument_static_input' => 'Please input name or batch number',
    'instrument_date_time' => 'date/use time(h)',
    'instrument_member_usage' => 'user/usage',
    'instrument_graph_type' => 'Graph type',
    'instrument_repair' => 'Repair',
    'instrument_approval_repair' => 'repair',
    'instrument_repair_reason' => 'Repair reason',
    'instrument_reminder' => 'Reminder',
    'instrument_adjust_column' =>'Adjust Column',
    'witness_remind' => 'witness remind',
    'record_number' => 'Record No.',
    'instrument_history' => 'History',
    'book_instrument' => 'Book Instrument',
    'modify_book' => 'Modify Book',
    'instrument_book_tip1' => 'Please select the start and end time of the instrument reservation',
    'instrument_check_tip1' => 'Please select the start and end time of the instrument calibration',
    'instrument_check_tip2' => 'Please input calibration person',
    'experiment_page_number' => 'Experiment Page Number',
    'experiment_separate_tip' => 'Separated by "," in English context',
    'instrument_historical_book_record' => 'Historical reservation of the instrument',
    'instrument_no_book_record' => 'No historical reservation',
    'order' => 'Order',
    'person' => 'Person',
    'instrument_book_time' => 'book time',
    'instrument_type' => 'Type',
    'data_type' => 'Category',
    'data_type_tip' => 'used to set the data type of the docking instrument, can be set as \'{numeric_data_type}\' or \'{file_data_type}\' or Nothing',
    'value' => 'value',
    'batch_number_conflict' => 'Device ID conflict',
    'input_unique_ID' => 'input unique device ID',
    'instrument_data_type_tip' => 'This is used to select the instrument data docking method. Docked/Total quantity:{0}/{1}',
    'InScada' => 'InScada',
    'instrument_related_experiments' => 'Related experiments',
    'already_deleted' => 'Deleted',
    'suspend_use' => 'Suspend use',
    'repairing' => 'Repairing',
    'checked' => 'Calibrated',
    'noNeedCheck' => 'No need for calibration',
    'newCheck' => 'Calibration Record',
    'needCheck' => 'Need calibration',
    'instrument_edit' => 'Edit Instrument',
    'upload_file' => 'Upload File',
    'check' => 'Calibration',
    'scrap' => 'Scrap',
    'unchecked' => 'No Calibration',
    'batch_add_instruments' => 'Batch add instruments',
    'batch_add_tip1' => '1 Download batch_add template:',
    'batch_add_tip2' => '2 Upload batch_add file:',
    'batch_add_tip3' => '1. The Device ID must be unique;',
    'batch_add_tip4' => '2. Group and Department: After the instrument belongs to the selected group or department, the Group member or Department member can view and use the instrument from "My Workspace-Instruments" (The default instrument is not limited to the Group or Department without setting);',
    'batch_add_tip5' => '3. Category: This is used to set the docking instrument is a numeric value or file type. Instrument values or files can be inserted directly into the experiment. For details, please contact<a target="_blank" href="http://www.integle.com/contact">Integle customer service</a>',
    'batch_add_tip6' => '4. The files or pictures of the instrument need to be individually set in the operation bar "Edit".',
    'no_instrument_result_tip1' => 'No results',
    'no_instrument_result_tip2' => 'If you do not find the instrument you need, please consult your instrument administrator. The possible reasons are:',
    'no_instrument_result_tip3' => '1. The instrument has not been added into the instrument database.',
    'no_instrument_result_tip4' => '2. The instrument has been added, but was not been authorized to your Group. ',
    'no_selected_file' => 'No files were selected',
    'choose_file' => 'Choose File',
    'EXCEL_template' => 'EXCEL template',
    'operate_history' => 'Operate History',
    'add_instrument' => 'Add Instrument',
    'edit_instrument' => 'Edited Instrument',
    'repair_instrument' => 'Repaired instrument',
    'delete_instrument' => 'Deleted instrument',
    'book_instrument1' => 'Booked instrument',
    'modify_book1' => 'Modified book',
    'delete_book' => 'Deleted book',
    'edit_operate_record' => 'Edit Operate Record',
    'edit_repair_record' => 'Edit Maintenance Record',
    'edit_check_record' => 'Edit Calibration Record',
    'upload_instrument_picture' => 'Upload instrument picture',
    'delete_instrument_picture' => 'Delete instrument picture',
    'instrument_approval' => 'Approval',
    'instrument_set' => 'Settings',
    'instrument_remind' => 'Reminders',
    'expiry_tip1'=> 'Send email/message to Head,Maintainer and Repairer',
    'expiry_tip2'=> 'days before expiration',

    'response_person'=>'Response person',
    'person_in_charge'=>'Repairer',


    'none' => 'None',
    'time' => 'Time',
    'operate_detail' => 'Operation detail',
    'operate_type' => 'Operation type',
    'wo_type' => 'Type:',
    'wo_process' => 'process:',
    'solve' => 'Solve',
    'activate' => 'Activate',
    'add_instrument1' => 'Add Instrument',
    'edit_instrument1' => 'Edit Instrument1',
    'instrument_detail' => 'Instrument Detail',
    'record_conflict_tip' => 'Instrument operate status was changed, please refresh current page!',
    'record_canceled' => 'The record is not support ending as it has been cancelled',
    'repair_instrument1' => 'Repair instrument1',
    'confirm_repair_instrument' => 'Are you sure you want to report for repair? The instrument will enter the maintenance state and cannot be reserved for use. The instrument library administrator needs to set the status to "Normal" in "InELN Administration"-" InELN Enterprise Setting "-" Instrument Manage"-" Edit "-"Status."', //'confirm_repair_instrument' => 'Are you Confirm to repair instrument?', by wzb
    'input_repair_reason' => 'Please input repair reason',
    'email_cc' => 'CC email list',
    'my_instruments' => 'My Instruments',
    'my_instruments_reservation' => 'My Reservation',
    'modify' => 'Modify',
    'more_filter' => 'More',
    'conflict_time_tip' => 'Conflict time: ',
    'excel_wrong_format' => 'Incorrect Excel file title',
    'choose' => 'Choose',
    'excel_wrong' => 'Incorrect file (possibly encrypted)',
    'inscada_not_activited' => 'InScada function has not been activated, no need to fill in "Category" ',
    'instrument_check' => 'Instrument Calibration',
    'instrument_record_check' => 'Instrument Check',
    'add_check' => 'Add Record',
    'add_check_pop' => 'Add Calibration',
    'add_check_info' => 'wait review',
    'edit_check' => 'Edit Check',
    'define_show_column' => 'Show/Hide Column',
    'show_hidden' => 'Show/Hide Column',
    'uncertainty' => 'Uncertainty',
    'credibility' => 'Credibility',
    'k_value' => 'k Value',
    'corrected_value' => 'Corrected Value',
    'expiry_date' => 'Expiry Date',
    'expiry_data_start' => 'Start expiry Date',
    'expiry_data_end' => 'End expiry Date',
    'expiry_date_remind' => 'Expiry reminder',
    'check_start_time' => 'Calibration start time',
    'check_end_time' => 'Calibration end time',
    'expiry_data_start_time' => 'Start expiry Date',
    'expiry_data_end_time' => 'End expiry Date',
    'check_institution' => 'Agency',
    'check_personnel' => 'Verifier',
    'check_content' => 'Content',
    'delete_reason' => 'Delete reason',
    'delete_file' => 'Delete file',
    'sign_reason' => 'reason',
    'signer' => 'signatory',
    'inscada_sign_detail'=>'{0}{1} sign,{2}',
    'instrument_delete_reason'=>'Delete reason',
    'receive_to_exp' => 'Receive to experiment',

    // 预约仪器相关
    'booking_instruments' => 'instruments booking',

    // inscada 数据详情
    'data' => 'data',
    'time_range' => 'Time range',
    'receive' => 'Receive',
    'receive_popup' => 'Insert',
    'receive_page' => 'Collect',
    'batch_receive' => 'Batch receive',
    'instant_receive' => 'Receive at experiment',
    'instant_receive_file' => 'Receive at experiment',
    'exp_pages' => 'Exp pages',
    'timestamp' => 'Time stamp',
    'password' => 'Password',
    'usrname_pwd_required' => 'Username and password are required',
    'usrname_pwd_err' => 'Username or password error',
    'numerical_value' => 'Value',
    'unit' => 'Unit',
    'raw_data' => 'Detail',
    'operate_users' => 'Operate users',
    'detect' => 'Detect',
    'input_one_exp_page' => 'Support only one Notebook page',
    'select_upload_module' => 'Multiple "upload files" module detected, please choose:',
    'filename' => 'File name',
    'filepath' => 'File path',
    'chart' => 'Chart',
    'attach_file' => 'Attach File',
    'repair_record' => 'Maintenance Record',
    'repair_type' => 'Maintenance Type',
    'check_record' => 'Calibration record',
    'usage_situation' => 'Usage situation',
    'operate_record' => 'Running Record',
    'operation_time' => 'Operation time',
    'operator' => 'Operator',
    'repair_start_time' => 'Start maintenance time',
    'repair_end_time' => 'End maintenance time',
    'repair_content' => 'Content',
    'repair_result' => 'Result ',
    'repair_person' => 'Maintainer',
    'reviewer' => 'Reviewer',
    'review_time' => 'Review time',
    'review_conclusion' => 'Review conclusion',
    'review_reason' => 'Review reason',
    'review_record' => 'Review record',
    'define_title' => 'Define title',
    'add_repair_record' => 'Add Record',
    'add_repair_record_pop' => 'Add Maintenance',
    'add_column' => 'Add Column',
    'cancellation' => 'Cancellation',
    'instrument_review' => 'Review',
    'start_record_time' => 'Start operate time',
    'end_operation_time' => 'End of Operation Time',
    'start_operator' => 'Created by', //  'start_operator' => 'Start operator',字段调整
    'start_running_time' => 'Start of Runtime',
    'end_record_time' => 'End record time',
    'end_operator' => 'End operator',
    'end_running_time' => 'End of Runtime',
    'operation_content' => 'Operation content',
    'start_running' => 'Add Running',
    'end_running' => 'End Running',
    'edit_running' => 'Edit Record', // edit
    'add_running' => 'Add Record',
    'add_column_tip' => 'Double click title of the column to edit',
    'instrument_conflict_tip1' => 'Instrument status is not correct, cannot submit!',
    'instrument_conflict_tip1_1' => 'Instrument status is not correct',
    'instrument_conflict_tip2' => 'Instrument is not calibrated, cannot submit!',
    'instrument_conflict_tip2_2' => 'Instrument is not calibrated',
    'instrument_conflict_tip3' => 'Instrument status is not correct, cannot submit!',
    'instrument_conflict_tip4' => 'Instrument operate time/calibration time/ repair time period disallows duplicates',
    'instrument_conflict_tip5' => 'Access denied ',
    'instrument_conflict_tip6' => 'No modification, invalid edit',
    'instrument_conflict_check' => 'Calibration Time can’t coincide with the following time',
    'instrument_conflict_repair' => 'Maintenance Time can’t coincide with the following time',
    'instrument_conflict_operate' => 'Running time can’t coincide with the following time',
    'check_time' => 'Calibration Time',
    'repair_time' => 'Maintenance Time',
    'running_time' => 'Running time',
    'on_going' => 'Ongoing',
    'on_going_records' => 'The Running（Calibration\Maintenance）record is ongoing, please add a record after it is finished: ',
    'on_going_records2' => 'The Running（Calibration\Maintenance）record is ongoing:',
    'operate_time_repeat_not_allow' => ' Repeat operate times are not allowed',
    'repair_time_repeat_not_allow' => ' Repeat maintenance times are not allowed',
    'check_time_repeat_not_allow' => 'Repeat calibration times are not allowed',
    'time_only_after_operate' => 'The time of creating the running (repair/validation) record should be earlier than "Start running(repair/validation) time"',
    'record_details' => 'Record details',
    'instrument_usage_percent' => 'Instrument usage (%)',
    'use_times' => 'Use times',
    'use_date' => 'Use date',
    'use_time' => 'Used time(h)',
    'record_config_tip' => 'Create Instrument record template quickly by using the fields in the Operate record, Repair&Maintenance record, or Validation record of the selected instrument',
    'instrument_setting' => 'Instrument setting',
    'instrument_check_setting' => 'Calibration setting',
    'operate_record_must_check' => 'Operate record must check',
    'check_record_must_check' => 'Calibration record must check',
    'repair_record_must_check' => 'Repair&Maintenance record must check',
    'please_select_check_user' => 'Please select check user',
    'status_change_setting' => 'Status change setting',
    'scrap_must_check' => 'Scrap must check',
    'suspend_use_must_check' => 'Suspend use must check',
    'delete_must_check' => 'Delete must check',
    'apply_repair_must_check' => 'Apply repair must check',
    'time_setting' => 'Time setting',

    'record_setting' => 'Approval&Time Config',
    'usage_statistics' => 'Usage Statistics',
    'batch_edit' => 'Batch edit',
    'instrument_id_repeat' => 'Device ID"{0}" is already in use',

    'instrument_batch_edit' => 'Batch edit instruments',
    'instrument_batch_edit_tip1' => '1 Please export instruments list first ',
    'instrument_batch_edit_tip2' => '2 Please import instruments list after edit',
    'export_list' => 'Export list',
    'import_list' => 'Import list',
    'edit_history' => 'Edit history',
    'import_number' => 'Import number',
    'success_number' => 'success',
    'fail_number' => 'failure',
    'before_import_file' => 'file before import',
    'after_import_file' => 'file after import',
    'add_check_record_tip' => 'Tips: <br>1. These items need to be filled when Calibration is completed: Start Time, End Time, Verifier and Expiry Date;<br>2. End Calibration will be submitted to the reviewer if there is a review. Review result displayed in review record column;<br>3. The status of the instrument is Calibrated after Calibration approval and Schedule/Run can be used.',
    'add_repair_record_tip' => 'Tips: <br>1. These items need to be filled when Maintenance is completed: Start Time, End Time, Content, Result (Optional when type is "Maintenance") and Maintainer;<br>2. End Maintenance will be submitted to the reviewer if there is a review. Review result displayed in Review Record column;<br>3. If “Status of the Instrument  changes to Normal” is selected, the Status of the instrument will be Normal after Maintenance Approval and Schedule/Run can be used.',
    'review_record_tip' => 'Tips: Current record will be submit to approver. Review result displayed in review record column;',
    'check_success' => 'review approved',
    'check_failed' => 'review rejected',
    'undo_check' => 'Undo review',
    'recover' => 'Recover',
    'undo_check_conflict_tip' => 'Approval of this record has been finished, this page will be refreshed',
    'no_review' => 'Under Review',
    'review_success' => 'Review Success',
    'open_record_tip' => 'View instrument operate record page',
    'running' => 'Operating',
    'running_review' => 'Operating reviewing',
    'repairing_review' => 'repair reviewing',
    'checking' => 'Checking',
    'checking_review' => 'Check reviewing',
    'instrument_reminder_tip10' => 'Columns exceeds the maximum limit',
    'checking_refuse_cancel' => 'Cancellation is not supported as records are in approval',


    /*end仪器库相关翻译*/


    /*begin sequence相关翻译*/

    'sequence_tag' => 'Tag',
    'sequence_name' => 'Sequence name',
    'add_sequence' => 'Add Sequence',
    'set_tags' => 'Set tags',
    'please_select_tag' => 'Please select tag',
    'please_input_name' => 'Please input name',
    'add_sequence_confirm' => 'Confirm and open InSequence',
    'cancel' => 'Cancel',
    'delete_sequence' => 'Delete sequence',
    'delete_sequence_tip' => 'Are you sure to delete the sequence ?',
    'delete_batch_sequence_tip' => 'Are you sure to delete all selected sequences ?',
    'sequence_line' => 'line',
    'sequence_ring' => 'ring',
    'sequence_single' => 'single',
    'sequence_double' => 'double',
    'select_create_tag' => 'Select or create tag',
    'edit_sequence' => 'Modify sequence info',
    'copy_sequence' => 'Copy sequence',
    'edit_success' => 'Modify success',
    'copy_success' => 'Copy success',
    'delete_success' => 'Delete success',
    'share_success' => 'Share success',
    'cancel_share_success' => 'Cancel share success',
    'select_tip' => 'Please select sequences to delete',
    'select_edit_tip' => 'Please select sequences to modify',
    'select_share_tip' => 'Please select sequences to share',
    'edit_group' => 'Modify group',
    'edit_tag' => 'Modify tag',
    'batch_edit_group' => 'Batch modify group',
    'batch_edit_tag' => 'Batch modify tag',
    'sequence_share_to_me' => 'Sequences shared',
    'batch_operation' => 'Batch',
    'open_sequence_editor' => 'Open InSequence editor',
    'confirm_and_open_sequence' => 'Confirm and open InSequence',
    'add_share_member' => 'Add share member',
    'confirm_share' => 'Confirm',
    'already_share' => 'Already shared',
    'cancel_share' => 'Cancel share',
    'share_to_user' => 'Share to user',
    'share_to_group' => 'Share to group',
    'view_my_sequence' => 'View my sequences',
    'copy_to_my_sequence' => 'Copy to my sequence',
    'insert_sequence' => 'Insert sequence',
    'insert_image' => 'Insert image',
    'insert_link' => 'Insert link',

    /*end sequence相关翻译*/


    'normal'=>'Normal',
    'recycle_bin'=>'Move to recycle bin',
    'please_select_group'=>'please select group',
    'instrument_group_tip'=>'After adding the Group, the members of the Group can see this instrument in "My Workspace"-"Instruments"',
    'instrument_type_tip'=>'You can change the default select options through company dictionary',
    'all'=>'All',
    'user_book_number'=>'User (Total notebook number)',
    'department'=>'Department',
    'exp_checker' => 'InELN Auditor',
    'belong_department'=>'Dept. ',
    'exp_fail_num'=>'Fail',
    'exp_stop_num'=>'Stop',
    'exp_record_num'=>'Record Time(Min)',
    'exp_login_num'=>'Login Time',
    'exp_unlock_num'=>'Unlock Screen Time',
    'exp_witness_num'=>'Number of experiments under review',
    'show_chats'=>'User activity graph',
    'hide_upload'=>'Hide upload files',
    'show_upload'=>'Show upload files',
    'use_static_mail_config'=>'Report',

    'user_static_chats'=>'User activity graph',
    'user_static_history_chats'=>'History graph',
    'user_static_history_title' =>'Title',
    'x_ray'=>'Abscissa',
    'y_ray'=>'Coordinate',
    'create_picture'=>'Take a Photo',
    'please_select_send_time'=>'Please select a period for sending the report through email:',
    'send_time_by_week'=>'By Week（AM 01:00 Saturday）',
    'send_time_by_month'=>'By Month（AM 01:00 on 1st of the month）',
    'please_select_send_user'=>'Please choose receiver',
    'use_statistics' => 'Use statistics',
    'batch_to_pdf' => 'Export as PDF',
    'batch_to_word' => 'Export as WORD',
    'export_to_word' => 'Export as WORD',
    'whole_to_pdf' => 'Export as PDF',
    'download_file' => 'Download file',
    'view_file' => 'View file',


    'submit_dict'=>'submit new dict',
    'download_new_dict'=>'download the latest dict',
    'download_init_dict'=>'download the system dict',
    'file_empty'=>'no file ',
    //'group_setting'=>'Group Setting',
    'general_setting'=>'General Setting',
    'pretrial setting'=>'Pretrial Setting',
    'other_setting' => 'Other settings',
    'setting need pretrial'=>'All experiments should be pretrial by',
    'setting need pretrial with file'=>'If InDraw has {0} then the experiment should pretrial by ',
    'no decimal auth'=>'Access denied, please contact System Admin./Group Owner/Group ELN Admin',
    'enable'=>'Enable',
    'disable'=>'Disable',
    'can not edit'=>'Access denied',
    'reopen need sign'=>'After signed,if you want reopen experiment,then the experiment should by approved by ',
    'print_setting'=>'Print Setting',
    'export_setting' => 'Export Setting',
    'whole_batch'=>'Whole/Batch Export',
    'send sign message'=>'the experiment unsigned after {0} days ,the system will send mail to this email',
    'set singer by'=>'Signed by',
    'master_0'=>'master',
    'creat_edit_temp_power'=>'Create template power',
    'set the user'=>'Set user ',
    'power is'=>'power is',
    'set group name'=>'Set group name',
    'read'=>'read',
    'edit'=>'edit',
	'no_modification_after_witness'=>'The Group is set to not allow modification after witness',
    'minute'=>'Minute',
    'hour'=>'Hour',
    'day'=>'Day',
	'set_as_home_page_dir' => 'As directory content',
	'cancel_as_home_page_dir' => 'Cancel as home page directory',
    'set_spell_check' => 'Open spell check',
    'cancel_spell_check' => 'Cancel spell check',
	'search_range_setting' => 'Search Range Setting',
	'show_revision_trace' => 'Show deleted trace',
	'set_text_as_structure' => 'Text sync.',
	'cancel_text_as_structure' => 'Cancel text sync.',
	'set_text_as_structure_tip' => 'The plain texts on two sides of the arrow will be synchronized in stoichiometry',
	'cancel_text_as_structure_tip' => 'The plain texts on two sides of the arrow will not be synchronized in stoichiometry',
	'set_font_size' => 'PDF Export settings',
	'pdf_font_size' => 'Export font size settings',
    'role_setting' => 'Role setting',

    'export_pdf'=>'Export in PDF format',
    'export_pdf_book'=>'Export in PDF format',
    'view_pdf'=>'PDF preview',
    'export_word'=>'Export in Word format',
    'export_word_book'=>'Export in PDF format',
    'export_pdf_tips_1'=>'<span>Export a compressed package of PDF + attachment.</span><br><span style="padding-left:20px">All files can be exported.</span>',
    'export_pdf_tips_2'=>'<span>Export a PDF file.</span><br><span style="padding-left:20px">If the attachment cannot be converted to PDF, it will not be exported.</span>',
    'export_pdf_tips_3'=>'Please select the export format',
    'export_pdf_tips_4'=>'Tip: If there is an encrypted file attached to the experiment, please choose the second export method',
    'export_pdf_single_file' => 'Export a PDF of each experiment, the upload files in each experiment is attached to the experiment content',
    'export_pdf_single_file_tip' => 'If the attachment cannot be converted to PDF, it will not be exported.',
    'export_pdf_with_attachment' => 'Export a PDF of each experiment, and the uploaded files in the experiment are exported separately and are not attached to the experiment content.',
    'export_pdf_with_attachment_tip' => 'All files can be exported.',

    'view_pdf_tips_1'=>'The experimental records and uploaded files will be exported in a compressed file package. After downloading and decompressing, you need to click the file link in the exported PDF and directly open the uploaded files.',
    'view_pdf_tips_2'=>'The experimental records and the uploaded files will be exported in the same PDF file. However, the uploaded files need to be in PDF, picture or Word format',
    'view_pdf_tips_3'=>'Please select the export format',

    //'export_pdf_book_tips_1'=>'The experimental records and uploaded files will be exported in a compressed file package. After downloading and decompressing, you need to click the file link in the exported PDF and directly open the uploaded files.',
    //'export_pdf_book_tips_2'=>'The experimental records and the uploaded files will be exported in the same PDF file. However, the uploaded files need to be in PDF, picture or Word format',
    'export_pdf_book_tips_1'=>'<span>Export each record as one PDF file</span><br><span style="padding-left:20px">If the attachment cannot be converted to PDF, the attachment cannot be exported</span>',
    'export_pdf_book_tips_2'=>'<span>Export each record as one compressed package of  a PDF file + all attachments</span><br><span style="padding-left:20px">All attachments can be exported</span>',
    'export_pdf_book_tips_3'=>'Please select the export format',

    'export_word_tips_1'=>'Export records in Word format, without exporting attachments',
    'export_word_tips_2'=>'Export a compressed package, containing records in Word format + attachments',
    'export_word_tips_3'=>'Please select the content to be exported',
    'export_word_tips_4'=>'Export All',
    'export_word_tips_5'=>'Export by component',
    'export_word_tips_6'=>'Export all the experimental records in one WORD file',

    'export_word_book_tips_1'=>'The upload file  only displays the name in Word file when export.',
    'export_word_book_tips_2'=>'The uploaded file is displayed as a link in Word when export. Click the link to directly open the file.',
    'export_word_book_tips_3'=>'Please select the content to be exported',
    'export_word_book_tips_4'=>'Export All',
    'export_word_book_tips_5'=>'Export by component',
    'export_word_book_tips_6'=>'Export all the experimental records in one WORD file',
    'contact_us' => 'Contact us',
    'batch_add_bio_tips'=>'Please import the file',
    'dict_upload_history'=>'Imported files history list',
    'dict_upload_download'=>'View file',
    'file_name'=>'File name',
    'biology_search_tips'=>'Please fill in the keywords',
    'batch_add_bio_tips_1'=>'Please choose to import an Excel file, the first column in Excel corresponds to the first column in the table here, the second column in Excel corresponds to the second column in the table here, and so on',


    'batch_add_define'=>'Import custom table in bulk',
    'batch_add_bio'=>'Import materials or instruments in bulk',
    'batch_add_ref'=>'Import references in bulk',
    'batch_add_define_tips'=>'Select the file for import',
    'define_search_tips'=>'Please fill in the keywords',
    'batch_insert'=>'Import in bulk',
    'batch_delete'=>'Batch deletion',
    'batch_adjust'=>'Batch change',

    'sync_wms'=>'Connecting InWMS setting',

    'sync_wms_tips'=>'Please select the data that need to connect InWMS',

    'please_select'=>'Please select',
    'batch_num'=>'Batch number',
    'unit_name'=>'Commodity unit',
    'tax_rate'=>'Tax rate',
    'price'=>'Unit price',
    'no_tax_price'=>'Unit price without tax',
    'pro_date'=>'Production date',
    'exp_date'=>'Expiration date',
    'exp_day'=>'Shelf life',
    'pro_num'=>'Batch number',
    'upload_invoice'=>'Upload invoice',
    'barcode'=>'Barcode',
    'bin'=>'Bin',

    'product_name'=>'Commodity name',
    'product_num'=>'SKU',
    'category_name'=>'Category',
    'brand_id'=>'Brand',
    'ics'=>'ICS号',
    'cas'=>'CAS号',
    'purity'=>'Purity',
    'transport_condition'=>'Transportation conditions',
    'storage_condition'=>'Storage conditions',
    'specs'=>'Packing specification',
    'smiles'=>'Smiles',
    'is_danger'=>'Hazardous goods',
    'danger_type'=>'Classification of hazardous goods',
    'remark'=>'Notes',
    'producer'=>'Manufacturer',
    'english_name'=>'English name',

    'batch_select_exp'=>'Add experiments in bulk',
    'batch_select_exp_num'=>'Add experiments notebook page numbers in bulk',
    'batch_select_exp_num_tips'=>'When add notebook page numbers in bulk, separate them with "" such as
N190728-001,N190728-002,N190728-003...
or add them in new lines
N190728-001
N190728-00
N190728-00
...',
    //'book' => '记录本',
    'select_status' => 'Please select the status',
    'select_result' => 'Please select the conclusion',
    'search_for' => 'Search',
    'empty_result' => 'No data',
    'no_exp'=>'No experiment has been found',
    'no_wms_app'=>'Not available for search. You need to get access into the InWMS',
    'structdata_tips3'=>'There are 3 ways to enter the notebook page number:<br>1. Enter one by one: Enter a notebook page number in each input box<br>2. Add records in batch: Click "Add in batch" to search all records in the same template, and add them in batch after selection<br>3. Paste notebook page number in batches: Click "Paste in batch" and paste notebook page numbers, then you can add in batches',
    'spell_check_tip' => 'To enable English spell check, you need to enable the English spell check function in the browser settings. The setting path of Google Chrome is: In the upper right corner of the browser, Settings-Advanced-Languages-Add Languages-English (United States)-Spell Check.',
    'echat_tips_x_tips' => 'The x-axis of this chart is the date, and the y-axis is the total time of the user recording data during the day',

    'structdata_tips4'=>'Created method template can be selected "Insert method template" at text editor or selected "Insert subtemplate" at "InTable-Template-Insert"',//added by zh 2021/9/14

    'function_template_save_tip' => 'After saving the template, please submit for approval from "My-Template".',

    'method_template_name' => 'Method template name', //added by zh 2021/9/14

    'echat_tips_x_name'=>'Date',
    'echat_tips_y_name'=>'Record time spent',

    'exp_close_num'=>'Witnessed Experiments',
    'exp_success_num'=>'Quantity of successful experiments',
    'exp_record_time'=>'Recording time(Min)',
    'group_num'=>'Quantity of groups',
    'book_num'=>'Quantity of notebooks',

    'activity_grap_tip' => 'User activity graph',   //Add by wzb 20200528
    'favorited' => 'Added to favorites',    //add by wzb 20200527
    'user_active_chart' => 'Generate',
    'pre_order_note_page' => 'Notebook page',
    'notebook_num' => 'Page number',
    'row' => 'Row',
    'column' => 'Column',
    'link_to_exp_or_wms'=>'If this is a notebook page number or inventory batch number, you can jump to the corresponding experiment record or inventory item',
    'company_temp' => 'Corporate template',
    'change_group_for_notebook'=>'Change Notebook\'s Group',
    'change_the_notebook_to_group'=>'Change the notebook to Group',
    'reason_for_change'=>'Change reason',
    'please_input_the_reason_for_change'=>'Please enter why you change the notebook\'s Group to a new one',
    'change_group_for_notebook_log'=>'Notebook Change Group Log',
    'create_user'=>'Creator',
    'operate_user'=>'Operator',
    'operate_reason'=>'Reason',
    'from_group'=>'Group before change',
    'to_group'=>'Group after change',
    'system_default_number' => 'System default number',
    'system_default_number_desc' => 'Consists of the prefix N and the system serial number',
    'custom_number' => 'Custom number',
    'custom_number_desc' => 'If the prefix is changed to ABC, the record book number is ABC201212',
    'prefix' => 'Prefix',
    'no_record' => 'No record',
    'other' => 'Other',
    'upload_dict_company' => 'update dropdown list ',
    'select_instrument_file'=> 'Select instrument file',
    'import_tip' => 'Please select and upload file',
    'created_time' => 'Creation Time',
    'file_path' => 'File path',
    'no_data' => 'No data',
    'share_exp'=>'Share',
    'search_operations' => 'Please input shared group/user',
    'share_approval' => 'Approval',
    'cancel_sharing' => 'Cancel sharing',
    'share_type' => 'Type',
    'share_whole_exp' => 'Share the whole experiment',
    'share_modules' => 'Share modules',
    'share_expiration_date' => 'Share expiration date',
    'result' =>'result',
    'share_success' => 'share successfully',
    'waiting_for_approval' => 'submitted for approval',
    'approved' => 'approved',
    'rejected' => 'rejected',
    'reason' => 'reason',
    'apply_user' => 'User',
    'sharing_canceled' => 'sharing canceled',
    'share_unchanged' => 'No settings have been changed',
    'more_data_not_exported' => 'Not all data was exported due to too much data.',
    'share_to' => 'Share to:',
    'auth_manage' => 'InELN Permissions',
    'role_manage' => 'InELN Roles',
    'check_experment' => 'InELN Auditor',

    'set_company_auth' => 'Assign enterprise permissions',
    'set_company_role' => 'Assign roles with enterprise-level permissions',
    'setCompanyRoleTip' =>'This line is used to assign roles with enterprise permissions, assign and view enterprise permissions',
    'company_auth' => 'Enterprise permissions',
    'company_role' => 'Enterprise roles',
    'w_order_cc' => 'work order cc user',
    'w_order_exp_page' => 'experiment page',
    'group_role' => 'Group role',

    'selected_user' => 'Selected users',
    'selected_dep' => 'Selected deps',
    'set_group_role' => 'Set group role',
    'pls_select_user' => 'Select users',
    'others' => 'Others',
    'company_feature' => 'Enterprise settings',

    'set_top' => 'Set top',
    'cancel_top' => 'Cancel top',

    'select_operation_type' => 'Please select operation type',
    'pls_select_type' => 'Please select type',

    'exp_check_by_group' => 'Exp check by group',
    'exp_check_by_project' => 'Exp check by project',
    'submit_witness_failed_tip' => 'Only submit your own experiments for review',
    'submit_share_failed_tip' => 'Only share your own experiments or those of group members',

    'sys_admin' => 'System admin',
    'group_owner' => 'Group owner',
    'eln_admin' => 'ELN admin',
    'group_admin' => 'Group admin',
    'ordinary_member'=>'User',


    'group_auth_config' => 'Group permission settings',
    'group_auth' => 'Group permission',
    'please_select_role' => 'Please select role',
    'set_check_user_tips_01'=>'The selected experiment auditors can view all experiments in selected groups/projects (View from "My Workspace"-"ELN Inspection")',
    'by_group'=>'By group',
    'by_project'=>'By project',
    'user_info'=>'userinfo',
    'user_name'=>'username',
    'receive_mail_notice'=>'receive mail notice',
    'notice_by_create'=>'notice by create',
    'notice_by_edit'=>'notice by edit',
    'sign_process'=>'Signature process',
    'sign_edit_config'=>'Signature edit settings',
    'approve'=>'approve',
    'no_power_by_auth'=>'Tips: Permissions can’t be assigned. Please ask the system admin to activate it in "InELN Enterprise Setting - InELN Permissions" .',
    'distribute'=>'Assign',
    'role_name'=>'role name',
    'company_role_tips_01'=>'1.After selecting "Company role" for the role, "enterprise-level permissions" can be displayed and configured in the corresponding role permissions;；',
    'company_role_tips_02'=>'2.Except for the system default roles (system administrator, group owner, group administrator, ELN administrator, common member), other company roles can be assigned to users in [Company Permissions - Permission Settings]',
    'group_role_tips_01'=>'1.After selecting "Group Role", "In-group permissions" can be displayed and configured in the corresponding role permissions；',
    'group_role_tips_02'=>'2.Except for the default roles of the system (system administrator, group owner, group administrator, common member), other roles in the group can be assigned to users in [Group Permissions - Assign Roles]',
    'group_role_tips'=>'The “Group owner" is assigned by default according to create the group and can’t be selected. Other roles can be assigned in "Group permissions"',
    'company_role_tips'=>'"System admin" in the enterprise-level role is assigned by default according to the role set in the Plug, and cannot be selected. Other enterprise-level roles can be assigned',
    'set_group_auth' => 'Assign Group permissions',

    'set_auth'=>'Assign permissions',
    'view_auth'=>'View permissions',
    'no_book_auth' => 'Experiment cannot be created under this notebook',
    'set_role_auth'=>'Assign role permissions',
    'sys_manager_edit_only' => 'Only system admin can edit the default role',
    'view_role_auth'=>'Role Permissions',
    'set_company_role_title'=>'Set up roles with enterprise-level permissions',
    'set_group_role_title'=>'Set a role with group permissions',
    'distribute_group_auth'=>'Assign group permissions',
    'distribute_company_auth'=>'Assign enterprise-level permissions',
    'view_group_auth' => 'View Group Permissions',
    'system_default'=>'Default',
    'add_role'=>'Add role',
    'role_tips'=>'1. The first row of each member can be assigned a role with enterprise-level permissions, and a role with intra-group permissions can be assigned to the row where each eagle group is located;<br>2.The system default roles (system administrator, group owner, group administrator, common member) do not support assignment;<br>3.Each role, including the Group owner, is first of all an ordinary member;<br>4.The final permission obtained by an account depends on the sum of all the role permissions granted to the account, including whether the approval process is required.',
    'set_company_role_tips'=>'Note: If the selected role includes "In-Group permissions", it will not be assigned, <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;because the In-Group permissions are only valid in a specific Group.',
    'invalid_approval' => 'Invalid approval, please contact Group Owner',
    'invalid_approval_contact_sys_admin' => 'Invalid approval, please contact System Administrator',
	'invalid_approval_used' => 'Invalid approval, it may have been approved, please refresh and try again or contact Group Owner',
    'set_witness_process' => 'Set up review process',
    'signature_reviewer' => 'Signature reviewer',
    'no_auth'=>'Permission denied',

    'view_company_auth'=>'View Enterprise Permissions',
    'uploaded_image' => 'Uploaded image:',
    'uploaded_file' => 'Uploaded file:',
    'deleted_file' => 'Deleted file:',
    'modified_file_name' => 'Modified file name:',
    'modified_file_content' => 'Modified file content:',
    'not_allow_delete'=>'Not allowed to delete required fields',
    'user_locked' => 'Historical user of this group',
    'manage_template'=>'Template Manage',
    'recycle_template'=>'Recycle Bin',
    'applopval_config_template'=>'Approval flows',
    'settings_template'=>'Settings',
    'temp_list'=>'Template List',
    'transfer'=>'Transfer',
    'by_department'=>'By department',
    'create_or_update'=>"Create/Modify",
    'publish'=>"Publish",
    'template_type'=>'Template Type',
    'normal_full'=>'Normal Full-text Template',
    'normal_function'=>'Normal Method Template',
    'company_full'=>'Enterprise Full-text Template',
    'company_function'=>'Enterprise Method Template',
    'operater_receive_mail'=>'Operators and approvers receive email reminders',

    'action_category' => 'Action Category',
    'remind' => 'Remind',
    'operator1' => 'Operator',
    'template_share'=>'Template share',

    'approver' => 'approver',
    'coapprover' => 'co-approver',
    'processing' => 'processing',
    'approval_route_cancelled' => 'Approval route cancelled',
    'scope_for_template_approval_manage'=>'Scope',
    'operate_type_for_template_approval_manage'=>'Approval Actions',
    'template_type_for_template_approval_manage'=>'Approval Templates',
    'approval_node_for_template_approval_manage'=>'Approval',
    'approval_start'=>'Start',
    'approval_end'=>'End',

    'instrument_number_not_exist' => 'Instrument number does not exist',
    'instrument_number_empty_error' => 'Instrument number should not be empty',
    'test_succeed' => 'Test succeed',
    'test_failed' => 'Test failed',
    'upload_file_failed' => 'File upload failed',
    'upload_success' => 'Upload success',
    'instrument_occupied_error' => 'Instrument ID occupied, please contact administrator',
    'no_bind_instrument' => 'No instrument bind',
    'read_instrument_data_failed' => 'If the data reading fails, please make sure the configuration of "InDraw-InScada-Value Transfer" function is normal in the computer connected to the instrument. If the configuration is working , the data return may be delayed due to the network, you can refresh this page later to check whether the data has been successfully received.',
    'read_instrument_data_success' => 'Read instrument data succeed',
    'select_instrument' => 'Select Instrument',
    'view_detail' => 'View Detail',
    'numerical_instrument' => 'Numerical',
    'file_instrument' => 'File',
    'id' => 'Order',
    'instrument_name_1' => 'Instrument Name',
    'instrument_id' => 'Instrument ID',
    'generation_time' => 'Generated Time',
    'no_group_history_member' => 'Hide history users of Group',
    'show_group_history_member' => 'Show history users of Group',
    'instrument_type_incorrect' => 'Wrong Category',
    'permissions' => 'Permissions',
    'my_permissions' => 'My Permissions',
    'instrument_id_empty_tip' => 'Instrument ID is not exist',
    'file_instrument_server_error_tip' => 'File instrument server address error, cannot establish connection.',

    'account_closed' =>'Account closed',
    'user_not_found' =>'User not found',
    'group_not_found' =>'Group not found',
    'dep_not_found' =>'Dep not found',
    'forbidden_own_exp' => "Prohibition of reviewing one's own experiments",
    'not_activate' => 'User has not activated',
    'please_input_reason' => 'Please input reason',
    'status_change_need_approval' => 'Status change need approval',
    'book_removed_icon_tip' => 'Has been moved to ths Recycle Bin by the user, but does not affect viewing through sharing',
    'group_removed_book_tip' => 'The Group the Notebook belonged to has been dismissed',
    'view_status_change' => 'View status change',
    'view_repair_application' => 'View repair application',

    'picture' => 'Picture',
    'delete_instrument_tip' => 'Are you sure to delete instrument?',

    'annotation_status' => 'Annotations Status',
    'include_annotations' => 'Included annotations',
    'without_annotations' => 'Without annotations',
    'include_incms_reg' => 'Included InCMS Reg',
    'without_incms_reg' => 'Without InCMS Reg',
    'coauthor_status' => 'Co-author Status',
    'include_co_author_in_exp' => 'Include co-author in exp',
    'without_co_author_in_exp' => 'No co-author in exp',
    'upload_picture' => 'Upload Picture',
    'select_valid_fields' => 'Select valid fields',
    'match_instrument_failed' => 'Matching instruments can not exceed the maximum',
    'instrument_edit_tip' => 'You are not in the selected Groups or departments and will not see this instrument after saving. Are you sure you want to save?',

    'project_task' => 'Project/Task',
    'please select project or task' => 'please select project or task',
    'template' => 'Template',
    'notebook_code' => 'Notebook No.',
    'update_time' => 'Update Time',
    'file_tips' => 'Please input file name',
    'experiments_include_comments' => 'Experiments include comments',
    'full text' => 'Full-text',
    'exp title'=>'Title',
    'full text tips' => 'Multiple keywords allowed', //dgh
    'exp title tips'=>'Multiple keywords allowed',
    'keywords tips'=>'From Experiment Keywords Fields',
    'create time'=>'Created time',
    'creator'=>'Creator',
    'time to' => 'To',
    'exp status'=>'Status',
    'only my exp'=>'Only in my experiments',
    'high search'=>'Advanced Search',
    'structure' => 'Structure',
    'search_include_recycle' => 'Include experiments in recycle',
    'detect_chinese' => 'Retrieves Chinese characters',
    'search_history' => 'Search history only',
    'belong_group' => 'Group',
    'trace_file' => 'file',
    'define_item' => 'Custom item',

    'reservationRuleSettings'=>'Appointment Rule Settings',
    'booking_rules_config' => 'Appointment Rule Settings',
    'available_slots' => 'Available Appointment Time',
    'max_advance_day' => 'Maximum Advance Booking Time',
    'min_advance' => 'Minimum Advance Booking Time',
    'max_booking_duration' => 'Maximum Duration per Appointment',
    'unlimited'=>'Unlimited',


    'indraw_material_data' => 'Reaction materials data',
    'indraw_material_data_export_access_tip' => 'The function of exporting details of reaction data has not been enabled yet, please contact Integle service staff',
    'file_exporting'=>'File is exporting...',
    'no_project' => 'No project',
    'structural_formula' => 'Structural formula',
    'approval_route' => 'Approval process',
    'revert_exp_tip' => 'Revert to this version',
    'revert_exp_err_1' => 'Version recovery is not supported as {0} is signed and locked',
    'book_code_exp_page' => 'Page Number',
    'exp_result_conclusion' => 'Result-Self-rating',
    'exp_result_star' => 'Result-Star',
    'exp_result_yield' => 'Result-Yield',
    'exp_result_cmpd_no' => 'Result-Cmpd no.',
    'exp_result_sample_id' => 'Result-Sample ID',
    'exp_result_barcode' => 'Result-Barcode',
    'save_exp_err_1' => 'The experiment is also opened in other tabs of the browser, you can edit after closing the prompt box',

    'submitted_for_review' => 'Submitted for review',
    'instrument_status_change' => 'Instrument status change',
    'exp_under_approval' => 'The experiment is under approval and does not support this operation',
    'duplic_exp_err_1' => 'Component adjustment is not supported as the selected notebook has no permission to change the template',

    'in_group_name' => 'Group Name',
    'in_group_master_name' => 'Group Master',
    'indirect_diagram'=>'indirect relationship diagram',
    'direct_diagram'=>'direct relationship diagram',
    'ins_status_to_normal'=>'"Status" of the instrument changes to "Normal"',
    'ins_status_to_repair' => '"Status" of the instrument changes to "Repairing"',
    'repair' => 'Repair',
    'Maintenance' => 'Maintenance',
    'wo_approval_status_tip'=>'The latest approval status of the Work Order',
    'refuse_reason'=>'Rejection reason:{0}',


    'ins_remind_setting_content_1' => 'Group {0}: Remind by period, once every {1} days, start time: {2}; Remind users: {3};Content: {4}.<br>',
    'ins_remind_setting_content_2' => 'Group {0}: Remind by date, {1}; Remind users: {2}; Content: {3}.<br>',
    'cancel_reminder_settings' => 'Cancel reminder settings',

    'numerical_instrument1'=>'other numerical',
    'numerical_instrument2' => 'balance',
    'numerical_instrument3'=>'pH meter',
    'numerical_instrument4'=>'pipette',
    'instrument_sign'=>'sign',
    'batch_sign'=>'Batch sign',
    'instrument_sign_reason'=>'签字原因',
    'instrument_receive'=>"hide another's data",
    'inscada_show_hide'=>'show/hide',
    'inscada_field_config'=>'Content for insert',
    'recent'=>'Recent',
    'auto_insert_tip1'=>'Auto mode is unavailable for files',
    'inscada_popup_input_tip1'=>'Hold down the shift key to quickly check off multiple pieces of data',
    'batch_insert_inscada'=>'Batch insert',
    'Horizontal_insert_inscada'=>'Horizontal',
    'Vertical_insert_inscada'=>'Vertical',
    'recipient'=>'Recipient',

    //结构化数据  2023/9/7
    'my_data_views' => 'My Data Views',
    'data_views_share_to_me' => 'Data Views Shared to Me',
    'my_reports' => 'My Reports',
    'my_reports_share_to_me' => 'Reports Shared with Me',
    'my_report_templates' => 'My Report Templates',
    'report' => 'Report',
    'report_name' => 'Report name',
    'report_template' => 'Report template',
    'report_exp_num' => 'Expand Page Numbers',
    'cycle_report' => 'Recycle Bin Reports',
    'cycle_report_template' => 'Recycle Bin Report Templates',
    'report_history' => 'View report history',
    'report_template_history' => 'View report template history',
    'create_report' => ' created ',
    'modify_report' => ' modified ',
    'look_report' => ' viewed ',
    'copy_report' => ' copied ',
    'download_report' => ' downloaded ',
    'shared_report' => ' shared ',
    'cancel_sharing_report' => ' cancel shared ',
    'delete_report' => ' deleted ',
    'restore_report' => ' restored ',
    'create_new_report_template' => 'Create',
    'to' => ' to',
    'data_view' => 'Data View',
    'sub_data_view' => 'Sub-Data View',
    'cancel_reason' => 'cancellation reason',
    'save_editor_comment_err' => 'The experiment has been updated. You need to close the Alert box before making annotations',


//    -----AI tips-----
    'tip-title' => 'Notice',
    'tip-line1' => 'The AI feature has not been enabled yet. You may choose:',
    'tip-line2' => 'a. Use Integle AI Service (Recommended)',
    'tip-line3' => 'Features: Integrated with professional knowledge graphs, optimized for patent drafting, academic paper writing, and more.',
    'tip-line4' => 'b. Connect Third-Party Commercial Models or Your Locally Deployed Models',
    'tip-line5' => 'Supported Models: DeepSeek, Qwen, etc.',
    'tip-line6' => "To enable this feature, please contact Integle's business representatives.",

    'no_wms_app_for_stock_in'=>'The InWMS application has not been enabled yet, so you cannot submit a inbound request for the time being.',

// 仪器创建
    'errorAlready' => 'This time slot is already booked',
    'errorMax1' => 'This instrument is only available for',
    'errorMax2' => 'bookings per day',
    'errorMin1' => 'This instrument requires at least',
    'errorMin2' => 'advance booking',
    'errorMaxDuration' => 'The maximum booking duration for this instrument is',
    'errorAvailable' => 'Please modify within available booking hours',
    'errorOver' => 'Please select a time after the current time',
    'timeError' => 'TIME ERROR',
    'instrumentError' => 'The current instrument is not available for borrowing.'

];

