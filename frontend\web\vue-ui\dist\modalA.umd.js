(function(e,o){typeof exports=="object"&&typeof module<"u"?module.exports=o(require("vue")):typeof define=="function"&&define.amd?define(["vue"],o):(e=typeof globalThis<"u"?globalThis:e||self,e.<PERSON>=o(e.Vue))})(this,function(e){"use strict";const o=(n,t)=>{const s=n.__vccOpts||n;for(const[d,c]of t)s[d]=c;return s},i={methods:{close(){this.$emit("close")}}},l={class:"modal-a"};function f(n,t,s,d,c,r){return e.openBlock(),e.createElementBlock("div",l,[t[1]||(t[1]=e.createElementVNode("h2",null,"弹窗 A",-1)),e.createElementVNode("button",{onClick:t[0]||(t[0]=(...u)=>r.close&&r.close(...u))},"关闭")])}return o(i,[["render",f],["__scopeId","data-v-b02bbf40"]])});
