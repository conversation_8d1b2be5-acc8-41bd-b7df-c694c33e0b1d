<?php

use frontend\services\CompanyServer;

$userInfo = Yii::$app->session->get('userinfo');
$recycleSetting = (new CompanyServer())->getCompanySetting([
    'ALLOW_CREATER_MOVE_BOOKK_TO_RECYCLE',
]);
$canMoveToRecycle = $recycleSetting['data']['ALLOW_CREATER_MOVE_BOOKK_TO_RECYCLE']['value'];
?>
<table class="user_module_list">
    <tr class="tr_bg">
        <td class="w50"><input type="checkbox" id="template_checkbox_all" /><label for="template_checkbox_all"></label></td>
        <td><?php echo Yii::t('temp', 'name')?></td>
        <td><?php echo Yii::t('temp', 'temp_descript')?></td>
        <!-- 只在发布管控模式下显示版本号列 -->
        <?php if (isset($templateEffectMode) && $templateEffectMode == 2): ?>
        <td><?php echo Yii::t('temp', 'version')?></td>
        <?php endif; ?>        
        <td><?php echo Yii::t('temp', 'temp_type')?></td>
        <td><?php echo Yii::t('temp', 'temp_from')?></td>
        <td><?php echo Yii::t('base', 'create_time')?></td>
        <td><?php echo Yii::t('base', 'update_time')?></td>
        <td><?php echo Yii::t('base', 'action')?></td>
    </tr>
    <?php if(!empty($tempLateData)){?>
        <?php foreach($tempLateData as $val){ ?>
            <tr data-id="<?php echo $val['id']?>">
                <td>
                    <?php
                    if ($val['is_system'] != 1) {
                        echo '<label><input type="checkbox" class="template-checkbox module_check" value="' . $val['id'] . '" /></label>';
                    }
                    ?>
                </td>
                <td><?php echo $val['name']; ?></td>
                <td class="truncate" title="<?php echo $val['descript']; ?>"><?php echo $val['descript']; ?></td>

                <!-- 版本号，发布管控模式才显示 -->
                <?php if (isset($templateEffectMode) && $templateEffectMode == 2): ?>
                <td><?php echo $val['active_version']; ?></td>
                <?php endif; ?>                
                    

                <!--模板类型-->
                <td>
                    <?php

                    $templateSubtypeList = isset($templateSubtypeList) && is_array($templateSubtypeList) ? 
                        array_column($templateSubtypeList, null, 'id') : [];

                    
                    $subtypeName = !isset($templateSubtypeList[$val['subtype_id']])||
                    $templateSubtypeList[$val['subtype_id']]['id'] == 0 ? Yii::t('temp', 'uncategorized') : $templateSubtypeList[$val['subtype_id']]['name'] ;
                    $subtypeName = $subtypeName == Yii::t('temp', 'uncategorized') ? '' : ' - ' . $subtypeName;
                    //如果子类型名称为空，则不显示
                    if(1 == $val['is_system'] || $val['type']==3 || $val['email'] == '<EMAIL>'){
                        $templateType = Yii::t('temp', 'normal_temp');
                    }else if(2 == $val['type']){
                        $templateType = Yii::t('temp', 'intext_template') . $subtypeName;
                    }else if( 4 == $val['type']){
                        $templateType = Yii::t('temp', 'intable_template'). $subtypeName;
                    }else if(1 == $val['type']){
                        $templateType = Yii::t('temp', 'all_temp'). $subtypeName;
                    }
                    
                    // 限制显示长度，超过则显示省略号
                    $maxLength = 20; // 最大显示字符数
                    $displayType = mb_strlen($templateType) > $maxLength ? 
                        mb_substr($templateType, 0, $maxLength) . '...' : 
                        $templateType;
                    
                    echo '<span class="truncate" title="' . htmlspecialchars($templateType) . '">' . $displayType . '</span>';
                    ?>
                </td>

                <!--模板来源-->
                <td data-uid="<?=$val['user_id'] //模板所有人id?>">
<!--                    --><?php //if (1 == $val['is_system']) {
//                        echo Yii::t('temp', 'integle_share');
//                    } else {
//                        if ($val['is_company'] === '1') {
//                            echo Yii::t('base', 'company_temp') . '<br/>(' . $userList[$val['user_id']]['real_name'] . ')';
//                        }
//                        else if (!empty($val['previous_id'])) {//added by xieyuxiang 2022.8.30 增加转让来源
//                            echo Yii::t('temp','transfer'). '<br/>(' . $userList[$val['previous_id']]['real_name'] . ')';
//                        }
//                        else if (isset($val['user_id']) && isset($this->params['curr_user_id']) && ($this->params['curr_user_id'] == $val['user_id'])) {
//                            echo Yii::t('temp', 'own_create');
//                        }
//                        else{
//                            if ($val['user_id'] > 0)
//                            echo Yii::t('base', 'shared') . '<br/>(' . $userList[$val['user_id']]['real_name'] . ')';
//                        }
//                    } ?>
<!--                    changed by xieyuxiang 2022.8.31 modulelsit_page_new更换为下面的代码，为了统一这里也更换，如有bug可换回上面的代码-->
                    <?php
                    $userShowName = isset($userList[$val['user_id']]) ? $userList[$val['user_id']]['real_name'] : '';
                    if (1 == $val['is_system'] || empty($val['user_id'])) {
                        echo Yii::t('temp', 'integle_share');
                    } else {
                        if ($val['is_company'] === '1') {
                            echo Yii::t('base', 'company_temp') . '<br/>(' . $userShowName . ')';
                        } else if (!empty($val['previous_id']) && isset($val['tfrom']) && $val['tfrom'] == 4) {//added by xieyuxiang 2022.8.22 增加转让来源
                            echo Yii::t('temp', 'transfer') . '<br/>(' . $userShowName . ')'; // $val['previous_id']
                        } else {
                            echo $userShowName;
                        }
                    } ?>
                </td>
                <td><?php echo $val['create_time'];?> <?php echo $val['edit_user_id'];?></td>
                <td><?php echo $val['update_time'];?></td>
                <!--操作-->
                <td>
                    <!--编辑/查看-->

                    <a href="javascript:void(0)" class="opts_btn table-ico look-ico" temp-type="<?php echo $val['type'];?>" temp-id="<?php echo $val['id'];?>" data-type="show" title="<?php echo Yii::t('base', 'view_or_edit');?>"></a>


                    <?php if('share' != $type && 1 != $val['is_system'] ){ ?>
                        <?php if (
                            (\Yii::$app->view->params['curr_user_id'] == $val['user_id'] || !empty($edit_template_manage)) &&
                            $val['email']!='<EMAIL>' &&
                            \Yii::$app->view->params['edit_template_manage']==1 &&
                            $val['step'] == 3   /*bug#30918，只有已经审核通过的模板可以分享 mod dx*/
                        ) {   //分享 ?>
                            <a href="javascript:void(0)" class="share_module table-ico share-ico" temp-id="<?php echo $val['id'];?>" title="<?php echo Yii::t('base', 'share');?>"></a>
                        <?php }?>

                        <?php if (($val['user_id'] == \Yii::$app->view->params['curr_user_id'] || !empty($edit_template_manage)) && ($canMoveToRecycle == 1) && \Yii::$app->view->params['edit_template_manage']==1 ) { //移入回收站?>
                            <a href="javascript:void(0)" class="del_module_temp table-ico file-ico"
                               temp-id="<?php echo $val['id']; ?>"
                               title="<?php echo Yii::t('base', 'move_recycle'); ?>"></a>
                        <?php } ?>

                         <?php
                        //模板 提交审批 || 撤销审批
                        if((!empty($val['temp_creator_appro']) && \Yii::$app->view->params['edit_template_manage']==1) ||   //模板创建人创建模板需要审批 且 当前管理员有模板管理权限
                            $val['user_id'] == \Yii::$app->view->params['curr_user_id'] //用户自己的模板仍然可以提交审批
                        ) {?>
                            <?php if (in_array($val['step'], [1, 4])) { // 草稿状态可以提交审核 ?>
                                <a href="javascript:void(0)" class="submit-temp-audit table-ico"  temp-id="<?php echo $val['id'];?>" title="<?php echo Yii::t('temp', 'submit_temp_audit');?>"></a>
                            <?php } else if (in_array($val['step'], [2, 5])) { // 审核状态可以撤销审核 ?>
                                <a href="javascript:void(0)" class="cancel-temp-audit table-ico"  temp-id="<?php echo $val['id'];?>" title="<?php echo Yii::t('temp', 'cancel_temp_audit');?>"></a>
                            <?php }?>
                        <?php }?>
                        <!--过滤掉方法模板-->
                        <?php if ($val['type'] == 1): ?>                            
                            <a href="javascript:void(0)" class="view-temp-history table-ico" temp-id="<?php echo $val['id'];?>" temp-power="<?php echo isset($edit_template_manage) ? $edit_template_manage : 0; ?>" effect-mode="<?php echo isset($templateEffectMode) ? $templateEffectMode : 1; ?>" update-time="<?php echo isset($val['update_time']) ? $val['update_time'] : ''; ?>"><i class="ico history"></i></a>
                            <div id="template-history-dialog" style="display: none; position: absolute;"></div>                            
                           
                        <?php endif; ?>
                    <?php }?>

                    <?php if(1 != $val['is_system'] && $set_conmany_template_write  && $val['email']!='<EMAIL>' && \Yii::$app->view->params['edit_template_manage']==1){ ?>
                        <a style="<?=3==$val['step']?'':'display:none'?>" href="javascript:void(0)" class="set_company_temp table-ico <?php echo $val['is_company']?'company-ico':'cancel-company-ico';?> " temp-id="<?php echo $val['id'];?>" data-val='<?php echo $val['is_company'];?>' title="<?php echo $val['is_company']?Yii::t('base', 'cancel_is_company'):Yii::t('base', 'set_is_company');?>"></a>
                    <?php }?>


                    <!--转让模板 ,不同于我的模板，模板管理可以转让属于我管理的企业的所有模板-->
                    <?php if ( 1 != $val['is_system']&& (isset( $this->params['edit_template_manage']) &&  $this->params['edit_template_manage'] == 1)  && $val['email']!='<EMAIL>' && \Yii::$app->view->params['edit_template_manage']==1) : ?>
                        <a href="javascript:void(0)" class="transfer_module table-ico transfer_ico" data-id="<?=$val['id']?>" data-owner_id="<?=$val['user_id']?>" data-operator_id="<?=$userInfo['id']?>" title=" <?=Yii::t('temp', 'transfer') ?>"></a>
                    <?php endif; ?>
                </td>
            </tr>
        <?php } }?>
</table>

<div
    class="page_box" style="padding-right:60px;"
    max-num="<?= $limit ?>"
    data-limit="<?php echo $limit; ?>"
    data-num="<?php echo !empty($totalCount) ? $totalCount : 0; ?>"
></div>
