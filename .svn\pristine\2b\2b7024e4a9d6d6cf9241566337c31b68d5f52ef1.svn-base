<?php
namespace frontend\controllers;

use frontend\core\CommonServer;
use frontend\interfaces\ChemInterface;
use frontend\interfaces\PMInterface;
use frontend\models\BookModel;
use frontend\models\ChemModel;
use frontend\models\ElnExperimentModel;
use frontend\models\EmptyExpTextModel;
use frontend\models\ExperimentFavoritesModel;
use frontend\models\ExperimentModel;
use frontend\models\HistoryModel;
use frontend\models\Link;
use frontend\models\ReactionDetailsModel;
use frontend\models\RecentlyViewedExperimentModel;
use frontend\models\SubstrateModel;
use frontend\models\DefineTableKeyModel;
use frontend\models\DefineTableValueModel;
use frontend\models\TemplateConfig;
use frontend\models\TemplateHistoryModel;
use frontend\models\TemplateRuleIndraw;
use frontend\models\WmsSyncOutLog;
use frontend\models\CompanySettingModel;
use frontend\services\ApprovalServer;
use frontend\services\CheckServer;
use frontend\services\CollaborationServer;
use frontend\services\CompanyAuthServer;
use frontend\services\GroupSettingServer;
use frontend\services\ModuleServer;
use frontend\services\ShareServer;
use frontend\services\ShareSettingServer;
use frontend\services\CompanyServer;
use frontend\widget\experiment\modules\indraw\MaterialMenuWidget;
use frontend\widget\experiment\modules\indraw\StoiSubs;
use yii;
use frontend\interfaces\CenterInterface;
use frontend\services\ExperimentServer;
use frontend\services\SignServer;
use yii\helpers\ArrayHelper;
use frontend\services\CommentServer;
use frontend\services\BookServer;
use frontend\services\TempleServer;
use frontend\services\HistoryServer;
use frontend\models\TmpPowerModel;

use frontend\models\ExperimentRelayModel;
use frontend\models\EditorModel;

use frontend\models\TemplateRelayModel;

use frontend\models\TemplateModel;
use frontend\models\CompanyCheckUser;
use yii\helpers\Curl;

/**
 *
 * <AUTHOR> @copyright 2016-3-1
 */
class ExperimentController extends MyController {

    public function init() {
        parent::init();
        $view = \Yii::$app->view;
        $view->params['curr_user_id'] = $this->userinfo->id;
    }

    static $category = [
        'my_exp' => 1,
        'coauthor' => 2,
        'share' => 3,
        'inspection' => 4,
        'company' => 5
    ];

    /**
     * Notes: 新建实验
     * Author: zhu huajun
     * Date: 2019/3/15 15:21
     */
    public function actionNewExp() {
        $postData = \Yii::$app->request->post();
        $postData['user_id'] = $this->userinfo->id;

        if (empty($postData['book_id'])) {
            return $this->fail(\Yii::t('book', 'check_book'));
        }

        // 获取记录本信息
        $bookCode = (new ExperimentServer())->addView($postData);
        if (empty($bookCode['status'])) {
            return $this->fail($bookCode['info']);
        }
        $bookCode = $bookCode['data'];

        $expData = [];
        $title = '';
        $keywords = '';
        $moduleData = [];
        $defineItemData = ''; // add by hkk 2019/10/16

        $templateId = empty($postData['module_id']) ? 0 : $postData['module_id'];
        if(!empty($templateId)) {
            // 获取模板生效模式
            $templateEffectMode = (new CompanyServer())->getCompanySetting('TEMPLATE_EFFECT_MODE')['data']['TEMPLATE_EFFECT_MODE']['value'];
            
            if ($templateEffectMode == 2) {
                // 发布管控模式，从生效中的模板版本获取数据
                $historyResult = (new TempleServer())->getEffectiveTemplateVersion($templateId);
                
                if (empty($historyResult['status'])) {
                    return $this->fail($historyResult['info']);
                }
                
                $tempData = $historyResult['data'];
            } else {
                // 即时生效模式，直接获取模板数据
                $tempResult = (new TempleServer())->getTempById($templateId);
                if($tempResult['status'] == 0) {
                    return $this->fail($tempResult['info']);
                }
                $tempData = $tempResult['data'];
            }

            $title = !empty($tempData['base_data']['title']) ? $tempData['base_data']['title'] : '';
            $keywords = !empty($tempData['base_data']['keywords']) ? $tempData['base_data']['keywords'] : '';
            $moduleData = !empty($tempData['module_data_arr']) ? $tempData['module_data_arr'] : [];
            $defineItemData = !empty($tempData['base_data']['define_item']) ? $tempData['base_data']['define_item'] : ''; // ADD BY HKK 2019/10/16
        }

        $time = date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']);

        //bug 1358 新建实验加项目状态判断
        if ($bookCode['project_id']) {
            $isInProAndTaskReturn = (new PMInterface())->isInProAndTask($this->userinfo, $bookCode['project_id'], 0);
            $bookCode['project_id'] = $isInProAndTaskReturn['isInPro'] ? $bookCode['project_id'] : '0';
        }

        $userDetail = (new CenterInterface())->getUserByUserId($this->userinfo->id);
        $createUserName = !empty($userDetail['real_name']) ? $userDetail['real_name'] : '';
        $baseData = [
            'step' => 1,
            'book_id' => $postData['book_id'],
            'exp_page' => $bookCode['exp_page'],
            'exp_code' => $bookCode['exp_all_code'],
            'exp_all_code' => $bookCode['exp_all_code'],
            'book_name' => $bookCode['book_name'],
            'code' => $bookCode['book_code'],
            'project_id' => (isset($postData['project_id']) && !empty($postData['project_id'])) ? $postData['project_id'] : $bookCode['project_id'], // add by hkk 2019/10/10 增加记录本所属项目id -- jiangdm 2022/6/6 从项目管理新建的实验，优先默认来源的项目
            'task_id' => (isset($postData['task_id']) && !empty($postData['task_id'])) ? $postData['task_id'] : null,// 项目管理新建实验，增加任务id jiangdm 2022/6/6
            'title' => $title,
            'keywords' => $keywords,
            'book_create_time' => $bookCode['book_create_time'],
            'group_id' => $bookCode['group_id'],
            'template_id' => $templateId,
            'user_id' => $this->userinfo->id,
            'edit_user_id' => $this->userinfo->id,
            'create_user_name' => $createUserName, // 增加创建人姓名，用于显示在实验头部
            'weather_json' => json_encode($this->getWether()),
            'create_time' => $time,
            'update_time' => $time,
            'action' => 'create',
            'auto_save' => 0,
            'define_item' => $defineItemData, // add by hkk 2019/10/16
        ];

        $expData['base_data'] = $baseData;

        $expData['module_data'] = $moduleData;
        $saveResult = (new ExperimentServer())->saveExperiment($expData);

        // 如果是CMS申领单创建的实验，异步请求添加链接信息 jiangdm
        if ((isset($postData['chemicals']) && !empty($postData['chemicals']))) {
            Curl::sendPostCurl(ELN_URL . '?r=async-task/sync-exp-link-from-cms-apply', [
                'exp_id' => $saveResult['data']['exp_id'],
                'exp_code' => $bookCode['exp_all_code'],
                'user_id' => $this->userinfo->id,
                'chemicals' => $postData['chemicals'],
            ], true);
        }

        // 根据模板ID更新 和  实验ID 更新 temp_relay_id
        if(!empty($templateId)) {
            $result = (new ExperimentServer())->updateExperimentTempRelayId($templateId,$saveResult['data']['exp_id']);
        }

        if(empty($saveResult['status'])){
            return $this->fail($saveResult['info']);
        }

        return $this->success([
            'id' => $saveResult['data']['exp_id']
        ]);
    }

    // 实验数据验证
    private function expDataCheck($data) {
        foreach($data as $value) {
            if(is_array($value)){
                foreach($value as $val){
                    if(isset($val['component_id']) && trim($val['name']) == ''){
                        return $this->fail('模块名称不能为空');
                    }
                }
            } else {
                if(isset($val['component_id']) && trim($val['name']) == ''){
                    return $this->fail('模块名称不能为空');
                }
            }
        }

    }

    /**
     * 保存实验
     *
     * @return \common\controllers\json
     */
    public function actionSaveExperiment() {
        // 内测用途，用于检测文本编辑器模块前端未传递内容，只针对手动保存
        if (0 && empty($_POST['base_data']['auto_save']) && !empty($_POST['module_data'])) {
            if ($_SERVER['REMOTE_ADDR'] === '180.167.101.178' || $_SERVER['REMOTE_ADDR'] === '127.0.0.1') {
                foreach ($_POST['module_data'] as $module) {
                    $comId = $module['info']['component_id'];
                    if (in_array($comId, [
                        \Yii::$app->params['component']['operation'],
                        \Yii::$app->params['component']['discuss'],
                        \Yii::$app->params['component']['lite'],
                        \Yii::$app->params['component']['abstract'],
                        \Yii::$app->params['component']['diy'],
                    ])) {
                        $html = isset($module['data']['html']) ? $module['data']['html'] : '';
                        if (empty($html) || strlen($html) < 100) {
                            $errorMsg = '内部测试: 检测到' . $module['info']['name'] . '模块内容为空或太少，保存失败，请联系技术人员及时查看.';
                            return $this->fail($errorMsg);
                        }
                    }
                }
            }
        }

        $expData = \Yii::$app->request->post();

        // 如果是有tempData 这个才走解压，用于当客户没有更新js的情况下，仍可以用老方法保存实验
        if (isset($expData['tempData'])) {
            $comStr = base64_decode($expData['tempData']); //获取加密后的base64 并解析
            $expData = gzdecode($comStr);//解压
            $expData = json_decode($expData, true);//json转数组

            // purify会导致文本编辑器模块的评论内容被过滤(bug#34918)，先取消掉
            // $expData = $this->purify($expData);
        }

        $logData = $expData;
        foreach ($logData['module_data'] as $key => $mData) {
            if ($mData['info']['component_id'] == \Yii::$app->params['component']['xsheet']) {
                if (strlen($mData['data']['data']) > 10000) {
                    $logData['module_data'][$key]['data'] = '因为数据太长未进行日志记录';
                }
            }
        }

        $logData = json_encode($logData);
        $logData = preg_replace('/<\/?[^>]+>/', '', $logData); // 移除HTML标签，减少体积
        // 因为保存实验的时候是数据压缩了之后才传过来，故 对post 做编码 还是没，所以单独对标题和关键字做了处理

        // $expData = ArrayHelper::HtmlEncode($expData);
        // print_r($expData);exit;
        $expData['base_data']['title'] = \yii\helpers\Html::encode($expData['base_data']['title']);
        $expData['base_data']['keywords'] = \yii\helpers\Html::encode($expData['base_data']['keywords']);
        \Yii::info('保存实验[' . $expData['base_data']['experiment_id'] . ']POST过来的数据' . $logData, 'ExperimentController');
        $saveResult = (new ExperimentServer())->saveExperiment($expData);
        \Yii::info('保存实验[' . $expData['base_data']['experiment_id'] . ']结果' . json_encode($saveResult), 'ExperimentController');

        if (empty($saveResult['status'])) {
            if (!empty($saveResult['data']['pop_dialog'])) {
                $dialogHtml = $this->renderAjax('/popup/save_exp', [
                    'setting' => [
                        'submit_reason_required' => 1
                    ]
                ]);
                $saveResult['data']['dialog_html'] = $dialogHtml;
            }
            return $this->fail($saveResult['info'], 'JSON', $saveResult['data']);
        }
        return $this->success($saveResult['data']);
    }


    /**
     * 存为模板
     *实验存为模板
     * @return \common\controllers\json
     */
    public function actionSaveAsTemp() {
        $postData = \Yii::$app->request->post();

        $postData = ModuleServer::decodeModuleData($postData);

        $saveResult = (new TempleServer())->saveTemp($postData, 'add');

        if (empty($saveResult['status'])) {
            return $this->fail($saveResult['info']);
        }

        $this->_addHistory($saveResult['data']['temp_id'],TemplateHistoryModel::TYPE_SAVE);

        return $this->success($saveResult['data']);
    }

    /**
     * Notes: 实验重开弹窗
     *
     * Author: zhu huajun
     * Date: 2019/11/1 11:30
     * @return \common\controllers\json
     */
    public function actionReopenView() {

        // 实验ID
        $expId = \Yii::$app->request->get('exp_id');

        // 获取实验所属鹰群
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        $groupId = $expBaseData['group_id'];

        // 获取鹰群的重开设置
        $groupSettingRes  = (new GroupSettingServer())->getGeneralSetting($groupId);
        $groupSetting = $groupSettingRes['data'];
        $approvalSetting = $groupSetting['approval_setting'];
        $approvalSetting = json_decode($approvalSetting, true);
        $reopenSetting = !empty($approvalSetting['reopen']) ? $approvalSetting['reopen'] : [];
        // 不允许重开
        if(!empty($reopenSetting['un_allow_edit'])) {
            return $this->fail(Yii::t('base', 'no_modification_after_witness'));
        }
        // 重开审批人
        $approverNames = [];
        if(!empty($reopenSetting['approval_nodes'])) {
            $approvalRouteRes = (new ApprovalServer())->getApprovalRoute($reopenSetting['approval_nodes'], [], $groupId);
            if ($approvalRouteRes['status'] != 1) {
                return $this->fail($approvalRouteRes['info'], 'JSON', ['type' => 'popContent']);
            }
            $approverNames = $approvalRouteRes['data']['approver_names'];
        }
        $file = $this->renderAjax('/popup/reopen', [
            'approverNames' => $approverNames
        ]);
        return $this->success([
            'html' => $file
        ]);
    }

    /**
     * Notes: 提交实验重开
     * Author: zhu huajun
     * Date: 2019/11/1 11:31
     * @return \common\controllers\json
     */
    public function actionReopen() {
        $postData = \Yii::$app->request->post();

        if (empty($postData['password'])) {
            return $this->fail(\Yii::t('sign', 'pass_must'));
        }
        $checkPwd = (new CenterInterface())->checkPwd($this->userinfo->id, $postData['password'], $this->userinfo->name);
        if (!$checkPwd) {
            return $this->fail(\Yii::t('sign', 'pass_error'));
        }

        $res = (new ExperimentServer())->reopenExp($postData['exp_id'], $this->userinfo->id, $postData['comment']);
        $resData = @getVar($res['data'], []);
        if (empty($res['status'])) {
            return $this->fail($res['info'], 'JSON',['type' => @getVar($resData['type'], '')]);
        }
        return $this->success($res);
    }

    /**
     * Notes: 撤销重开
     * Author: zhu huajun
     * Date: 2019/3/7 16:51
     * @return \common\controllers\json
     */
    public function actionCancelReopen() {
        $expId = \Yii::$app->request->post('exp_id');
        if(empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        $result = (new ExperimentServer())->cancelReopen($expId);
        if($result['status'] != 1) {
            return $this->fail($result['info']);
        }
        return $this->success([]);
    }

    /**
     * Notes: 根据实验页码获取实验id
     * Author: szq
     * Date: 2021/3/1 8:54
     * @return \common\controllers\json
     */
    public function actionGetExpIdByPage() {
        $expPage = \Yii::$app->request->post('page', '');
        $result = (new ExperimentServer())->getExpIdByExpNum($expPage);
        return empty($result['data'])
            ? $this->fail(\Yii::t('comment', 'no_exp'))
            : $this->success(['expId' => $result['data']]);
    }
    /**
     * Notes: 模糊搜索根据实验页码获取实验信息
     * Author: zsm
     * Date: 2025/5/21 14:16
     * @return \common\controllers\json
     */
    public function actionGetExpPageByExpPage() {
        $expPage = \Yii::$app->request->post('page', '');
        $result = (new ExperimentServer())->getExpPageByExpPage($expPage);
        return $this->success(['exp' => $result['data']]);
    }
    /**
     * Notes: 新的查看实验接口,代替actionGetExp
     * Author: zhu huajun
     * Date: 2019/2/21 16:02
     */
    public function actionViewExp() {
        $expId = \Yii::$app->request->post('id', 0);
        $expServer = new ExperimentServer();

        // 如果不是数字 则根据实验编号查找实验
        if (!is_numeric($expId)) {
            $result = $expServer->getExpIdByExpNum($expId);
            if (empty($result['data'])) {
                return $this->fail(\Yii::t('comment', 'no_exp'));
            } else {
                $expId = $result['data'];
            }
        }
        if (empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        // 获取实验数据
        $expResult = $expServer->getExpById($expId);

        if($expResult['status'] == 0) {
            return $this->fail($expResult['info']);
        }
        $expData = $expResult['data'];
        $expBaseData = $expData['base_data'];

        if($expData['base_data']['group_id']){
            $group_setting_require = (new GroupSettingServer())->getRequireSetting($expData['base_data']['group_id']);
            $require_setting = $group_setting_require['data'];
            if($require_setting){
                $expData['base_data']['require_title'] = $require_setting->title?$require_setting->title:0;
                $expData['base_data']['require_keyword'] = $require_setting->keyword?$require_setting->keyword:0;
                $expData['base_data']['require_project'] = $require_setting->project?$require_setting->project:0;
                $expData['base_data']['require_task'] = $require_setting->task?$require_setting->task:0;
            }else
            {
                $expData['base_data']['require_title']=0;
                $expData['base_data']['require_keyword']=0;
                $expData['base_data']['require_project']=0;
                $expData['base_data']['require_task']=0;

            }
        }

        //根据模板ID获取基础数据规则
        if($expBaseData['template_id']>0){
            $templateConfig = new TemplateConfig();
            $base_data_rule = $templateConfig->getRequireData($expBaseData['template_id'], 0);
            if($expData['base_data']['require_title']!=1){
                $expData['base_data']['require_title']=isset($base_data_rule['title'])?$base_data_rule['title']:0;
            }
            if($expData['base_data']['require_keyword']!=1){
                $expData['base_data']['require_keyword']=isset($base_data_rule['keyword'])?$base_data_rule['keyword']:0;
            }
            //print_r($expData);exit;
            foreach($expData['module_data_arr'] as $key=>$value)
            {
                //模块验证(摘要)
                //模块验证(Indrwa)
                if($value['info']['component_id'] == 1)
                {
                    //$relayInfo = (new ExperimentRelayModel())->getRelayById($value['info']['id']);

                    $relayInfo['temp_relay_id']=isset($value['info']['temp_relay_id'])?$value['info']['temp_relay_id']:0;

                    $templateRuleIndraw = TemplateRuleIndraw::findOne(['relay_id' => $relayInfo['temp_relay_id']]);
                    $indrawRequiredFields = $templateRuleIndraw ? $templateRuleIndraw->getAttributes(['reactant', 'solvent', 'condition', 'product', 'details']) :
                        ['reactant' => null, 'solvent' => null, 'condition' => null, 'product' => null, 'details' => null];
                    foreach ($indrawRequiredFields as $name=>$col) {
                        $colData = json_decode($col, true);
                        $fieldList = !empty($colData) && is_array($colData) ? $colData : [];
                        foreach ($fieldList as $field) {$expData['module_data_arr'][$key]['info'][$name][$field] = 1;
                        }
                    }
                }

                //模块验证(摘要)
                if(in_array($value['info']['component_id'],[2,3,4,11,12,7,8]) )
                {
                    //$relayInfo = (new ExperimentRelayModel())->getRelayById($value['info']['id']);
                    $relayInfo['temp_relay_id']=isset($value['info']['temp_relay_id'])?$value['info']['temp_relay_id']:0;
                    $result = [];
                    if($relayInfo['temp_relay_id'] > 0) {
                        $result = (new TemplateRelayModel)->relayDataByRelayId(isset($relayInfo['temp_relay_id'])?$relayInfo['temp_relay_id']:0);
                        $result['is_require'] = isset($templateConfig->getRequireData($expBaseData['template_id'], $relayInfo['temp_relay_id'])['is_require'])
                            ? $templateConfig->getRequireData($expBaseData['template_id'], $relayInfo['temp_relay_id'])['is_require'] : 0;
                    }
                    $expData['module_data_arr'][$key]['info']['is_require']=isset($result['is_require'])?$result['is_require']:0;
                    //$empty_str=array_merge($empty_str,$result);
                }
                //模块验证(自定义表格)
                if(in_array($value['info']['component_id'],[13]) )
                {
                    //$relayInfo = (new ExperimentRelayModel())->getRelayById($value['info']['id']);
                    $relayInfo['temp_relay_id']=isset($value['info']['temp_relay_id'])?$value['info']['temp_relay_id']:0;

                    if($relayInfo['temp_relay_id'] > 0) {
                        $define = $templateConfig->getRequireData($expBaseData['template_id'], $relayInfo['temp_relay_id']);
                    }
                    $expData['module_data_arr'][$key]['info']['define_field1']=isset($define['define_field1'])?$define['define_field1']:0;
                    $expData['module_data_arr'][$key]['info']['define_field2']=isset($define['define_field2'])?$define['define_field2']:0;
                    $expData['module_data_arr'][$key]['info']['define_field3']=isset($define['define_field3'])?$define['define_field3']:0;
                    $expData['module_data_arr'][$key]['info']['define_field4']=isset($define['define_field4'])?$define['define_field4']:0;
                    $expData['module_data_arr'][$key]['info']['define_field5']=isset($define['define_field5'])?$define['define_field5']:0;
                    $expData['module_data_arr'][$key]['info']['define_field6']=isset($define['define_field6'])?$define['define_field6']:0;
                    $expData['module_data_arr'][$key]['info']['define_field7']=isset($define['define_field7'])?$define['define_field7']:0;
                    $expData['module_data_arr'][$key]['info']['define_field8']=isset($define['define_field8'])?$define['define_field8']:0;
                    $expData['module_data_arr'][$key]['info']['define_field9']=isset($define['define_field9'])?$define['define_field9']:0;
                    $expData['module_data_arr'][$key]['info']['define_field10']=isset($define['define_field10'])?$define['define_field10']:0;
                    $expData['module_data_arr'][$key]['info']['define_field11']=isset($define['define_field11'])?$define['define_field11']:0;
                    $expData['module_data_arr'][$key]['info']['define_field12']=isset($define['define_field12'])?$define['define_field12']:0;
                    $expData['module_data_arr'][$key]['info']['define_field13']=isset($define['define_field13'])?$define['define_field13']:0;
                    $expData['module_data_arr'][$key]['info']['define_field14']=isset($define['define_field14'])?$define['define_field14']:0;
                    $expData['module_data_arr'][$key]['info']['define_field15']=isset($define['define_field15'])?$define['define_field15']:0;
                    $expData['module_data_arr'][$key]['info']['define_field16']=isset($define['define_field16'])?$define['define_field16']:0;
                    $expData['module_data_arr'][$key]['info']['define_field17']=isset($define['define_field17'])?$define['define_field17']:0;
                    $expData['module_data_arr'][$key]['info']['define_field18']=isset($define['define_field18'])?$define['define_field18']:0;
                    $expData['module_data_arr'][$key]['info']['define_field19']=isset($define['define_field19'])?$define['define_field19']:0;
                    $expData['module_data_arr'][$key]['info']['define_field20']=isset($define['define_field20'])?$define['define_field20']:0;
                    $expData['module_data_arr'][$key]['info']['define_field21']=isset($define['define_field21'])?$define['define_field21']:0;

                }

                if(in_array($value['info']['component_id'],[16]) )
                {
                    //$relayInfo = (new ExperimentRelayModel())->getRelayById($value['info']['id']);
                    $relayInfo['temp_relay_id']=isset($value['info']['temp_relay_id'])?$value['info']['temp_relay_id']:0;
                    if($relayInfo['temp_relay_id'] > 0) {
                        $tlc = $templateConfig->getRequireData($expBaseData['template_id'], $relayInfo['temp_relay_id']);
                    }
                    $expData['module_data_arr'][$key]['info']['tlc_eluent']=isset($tlc['tlc_eluent'])?$tlc['tlc_eluent']:0;
                    $expData['module_data_arr'][$key]['info']['tlc_ratio']=isset($tlc['tlc_ratio'])?$tlc['tlc_ratio']:0;
                    $expData['module_data_arr'][$key]['info']['tlc_cdr']=isset($tlc['tlc_cdr'])?$tlc['tlc_cdr']:0;
                }
            }
        }

        $userDetail = (new CenterInterface())->getUserByUserId($expBaseData['user_id']);
        $expData['base_data']['create_user_name'] = !empty($userDetail['real_name']) ? $userDetail['real_name'] : '';

        // 获取模板权限
        $groupOtherSetting = CompanyAuthServer::getOtherSetting($this->userinfo->id, $expBaseData['group_id']);
        $tmpPower = empty($groupOtherSetting['not_allow_create_full_temp']) ? 1 : 0;

        // 判断是否可以代办
        $allowProxy = $expServer->checkProxy($expBaseData['group_id'], $this->userinfo->id, $expBaseData['user_id']);

        // 是否可以编辑((创建人员 | 代办) & 草稿状态 & 非预审中)
        $edit = 2;
        if (
            ($expBaseData['user_id'] == $this->userinfo->id || $allowProxy)
            && ($expBaseData['step'] == 1) && ($expBaseData['pretrial_status'] != 1)
        ) {
            $edit = 1;

            // 判断实验是否有模块被其他人编辑
            $editingUserIds = $expServer->getEditingUsers($expId);
            $editingUserIds = array_filter($editingUserIds, function ($userId) {
                return $userId != $this->userinfo->id;
            });

            if (!empty($editingUserIds)) {
                $edit = 2;
                $editingUsers = (new CenterInterface())->userDetailsByUserIds($editingUserIds);
                $userNames = array_column($editingUsers, 'nick_name');
                $tipInfo = join(',', $userNames) . \Yii::t('exp', 'experiment_is_editing_by_others');
            } else {
                ExperimentModel::updateAll([
                    'editing_user' => $this->userinfo->id
                ], [
                    'id' => $expId
                ]);
                ExperimentRelayModel::updateAll([
                    'editing_user' => $this->userinfo->id
                ], [
                    'experiment_id' => $expId,
                    'status' => 1
                ]);
            }
        }
        \Yii::$app->view->params['edit'] = $edit;
        \Yii::$app->view->params['exp_id'] = $expId;

        $projectList = []; // 用户参与的项目列表
        $currProjectTaskList = []; // 当前项目下用户参与的任务列表
        $currProjectInfo = []; // 当前项目信息
        $currTaskInfo = []; // 当前任务信息
        $pmInterface = new PMInterface();
        $currentTime = date('Y-m-d H:i:s');
        if ($edit == 1) {
            $projectList = $pmInterface->getUserProjects($this->userinfo, ['only_active' => 0]);
            $projectList = ArrayHelper::index($projectList, 'id');
            if (!empty($expBaseData['project_id']) && isset($projectList[$expBaseData['project_id']])) {
                $currProjectTaskList = $pmInterface->getProjectUserTasks($expBaseData['project_id'], $this->userinfo);
            }
            if (!empty($expBaseData['task_id'])) {
                $visibleTaskIds = array_column($currProjectTaskList, 'id');
                if (!in_array($expBaseData['task_id'], $visibleTaskIds)) { // 若设置的任务并非进行中则加进列表
                    $taskInfo = $pmInterface->getProjectAndTaskById(0,  $expBaseData['task_id']);
                    if (!empty($taskInfo['task'])) {
                        $currProjectTaskList []= $taskInfo['task'];
                    }
                }
            }
            // 更新最后打开时间
            $expServer->updateLastTabOpenTime($expId, $currentTime);
        } else {
            $projectTaskInfo = $pmInterface->getProjectAndTaskById($expBaseData['project_id'], $expBaseData['task_id']);
            if (!empty($projectTaskInfo['project'])) {
                $currProjectInfo = $projectTaskInfo['project'];
            }
            if (!empty($projectTaskInfo['task'])) {
                $currTaskInfo = $projectTaskInfo['task'];
            }
        }
        $expData['last_tab_open_time'] = $currentTime;

        // 判断实验是否需要预审
        $needPretrial = (new CheckServer())->isExpNeedPretrial($expId);

        // 获取盐型列表 add by zhj hkk 2019/3/14
        $saltList = $expServer->saltList();
        $expData['saltList'] = $saltList['data'];

        // 获取小数点设置 add by hkk 019/3/19
        $decimalSetting = (new CompanyServer())->getDecimalSetting($this->userinfo->current_company_id);

        // 签字设置, 以实验创建者查找
        $signingSetting = CompanyAuthServer::getSigningSetting($expBaseData['user_id'], $expBaseData['group_id']);

        // 获取用于合著和xSheet协作的人员列表 add by hkk 2020/3/25
        $collaborationUserIds = $expServer -> getCoauthorUserIds($expId,$this->userinfo->id,$this->userinfo->current_company_id);
        // 获取公司所有人员
        $cmpUsers = (new CenterInterface())->getUserListByCompanyId(1, true);
        $cmpUserList = ArrayHelper::index($cmpUsers['list'], 'id');
        $collaborationUserList = [];
        foreach ($collaborationUserIds as $userId) {
            $collaborationUserList[] = $cmpUserList[$userId];
        }
        $currentUserInfo = [
            'user_id' => $this->userinfo->id,
            'email' => $this->userinfo->email,
            'name' => $this->userinfo->name,
            'real_name' => $this->userinfo->real_name,
            'nick_name' => $this->userinfo->nick_name,
            'phone' => $this->userinfo->phone,
        ];

        $pageTools = $expServer->getExpPageTools([
            'id' => $expId,
            'group_id' => $expBaseData['group_id'],
            'user_id' => $expBaseData['user_id'],
            'step' => $expBaseData['step'],
            'pretrial_status' => $expBaseData['pretrial_status'],
            'reopen_status' => $expBaseData['reopen_status'],
            'can_edit' => $edit == 1,
            'signing_setting' => $signingSetting,
            'allow_proxy' => $allowProxy
        ], $this->userinfo->id, $tmpPower);

        //add by wy 2023/4/20 判断是否能复制实验
        $canCopyExp = true;
        $canCopyExp = $detailTraceAuth = (new CompanyServer())->getCompanySetting( 'SHOW_COPY_EXP_BUTTON');
        if(@getVar($detailTraceAuth['data']['SHOW_COPY_EXP_BUTTON']['value']) == 0){
            $canCopyExp = false;
        }
        ///! 向视图传递企业小数点设置
        $expHtml = $this->renderAjax('experiment', [
            'experimentData' => $expData,
            'projectList' => $projectList,
            'currProjectTaskList' => $currProjectTaskList,
            'currProjectInfo' => $currProjectInfo,
            'currTaskInfo' => $currTaskInfo,
            'tool' => 'detail',
            'type' => 'save_exp',
            'edit' => $edit, // add by hkk zhj 2019/3/21
            'tmpPower' => $tmpPower,
            'pageTools' => $pageTools,
            'needPretrial' => $needPretrial,
            'decimalSetting' => $decimalSetting, //获取小数点设置 add by hkk 019/3/19
            'signingSetting' => $signingSetting,
            'allowProxy' => $allowProxy,
            'collaborationUserList' => $collaborationUserList, // add by hkk 2020/3/25
            'currentUserInfo' => $currentUserInfo, // add by hkk 2020/3/25
            'companyUserList' => (new CenterInterface())->getUserListByCompanyId($this->userinfo->current_company_id), // add by hkk 2020/3/25
            'canCopyExp' => $canCopyExp // add by wy 2023/4/20
        ]);
        \Yii::info('打开实验**[' . $expId . ']的expData' . json_encode($expData), 'ViewExp');
        \Yii::info('打开实验**[' . $expId . ']的expHtml' . $expHtml, 'ViewExp');


        // 实验查看后需要异步执行的任务(添加查看日志, 将实验加入最近访问列表)
        Curl::sendPostCurl(ELN_URL . '?r=async-task/after-exp-view', [
            'exp_id' => $expId,
            'params' => [
                'user_id' => $this->userinfo->id,
                'log_ip' => $_SERVER[ 'REMOTE_ADDR' ],
            ]
        ], true);

        return $this->success([
            'contentHtml' => mb_convert_encoding($expHtml, 'UTF-8', 'UTF-8'),
            'expId' => $expId,
            'expUserId' => $expBaseData['user_id'],
            'edit' => 1,
            'tabName' => $expData['base_data']['exp_all_code'],
            'tabTitle' => $expData['base_data']['exp_all_code'] . ' ' . $expData['base_data']['book_name'] . ' ' . $expData['base_data']['title'],
            'tipInfo' => !empty($tipInfo) ? $tipInfo : null,
            'material_menu_config' => json_decode(MaterialMenuWidget::widget()),
        ], 'JSON');
    }

    /**
     * 获取登录用户的模板权限
     *
     * @date: 2018年6月27日 下午4:05:50
     * <AUTHOR>
     * @param unknown $groupId
     * @return number
     */
    private function _tmpPower($groupId){
        $user = TmpPowerModel::find()->where([
            'group_id'=>$groupId,
            'user_id'=>$this->userinfo->id,
            'status'=>1
        ])->one();
        if ( $user ){
            // 没有权限
            return 0;
        }
        // 有权限
        return 1;
    }

    /**
     * 获取当前实验的上一个实验
     *
     * <AUTHOR> @copyright 2016-3-9
     * @param int id 当前实验的id
     * @param int book_id 当前实验的记录本id
     * $postData = ['id'=>5102, 'book_id'=>1262];
     */
    public function actionGetPrevExp() {
        $postData = \Yii::$app->request->post();

        if (empty($postData['id'] || empty($postData['book_id']))) {
            return $this->fail(\Yii::t('exp', 'select_exp_book'));
        }

        $id = $postData['id'];
        $bookId = $postData['book_id'];

        $email = !empty($this->userinfo->email) ? $this->userinfo->email : (!empty($this->userinfo->phone) ? $this->userinfo->phone : '');
        $experimentData = (new ExperimentServer())->getPreExpById($id, $bookId, $this->userinfo->id, $email);

        $expFile = $this->renderAjax('experiment', [
            'experimentData' => $experimentData
        ]);
        return $this->success([
            'expFile' => $expFile
        ]);
    }

    /**
     * 获取当前实验的下一个实验
     *
     * <AUTHOR> @copyright 2016-3-9
     * @param int id 当前实验的id
     * @param int book_id 当前实验的记录本id
     */
    public function actionGetNextExp() {
        $postData = \Yii::$app->request->post();
        if (empty($postData['id'] || empty($postData['book_id']))) {
            return $this->fail(\Yii::t('exp', 'select_exp_book'));
        }

        $id = $postData['id'];
        $bookId = $postData['book_id'];

        $email = !empty($this->userinfo->email) ? $this->userinfo->email : (!empty($this->userinfo->phone) ? $this->userinfo->phone : '');
        $experimentData = (new ExperimentServer())->getNextExpById($id, $bookId, $this->userinfo->id, $email);

        $expFile = $this->renderAjax('experiment', [
            'experimentData' => $experimentData
        ]);
        return $this->success([
            'expFile' => $expFile
        ]);
    }

    /**
     * 获取人名反应实验列表
     *
     * <AUTHOR> <<EMAIL>>
     * @copyright 2016-3-16 13:14:57
     * @param integer $page 页码
     * @param integer $order_by 1 创建时间排序 2 修改时间排序
     * @param integer $order_type 1 倒序 2 正序
     * @return json
     */
    public function actionListPerson() {
        $postData = \Yii::$app->request->post();

        $orderBy = !empty($postData['order_by']) ? $postData['order_by'] : 1;
        $orderType = !empty($postData['order_type']) ? $postData['order_type'] : 1;

        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];
        $searhWord = isset($postData['search_word']) ? $postData['search_word'] : '';

        $experiment = (new ExperimentServer())->listPersonExperiment($page, $limit, $orderBy, $orderType, $searhWord);

        $returnData = FALSE;
        if (!empty($searhWord)) {
            $returnData = TRUE;
        }

        return $this->returnHandle($experiment['experiment_ids'], $experiment['cnt'], 'isPerson', $page, $orderBy, $orderType, $returnData, NULL, NULL, $experiment['cnt']);
    }

    /**
     * 获取标准实验的列表
     *
     * <AUTHOR> @copyright 2016-5-4
     * @return Ambigous <\frontend\controllers\Ambigous, \common\controllers\json>
     * ?r=experiment/list-normal
     */
    public function actionListNormals() {
        $postData = \Yii::$app->request->post();
        $orderBy = !empty($postData['order_by']) ? $postData['order_by'] : 1;
        $orderType = !empty($postData['order_type']) ? $postData['order_type'] : 1;
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $cnt = (1 == $page) ? TRUE : FALSE;

        $experiment = (new ExperimentServer())->listNormalExperiment($page, \Yii::$app->params['default_page_size'], $orderBy, $orderType, $cnt);

        return $this->returnHandle($experiment['experiment_ids'], $experiment['cnt'], 'isPerson', $page, $orderBy, $orderType);
    }

    /**
     * 获取复核实验列表
     *
     * <AUTHOR> <<EMAIL>>
     * @copyright 2016-3-16 16:49:18
     * @method post
     * @param integer $page
     * @return json
     */
    public function actionListReview() {
        $postData = \Yii::$app->request->post();
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $limit = !empty($postData['limit']) ? $postData['limit'] : \Yii::$app->params['default_page_size'];
        $cnt = (1 == $page) ? TRUE : FALSE;

        $experiment = (new ExperimentServer())->listReviewExperiment($this->userinfo->id, $page, $limit, $cnt);

        return $this->returnHandle($experiment['experiment_ids'], $experiment['cnt'], 'review', $page, 2, 1, FALSE, NULL, NULL, $experiment['cnt']);
    }

    /**
     * 获取项目对应下的实验
     * ?r=experiment/list-project-exp
     *
     * <AUTHOR> @copyright 2017-4-13
     *
     * @param int project_id
     * @param int order_by
     * @param int order_type
     * @param int page
     *
     * @return \common\controllers\json
     */
    public function actionListProjectExp(){
        $postData = \Yii::$app->request->post();

        if(empty($postData['project_id'])){
        	return $this->fail(\Yii::t('exp', 'select_project'));
        }

        $orderBy = !empty($postData['order_by']) ? $postData['order_by'] : 1;
        $orderType = !empty($postData['order_type']) ? $postData['order_type'] : 1;
        $page = !empty($postData['page']) ? $postData['page'] : 1;
        $pageNum = \Yii::$app->getRequest()->post('limit', \Yii::$app->params['default_page_size']);

        $cnt = (1 == $page) ? TRUE : FALSE;

        $experiment = (new ExperimentServer())->listProjectExp($this->userinfo->id, $postData['project_id'], $pageNum, $page, $cnt);
        $experiment = $experiment['data'];

        return $this->returnHandle($experiment['experiment_ids'], $experiment['cnt'], 'list', $page, $orderBy, $orderType);
    }

    /**
     * 根据页码获取实验id
     *
     * <AUTHOR> @copyright 2016-4-14
     * @return \common\controllers\json
     */
    public function actionGetIdByPage() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['exp_page'])) {
            return $this->fail(\Yii::t('exp', 'exp_code_empty'));
        }

        $res = (new ExperimentServer())->getIdByPage($postData);
        if (empty($res['data'])) {
            return $this->fail($res['info']);
        }

        return $this->success([
            'exp_id' => $res['data']
        ]);
    }

    /**
     * 获取前后实验
     */
    public function actionGetRoute() {
        $expId = \Yii::$app->request->get('exp_id', 0);
        $result = (new ExperimentServer())->getRoute($expId);
        return $this->success($result['data']);
    }

    /**
     * 设置前后实验
     */
    public function actionSetRoute() {
        $expId = \Yii::$app->request->post('exp_id', 0);
//        $expNumArr = \Yii::$app->request->post('exp_num_arr', []);
        $beforeExpNumArr = \Yii::$app->request->post('before_exp_num_arr', []);
        $afterExpNumArr = \Yii::$app->request->post('after_exp_num_arr', []);
        $result = (new ExperimentServer())->setRoute($expId, $beforeExpNumArr, $afterExpNumArr);
        if($result['status'] == 1) {
            return $this->success($result['data']);
        }
        return $this->fail($result['info']);
    }

    /**
     * 创建下一步反应
     */
    public function actionCreateNext() {
        $expId = \Yii::$app->request->post('exp_id', 0);
        $indrawData = \Yii::$app->request->post('indraw_data', []);
        $indrawData['int'] = $indrawData['int'] ? urldecode($indrawData['int']) : '';/* bug#513, mod dx */

        $result = (new ExperimentServer())->createNext($expId, $indrawData);
        if($result['status'] == 1) {
            return $this->success($result['data']);
        }
        return $this->fail($result['info']);
    }

    /**
     * 根据实验编号获取反应式图片
     */
    public function actionGetImgByExpNum() {
        $expNum = \Yii::$app->request->get('exp_num');
        $result = (new ExperimentServer())->getImgByExpNum($expNum);
        return $this->success($result['data']);
    }

    /**
     * Notes: 根据实验编号获取实验缩略信息
     * Author: zhu huajun
     * Date: 2019/10/17 9:46
     * @return \common\controllers\json
     */
    public function actionGetAbbrInfoByExpNum() {
        $expNum = \Yii::$app->request->get('exp_num');
        $expServer = new ExperimentServer();

        // 根据实验页码查找实验ID
        $expIdRes = $expServer->getExpIdByExpNum($expNum);
        $expId = $expIdRes['data'];

        if(empty($expId)) {
            return $this->success('');
        }

        $expAttr = ExperimentModel::find()->select(['abbr_info'])->where(['id' => $expId])->asArray()->one();

        return $this->success(@getVar($expAttr['abbr_info'], ''));
    }

    //    关系图api
    public function actionGetRelationInfo() {
        $expId = \Yii::$app->request->get('id');
        if(empty($expId)) {
            return $this->success('');
        }

        //查询所有有关的实验生成G6图
        $result = (new ExperimentServer())->getEntireRoute($expId);
//        $expAttr = ExperimentModel::find()->select(['abbr_info'])->where(['id' => $expId])->asArray()->one();
        $relationshipHtml = $this->renderAjax('/experiment/relationship_map.php', [
            'relationshipData' => $result,
            'expId' => $expId,
        ]);
        return $this->success([
            'contentHtml' => $relationshipHtml,
            'expId' => $expId,
        ]);
    }

    /**
     * 根据实验编号获取实验id
     * add by hkk 2019/3/20
     */
    public function actionGetExpIdByExpNum() {
        $expNum = \Yii::$app->request->get('exp_num');
        $result = (new ExperimentServer())->getExpIdByExpNum($expNum);
        return $this->success($result['data']);
    }

    /**
     * 查看反应路线
     */
    public function actionViewRoutes() {
        $expId = \Yii::$app->request->get('exp_id', 0);
        $result = (new ExperimentServer())->viewRoutes($expId);
        return $this->success($result['data']);
    }

    /**
     * 从反应路线导出pdf
     */
    public function actionExportPdf() {
        $expId = \Yii::$app->request->post('exp_id', 0);
        $expNumArr = \Yii::$app->request->post('exp_num_arr', []);
        if (!empty($expNumArr)) {
            $ExperimentServer = new ExperimentServer();
            $expIdArr = [];
            $userId = $this->userinfo->id;

            foreach ($expNumArr as $expNum) {
                $expIdRes = $ExperimentServer->getExpIdByExpNum($expNum);
                $expId = $expIdRes['data'];
                if ($expId != null) {
                    array_push($expIdArr, $expId);
                } else {
                    return $this->fail($expNum . \Yii::t('common', 'no exp'));
                }
            }

//            $printSetting = (new GroupSettingServer())->isPrintSingle([
//                'exp_ids' => $expIdArr,
//                'user_id' => $userId,
//            ]);

            $checkExportRes = (new GroupSettingServer())->checkExpExportPrior($this->userinfo->id, $expIdArr, 'pdf_single')['data'];
            if (!empty($checkExportRes['no_export_exps'])) {
                $no_export_exps = array_column($checkExportRes['no_export_exps'],'exp_all_code');
                return $this->fail(
                    \Yii::t('print_setting', 'print_same_exp_tips01', [
                        join(',', $no_export_exps)
                    ])
                );
            }


            $canExportExpIds = array_column($checkExportRes['can_export_exps'], 'id');
            return $this->success($canExportExpIds);

//            if (empty($printSetting['data'])) {
//                return $this->fail($printSetting['info']);
//            }
            return $this->success($expIdArr);
        } else if ($expId != 0) {
            $result = (new ExperimentServer())->getRouteExp($expId);
            if($result['status'] == 1) {
                return $this->success($result['data']);
            }
            return $this->fail($result['info']);
        } else {
            return $this->fail('');
        }
    }

    /**
     * 从反应路线导出pdf
     */
    public function actionExportPdfBak() {
        $expId = \Yii::$app->request->post('exp_id', 0);
        $expNumArr = \Yii::$app->request->post('exp_num_arr', []);
        $result = (new ExperimentServer())->getRouteExp($expId, $expNumArr);
        //print_r($result);exit;
        if($result['status'] == 1) {
            return $this->success($result['data']);
        }
        return $this->fail($result['info']);
    }

    /**
     * Notes: 实验合著设置页面
     * Author: zhu huajun
     * Date: 2019/3/18 15:28
     */
    public function actionCoauthorView() {
        $expId = \Yii::$app->request->post('exp_id');
        $userId = $this->userinfo->id;
        $companyId = $this->userinfo->current_company_id;

        $expServer = new ExperimentServer();
        $coauthorIds = $expServer->getCoauthorUserIds($expId, $userId, $companyId);

        if (empty($coauthorIds)) {
            return $this->fail(\Yii::t('exp', 'no_coauthor_tip'));
        }

        // 获取实验模块
        $moduleList = $expServer->getUserAccessModules($expId, $userId);

        // 获取当前的合著设置
        $coauthorSetting = $expServer->getCoauthorSetting($expId);
        if (empty($coauthorSetting)) {
            $coauthorSetting = [
                [
                    'user_ids' => [],
                    'module_ids' => []
                ]
            ];
        }

        // 获取公司所有人员
        $cmpUsers = (new CenterInterface())->getUserListByCompanyId(1, true);
        $cmpUserList = ArrayHelper::index($cmpUsers['list'], 'id');

        $html = $this->renderAjax('/popup/coauthor.php', [
            'coauthorIds' => $coauthorIds,
            'moduleList' => $moduleList,
            'coauthorSetting' => $coauthorSetting,
            'cmpUserList' => $cmpUserList
        ]);

        return $this->success($html);
    }

    /**
     * Notes: 实验合著设置
     * Author: zhu huajun
     * Date: 2019/3/18 15:28
     */
    public function actionCoauthorSubmit() {
        $expId = \Yii::$app->request->post('exp_id', 32363);
        $setting = \Yii::$app->request->post('setting', []);

        $result = (new ExperimentServer())->setCoauthorSetting($expId, $setting);

        if (empty($result['status'])) {
            return $this->fail($result['info']);
        }
        return $this->success($result['data']);
    }

    /**
     * Notes: xSheet模块的协作触发设置
     * Author: hkk
     * Date: 2020/3/5 15:28
    */
    public function actionXSheetCollaborationSubmit() {
        $expId = \Yii::$app->request->post('exp_id');
        $setting = \Yii::$app->request->post('setting');

        $result = (new ExperimentServer())->setXSheetCollaborationSubmit($expId, $setting);

        if (empty($result['status'])) {
            return $this->fail($result['info']);
        }
        return $this->success($result['data']);
    }


    /**
     * Notes: 重新加载实验模块
     * Author: zhu huajun
     * Date: 2019/3/21 11:02
     */
    public function actionReloadModule() {
        $relayId = \Yii::$app->request->get('relay_id');
        $forEdit = \Yii::$app->request->get('for_edit');
        $expServer = new ExperimentServer();

        if (!empty($forEdit)) {
            // 申请编辑模块
            $applyResult = $expServer->applyForEditModule($relayId);
            if (empty($applyResult['status'])) {
                return $this->fail($applyResult['info']);
            }
        }

        // 获取模块数据
        $moduleResult = $expServer->getModuleData($relayId);
        if (empty($moduleResult['status'])) {
            return $this->fail($moduleResult['info']);
        }

        $module = $moduleResult['data'];
        if(!empty($forEdit)) {
            $module['info']['coauthor_action'] = 'save';
        } else {
            $module['info']['coauthor_action'] = 'edit';
        }
        $expId = $module['exp_id'];

        $baseDataRes = $expServer->getBaseDataById($expId);
        $baseData = $baseDataRes['data'];

        // 获取模板权限
        $tmpPower = $this->_tmpPower($baseData['group_id']);

        // 渲染模块html
        $viewData = [
            'module' => $module,
            'experimentId' => $expId,
            'tmpPower' => $tmpPower,
            'module_edit' => $forEdit,
        ];
        $viewFile = '';
        switch($module['info']['component_id']) {
            case \Yii::$app->params['component']['chem']: // InDraw
                // 获取盐型列表
                $saltList = (new ExperimentServer())->saltList();
                $viewData['saltList'] = $saltList['data'];
                $viewFile = '/experiment/module/indraw_v2.php';
                break;
            case \Yii::$app->params['component']['operation']:
            case \Yii::$app->params['component']['discuss']:
            case \Yii::$app->params['component']['lite']:
            case \Yii::$app->params['component']['abstract']:
                $viewFile = '/experiment/module/editor.php';
                break;
            case \Yii::$app->params['component']['tlc']:
                $viewFile = '/experiment/module/tlc.php';
                break;
            case \Yii::$app->params['component']['reference']:
                $viewFile = '/experiment/module/reference.php';
                break;
            case \Yii::$app->params['component']['biology']:
                $viewFile = '/experiment/module/biology.php';
                break;
            case \Yii::$app->params['component']['upload']:
                $viewFile = '/experiment/module/file.php';
                break;
            case \Yii::$app->params['component']['picture']:
                $viewFile = '/experiment/module/image.php';
                break;
            case \Yii::$app->params['component']['comment']:
                $viewFile = '/experiment/module/comment.php';
                break;
            case \Yii::$app->params['component']['define_table']:
                $viewFile = '/experiment/module/define.php';
                break;
            case \Yii::$app->params['component']['custom_table']:
                $viewFile = '/experiment/module/custom_table.php';
                break;
            case \Yii::$app->params['component']['excel']:
                $viewFile = '/experiment/module/spreadsheet.php';
                break;
            case \Yii::$app->params['component']['xsheet']:
                $viewFile = '/experiment/module/xsheet.php';
                break;
        }

        \Yii::$app->view->params['edit'] = 2;
        \Yii::$app->view->params['exp_id'] = $expId;
        $moduleHtml = $this->renderAjax($viewFile, $viewData);
        return $this->success($moduleHtml);
    }

    /**
     * Notes: 保存实验模块
     * Author: zhu huajun
     * Date: 2019/3/21 11:02
     */
    public function actionSaveModule() {
        $expId = \Yii::$app->request->post('exp_id');
        $password = \Yii::$app->request->post('password');
        $reason = \Yii::$app->request->post('reason');
        $moduleData = \Yii::$app->request->post('module_data');
        $moduleData['info']['editing_user'] = 0;

        // bug#777, 如果是有$encodeType才解压,和\frontend\controllers\ExperimentController::actionSaveExperiment保持一致
        if ('gzip' == $moduleData['info']['encode_type']) {
            //获取加密后的base64 并解析
            $moduleData['data'] = json_decode(gzdecode(base64_decode($moduleData['data'])), true);
        }

        $expData = [
            'base_data' => [
                'experiment_id' => $expId,
                'update_comment' => 'coauthor_module:' . $moduleData['info']['name']
            ],
            'module_data' => [$moduleData],
            'password' => $password,
            'reason' => $reason,
            'coauthor_save' => [
                'module_id' => $moduleData['info']['real_id'],
                'module_name' => $moduleData['info']['name']
            ]
        ];
        $saveResult = (new ExperimentServer())->saveExperiment($expData);

        if (empty($saveResult['status'])) {
            if (!empty($saveResult['data']['pop_dialog'])) {
                $dialogHtml = $this->renderAjax('/popup/save_exp', [
                    'setting' => [
                        'submit_reason_required' => 1
                    ]
                ]);
                $saveResult['data']['dialog_html'] = $dialogHtml;
            }
            return $this->fail($saveResult['info'], 'JSON', $saveResult['data']);
        }
        return $this->success(['create_time'=>$saveResult['data']['create_time']]);
    }

    /**
     * Notes: 关闭实验时调用，释放对实验模块的编辑锁定
     * Author: zhu huajun
     * Date: 2019/4/10 14:28
     */
    public function actionUnlockExperiment() {
        $expId = \Yii::$app->request->post('exp_id');

        ExperimentModel::updateAll([
            'editing_user' => 0
        ], [
            'id' => $expId
        ]);

        ExperimentRelayModel::updateAll([
            'editing_user' => 0
        ], [
            'experiment_id' => $expId,
            'editing_user' => $this->userinfo->id,
            'status' => 1
        ]);
        return $this->success([]);
    }

    /**
     * Notes: 提交预审界面
     * Author: zhu huajun
     * Date: 2019/9/23 15:48
     * @return \common\controllers\json
     */
    public function actionPretrialView() {
        // 实验ID
        $expId = \Yii::$app->request->get('exp_id');
        if (empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        // 获取实验所在鹰群，项目
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        $groupId = $expBaseData['group_id'];
        $projectId = $expBaseData['project_id'];

        // 获取预审设置
        $pretrialSetting = CompanyAuthServer::getPretrialSetting($expBaseData['user_id'], $groupId);

        // 获取群审核设置，判断原因是否必填
        $generalSettingRes = (new GroupSettingServer())->getGeneralSetting($groupId);
        $generalSetting = $generalSettingRes['data'];
        $submitReasonRequired = false; // 标记原因是否必填
        if (!empty($generalSetting['approval_setting'])) {
            $approvalSetting = json_decode($generalSetting['approval_setting'], true);
            if (!empty($approvalSetting['pretrial']['submit_reason_required'])) {
                $submitReasonRequired = 1;
            }
        }
        $pretrialSetting['submit_reason_required'] = $submitReasonRequired;

        // 审批节点
        $approvalNodes = [];
        $approvalNodesSource = [];
        // 针对所有实验的预审设置
        if (!empty($pretrialSetting['all_setting']['approval_nodes'])) {
            $approvalNodes = $pretrialSetting['all_setting']['approval_nodes'];
            $approvalNodesSource = @getVar($pretrialSetting['all_setting']['approval_nodes_source'], []);
        }
        // 针对InDraw物料表的预审设置
        if (empty($approvalNodes) && !empty($pretrialSetting['indraw_setting'])) {
            $riskType = $pretrialSetting['indraw_setting']['checked_arr'];
            /* 通过反应明细中的cas信息判断物料表中是否存在知道危险信息的化合物 */
            // 查找InDraw模块
            $indrawModule = ExperimentRelayModel::findOne([
                'experiment_id' => $expBaseData['id'],
                'component_id' => 1,
                'status' => 1,
            ]);
            if ($indrawModule) {
                $chem = ChemModel::findOne([
                    'parent_id' => $indrawModule['id'],
                    'type' => 1,
                    'status' => 1
                ]);
                if ($chem) {
                    $details = ReactionDetailsModel::find()->select('cas')->where([
                        'chem_id' => $chem['id'],
                        'type' => 1,
                        'status' => 1
                    ])->andWhere(['not', ['cas' => null]])->asArray()->all();
                    $cas = array_column($details, 'cas');
                    if (!empty($cas)) {
                        $result = ChemInterface::judgeChemicalRisk($cas, $riskType);
                        if (!empty($result['data']['has_risk'])) {
                            $approvalNodes = $pretrialSetting['indraw_setting']['approval_nodes'];
                            $approvalNodesSource = @getVar($pretrialSetting['indraw_setting']['approval_nodes_source'], []);
                        }
                    }
                }
            }
        }
        // 针对项目的预审设置
        if (empty($approvalNodes) && !empty($expBaseData['project_id']) && !empty($pretrialSetting['project_setting'])) {
            foreach ($pretrialSetting['project_setting'] as $ps) {
                if (in_array($projectId, $ps['project_ids'])) {
                    $approvalNodes = $ps['approval_nodes'];
                    $approvalNodesSource = [];
                    break;
                }
            }
        }

        // 获取鹰群成员
        $groupMember = (new CenterInterface())->getGroupMembers(['group_id' => $groupId]);
        $userList = yii\helpers\ArrayHelper::index($groupMember, 'user_id');
        foreach ($userList as $key => $value) {
            if (empty($value['user_id'])) {
                unset($userList[$key]);
            }
        }

        // 审批路径
        // $approvalRouteRes = (new ApprovalServer())->getApprovalRoute($approvalNodes, $userList); 24536 plug:预审设置的时候如果选择了一份非群内的人员，预审的时候审批人字段消失
        $approvalRouteRes = (new ApprovalServer())->getApprovalRoute($approvalNodes, $approvalNodesSource, $groupId);
        if ($approvalRouteRes['status'] != 1) {
            return $this->fail($approvalRouteRes['info'], 'JSON', ['type' => 'popContent']);
        }
        $approverNames = $approvalRouteRes['data']['approver_names'];

        $file = $this->renderAjax('/popup/pretrial', [
            'approverNames' => $approverNames,
            'setting' => $pretrialSetting,
            'userList' => $userList,
            'userId' => $this->userinfo->id,
        ]);
        return $this->success($file);
    }

    /**
     * Notes: 提交预审
     * Author: zhu huajun
     * Date: 2019/9/23 15:48
     * @return \common\controllers\json
     */
    public function actionSubmitPretrial() {
        $expId = \Yii::$app->request->post('exp_id');
        if(empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        $nodeArr = \Yii::$app->request->post('node_arr', []);
        $reason = \Yii::$app->request->post('reason', '');

        // 获取实验所在鹰群，项目
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        $groupId = $expBaseData['group_id'];
        $userId = $expBaseData['user_id'];
        $projectId = $expBaseData['project_id'];

        // 获取预审设置
        $pretrialSetting = CompanyAuthServer::getPretrialSetting($userId, $groupId);

        // 审批节点
        $approvalNodes = $nodeArr;
        $approvalNodesSource = [];
        // 针对所有实验的预审设置
        if (!empty($pretrialSetting['all_setting']['approval_nodes'])) {
            $approvalNodes = $pretrialSetting['all_setting']['approval_nodes'];
            $approvalNodesSource = @getVar($pretrialSetting['all_setting']['approval_nodes_source'], []);
        }

        // 针对InDraw物料表的预审设置
        if (empty($approvalNodes) && !empty($pretrialSetting['indraw_setting'])) {
            $riskType = $pretrialSetting['indraw_setting']['checked_arr'];
            /* 通过反应明细中的cas信息判断物料表中是否存在知道危险信息的化合物 */
            // 查找InDraw模块
            $indrawModule = ExperimentRelayModel::findOne([
                'experiment_id' => $expBaseData['id'],
                'component_id' => 1,
                'status' => 1,
            ]);
            if ($indrawModule) {
                $chem = ChemModel::findOne([
                    'parent_id' => $indrawModule['id'],
                    'type' => 1,
                    'status' => 1
                ]);
                if ($chem) {
                    $details = ReactionDetailsModel::find()->select('cas')->where([
                        'chem_id' => $chem['id'],
                        'type' => 1,
                        'status' => 1
                    ])->andWhere(['not', ['cas' => null]])->asArray()->all();
                    $cas = array_column($details, 'cas');
                    if (!empty($cas)) {
                        $result = ChemInterface::judgeChemicalRisk($cas, $riskType);
                        if (!empty($result['data']['has_risk'])) {
                            $approvalNodes = $pretrialSetting['indraw_setting']['approval_nodes'];
                            $approvalNodesSource = @getVar($pretrialSetting['indraw_setting']['approval_nodes_source'], []);
                        }
                    }
                }
            }
        }

        // 针对项目的预审设置
        if (empty($approvalNodes) && !empty($expBaseData['project_id']) && !empty($pretrialSetting['project_setting'])) {
            foreach ($pretrialSetting['project_setting'] as $ps) {
                if (in_array($projectId, $ps['project_ids'])) {
                    $approvalNodes = $ps['approval_nodes'];
                    $approvalNodesSource = [];
                    break;
                }
            }
        }

        if(empty($approvalNodes)) {
            return $this->fail(\Yii::t('base', 'please_choose_approver'));
        }

        // 创建审批流程
        $createApprovalRes = (new ApprovalServer())->createApproval(
            \Yii::$app->params['approval_type']['pretrial'],
            $expId,
            $groupId,
            json_encode([
                'send_email' => 1,
                'reason' => $reason,
                'pretrial_setting' => $pretrialSetting
            ]),
            $approvalNodes,
            $approvalNodesSource
        );

        if (empty($createApprovalRes['status'])) {
            return $this->fail($createApprovalRes['info'], 'JSON',['tipType' => 'popContent']);
        }

        // 更新实验状态
        ExperimentModel::updateAll([
            'pretrial_status' => 1
        ], [
            'id' => $expId
        ]);
        //add by wy 2023/4/14 新在痕迹中记录提交给的审批人员的功能
        $extraHistoryData = [
          'approval_route' => $createApprovalRes['data']['approval_route']
        ];
        // 生成一条痕迹记录
        (new HistoryModel())->addHistoryAllData($expId, \Yii::$app->view->params['curr_user_id'], 'pretrial', $reason, FALSE, $extraHistoryData);
        // 更新实验数据至Pgsql，用于结构式搜索和高级搜索
        (new ExperimentServer())->syncExpToPgsql($expId);
        return $this->success([]);
    }

    /**
     * Notes: 撤销预审
     * Author: zhu huajun
     * Date: 2019/9/23 15:48
     * @return \common\controllers\json
     */
    public function actionCancelPretrial() {
        $expId = \Yii::$app->request->post('exp_id');
        if(empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        $result = (new ExperimentServer())->cancelPretrial($expId);
        if($result['status'] != 1) {
            return $this->fail($result['info']);
        }
        return $this->success([]);
    }

    /**
     * Notes: 提交签字界面
     * Author: zhu huajun
     * Date: 2019/9/23 15:48
     * @return \common\controllers\json
     */
    public function actionSigningView() {
        $expId = \Yii::$app->request->get('exp_id');
        if (empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        // 获取实验所在鹰群
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        $groupId = $expBaseData['group_id'];
        //获得实验的签字设置
        $setting = CompanyAuthServer::getSigningSetting($expBaseData['user_id'], $groupId);

        //查看模块
        $moduleList = (new ExperimentServer())->getUserAccessModules($expId, $this->userinfo->id);
        // Bug 676 // 1->初始状态;2->签字待审核;3->签字通过;4->启动编辑待审核
        $statusToExclude = isset($setting['re_edit_approval_user_ids']) && !empty($setting['re_edit_approval_user_ids'])
            ? [2, 3, 4] // 如果设置了签字通过后编辑审核人
            : [2, 4]; // 如果没有设置签字通过后编辑审核人

        $moduleList = array_filter($moduleList, function ($m) use ($statusToExclude) {
            return !in_array($m['module_status'], $statusToExclude);
        });
        // 判断是否为合著, 若是则取合著的模块 bug#1992
        if (!empty($moduleList) && $expBaseData['user_id'] != $this->userinfo->id) {
            $expId2coauthorRelayIds = (new ExperimentServer())->getCoauthorRelayIds($expId, $this->userinfo->id);
            $coauthorRelayIds = @getVar($expId2coauthorRelayIds[$expId], []);
            if (count($coauthorRelayIds) > 0) {
                $moduleList = array_filter($moduleList, function ($m) use ($coauthorRelayIds) {
                    return in_array($m['id'], $coauthorRelayIds);
                });
            }
        }

        if (empty($moduleList)) {
            return $this->fail(\Yii::t('exp', 'no_module_to_sign'));
        }

        // 获取公司所有人员
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');

        // 获取群审核设置，判断原因是否必填
        $generalSettingRes = (new GroupSettingServer())->getGeneralSetting($groupId);
        $generalSetting = $generalSettingRes['data'];
        $submitReasonRequired = false; // 标记原因是否必填
        if (!empty($generalSetting['approval_setting'])) {
            $approvalSetting = json_decode($generalSetting['approval_setting'], true);
            if (!empty($approvalSetting['signing']['submit_reason_required'])) {
                $submitReasonRequired = 1;
            }
        }
        $setting['submit_reason_required'] = $submitReasonRequired;

        // 审批路径
        $approvalRouteRes = (new ApprovalServer())->getApprovalRoute(@getVar($setting['approval_nodes'], []), @getVar($setting['approval_nodes_source'], []), $groupId);
        if ($approvalRouteRes['status'] != 1) {
            return $this->fail($approvalRouteRes['info'], 'JSON', ['type' => 'popContent']);
        }

        $approvalNodes = $approvalRouteRes['data']['approval_nodes'];
        $approvalUsers = [];
        if (!empty($approvalNodes)) {
            $approvalUserIds = [];
            foreach ($approvalNodes as $node) {
                if (!empty($node['approval_user_ids'])) {
                    $approvalUserIds = array_merge($approvalUserIds, $node['approval_user_ids']);
                }
                if (!empty($node['coapproval_user_ids'])) {
                    $approvalUserIds = array_merge($approvalUserIds, $node['coapproval_user_ids']);
                }
            }
            $approvalUserIds = array_unique($approvalUserIds);

            $usersRes = (new CenterInterface())->userDetailsByUserIds($approvalUserIds);
            $approvalUsers = ArrayHelper::index($usersRes, 'id');
        }

        $file = $this->renderAjax('/popup/signing', [
            'moduleList' => $moduleList,
            'approvalNodes' => $approvalNodes,
            'approvalUsers' => $approvalUsers,
            'setting' => $setting,
            'userList' => $userList,
            'userId' => $this->userinfo->id,
        ]);
        return $this->success($file);
    }

    /**
     * Notes: 提交签字
     * Author: zhu huajun
     * Date: 2019/9/23 15:48
     * @return \common\controllers\json
     */
    public function actionSubmitSigning() {
        $expId = \Yii::$app->request->post('exp_id');
        $moduleArr = \Yii::$app->request->post('module_arr', []);
        $nodeArr = \Yii::$app->request->post('node_arr', []);
        $reason = \Yii::$app->request->post('reason', '');
        if(empty($expId) || empty($moduleArr)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        $result = (new ApprovalServer())->submitSigning($expId, $moduleArr, $nodeArr, $reason);
        $resultData = @getVar($result['data'], []);
        if (empty($result['status'])) {
            return $this->fail($result['info'], 'JSON',['type' => @getVar($resultData['type'], '')]);
        }

        return $this->success([]);
    }

    /**
     * Notes: 签字后申请重新编辑界面
     * Author: zhhj
     * Date: 2021/4/16
     * @return \common\controllers\json
     */
    public function actionReqReeditView() {
        $expId = \Yii::$app->request->post('exp_id');
        $moduleId = \Yii::$app->request->post('module_id');
        if (empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        // 获取实验中签字通过的模块
        $moduleList = (new ExperimentServer())->listSignedModule($expId, $moduleId);
        if (empty($moduleList) && empty($moduleId)) {
            return $this->fail(\Yii::t('exp', 'no_module_to_reedit'));
        }

        // 获取实验所在鹰群
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        $groupId = $expBaseData['group_id'];

        // 获取签字设置
        $setting = CompanyAuthServer::getSigningSetting($expBaseData['user_id'], $groupId);

        // 重新编辑审批人
        $approverIds = @getVar($setting['re_edit_approval_user_ids'], []);
        if (empty($approverIds)) {
            // 重新编辑审批人为空，说明重新编辑无需审批， 提示前端直接重新加载模块
            return $this->success([
                'reloadModule' => 1
            ]);
        }

        $usersRes = (new CenterInterface())->userDetailsByUserIds($approverIds);
        $userDetailArr = yii\helpers\ArrayHelper::index($usersRes, 'id');
        $approverNames = array_map(function ($uid) use ($userDetailArr) {
            return CommonServer::displayUserName(@getVar($userDetailArr[$uid]));
        }, $approverIds);
        $approverNames = implode(',', $approverNames);

        $file = $this->renderAjax('/popup/req_for_reedit', [
            'moduleId' => $moduleId,
            'moduleList' => $moduleList,
            'approverNames' => $approverNames,
            'userId' => $this->userinfo->id,
        ]);
        return $this->success([
            'html' => $file
        ]);
    }

    /**
     * Notes: 申请重新编辑——>提交
     * Author: zhhj
     * Date: 2021/4/16
     * @return \common\controllers\json
     */
    public function actionReqReedit() {
        $password = \Yii::$app->request->post('password');
        if (empty($password)) {
            return $this->fail(\Yii::t('sign', 'pass_must'));
        }

        $checkPwd = (new CenterInterface())->checkPwd($this->userinfo->id, $password, $this->userinfo->name);
        if (!$checkPwd) {
            return $this->fail(\Yii::t('sign', 'pass_error'));
        }

        $expId = \Yii::$app->request->post('exp_id');
        $moduleArr = \Yii::$app->request->post('module_arr', []);
        $reason = \Yii::$app->request->post('reason');

        // 获取实验所在鹰群
        $expBaseData = (new ExperimentServer())->getExpBaseData($expId);
        $groupId = $expBaseData['group_id'];

        // 获取签字设置
        $setting = CompanyAuthServer::getSigningSetting($expBaseData['user_id'], $groupId);

        // 重新编辑审批人
        $approverIds = @getVar($setting['re_edit_approval_user_ids'], []);
        if (empty($approverIds)) {
            // 重新编辑审批人为空，说明重新编辑无需审批， 提示前端直接重新加载模块
            return $this->success([
                'reloadModule' => 1
            ]);
        }

        // 创建审批流程
        $createApprovalRes = (new ApprovalServer())->createApproval(
            \Yii::$app->params['approval_type']['module_reedit'],
            $expId,
            $groupId,
            json_encode([
                'module_arr' => $moduleArr,
                'reason' => $reason
            ]),
            [['approval_user_ids' => $approverIds]],
            @getVar($setting['re_edit_approval_nodes_source'], [])
        );

        if (empty($createApprovalRes['status'])) {
            return $this->fail($createApprovalRes['info'], 'JSON',['tipType' => 'popContent']);
        }
        $approverNames = @getVar($createApprovalRes['data']['approver_names'], []);

        // 更新模块状态
        ExperimentRelayModel::updateAll([
            'module_status' => 4
        ], [
            'id' => $moduleArr
        ]);

        // 生成一条痕迹记录
        $moduleRes = (new ExperimentServer())->getModuleNames($moduleArr);
        $moduleData = @getVar($moduleRes['data'], []);
        $moduleNames = array_column($moduleData, 'name');

        $extraData = [
            'module_ids' => $moduleArr,
            'module_names' => $moduleNames,
            'approver_names' => $approverNames,
        ];
        (new HistoryModel())->addHistoryAllData($expId, \Yii::$app->view->params['curr_user_id'], 'module_reedit', $reason, FALSE, $extraData);

        return $this->success([]);
    }

    /**
     * Notes: 撤销撤销编辑
     * Author: zhhj
     * Date: 2021/4/20
     */
    public function actionCancelReqReedit() {
        $expId = \Yii::$app->request->post('exp_id');
        $moduleId = \Yii::$app->request->post('module_id');
        if (empty($expId) || empty($moduleId)) {
            return $this->fail(\Yii::t('exp', 'invalid_module'));
        }

        // 查找模块
        $module = ExperimentRelayModel::find()->where([
            'id' => $moduleId,
            'status' => 1
        ])->one();
        if (!$module) {
            return $this->fail(\Yii::t('exp', 'invalid_module'));
        }

        if ($module['module_status'] != 4) {
            return $this->fail(\Yii::t('exp', 'invalid_module'));
        }

        // 撤销审核
        (new ApprovalServer())->cancelReeditApproval($expId, $moduleId);

        // 更新模块状态
        ExperimentRelayModel::updateAll([
            'module_status' => 3
        ], [
            'id' => $moduleId
        ]);

        // 生成一条痕迹记录
        $moduleRes = (new ExperimentServer())->getModuleNames([$moduleId]);
        $moduleData = @getVar($moduleRes['data'], []);
        $moduleNames = array_column($moduleData, 'name');

        $extraData = [
            'module_ids' => [$moduleId],
            'module_names' => $moduleNames,
        ];
        (new HistoryModel())->addHistoryAllData($expId, \Yii::$app->view->params['curr_user_id'], 'module_reedit_canceled', NULL, FALSE, $extraData);

        return $this->success([]);
    }

    /**
     * Notes: 设置/取消->首页目录
     * Author: zhu huajun
     * Date: 2019/11/7 17:56
     */
    public function actionSwitchHomePageDir() {
        $action = \Yii::$app->request->post('action');
        $type = \Yii::$app->request->post('type');
        $moduleId = \Yii::$app->request->post('module_id');

        if(empty($action) || empty($moduleId)) {
            return $this->fail(\Yii::t('base', 'lost_params'));
        }

        $res = (new ExperimentServer())->switchHomePageDir($action, $type, $moduleId);

        if($res['status'] != 1) {
            return $this->fail($res['info']);
        }

        return $this->success([]);
    }

    /**
     * Notes: 设置文本编辑器开启拼写检查
     * Author: hkk
     * Date: 2019/11/21 15:06
     * @return \common\controllers\json
     */
    public function actionSwitchSpellCheck() {
        $action = \Yii::$app->request->post('action');
        $type = \Yii::$app->request->post('type');
        $moduleId = \Yii::$app->request->post('module_id');

        if(empty($action) || empty($moduleId)) {
            return $this->fail(\Yii::t('base', 'lost_params'));
        }

        $res = (new ExperimentServer())->switchSpellCheck($action, $type, $moduleId);

        if($res['status'] != 1) {
            return $this->fail($res['info']);
        }

        return $this->success([]);
    }

    /**
     * Notes: 获取实验的提醒设置
     * Author: zhu huajun
     * Date: 2019/11/19 14:01
     */
    public function actionGetReminderSetting() {
        // 实验id
        $experimentId = \Yii::$app->getRequest()->get('experiment_id');

        // 获取设置
        $setting = [];
        if (!empty($experimentId)) {
            $settingResult  = (new ExperimentServer())->getReminderSetting($this->userinfo->id, $experimentId);
            $settingRecord = $settingResult['data'];
            if(!empty($settingRecord['setting'])) {
                $setting = json_decode($settingRecord['setting'], true);
            }
        }

        // 获取公司所有人员
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');

        $html = $this->renderAjax('/setting/experiment_remind_setting.php', [
            'user_list' => $userList,
            'setting' => $setting
        ]);

        return $this->success([
            'html' => $html
        ]);
    }

    /**
     * Notes: 保存实验的提醒设置
     * Author: zhu huajun
     * Date: 2019/11/19 14:01
     */
    public function actionSaveReminderSetting() {
        // 实验id数组
        $experimentIdArr = \Yii::$app->request->post('experiment_ids', []);
        if(empty($experimentIdArr)) {
            return $this->fail('');
        }

        // 设置内容
        $setting = \Yii::$app->getRequest()->post('setting');

        $result = (new ExperimentServer())->saveReminderSetting($this->userinfo->id, $experimentIdArr, $setting);
        if (empty($result['status'])) {
            return $this->fail($result['info']);
        }

        return$this->success([]);
    }

    /**
     * Notes: 收藏夹实验列表
     *
     * Author: zhu huajun
     * Date: 2019/12/2 13:52
     * @return \common\controllers\json
     */
    public function actionFavoritesList() {
        $filter = \Yii::$app->request->post('filter', []);
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 1);
        $orderType = \Yii::$app->request->post('order_type', 1);

        /*// 用户全部鹰群
        $groupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id, $this->userinfo->current_company_id);
        $groupIds = array_column($groupList, 'group_id');

        // 鹰群筛选
        if(empty($filter['group_ids'])) {
            $filter['group_ids'] = $groupIds;
        } else {
            $filter['group_ids'] = array_intersect($groupIds, $filter['group_ids']);
        }

        // 用户筛选
        $filter['user_ids'] = $this->userinfo->id;

        // 根据鹰群获取项目，以及项目下的目标、任务，以pId表示树形关系
        $projectTree = (new PMInterface())->listProjectTreeByGroup($groupIds);
        // 添加全部项目作为根节点
        array_push($projectTree, [
            'id' => 0,
            'pId' => 0,
            'name' => \Yii::t('base', 'all'),
            'open' => true,
            'checked' => true,
        ]);

        // 模板列表
        $tempParams = [
            'user_id' => $this->userinfo->id,
            'group_ids' => $groupIds,
            'company_id' => $this->userinfo->current_company_id
        ];
        $tempRes = (new TempleServer())->tempForExpFilter($tempParams);
        $tempList = @getVar($tempRes['data'], []);*/

        $res = (new ExperimentServer())->favoritesList($this->userinfo->id);
        $experimentIds = @getVar($res['data']['experiment_ids'], []);

        if(empty($experimentIds)) {
            $experimentRes = [
                'id' => [],
                'total' => 0
            ];
        } else {
            $filter['experiment_ids'] = $experimentIds;
            $experimentRes = (new ExperimentServer())->listExperimentIdByFilter($filter, $limit, $page, $orderBy, $orderType, [
                'join' => [
                    'table' => ExperimentFavoritesModel::tableName(),
                    'field' => 'experiment_id'
                ],
                'order_by' => [
                    'table' => ExperimentFavoritesModel::tableName(),
                    'field' => 'update_time',
                    'type' => 'DESC'
                ],
            ]);
        }

        return $this->returnHandle($experimentRes['id'], $experimentRes['total'], 'favorites', $page, $orderBy, $orderType, FALSE, NULL, NULL,  $experimentRes['total'], [
            'filter_list' => [
//                'group_list' => $groupList,
//                'project_tree' => $projectTree,
//                'temp_list' => $tempList
            ],
            'filter' => \Yii::$app->request->post('filter', []),
        ]);
    }

    /**
     * Notes: 将实验加入收藏夹
     *
     * Author: zhu huajun
     * Date: 2019/12/2 13:52
     * @return \common\controllers\json
     */
    public function actionAddToFavorites() {
        $experimentIds = \Yii::$app->request->post('experiment_ids', []);
        $res = (new ExperimentServer())->addToFavorites($this->userinfo->id, $experimentIds);
        return $this->success([]);
    }

    /**
     * Notes: 将实验从收藏夹中移除
     *
     * Author: zhu huajun
     * Date: 2019/12/2 13:52
     * @return \common\controllers\json
     */
    public function actionRemoveFromFavorites() {
        $experimentIds = \Yii::$app->request->post('experiment_ids', []);
        $res = (new ExperimentServer())->removeFromFavorites($this->userinfo->id, $experimentIds);
        return $this->success([]);
    }



    /**
     * Notes: 最近访问实验列表
     *
     * Author: zhu huajun
     * Date: 2019/12/2 13:52
     * @return \common\controllers\json
     */
    public function actionRecentlyViewedList() {
        $filter = \Yii::$app->request->post('filter', []);
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 1);
        $orderType = \Yii::$app->request->post('order_type', 1);

        /*// 用户全部鹰群
        $groupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id, $this->userinfo->current_company_id);
        $groupIds = array_column($groupList, 'group_id');

        // 鹰群筛选
        if(empty($filter['group_ids'])) {
            $filter['group_ids'] = $groupIds;
        } else {
            $filter['group_ids'] = array_intersect($groupIds, $filter['group_ids']);
        }

        // 用户筛选
        $filter['user_ids'] = $this->userinfo->id;

        // 根据鹰群获取项目，以及项目下的目标、任务，以pId表示树形关系
        $projectTree = (new PMInterface())->listProjectTreeByGroup($groupIds);
        // 添加全部项目作为根节点
        array_push($projectTree, [
            'id' => 0,
            'pId' => 0,
            'name' => \Yii::t('base', 'all'),
            'open' => true,
            'checked' => true,
        ]);

        // 模板列表
        $tempParams = [
            'user_id' => $this->userinfo->id,
            'group_ids' => $groupIds,
            'company_id' => $this->userinfo->current_company_id
        ];
        $tempRes = (new TempleServer())->tempForExpFilter($tempParams);
        $tempList = @getVar($tempRes['data'], []);*/

        $res = (new ExperimentServer())->recentlyViewedList($this->userinfo->id);
        $experimentIds = @getVar($res['data']['experiment_ids'], []);

        if(empty($experimentIds)) {
            $experimentRes = [
                'id' => [],
                'total' => 0
            ];
        } else {
            $filter['experiment_ids'] = $experimentIds;
            $experimentRes = (new ExperimentServer())->listExperimentIdByFilter($filter, $limit, $page, $orderBy, $orderType, [
                'join' => [
                    'table' => RecentlyViewedExperimentModel::tableName(),
                    'field' => 'experiment_id'
                ],
                'order_by' => [
                    'table' => RecentlyViewedExperimentModel::tableName(),
                    'field' => 'update_time',
                    'type' => 'DESC'
                ],
            ]);
        }

        return $this->returnHandle($experimentRes['id'], $experimentRes['total'], 'recently_viewed', $page, $orderBy, $orderType, FALSE, NULL, NULL,  $experimentRes['total'], [
            'filter_list' => [
//                'group_list' => $groupList,
//                'project_tree' => $projectTree,
//                'temp_list' => $tempList
            ],
            'filter' => \Yii::$app->request->post('filter', []),
        ]);
    }

    /**
     * Notes: 设置/取消->TextAsStructure
     * Author: zhu huajun
     * Date: 2019/12/25 20:26
     */
    public function actionSwitchTextAsStructure() {
        $action = \Yii::$app->request->post('action');
        $type = \Yii::$app->request->post('type');
        $moduleId = \Yii::$app->request->post('module_id');

        if(empty($action) || empty($moduleId)) {
            return $this->fail(\Yii::t('base', 'lost_params'));
        }

        $res = (new ExperimentServer())->switchTextAsStructure($action, $type, $moduleId);

        if($res['status'] != 1) {
            return $this->fail($res['info']);
        }

        return $this->success([]);
    }

    /**
     * Notes: 仪器材料，参考文献，多功能表格模块->设置字体
     * Author: zhu huajun
     * Date: 2020/1/7 17:23
     */
    public function actionSetFont() {
        $type = \Yii::$app->request->post('type');
        $moduleId = \Yii::$app->request->post('module_id');
        $fontSize = \Yii::$app->request->post('font_size');
        $fontAlgin = \Yii::$app->request->post('font_algin');

        if(empty($moduleId) || empty($fontSize)) {
            return $this->fail(\Yii::t('base', 'lost_params'));
        }

        $res = (new ExperimentServer())->setFont($type, $moduleId, $fontSize, $fontAlgin);

        if($res['status'] != 1) {
            return $this->fail($res['info']);
        }

        return $this->success([]);
    }

    /**
     * Notes: 查找翻页的实验id
     * Author: szq
     * Date: 2020/4/27 13:49
     * url = ?r=experiment/turn-page
     * @return String 翻页的实验id
     */
    public function actionTurnPage() {
        $exp_id = \Yii::$app->request->get('exp_id');
        $turn_type = \Yii::$app->request->get('turn_type');

        $turnRes = (new ExperimentServer())->turnPage($exp_id, $turn_type);
        $turnExpId = @getVar($turnRes['data'], 0);

        return $this->success($turnExpId);
    }
    /**
     * 获取当前用户实验列表
     * ?r=experiment/list-experiment
     *
     * <AUTHOR> @copyright 2017-4-13
     *
     * @param int filter 筛选条件
     * @param int page
     * @param int limit 查询量
     * @param int order_by
     * @param int order_type
     *
     * @remark fixed whl
     * @return \common\controllers\json
     */
    public function actionListExperimentForStruct() {
        // 查询我所在的鹰群
        $groupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id, $this->userinfo->current_company_id);
        $groupIds = array_column($groupList, 'id');
        $data['group_list'] = $groupList;

        // 获取模板列表
        $params = [
            'user_id' => $this->userinfo->id,
            'group_ids' => $groupIds,
            'company_id' => $this->userinfo->current_company_id
        ];
        $templateListRes = (new TempleServer())->tempForExp($params);
        $templateList = @getVar($templateListRes['data'], []);
        foreach ($templateList as $key => $temp) {
            switch ($temp['name']) {
                case 'Classic biological template':
                case '经典生物模板':
                    $templateList[$key]['name'] = \Yii::t('views/set_require', 'bio_template');
                    break;
                case 'Classic chemical template':
                case '经典化学模板':
                    $templateList[$key]['name'] = \Yii::t('views/set_require', 'chem_template');
                    break;
            }
        }
        $data['template_list'] = $templateList;

        // 查询我创建的记录本
        $where['b.user_id'] = $this->userinfo->id;
        $where['b.status'] = 1;
        $bookList = (new BookServer())->listBook($where);
        $data['book_list'] = $bookList['data'];

        // 查询项目
        $projectList = (new PMInterface())->getUserProjects($this->userinfo);
        $data['project_list'] = $projectList;

        $html = $this->renderAjax('/layouts/exp_list_for_struct.php', $data);
        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * 获取当前用户实验列表
     * ?r=experiment/list-experiment
     *
     * <AUTHOR> @copyright 2017-4-13
     *
     * @param int filter 筛选条件
     * @param int page
     * @param int limit 查询量
     * @param int order_by
     * @param int order_type
     *
     * @remark fixed whl
     * @return \common\controllers\json
     */
    public function actionListExperimentForStructData() {
        //查询我的模板和分享给我的模板
        $postDatas = \Yii::$app->request->post();

        $postDatas['user_id'] = $this->userinfo->id;

        if(empty($postDatas['template_id'])){
            return $this->fail(\Yii::t('share', 'temp_id_is_required'));
        }
        // 先获取这个人能看到全部实验，
        $result = $this->_search([]);


        $dataResult= (new ExperimentServer())->listExperimentForStruct($postDatas,$result['ids']);


        $html = $this->renderAjax('/layouts/exp_list_for_struct_data.php', ['data'=>$dataResult]);
                return $this->success([
                    'file' => $html
                ]);
    }

    /**
     * 批量粘贴记录本页码
     * ?r=experiment/list-experiment
     *
     * <AUTHOR> @copyright 2017-4-13
     *
     * @param int filter 筛选条件
     * @param int page
     * @param int limit 查询量
     * @param int order_by
     * @param int order_type
     *
     * @remark fixed whl
     * @return \common\controllers\json
     */
    public function actionListExperimentExpnumForStruct() {
        //查询我的模板和分享给我的模板
        $data=[];
        $html = $this->renderAjax('/layouts/exp_list_num_for_struct.php', $data);
                return $this->success([
                    'file' => $html
                ]);
    }
    /**
     * 基于条件从eln_experiment表中检索匹配的实验
     *
     * @access private
     * @param array $conditions
     * @param integer $page
     * @param integer $limit
     * @return 返回匹配的实验ID集合，并基于$page和$limit条件分页
     */
    private function _search($conditions) {
        $texts = preg_split('/\s+/', isset($conditions['allwords']) ? $conditions['allwords'] : '', null, PREG_SPLIT_NO_EMPTY);
        $titles = preg_split('/\s+/', isset($conditions['title']) ? $conditions['title'] : '', null, PREG_SPLIT_NO_EMPTY);
        $keywords = preg_split('/\s+/', isset($conditions['keyword']) ? $conditions['keyword'] : '', null, PREG_SPLIT_NO_EMPTY);
        $startTime = isset($conditions['start_time']) ? trim($conditions['start_time']) : '0';
        $endTime = isset($conditions['end_time']) ? trim($conditions['end_time']) : date('Y-m-d');
        $includeRecycle = isset($conditions['include_recycle']) ? $conditions['include_recycle'] : 0;
        $detectChinese = isset($conditions['detect_chinese']) ? $conditions['detect_chinese'] : 0;
        $smiles = isset($conditions['smiles']) ? urldecode($conditions['smiles']) : '';
        $searchFilter = @getVar($conditions['search_filter'], []);

        $psql = \Yii::$app->pg_db;
        $query = new \yii\db\Query;

        $query->select('experiment_id')->from(ElnExperimentModel::tableName());

        foreach ($texts as $text) {
            $query->andWhere(['ILIKE', 'digest', $text]);
        }

        foreach ($titles as $title) {
            $query->andWhere(['ILIKE', 'title', $title]);
        }

        foreach ($keywords as $keyword) {
            $query->andWhere(['ILIKE', 'keywords', $keyword]);
        }

        if ($startTime) {
            $query->andWhere(['>=', 'create_time', $startTime . ' 00:00:00']);
        }

        if ($endTime) {
            $query->andWhere(['<=', 'create_time', $endTime . ' 23:59:59']);
        }

        // 实验状态筛选
        if (!empty($searchFilter['step'])) {
            $query->andWhere(['step' => $searchFilter['step']]);
        }
        if (!empty($searchFilter['pretrial_status'])) {
            $query->andWhere(['pretrial_status' => $searchFilter['pretrial_status']]);
        }
        if (!empty($searchFilter['reopen_status'])) {
            $query->andWhere(['reopen_status' => $searchFilter['reopen_status']]);
        }

        // 鹰群筛选
        if (!empty($searchFilter['group_ids'])) {
            $query->andWhere(['group_id' => $searchFilter['group_ids']]);
        }

        // 用户筛选
        if (!empty($searchFilter['user_ids'])) {
            $query->andWhere(['user_id' => $searchFilter['user_ids']]);
        }

        // 实验结论筛选
        if (!@emptyExclude0($searchFilter['result'])) {
            $query->andWhere(['result' => $searchFilter['result']]);
        }

        // 推荐指数筛选
        if (!@emptyExclude0($searchFilter['star'])) {
            $query->andWhere(['star' => $searchFilter['star']]);
        }

        // 项目、任务筛选
        if (!empty($searchFilter['project_ids'])) {
            $query->andWhere(['project_id' => $searchFilter['project_ids']]);
        }
        if (!empty($searchFilter['task_ids'])) {
            $query->andWhere(['task_id' => $searchFilter['task_ids']]);
        }

        /*类别筛选 开始*/
        if (empty($searchFilter['category_ids'])) {
            $searchFilter['category_ids'] = [self::$category['my_exp'], self::$category['coauthor'], self::$category['share'], self::$category['inspection']];
        }
        $userId = $this->userinfo->id;
        if(!in_array(self::$category['company'], $searchFilter['category_ids'])) {
            $categoryWhere = ['OR'];

            // 我的实验
            if (in_array(self::$category['my_exp'], $searchFilter['category_ids'])) {
                $categoryWhere[] = ['user_id' => $userId];
            }

            // 需要我合著的实验
            if (in_array(self::$category['coauthor'], $searchFilter['category_ids'])) {
                $categoryWhere[] = $psql->quoteValue($userId) . ' = ANY (coauthor_user_ids)';
            }

            // 分享给我的实验
            if (in_array(self::$category['share'], $searchFilter['category_ids'])) {
                $categoryWhere[] = $psql->quoteValue($userId) . ' = ANY (shared_user_ids)';
                $groups = (new CenterInterface())->elnGroupListByUserId($userId, $this->userinfo->current_company_id);
                foreach ($groups as $group) {
                    $categoryWhere[] = $psql->quoteValue($group['group_id']) . ' = ANY (shared_group_ids)';
                }
            }

            // 实验核查
            if (in_array(self::$category['inspection'], $searchFilter['category_ids'])) {
                $checkRange = (new CompanyAuthServer())->getCheckRange($userId);
                if (!empty($checkRange['group_ids'])) {
                    $categoryWhere[] = ['group_id' => $checkRange['group_ids']];
                }
                if (!empty($checkRange['project_ids'])) {
                    $categoryWhere[] = ['project_id' => $checkRange['project_ids']];
                }
            }

            if (count($categoryWhere) > 1) {
                $query->andWhere($categoryWhere);
            }
        }
        /*类别筛选 结束*/

        // 包含中文筛选
        if($detectChinese) {
            $query->andWhere(['~', 'digest', '[\u4e00-\u9fa5]']);
        }

        if (!$includeRecycle) {
            $query->andWhere(['status' => 1]);
        }

        if (!empty($conditions['order_by']) && !empty($conditions['order_type'])) {
            $orderBy = $conditions['order_by'] == 1 ? 'create_time' : 'update_time';
            $orderBy .= $conditions['order_type'] == 1 ? ' DESC' : ' ASC';
            $query->orderBy($orderBy);
        }

        if ($smiles) {
            if (FALSE !== strpos($smiles, '>>')) {
                // 反应式
                $query->andWhere(['@>', 'reaction', $smiles]);
            } else {
                $query->andWhere(['@>', 'molecule_smiles_mol', $smiles]);
                // 非反应式
                $query->andWhere(['OR',
                     // 箭头在后面
                     ['@>', 'reaction', $smiles . '>>'],
                     // 箭头在前面
                     ['@>', 'reaction', '>>' . $smiles],
                     // 没有箭头
                     ['@>', 'molecule_smiles_mol', $smiles]
                 ]);
            }

            if (empty($orderBy)) {
                $query->orderBy('product_weight ASC, substrate_weight ASC, experiment_id DESC, update_time DESC');
            }
        } else {
            if (empty($orderBy)) {
                $query->orderBy('update_time DESC');
            }
        }

        $command = $query->createCommand($psql);

        // FIXME: 因为结构式搜索插件健壮性不足，当前先直接忽略可能出现的异常，后续可以考虑更完善的处理。
        try {
            $experiments = $command->queryAll();
        } catch (\Exception $e) {
            $experiments = [];
            \Yii::info('Query reaction failed: ' . $e->getMessage());
        }

        $ids = array_column($experiments, 'experiment_id');

        return [
            'ids' => $ids
        ];
    }

    /**
     * Notes: 实验添加模块接口
     * Author: szq
     * Date: 2020/7/9 17:00
     */
    public function actionAddModule() {
        $type = Yii::$app->request->post('type', 0);
        $id = Yii::$app->request->post('id');
        $componentId = Yii::$app->request->post('componentId');
        $name = Yii::$app->request->post('name');
        $class = Yii::$app->request->post('class');
        $componentType = Yii::$app->request->post('componentType', 0);

        $moduleInfo = [
            'id' => $id,
            'componentId' => $componentId,
            'name' => $name,
            'class' => $class,
            'componentType' => $componentType,
            'editUser' => $this->userinfo->id,
        ];

        \Yii::info('实验[' . $id . ']添加模块' . json_encode($moduleInfo), 'ExperimentController');

        $experimentServer = new ExperimentServer();
        $res = $experimentServer->addModule($moduleInfo, $type);

        \Yii::info('实验[' . $id . ']添加模块结果' . json_encode($res), 'ExperimentController');

        return $this->success($res['data']);
    }

    /**
     * Notes: 签名记录列表接口
     * Author: zhhj
     * Date: 2021/2/1
     */
    public function actionSignOffList() {
        $expId = \Yii::$app->request->get('experiment_id');

        $listRes = (new HistoryServer())->historyList($expId);
        $list = @getVar($listRes['data'], []);

        $hisList = @getVar($list['list'], []);
        $userList = @getVar($list['userList'], []);

        // 筛选出签名记录的痕迹
        $hisActionCodes = \Yii::$app->params['actionCode'];
        $signOffCodes = [
            $hisActionCodes['reopening'],
            $hisActionCodes['reopened'],
            $hisActionCodes['signed'],
            $hisActionCodes['waiting'],
            $hisActionCodes['refused'],
            $hisActionCodes['countersigned'],
            $hisActionCodes['pretrial_approved'],
            $hisActionCodes['pretrial_rejected'],
            $hisActionCodes['reopen_approved'],
            $hisActionCodes['reopen_rejected'],
            $hisActionCodes['signing'],
            $hisActionCodes['signing_approved'],
            $hisActionCodes['signing_rejected'],
            $hisActionCodes['coauthor_sign_approved'],
            $hisActionCodes['coauthor_sign_rejected']
        ];
        // 采用$signOffCodes判断是因为is_sign_off字段未对老数据初始化，因为history表可能很大
        $signOffList = array_filter($hisList, function ($item) use ($signOffCodes) {
            return !empty($item['is_sign_off']) || in_array($item['action_num'], $signOffCodes);
        });

        $file = $this->renderAjax('/popup/sign_off_list', [
            'list' => $signOffList,
            'userList' => $userList
        ]);
        return $this->success([
            'html' => $file
        ]);
    }

    /**
     * Notes: 获取实验中的评论、批注
     * Author: zhhj
     * Date: 2021/4/28
     */
    public function actionGetComments() {
        $expId = \Yii::$app->request->get('experiment_id');
        if (empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        $commentsRes = (new ExperimentServer())->getExpComments($expId);

        $file = $this->renderAjax('/popup/clear_comments', [
            'moduleList' => $commentsRes['data']
        ]);

        return $this->success([
            'html' => $file
        ]);
    }

    /**
     * Notes: 清除实验中的评论、批注
     * Author: zhhj
     * Date: 2021/4/28
     */
    public function actionClearComments() {
        $expId = \Yii::$app->request->post('experiment_id');
        if (empty($expId)) {
            return $this->fail(\Yii::t('exp', 'select_exp'));
        }

        $clearRes = (new ExperimentServer())->clearExpComments($expId);
        return $this->success([]);
    }

    /**
     * Notes: 获取实验内的链接
     * Author: jiangdm
     */
    public function actionGetExpLink()
    {
        $expId = \Yii::$app->request->post('experiment_id');
        $links = (new Link())->getLinks(1, $expId);
        $route = (new ExperimentServer())->getRoute($expId)['data'];
        if (isset($route['after_exps']) && !empty($route['after_exps'])) {
            foreach ($route['after_exps'] as $item) {
                $link = [
                    'id' => '0',
                    'save_type' => '1',
                    'target_type' => '10',
                    'target_name' => $item['num'],
                    'target_content' => $item['id'],
                    'url' => $item['id'],
                ];
                array_push($links, $link);
            }
        }
        if (isset($route['before_exps']) && !empty($route['before_exps'])) {
            foreach ($route['before_exps'] as $item) {
                $link = [
                    'id' => '0',
                    'save_type' => '1',
                    'target_type' => '10',
                    'target_name' => $item['num'],
                    'target_content' => $item['id'],
                    'url' => $item['id'],
                ];
                array_push($links, $link);
            }
        }
        return $this->success(['links' => $links]);
    }

    /**
     * Notes: 设置链接弹窗
     * Author: jiangdm
     * Date: 2022/6/9
     */
    public function actionSetExpLink() {
        $expId = \Yii::$app->request->post('experiment_id');

        $links = (new Link())->getLinks(1, $expId);

        $file = $this->renderAjax('/popup/exp_link');

        return $this->success([
            'html' => $file,
            'links' => $links,
        ]);
    }

    /**
     * Notes: 保存来自实验的链接
     * Author: jiangdm
     */
    public function actionSaveExpLink() {
        $expLinks = \Yii::$app->request->post('exp_links');
        $chemLinks = \Yii::$app->request->post('chemical_links');
        $expId = \Yii::$app->request->post('exp_id', 0);
        (new ExperimentServer())->saveLink($expLinks);
        (new CenterInterface())->syncChemicalLinks($chemLinks);
        if ($expId > 0) {
            $links = (new Link())->getLinks(1, $expId);
            return $this->success(['links' => $links]);
        }
    }

    /**
     * Notes: 删除链接
     * Author: jiangdm
     */
    public function actionDeleteLink() {
        $linkId = \Yii::$app->request->post('link_id');
        if (empty($linkId)) {
            return $this->fail(Yii::t('base', 'params_error'));
        }

        Link::updateAll(['status' => 0], ['id' => $linkId]);

        // 删除时 更新实验表的 incms_link_status
        $linkRes = Link::find()->where(['id' => $linkId])->asArray()->one();
        if (!empty($linkRes) && $linkRes['source_type'] == 1 && $linkRes['target_type'] == 30) {
            $expId = $linkRes['source_id'];
            $inCMSLinkRes = Link::find()->where(
                ['source_type' => 1, 'target_type' => 30, 'source_id' => $expId, 'status' => 1]
            )->asArray()->all();
            // 若没有其他的化合物链接, 才更新
            if (empty($inCMSLinkRes)) {
                ExperimentModel::updateAll(['incms_link_status' => 0], ['id' => $expId]);
                ElnExperimentModel::updateAll(['incms_link_status' => 0], ['experiment_id' => $expId]);
            }
        }

        return $this->success([]);
    }

    /**
     * Notes: 同步扣减库存信息弹窗
     * Author: jiangdm
     * Date: 2022/3/7
     * @throws \Exception
     */
    public function actionSyncInventoryData() {
        $source = \Yii::$app->request->post('source');
        if ($source == 'module'){
            // 获取模块信息
            $relayId = \Yii::$app->request->post('relay_id');
            $expComponents = ExperimentRelayModel::find()->where(['id'=>$relayId])->asArray()->all();
            $expId = $expComponents[0]['experiment_id'];
        } else {
            // 实验复核关闭
            // 判断是否需要在复核关闭实验前查看库存待扣减物品
            $needConfirm = CompanySettingModel::findOne(['company_id'=>\Yii::$app->view->params['curr_company_id'],'key'=>'SYNC_INVENTORY_CONFIRM','status'=>1]);
            if (empty($needConfirm['value'])) return $this->fail('');
            $expId = \Yii::$app->request->post('exp_id');
            if (count($expId) > 1) {
                $html = $this->renderAjax('module/sync_inventory.php', [
                    'showDetail' => 0,
                    'source' => $source,
                ]);
                return $this->success(['file' => $html]);
            }
            $expComponents = ExperimentRelayModel::find()->where(['experiment_id' => $expId, 'status' => 1])->asArray()->all();
            $relayId = array_column($expComponents, 'id');
            $expId = $expId[0];
        }
        $expComponentNames = array_column($expComponents, 'name', 'id');
        // 查询indraw物料表和自定义表格中的信息
        $chemInfo = ChemModel::find()->from(ChemModel::tableName(). ' as c')
            ->innerJoin(SubstrateModel::tableName() . ' as s', 'c.id = s.chem_id')
            ->select('c.id item_id, c.parent_id relay_id, c.mass_unit item_mass_unit, c.volume_unit item_volume_unit, c.solvent_mass_unit, c.solvent_volume_unit,
                     s.id sub_id, s.wms_batch_id, s.mass item_mass, s.volume item_volume, s.part sub_part, s.name item_name, s.salt, s.salt_eq')
            ->where(['c.parent_id' => $relayId, 'c.type' => 1, 'c.status' => 1, 's.status' => 1])->asArray()->all();
        $chemBatchIds = array_filter(array_column($chemInfo, 'wms_batch_id'));
        $defineInfo = DefineTableKeyModel::find()->from(DefineTableKeyModel::tableName(). ' as k')
            ->innerJoin(DefineTableValueModel::tableName(). ' as v', 'k.id = v.define_key_Id')
            ->select('k.id item_id, k.parent_id relay_id, v.id sub_id, v.data3, v.wms_batch_id, v.data1 item_name')
            ->where(['k.parent_id' => $relayId, 'k.type' => 1, 'k.status' => 1])->asArray()->all();
        // 调用接口，根据批次id获取库存信息
        $defineBatchIds = array_filter(array_column($defineInfo, 'wms_batch_id'));
        $batchIds = array_unique(array_merge($defineBatchIds, $chemBatchIds));
        if (empty($batchIds)) {
            if ($source == 'module') {
                $html = $this->renderAjax('module/sync_inventory.php', [
                    'showDetail' => 1,
                    'productList' => [],
                    'source' => $source,
                ]);
                return $this->success(['file' => $html]);
            } else {
                return $this->fail('');
            }
        }
        // 校验是否开通库存权限
        $isWms = (new CenterInterface())->isWmsByUserId($this->userinfo->id);
        if($isWms['status'] == 2){
            return $this->fail(\Yii::t('exp', 'wms_authority_disabled_tip'));
        }
        $wmsInfo = (new CenterInterface())->searchWms($this->userinfo->id, '', '', 0, 1, $batchIds);
        $batchList = $wmsInfo['product_list'];
        $batchList = array_column($batchList, null, 'batch_id');
        $updateLog = $this->wmsSyncLog($relayId);
        // 单位基本配置
        $moduleMassMap = ['','mg','g','kg','ton'];
        $moduleVolumeMap = ['','μL','mL','L','kL'];
        $massUnits = ['μg' => 0, 'mg' => 1, 'g' => 2, 'kg' => 3, 't' => 4];
        $volumeUnits = ['μl' => 1, 'ml' => 2, 'l' => 3];
        // indraw物料数据处理
        $relayBatches = [];
        $productList = [];
        foreach ($chemInfo as $chem) {
            $chemBatchId = $chem['wms_batch_id'];
            // changed by xyx 2023.8.10 批次数据为空，证明wms没收到该批次，可能被删除了，可能就不存在，则continue
            if (empty($chemBatchId) || empty($batchList[$chemBatchId])) {
                continue;
            }
            $chemRelayId = $chem['relay_id'];
            $product = array_merge($chem, $batchList[$chemBatchId]);
            $product['log'] = !isset($updateLog[$chemRelayId][$chemBatchId]) ? [] : $updateLog[$chemRelayId][$chemBatchId];
            // 判断该行质量/体积单位
            $productMassUnit = $chem['sub_part'] == 3 ? $product['solvent_mass_unit'] : $product['item_mass_unit'];
            // $productMassUnit = $chem['sub_part'] == 3 ? $product['solvent_mass_unit'] : $massUnits['g']; // bug#8929, eln实验质量的计算基准总是以g为基准, 不再以产物计算为基准. 2024/08/07 mod dx
            $productVolumeUnit = $chem['sub_part'] == 3 ? $product['solvent_volume_unit'] : $product['item_volume_unit'];
            if (!isset($relayBatches[$chemRelayId]) || !isset($relayBatches[$chemRelayId][$chemBatchId])) {
                // 模块内同一批次第一次出现处理，需要确定展示的单位类别
                $proUnit = $product['single_bottle_volume_unit_name'];
                $productUnit = strtolower($proUnit);
                // 如果绑定批次在库存的单位为体积或质量，以库存单位为准
                if (isset($massUnits[$productUnit])) {
                    $unitType = 1; // 单位类型 1质量 2体积
                    $unitLevel = $massUnits[$productUnit]; // 单位级别
                    $unitName = $productUnit; // 单位名称
                } elseif (isset($volumeUnits[$productUnit])) {
                    $unitType = 2;
                    $unitLevel = $volumeUnits[$productUnit];
                    $unitName = $productUnit;
                    // 如果绑定批次在库存的单位为其他，以物料标表批次第一次出现的单位为准
                } elseif (!empty($product['item_mass'])) {
                    $unitType = 1;
                    $unitLevel = $productMassUnit;
                    $unitName = $moduleMassMap[$productMassUnit];
                } else {
                    $unitType = 2;
                    $unitLevel = $productVolumeUnit;
                    $unitName = $moduleVolumeMap[$productVolumeUnit];
                }
                // 单位换算
                $baseAmount = $unitType == 1 ? $product['item_mass'] : $product['item_volume'];
                $baseLevel = $unitType == 1 ? $productMassUnit : $productVolumeUnit;
                // $product['used_mass'] = floatval(bcmul($baseAmount, bcpow(10, ($baseLevel - $unitLevel) * 3, 8), 8));


                //<editor-fold desc="bug#8933, 将物料的量根据物料表小数点设置进行约入, 以保持和物料表显示的内容一致">

                // 物料的量, 可能是质量或体积
                $substrateUsedFdKey = ($unitType === 1) ? 'mass' : 'volume';
                $substrateUsedQuantum = bcmul($baseAmount, bcpow(10, ($baseLevel - $unitLevel) * 3, 8), 8);
                $substrateMaterial = (new StoiSubs())->setupBySubstrateModel($chem, function ($subs) use ($substrateUsedFdKey, $substrateUsedQuantum) {
                    $subsValues = [$substrateUsedFdKey => $substrateUsedQuantum];
                    return [
                        'subs_type' => StoiSubs::getSubstanceTypeByMaterialPart($subs['sub_part']),
                        'subs_values' => $subsValues,
                    ];
                });
                $product['used_mass'] = $substrateMaterial->getValueByFmtFn($substrateUsedFdKey, [StoiSubs::class, 'getFmtValue'], ['default_val' => 0]);
                //</editor-fold>

                $product['used_unit'] = $unitName;
                // 弹窗信息同步，缓存单位及列表位置信息
                $productList[] = $product;
                $relayBatches[$chemRelayId][$chemBatchId]['product_index'] = count($productList) - 1;
                $relayBatches[$chemRelayId][$chemBatchId]['unit_type'] = $unitType;
                $relayBatches[$chemRelayId][$chemBatchId]['unit_level'] = $unitLevel;
            } else {
                // 模块内同一批次重复出现处理，读取缓存里面的配置
                $batchInfo = $relayBatches[$chemRelayId][$chemBatchId];
                $unitType = $batchInfo['unit_type'];
                $unitLevel = $batchInfo['unit_level'];

                $_substrateUsedFdKey2 = ($unitType == 1) ? 'mass' : 'volume';
                // 物料待扣减的字段对应的小数位数
                $_substrateUsedFdDecimal = StoiSubs::getChemDecimalBySubstanceTypeAndFdKey(
                    StoiSubs::getSubstanceTypeByMaterialPart($chem['sub_part']),
                    $_substrateUsedFdKey2
                );

                // 单位换算，累加
                $baseAmount = $unitType == 1 ? $product['item_mass'] : $product['item_volume'];
                $baseLevel = $unitType == 1 ? $productMassUnit : $productVolumeUnit;
                // $product['used_mass'] = floatval(bcpow($baseAmount, bcpow(10, ($baseLevel - $unitLevel) * 3, 8), 8));
                $_substrateUsedQuantum2 = bcmul($baseAmount, bcpow(10, ($baseLevel - $unitLevel) * 3, 8), 8);
                $product['used_mass'] = floatval($_substrateUsedQuantum2);
                $productList[$relayBatches[$chemRelayId][$chemBatchId]['product_index']]['used_mass'] = bcadd($_substrateUsedQuantum2, $productList[$batchInfo['product_index']]['used_mass'], $_substrateUsedFdDecimal);

            }
        }
        // 自定义表格数据处理
        foreach ($defineInfo as $define) {
            $defineBatchId = $define['wms_batch_id'];
            if (empty($defineBatchId)) continue;
            $defineRelayId = $define['relay_id'];
            if (isset($batchList[$defineBatchId])) {
                $product = array_merge($define, $batchList[$defineBatchId]);
            }
            else {
                continue;
            }
            $product['log'] = !isset($updateLog[$defineRelayId][$defineBatchId]) ? [] : $updateLog[$defineRelayId][$defineBatchId];
            // 正则匹配数量和单位
            preg_match('/(^([0-9]+([\.][0-9]*)?)(.*)$)/', $product['data3'], $userData);
            $userMass = empty($userData) ? 0 : $userData[2];
            $userUnit = empty($userData) ? '' : strtolower($userData[count($userData) - 1]);

            if (!isset($relayBatches[$defineRelayId]) || !isset($relayBatches[$defineRelayId][$defineBatchId])) {
                $proUnit = $product['single_bottle_volume_unit_name'];
                $productUnit = strtolower($proUnit);
                if (isset($massUnits[$productUnit])) {
                    $unitType = 1;
                    $unitLevel = $massUnits[$productUnit];
                    $unitName = $productUnit;
                } elseif (isset($volumeUnits[$productUnit])) {
                    $unitType = 2;
                    $unitLevel = $volumeUnits[$productUnit];
                    $unitName = $productUnit;
                } else {
                    // 自定义表格，物品在库存的单位不属于质量/体积的，同一批次按照第一个遇到的单位来统计
                    $unitType = 3;
                    $unitLevel = 0;
                    $unitName = $userUnit;
                }
                switch ($unitType) {
                    case 1:
                        $userAmount = isset($massUnits[$userUnit]) ? floatval(bcmul($userMass, bcpow(10, ($massUnits[$userUnit] - $unitLevel) * 3, 8), 8)) : 0;
                        break;
                    case 2:
                        $userAmount = isset($volumeUnits[$userUnit]) ? floatval(bcmul($userMass, bcpow(10, ($volumeUnits[$userUnit] - $unitLevel) * 3, 8), 8)) : 0;
                        break;
                    default:
                        $userAmount = $userMass;
                }

                $product['used_mass'] = $userAmount;
                $product['used_unit'] = $unitName;
                $productList[] = $product;
                $relayBatches[$defineRelayId][$defineBatchId]['product_index'] = count($productList) - 1;
                $relayBatches[$defineRelayId][$defineBatchId]['unit_type'] = $unitType;
                $relayBatches[$defineRelayId][$defineBatchId]['unit_level'] = $unitLevel;
                $relayBatches[$defineRelayId][$defineBatchId]['unit_name'] = $unitName;
            } else {
                $batchInfo = $relayBatches[$defineRelayId][$defineBatchId];
                $unitType = $batchInfo['unit_type'];
                $unitLevel = $batchInfo['unit_level'];
                $unitName = $batchInfo['unit_name'];
                switch ($unitType) {
                    case 1:
                        $userAmount = isset($massUnits[$userUnit]) ? floatval(bcmul($userMass, bcpow(10, ($massUnits[$userUnit] - $unitLevel) * 3, 8), 8)) : 0;
                        break;
                    case 2:
                        $userAmount = isset($volumeUnits[$userUnit]) ? floatval(bcmul($userMass, bcpow(10, ($volumeUnits[$userUnit] - $unitLevel) * 3, 8), 8)) : 0;
                        break;
                    default:
                        // 自定义表格，物品在库存的单位不属于质量/体积的，单位与第一个相同才累加
                        $userAmount = $unitName == $userUnit ? $userMass : 0;
                }
                $productList[$relayBatches[$defineRelayId][$defineBatchId]['product_index']]['used_mass'] = bcadd($userAmount, $productList[$batchInfo['product_index']]['used_mass']);
            }
        }

        $html = $this->renderAjax('module/sync_inventory.php', [
            'productList' => $productList,
            'source' => $source,
            'showDetail' => 1,
            'expId' => $expId,
        ]);

        return $this->success([
            'show_detail' => 1,
            'file' => $html
        ]);
    }

    /**
     * Notes: ELN同步扫码出库
     * Author: jiangdm
     * Date: 2022/3/7
     */
    public function actionSyncInventoryOut() {
        $data = Yii::$app->getRequest()->post();
        // 获取并拼接实验信息，调用接口推送扫码出库
        $expInfo = ExperimentModel::find()->select('E.exp_page, E.project_id, B.book_code')
            ->from(ExperimentModel::tableName() . ' as E')
            ->innerJoin(BookModel::tableName() . ' as B', 'E.book_id = B.id')
            ->where(['E.id' => $data['exp_id']])->asArray()->one();
        $projectId = $expInfo['project_id'];
        $data['user_id'] = $this->userinfo->id;
        $data['real_name'] = $this->userinfo->real_name;
        $data['exp_name'] = $expInfo['book_code']. '-' . sprintf('%03s', $expInfo['exp_page']);
        $data['project_id'] = empty($projectId) ? 0 : $projectId;
        $projectTaskRes = (new PMInterface())->getProjectsAndTasksByIds([$projectId]);
        $projectList = @getVar($projectTaskRes['projects'], []);
        $projectList = ArrayHelper::index($projectList, 'id');
        $data['project_name'] = @getVar($projectList[$projectId]['name']);
        $syncResult = (new CenterInterface())->syncInventoryOut($data);
        // 返回数据处理，写入日志记录，并返回前端
        foreach ($syncResult['data'] as $result) {
            $logInfo = [];
            $logData = $result['data'];
            $logInfo['batch_id'] = $logData['batch_id'];
            $logInfo['relay_id'] = $logData['relay_id'];
            $logInfo['inventory_id'] = $logData['inventory_id'];
            $status = $result['status'];
            $logInfo['status'] = $status;
            $logInfo['amount'] = empty($status) ? 0 : $logData['reduced_mass'];
            $logInfo['unit'] = empty($status) ? '' : strtolower($logData['reduced_unit']);
            // 日志详情处理
            if (empty($status)) {
                if (isset($logData['err_no'])) {
                    $logInfo['remark'] = \Yii::t('exp', 'wms_sync_remark_' . $logData['err_no'], [], 'zh-CN');
                    $logInfo['remark_en'] = \Yii::t('exp', 'wms_sync_remark_' . $logData['err_no'], [], 'en-US');
                    $logInfo['error_type'] = $logData['err_no'];
                } else {
                    $logInfo['remark'] = $result['message'];
                    $logInfo['error_type'] = -1;
                }
            } else {
                $logInfo['remark'] = \Yii::t('exp', 'deduce', [], 'zh-CN') . $logInfo['amount'] .$logInfo['unit'];
                $logInfo['remark_en'] = \Yii::t('exp', 'deduce', [], 'en-US') . $logInfo['amount'] .$logInfo['unit'];
                $logInfo['error_type'] = 0;
            }
            $logModel = new WmsSyncOutLog();
            $logModel->setAttributes($logInfo);
            if (!$logModel->save()) {
                return $this->fail($logModel->getFirstErrors());
            }
        }
        $updateRelays = array_unique(array_column($data['batch_info'], 'relay_id'));
        $updateResult = $this->wmsSyncLog($updateRelays);
        foreach ($syncResult['data'] as $result) {
            $logData = $result['data'];
            if ($result['status'] == 1) {
                $updateResult[$logData['relay_id']][$logData['batch_id']]['available_mass_value'] = $logData['available_mass_value'];
            }
        }
        return $this->success($updateResult);
    }

    /**
     * Notes: 获取模块下的所有库存同步日志
     * Author: jiangdm
     * Date: 2022/3/25
     */
    public function wmsSyncLog($relayId) {
        $updateLog = (new WmsSyncOutLog())->find()->where(['relay_id'=>$relayId])->asArray()->all();
        $updateResult = [];
        foreach ($updateLog as $log) {
            $parentId = $log['relay_id'];
            $batchId = $log['batch_id'];
            if (!isset($updateResult[$parentId])) $updateResult[$parentId] = [];
            if (!isset($updateResult[$parentId][$batchId])) {
                $updateResult[$parentId][$batchId] = ['amount' => 0, 'log' => [], 'unit' => ''];
            }
            if ($log['status'] == 1) {
                $updateResult[$parentId][$batchId]['amount'] = bc_add($log['amount'], $updateResult[$parentId][$batchId]['amount']);
                $updateResult[$parentId][$batchId]['unit'] = $log['unit'];
            }
            if (\Yii::$app->language == 'en-US') $log['remark'] = $log['remark_en'];
            $updateResult[$parentId][$batchId]['log'][] = $log;
        }
        return $updateResult;
    }

    /**
     * Notes: ELN同步扫码领料
     * Author: jiangdm
     * Date: 2022/3/25
     */
    public function actionSyncInventoryCart() {
        $data = Yii::$app->getRequest()->post();
        $data['user_id'] = $this->userinfo->id;
        $data['real_name'] = $this->userinfo->real_name;
        $addResult = (new CenterInterface())->syncInventoryCart($data);
        $addResult = $addResult['data'];
        $relayId = $addResult['relay_id'];
        $batchId = $addResult['batch_id'];
        $logInfo = [
            'batch_id' => $batchId,
            'relay_id' => $relayId,
            'inventory_id' => $addResult['inventory_id'],
            'status' => 0,
            'amount' => 0,
            'unit' => '',
            'remark' => \Yii::t('exp', 'wms_sync_remark_' . (empty($addResult['successRecords']) ? '7' : '8')),
            'error_type' => empty($addResult['successRecords']) ? 7 : 8,
        ];
        $logModel = new WmsSyncOutLog();
        $logModel->setAttributes($logInfo);
        if (!$logModel->save()) {
            return $this->fail($logModel->getFirstErrors());
        }
        $updateResult = $this->wmsSyncLog($relayId);
        $updateResult[$relayId][$batchId]['available_mass_value'] = $addResult['available_mass_value'];
        return $this->success($updateResult);
    }

    /**
     * Notes: 记录痕迹
     *
     * Author: zhu huajun
     * Date: 2020/4/29 16:43
     * @param $tempId
     * @param $type
     */
    private function _addHistory($tempId, $type,$transferToValue='') {
        // 获取数据

        $tempResult = (new TempleServer())->getTempById($tempId, -1);
        $tempHtml = $this->renderAjax('/template/history_detail', $tempResult['data']);
        (new TempleServer())->addHistory($tempId, $type, $tempHtml,'',$transferToValue);
    }

    /**
     * Note: 更新实验模板
     * @return void
     * <AUTHOR>
     * @date 2023/3/3 15:15
     */
    public function actionReplaceTemplate() {
        $expId = \Yii::$app->request->post('exp_id');
        $templateId = \Yii::$app->request->post('template_id');

        $result = (new ExperimentServer())->replaceTemplate($expId, $templateId);
        if ($result['status'] != 1) {
            return $this->fail($result['info']);
        }

        return $this->success([]);
    }

    /**
     * Note: 粘贴实验模块
     * @return \common\controllers\json
     * <AUTHOR>
     * @date 2023/3/22 9:41
     */
    public function actionPasteModule() {
        $expId = \Yii::$app->request->post('exp_id');
        $relayId = \Yii::$app->request->post('relay_id');
        $position = \Yii::$app->request->post('position');

        $expServer = new ExperimentServer();
        $result = $expServer->pasteModule($expId, $relayId, $position);
        $newRelayId = $result['data']['relay_id'];

        // 获取模块数据
        $moduleResult = $expServer->getModuleData($newRelayId);
        if (empty($moduleResult['status'])) {
            return $this->fail($moduleResult['info']);
        }
        $module = $moduleResult['data'];

        // 渲染模块html
        $viewData = [
            'module' => $module,
            'experimentId' => $expId,
            'tmpPower' => 1,
            'module_edit' => 1,
        ];
        $viewFile = '';
        switch ($module['info']['component_id']) {
            case \Yii::$app->params['component']['chem']: // InDraw
                // 获取盐型列表
                $saltList = (new ExperimentServer())->saltList();
                $viewData['saltList'] = $saltList['data'];
                $viewFile = '/experiment/module/indraw_v2.php';
                break;
            case \Yii::$app->params['component']['operation']:
            case \Yii::$app->params['component']['discuss']:
            case \Yii::$app->params['component']['lite']:
            case \Yii::$app->params['component']['abstract']:
                $viewFile = '/experiment/module/editor.php';
                break;
            case \Yii::$app->params['component']['tlc']:
                $viewFile = '/experiment/module/tlc.php';
                break;
            case \Yii::$app->params['component']['reference']:
                $viewFile = '/experiment/module/reference.php';
                break;
            case \Yii::$app->params['component']['biology']:
                $viewFile = '/experiment/module/biology.php';
                break;
            case \Yii::$app->params['component']['upload']:
                $viewFile = '/experiment/module/file.php';
                break;
            case \Yii::$app->params['component']['picture']:
                $viewFile = '/experiment/module/image.php';
                break;
            case \Yii::$app->params['component']['comment']:
                $viewFile = '/experiment/module/comment.php';
                break;
            case \Yii::$app->params['component']['define_table']:
                $viewFile = '/experiment/module/define.php';
                break;
            case \Yii::$app->params['component']['custom_table']:
                $viewFile = '/experiment/module/custom_table.php';
                break;
            case \Yii::$app->params['component']['excel']:
                $viewFile = '/experiment/module/spreadsheet.php';
                break;
            case \Yii::$app->params['component']['xsheet']:
                $viewFile = '/experiment/module/xsheet.php';
                break;
        }

        \Yii::$app->view->params['edit'] = 1;
        \Yii::$app->view->params['exp_id'] = $expId;
        $moduleHtml = $this->renderAjax($viewFile, $viewData);
        return $this->success($moduleHtml);
    }

    /**
     * Note: 根据指定的痕迹恢复实验内容
     * @return \common\controllers\json
     * <AUTHOR>
     * @date 2023/5/31 14:19
     */
    public function actionRecoverFromHistory() {
        $expId = \Yii::$app->request->post('experiment_id');
        $hisId = \Yii::$app->request->post('history_id');
        $result = (new ExperimentServer())->recoverExpFromHistory($expId, $hisId);
        if ($result['status'] != 1) {
            return $this->fail($result['info']);
        }

        return $this->success([]);
    }


    /**
     * 根据实验 id, 获取 ai 对话需要的上下文信息
     * @return \common\controllers\json
     */
    public function actionGetReferenceContextForAi()
    {
        $expIds = \Yii::$app->request->post('experiment_ids', []);
        $curUid = $this->userinfo->id;

        $res = [];
        $experimentServer = new ExperimentServer();
        foreach ($expIds as $expId) {
            $referenceContext = $experimentServer->getExpStructureInfoForAI($expId, $curUid);
            $res[] = $referenceContext;
        }

        return $this->success($res);
    }
}
