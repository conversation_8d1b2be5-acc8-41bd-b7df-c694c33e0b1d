a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:59:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:53:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:57:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:12:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:4:"2683";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:200:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; page_type=0; last_active_time=1750298678225";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:20:"template/update-temp";s:6:"action";s:59:"frontend\controllers\TemplateController::actionUpdateTemp()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:2683:"insertData%5Bname%5D=%E6%95%B0%E6%8D%AE%E8%A7%86%E5%9B%BEen&insertData%5Btfrom%5D=2&insertData%5Btype%5D=1&insertData%5Bdescript%5D=%E7%BB%9F%E5%80%9F%E7%BB%9F%E8%BF%98%E6%9F%93%E5%8F%91%E8%86%8F&insertData%5Bstatus%5D=1&insertData%5Bdefine_item%5D=%5B%5D&insertData%5Bsubtype_id%5D=3&insertData%5Bgroup_ids%5D%5B%5D=598&temp_id=4406&auto_save=1&base_data%5Btemplate_id%5D=4406&base_data%5Bkeywords%5D=&base_data%5Btitle%5D=&base_data%5Bweather_json%5D=%7B%22create_weather%22%3A%22%22%2C%22create_temperature%22%3A%22%22%2C%22create_humidity%22%3A%22%22%7D&base_data%5Bdefine_item%5D=%5B%5D&module_data=H4sIAAAAAAAAA2VUS6%2Bj2Bn8L2y5Ei9joHc2YPO2wWAD3a0rHoeXDw%2FzMnbrSrOaXTSKokgjzaKX2UWzHyn5Nd3TP2Nw7u1RlLBAh6qiTtXHEZ%2BQvOpA2wtBHyDvPiFVUALkHfL1779%2B%2Fcs%2Fv%2F3jxy%2B%2F%2FAtUyBPSJ21dzgT5WN%2BaWUM8ITHoojZv%2Bhn%2F%2FbfPX374PN%2B%2F%2Ffvnr5%2F%2F9uWnv3778adZ2%2FVBP3Rv6iSvwHPeg4fR%2B48PdggfZs95PCPUDKRtPTTz4%2FzGe4TmWOTjy7wfKB%2FYLFks8OWsCoa%2Bfu6C8TVFGHTgOX7L%2F9DCoH%2BzfNOfwe1atw9T5BE%2F7%2BGj47y8gqDPQPtcdHU1I58%2BIFE7Q%2BD5jfiAvPuAfECe%2FsQf9qCdK7Xg%2F7hsKPM4729vxMvsH4P%2FqTy3Ket4gN8Dv%2F80zz%2BpH5OP6rKpK1D1%2F6lKcE%2F%2FhbxO%2FJH47fvIlR2Ec4snZM4LX4dDEDRNzAiYmue3TVoAg9ur34OcLWHQvX6NqK6SPH0tnYE8zfo59xLHX5A545%2FTbINo3vj9xyckySGQgo7PgiqdoSSAHfguRKRFJ6%2B%2BXyhxupNsaFk26qIBu%2Bb6CW9VylNN0oEbKx1W5jH2cW5BUJ4USmVDEsGB0CRFlJ02xw97m08Pqllv5HEt206yDHZsmVzHGMYANUxfXdQZfSSp6j7SzESzN0JUW9xhAeQVw%2FcUUs7kRmqgaEf54nCPJokCWCGEjePeDzJUDmNRoBS7H6BC51t0u4DbXauVDk0tbd2iUrjMONEWeXprl6xw8bJK39byhF1Yo6UTLx0B3nF9vG%2B9QXFjR9107Y1tKqKssEyJvSWradMi3CoWVDEtd3QqE8Pac9LsuhNyeaJYu2Qsa202PtGVQr3TQDdoAKPQVWi6mFdcD3g5HLd9TEgbR1PsOrFYY1sGasLg5vZEruu2JExolmfqxErr8ygdFNXQg8YuN2IfJxerIipMVz3oHY7GzHmx6MerAOwVw6rcYwA3cr64eHePOB%2BuXcPH0D0w7nQSMTt2Ud6TdKdQi8tokuFSueBC4R%2BlwWA5kmzoiRsKZmULF6fqDTooCVRTt%2Bu4lmxXkhlWDHwyOXMNp0tDLjjrZoyggpo4XletweOHfHMP5Nwu9ILCg2B52JUargIIaUbje9eUHWlR%2Bk0NSYZY9VdfJXYeb3kLfpdNG%2FFMhlVf6DsB3NmwHati1FLbLa%2B9Vfh4R2Y3R742CoEf94SQT2ho3%2Bwoqnz9LmxtCJn9JOShp7UmlqFjnfnlQhWDfeUPW1TWQy2nOTdhaLFewcuwXqkG4Ub1Qo2M9ZpXco%2FP6vVYh%2BmGZ7ZkFjOAlFpyQluW5IiOiIXTJtcu0%2BokqIIhERzHxxrJjW4%2BnW%2FjtneXacJR%2B3rv3UlRwbHghoJwaUtsQCQD3aFc2WJg6uWwQduKZjtaT6b2iOorcqAXuuAXmKBFByLRafK%2BAgs6ka0KCzHgCi7OmC4XtjZUALXAZKyIxrUP7znbhoV43vVbPzLjzs%2F9uxDHfdGnzN1Omdt82Hmd8bt4PWyZXX6PGN8rj%2FkJ1spat0ydOu8rB18ZsV5DL13PB%2Bxcoy6jZ4wHmzzMWDYaL%2B2Nc%2FcUaagqAxIJI65cFeYENo1Yy3HgQnXsMN7Px5qWwCXBgHoF0YhNBMkbAoOKwUq2fR8WoeVtzIqCJ6WdrhiXypZtkqKFX28rbamgFx5ShmyNaKpfezQLKOVUnifTz0GjldJwMyHdLvpVI47lno1uROKdcWNaxVDtN8L5IqYtflnpe2JoUnN9NO%2F0IF83OLDLodx4Ad8M6pZn73l9VR2lvWNAsoPJV%2F2Y1PqNdhBt%2ByBtvALaShQwe8dcnijxKPCFH1dcNtQUZYQ%2B39wO3WRy45bGVqgsA2cZC%2FP%2FEHl5%2BfjyB2bpyuJjBwAA&encode_type=gzip";s:17:"Decoded to Params";a:6:{s:10:"insertData";a:8:{s:4:"name";s:14:"数据视图en";s:5:"tfrom";s:1:"2";s:4:"type";s:1:"1";s:8:"descript";s:21:"统借统还染发膏";s:6:"status";s:1:"1";s:11:"define_item";s:2:"[]";s:10:"subtype_id";s:1:"3";s:9:"group_ids";a:1:{i:0;s:3:"598";}}s:7:"temp_id";s:4:"4406";s:9:"auto_save";s:1:"1";s:9:"base_data";a:5:{s:11:"template_id";s:4:"4406";s:8:"keywords";s:0:"";s:5:"title";s:0:"";s:12:"weather_json";s:66:"{"create_weather":"","create_temperature":"","create_humidity":""}";s:11:"define_item";s:2:"[]";}s:11:"module_data";s:1920:"H4sIAAAAAAAAA2VUS6+j2Bn8L2y5Ei9joHc2YPO2wWAD3a0rHoeXDw/zMnbrSrOaXTSKokgjzaKX2UWzHyn5Nd3TP2Nw7u1RlLBAh6qiTtXHEZ+QvOpA2wtBHyDvPiFVUALkHfL1779+/cs/v/3jxy+//AtUyBPSJ21dzgT5WN+aWUM8ITHoojZv+hn//bfPX374PN+//fvnr5//9uWnv3778adZ2/VBP3Rv6iSvwHPeg4fR+48PdggfZs95PCPUDKRtPTTz4/zGe4TmWOTjy7wfKB/YLFks8OWsCoa+fu6C8TVFGHTgOX7L/9DCoH+zfNOfwe1atw9T5BE/7+Gj47y8gqDPQPtcdHU1I58+IFE7Q+D5jfiAvPuAfECe/sQf9qCdK7Xg/7hsKPM4729vxMvsH4P/qTy3Ket4gN8Dv/80zz+pH5OP6rKpK1D1/6lKcE//hbxO/JH47fvIlR2Ec4snZM4LX4dDEDRNzAiYmue3TVoAg9ur34OcLWHQvX6NqK6SPH0tnYE8zfo59xLHX5A545/TbINo3vj9xyckySGQgo7PgiqdoSSAHfguRKRFJ6++XyhxupNsaFk26qIBu+b6CW9VylNN0oEbKx1W5jH2cW5BUJ4USmVDEsGB0CRFlJ02xw97m08Pqllv5HEt206yDHZsmVzHGMYANUxfXdQZfSSp6j7SzESzN0JUW9xhAeQVw/cUUs7kRmqgaEf54nCPJokCWCGEjePeDzJUDmNRoBS7H6BC51t0u4DbXauVDk0tbd2iUrjMONEWeXprl6xw8bJK39byhF1Yo6UTLx0B3nF9vG+9QXFjR9107Y1tKqKssEyJvSWradMi3CoWVDEtd3QqE8Pac9LsuhNyeaJYu2Qsa202PtGVQr3TQDdoAKPQVWi6mFdcD3g5HLd9TEgbR1PsOrFYY1sGasLg5vZEruu2JExolmfqxErr8ygdFNXQg8YuN2IfJxerIipMVz3oHY7GzHmx6MerAOwVw6rcYwA3cr64eHePOB+uXcPH0D0w7nQSMTt2Ud6TdKdQi8tokuFSueBC4R+lwWA5kmzoiRsKZmULF6fqDTooCVRTt+u4lmxXkhlWDHwyOXMNp0tDLjjrZoyggpo4XletweOHfHMP5Nwu9ILCg2B52JUargIIaUbje9eUHWlR+k0NSYZY9VdfJXYeb3kLfpdNG/FMhlVf6DsB3NmwHati1FLbLa+9Vfh4R2Y3R742CoEf94SQT2ho3+woqnz9LmxtCJn9JOShp7UmlqFjnfnlQhWDfeUPW1TWQy2nOTdhaLFewcuwXqkG4Ub1Qo2M9ZpXco/P6vVYh+mGZ7ZkFjOAlFpyQluW5IiOiIXTJtcu0+okqIIhERzHxxrJjW4+nW/jtneXacJR+3rv3UlRwbHghoJwaUtsQCQD3aFc2WJg6uWwQduKZjtaT6b2iOorcqAXuuAXmKBFByLRafK+Ags6ka0KCzHgCi7OmC4XtjZUALXAZKyIxrUP7znbhoV43vVbPzLjzs/9uxDHfdGnzN1Omdt82Hmd8bt4PWyZXX6PGN8rj/kJ1spat0ydOu8rB18ZsV5DL13PB+xcoy6jZ4wHmzzMWDYaL+2Nc/cUaagqAxIJI65cFeYENo1Yy3HgQnXsMN7Px5qWwCXBgHoF0YhNBMkbAoOKwUq2fR8WoeVtzIqCJ6WdrhiXypZtkqKFX28rbamgFx5ShmyNaKpfezQLKOVUnifTz0GjldJwMyHdLvpVI47lno1uROKdcWNaxVDtN8L5IqYtflnpe2JoUnN9NO/0IF83OLDLodx4Ad8M6pZn73l9VR2lvWNAsoPJV/2Y1PqNdhBt+yBtvALaShQwe8dcnijxKPCFH1dcNtQUZYQ+39wO3WRy45bGVqgsA2cZC/P/EHl5+fjyB2bpyuJjBwAA";s:11:"encode_type";s:4:"gzip";}}s:6:"SERVER";a:45:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:4:"2683";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:200:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; center_language=CN; page_type=0; last_active_time=1750298678225";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:52:"D:/integle2025/eln_5.3.11_dev/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"64283";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:22:"r=template/update-temp";s:11:"REQUEST_URI";s:24:"/?r=template/update-temp";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1750298713.8280001;s:12:"REQUEST_TIME";i:1750298713;}s:3:"GET";a:1:{s:1:"r";s:20:"template/update-temp";}s:4:"POST";a:6:{s:10:"insertData";a:8:{s:4:"name";s:14:"数据视图en";s:5:"tfrom";s:1:"2";s:4:"type";s:1:"1";s:8:"descript";s:21:"统借统还染发膏";s:6:"status";s:1:"1";s:11:"define_item";s:2:"[]";s:10:"subtype_id";s:1:"3";s:9:"group_ids";a:1:{i:0;s:3:"598";}}s:7:"temp_id";s:4:"4406";s:9:"auto_save";s:1:"1";s:9:"base_data";a:5:{s:11:"template_id";s:4:"4406";s:8:"keywords";s:0:"";s:5:"title";s:0:"";s:12:"weather_json";s:66:"{"create_weather":"","create_temperature":"","create_humidity":""}";s:11:"define_item";s:2:"[]";}s:11:"module_data";s:1920:"H4sIAAAAAAAAA2VUS6+j2Bn8L2y5Ei9joHc2YPO2wWAD3a0rHoeXDw/zMnbrSrOaXTSKokgjzaKX2UWzHyn5Nd3TP2Nw7u1RlLBAh6qiTtXHEZ+QvOpA2wtBHyDvPiFVUALkHfL1779+/cs/v/3jxy+//AtUyBPSJ21dzgT5WN+aWUM8ITHoojZv+hn//bfPX374PN+//fvnr5//9uWnv3778adZ2/VBP3Rv6iSvwHPeg4fR+48PdggfZs95PCPUDKRtPTTz4/zGe4TmWOTjy7wfKB/YLFks8OWsCoa+fu6C8TVFGHTgOX7L/9DCoH+zfNOfwe1atw9T5BE/7+Gj47y8gqDPQPtcdHU1I58+IFE7Q+D5jfiAvPuAfECe/sQf9qCdK7Xg/7hsKPM4729vxMvsH4P/qTy3Ket4gN8Dv/80zz+pH5OP6rKpK1D1/6lKcE//hbxO/JH47fvIlR2Ec4snZM4LX4dDEDRNzAiYmue3TVoAg9ur34OcLWHQvX6NqK6SPH0tnYE8zfo59xLHX5A545/TbINo3vj9xyckySGQgo7PgiqdoSSAHfguRKRFJ6++XyhxupNsaFk26qIBu+b6CW9VylNN0oEbKx1W5jH2cW5BUJ4USmVDEsGB0CRFlJ02xw97m08Pqllv5HEt206yDHZsmVzHGMYANUxfXdQZfSSp6j7SzESzN0JUW9xhAeQVw/cUUs7kRmqgaEf54nCPJokCWCGEjePeDzJUDmNRoBS7H6BC51t0u4DbXauVDk0tbd2iUrjMONEWeXprl6xw8bJK39byhF1Yo6UTLx0B3nF9vG+9QXFjR9107Y1tKqKssEyJvSWradMi3CoWVDEtd3QqE8Pac9LsuhNyeaJYu2Qsa202PtGVQr3TQDdoAKPQVWi6mFdcD3g5HLd9TEgbR1PsOrFYY1sGasLg5vZEruu2JExolmfqxErr8ygdFNXQg8YuN2IfJxerIipMVz3oHY7GzHmx6MerAOwVw6rcYwA3cr64eHePOB+uXcPH0D0w7nQSMTt2Ud6TdKdQi8tokuFSueBC4R+lwWA5kmzoiRsKZmULF6fqDTooCVRTt+u4lmxXkhlWDHwyOXMNp0tDLjjrZoyggpo4XletweOHfHMP5Nwu9ILCg2B52JUargIIaUbje9eUHWlR+k0NSYZY9VdfJXYeb3kLfpdNG/FMhlVf6DsB3NmwHati1FLbLa+9Vfh4R2Y3R742CoEf94SQT2ho3+woqnz9LmxtCJn9JOShp7UmlqFjnfnlQhWDfeUPW1TWQy2nOTdhaLFewcuwXqkG4Ub1Qo2M9ZpXco/P6vVYh+mGZ7ZkFjOAlFpyQluW5IiOiIXTJtcu0+okqIIhERzHxxrJjW4+nW/jtneXacJR+3rv3UlRwbHghoJwaUtsQCQD3aFc2WJg6uWwQduKZjtaT6b2iOorcqAXuuAXmKBFByLRafK+Ags6ka0KCzHgCi7OmC4XtjZUALXAZKyIxrUP7znbhoV43vVbPzLjzs/9uxDHfdGnzN1Omdt82Hmd8bt4PWyZXX6PGN8rj/kJ1spat0ydOu8rB18ZsV5DL13PB+xcoy6jZ4wHmzzMWDYaL+2Nc/cUaagqAxIJI65cFeYENo1Yy3HgQnXsMN7Px5qWwCXBgHoF0YhNBMkbAoOKwUq2fR8WoeVtzIqCJ6WdrhiXypZtkqKFX28rbamgFx5ShmyNaKpfezQLKOVUnifTz0GjldJwMyHdLvpVI47lno1uROKdcWNaxVDtN8L5IqYtflnpe2JoUnN9NO/0IF83OLDLodx4Ad8M6pZn73l9VR2lvWNAsoPJV/2Y1PqNdhBt+yBtvALaShQwe8dcnijxKPCFH1dcNtQUZYQ+39wO3WRy45bGVqgsA2cZC/P/EHl5+fjyB2bpyuJjBwAA";s:11:"encode_type";s:4:"gzip";}s:6:"COOKIE";a:8:{s:14:"eln_page_limit";s:2:"15";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"0vd1scddjptlgvq6v4umqol4j7";s:6:"sims_u";s:32:"38828f261ee60584144cf546b2ff9ece";s:13:"lock_interval";s:3:"180";s:15:"center_language";s:2:"CN";s:9:"page_type";s:1:"0";s:16:"last_active_time";s:13:"1750298678225";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1135";s:5:"email";N;s:4:"name";s:6:"chenqi";s:5:"phone";N;s:6:"ticket";s:32:"38828f261ee60584144cf546b2ff9ece";s:8:"reg_time";s:10:"1744077856";s:5:"Token";s:32:"7eb44480540d6e80df79fce77c791828";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:6:"陈奇";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"2";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,84";s:10:"department";a:0:{}s:2:"id";s:4:"1135";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"3";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:59:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1750298713.8816631;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1750298713.908946;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.092392;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.0945289;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.097729;i:4;a:0:{}}i:5;a:5:{i:0;s:39:"Route requested: 'template/update-temp'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.097791;i:4;a:0:{}}i:6;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.683054;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:7;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.6995449;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7000639;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7004471;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.7009521;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.7010231;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.701827;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.7039001;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7297709;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7302721;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7307811;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:34:"Route to run: template/update-temp";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.733228;i:4;a:0:{}}i:18;a:5:{i:0;s:75:"Running action: frontend\controllers\TemplateController::actionUpdateTemp()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.7339549;i:4;a:0:{}}i:19;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750298715.0264151;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2096;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2096;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:694;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:20;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1750298715.0509429;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:23;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0708389;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:26;a:5:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1750298715.072953;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:704;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4406'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0754261;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.077693;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.082154;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:36;a:5:{i:0;s:75:"Failed to set unsafe attribute 'status' in 'frontend\models\TemplateModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1750298715.0994289;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:729;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:78:"Failed to set unsafe attribute 'group_ids' in 'frontend\models\TemplateModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1750298715.0995009;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:729;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=3 WHERE `id`=4406";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1016569;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11551') AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1129489;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.113843;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1166601;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:50;a:5:{i:0;s:81:"Failed to set unsafe attribute 'real_id' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1750298715.121139;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:51;a:5:{i:0;s:93:"Failed to set unsafe attribute 'exp_module_relay_id' in 'frontend\models\TemplateRelayModel'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1750298715.1211829;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:137;s:8:"function";s:13:"setAttributes";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:52;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11551) AND (`type`=2) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.124567;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.125679;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.128576;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:61;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1345489;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:62;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1750298715.1345911;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:67;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.149509;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:70;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.151814;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:73;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1527441;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:76;a:5:{i:0;s:15:"Set savepoint 1";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1750298715.1538119;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1538639;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:80;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.15763;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:83;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1604321;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:86;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4406) AND (`status`=1)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1614339;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:89;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4406) AND (`group_id`='598')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1698129;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:92;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4461'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.174933;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:95;a:5:{i:0;s:19:"Release savepoint 1";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1750298715.1756041;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:96;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1756639;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:99;a:5:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1750298715.176121;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:810;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:100;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4406) AND (`status`=1) ORDER BY `class`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2341919;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:103;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2468209;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1049;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:106;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11551) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2481339;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1057;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:109;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2503099;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:112;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2527981;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:115;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4406";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.2535391;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:118;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750298715.2853789;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:16597200;s:4:"time";d:1.4441227912902832;s:8:"messages";a:60:{i:21;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1750298715.051018;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:22;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1750298715.0707071;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:24;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.07091;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0717199;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4406'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0754631;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4406'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0760829;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.077817;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.080487;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.082191;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0829351;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=3 WHERE `id`=4406";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1017361;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=3 WHERE `id`=4406";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1026471;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11551') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1130121;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11551') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1137149;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1139021;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.11623;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.116729;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1175151;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:53;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11551) AND (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.124651;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:54;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11551) AND (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.125541;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:56;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1257579;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.128196;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:59;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1286321;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.129421;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:63;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1750298715.13462;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1750298715.1484859;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1486011;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1494551;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1495359;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1516299;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1518431;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.152504;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1527679;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1534979;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.153904;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:79;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.154249;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:81;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.157726;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:82;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1600189;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:84;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.160507;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:85;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.161272;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:87;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4406) AND (`status`=1)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.161489;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:88;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4406) AND (`status`=1)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.169517;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:90;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4406) AND (`group_id`='598')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1699009;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:91;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4406) AND (`group_id`='598')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1743519;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:93;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4461'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.175019;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:94;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4461'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.175513;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:97;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1757171;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:98;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1760571;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:101;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4406) AND (`status`=1) ORDER BY `class`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2342539;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:102;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4406) AND (`status`=1) ORDER BY `class`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2350919;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:104;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2469449;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1049;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:105;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2479489;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1049;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:107;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11551) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.248174;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1057;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:108;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11551) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2487929;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1057;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:110;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2503519;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:111;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2525859;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:113;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2528269;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:114;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2534609;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:116;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4406";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.2535591;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:117;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4406";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.2838571;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:56:{i:24;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.07091;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:85:"SELECT * FROM `company_setting` WHERE (`key`='CREATE_EDIT_TEMPLATE') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0717199;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:65:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyServer.php";s:4:"line";i:101;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:698;s:8:"function";s:17:"getCompanySetting";s:5:"class";s:31:"frontend\services\CompanyServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4406'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0754631;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:42:"SELECT * FROM `template` WHERE `id`='4406'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0760829;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.077817;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:33:"SHOW FULL COLUMNS FROM `template`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.080487;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.082191;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:603:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template' AND kcu.table_name = 'template'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.0829351;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:711;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=3 WHERE `id`=4406";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1017361;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:63:"UPDATE `template` SET `tfrom`=2, `subtype_id`=3 WHERE `id`=4406";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1026471;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:737;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11551') AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1130121;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:68:"SELECT * FROM `template_relay` WHERE (`id`='11551') AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1137149;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1139021;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `template_relay`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.11623;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.116729;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_relay' AND kcu.table_name = 'template_relay'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1175151;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\ModuleServer.php";s:4:"line";i:79;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:51;s:8:"function";s:4:"save";s:5:"class";s:30:"frontend\services\ModuleServer";s:4:"type";s:2:"->";}}}i:53;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11551) AND (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.124651;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:54;a:5:{i:0;s:80:"SELECT * FROM `xsheet` WHERE (`parent_id`=11551) AND (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.125541;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:56;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1257579;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:31:"SHOW FULL COLUMNS FROM `xsheet`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.128196;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:59;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1286321;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:599:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'xsheet' AND kcu.table_name = 'xsheet'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.129421;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:69;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:789;s:8:"function";s:4:"save";s:5:"class";s:38:"frontend\services\modules\XSheetServer";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1486011;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1494551;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1495359;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:40:"SHOW FULL COLUMNS FROM `template_config`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1516299;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1518431;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:617:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_config' AND kcu.table_name = 'template_config'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.152504;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:100;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:131;s:8:"function";s:13:"setStructData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1527679;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1534979;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\models\TemplateConfig.php";s:4:"line";i:174;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:72:"D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php";s:4:"line";i:132;s:8:"function";s:14:"setRequireData";s:5:"class";s:30:"frontend\models\TemplateConfig";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.153904;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:79;a:5:{i:0;s:16:"SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.154249;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1037;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:81;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.157726;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:82;a:5:{i:0;s:43:"SHOW FULL COLUMNS FROM `template_for_group`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1600189;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:84;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.160507;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:85;a:5:{i:0;s:623:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'template_for_group' AND kcu.table_name = 'template_for_group'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.161272;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:87;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4406) AND (`status`=1)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.161489;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:88;a:5:{i:0;s:86:"UPDATE `template_for_group` SET `status`=0 WHERE (`template_id`=4406) AND (`status`=1)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.169517;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1039;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:90;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4406) AND (`group_id`='598')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1699009;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:91;a:5:{i:0;s:84:"SELECT * FROM `template_for_group` WHERE (`template_id`=4406) AND (`group_id`='598')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.1743519;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1051;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:93;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4461'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.175019;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:94;a:5:{i:0;s:60:"UPDATE `template_for_group` SET `status`=1 WHERE `id`='4461'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.175513;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1060;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:97;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1757171;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:98;a:5:{i:0;s:24:"RELEASE SAVEPOINT LEVEL1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.1760571;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:1066;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:803;s:8:"function";s:14:"_saveTempGroup";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:101;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4406) AND (`status`=1) ORDER BY `class`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2342539;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:102;a:5:{i:0;s:91:"SELECT * FROM `template_relay` WHERE (`template_id`=4406) AND (`status`=1) ORDER BY `class`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2350919;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:816;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:104;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2469449;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1049;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:105;a:5:{i:0;s:56:"SELECT * FROM `template_config` WHERE `template_id`=4406";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2479489;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1049;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:107;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11551) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.248174;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1057;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:108;a:5:{i:0;s:66:"SELECT * FROM `template_relay` WHERE (`id`=11551) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2487929;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\StructDataServer.php";s:4:"line";i:1057;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:821;s:8:"function";s:26:"getStructDataFieldByConfig";s:5:"class";s:34:"frontend\services\StructDataServer";s:4:"type";s:2:"->";}}}i:110;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2503519;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:111;a:5:{i:0;s:39:"SHOW FULL COLUMNS FROM `structdata_key`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2525859;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:113;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2528269;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:114;a:5:{i:0;s:615:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'structdata_key' AND kcu.table_name = 'structdata_key'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1750298715.2534609;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:116;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4406";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.2535591;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}i:117;a:5:{i:0;s:63:"UPDATE `structdata_key` SET `status`=0 WHERE `template_id`=4406";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1750298715.2838571;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:29;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php";s:4:"line";i:824;s:8:"function";s:9:"updateAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:73:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php";s:4:"line";i:167;s:8:"function";s:8:"saveTemp";s:5:"class";s:30:"frontend\services\TempleServer";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"6853705a1627a";s:3:"url";s:50:"http://dev.eln.integle.com/?r=template/update-temp";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1750298715;s:10:"statusCode";i:200;s:8:"sqlCount";i:28;s:9:"mailCount";i:0;}}