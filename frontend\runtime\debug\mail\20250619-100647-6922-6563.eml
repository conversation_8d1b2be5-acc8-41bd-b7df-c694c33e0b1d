Message-ID: <<EMAIL>>
Date: Thu, 19 Jun 2025 10:06:45 +0800
Subject: http://dev.eln.integle.com/ 2025-06-19 10:06:43
 =?UTF-8?Q?=E7=B3=BB=E7=BB=9F=E6=8A=A5=E9=94=99?=
From: Integle message <<EMAIL>>
To: <EMAIL>, <EMAIL>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: quoted-printable

2025-06-19 10:06:45
[::1][-][0vd1scddjptlgvq6v4umqol4j7][error][yii\base\ErrorException:2]
exception 'yii\base\ErrorException' with message 'Illegal string
offset 'str'' in
D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php:=
109
Stack trace:
#0
D:\integle2025\eln_5.3.11_dev\frontend\services\modules\XSheetServer.php(1=
09):
yii\base\ErrorHandler->handleError(2, 'Illegal string ...',
'D:\\integle2025\\...', 109, Array)
#1
D:\integle2025\eln_5.3.11_dev\frontend\services\TempleServer.php(789):
frontend\services\modules\XSheetServer->save(4406, Array, 2)
#2
D:\integle2025\eln_5.3.11_dev\frontend\controllers\TemplateController.php(=
167):
frontend\services\TempleServer->saveTemp(Array, 'update')
#3 [internal function]:
frontend\controllers\TemplateController->actionUpdateTemp()
#4
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\InlineAction.php(55=
):
call_user_func_array(Array, Array)
#5
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Controller.php(154):
yii\base\InlineAction->runWithParams(Array)
#6
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Module.php(454):
yii\base\Controller->runAction('update-temp', Array)
#7
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\web\Application.php(84):
yii\base\Module->runAction('template/update...', Array)
#8
D:\integle2025\eln_5.3.11_dev\vendor\yiisoft\yii2\base\Application.php(37=
5):
yii\web\Application->handleRequest(Object(yii\web\Request))
#9 D:\integle2025\eln_5.3.11_dev\frontend\web\index.php(33):
yii\base\Application->run()
#10 {main}
2025-06-19 10:06:43
[::1][-][0vd1scddjptlgvq6v4umqol4j7][info][application] $_GET =3D [
    'r' =3D> 'template/update-temp'
]

$_POST =3D [
    'insertData' =3D> [
        'name' =3D> '=E6=95=B0=E6=8D=AE=E8=A7=86=E5=9B=BEen'
        'tfrom' =3D> '2'
        'type' =3D> '1'
        'descript' =3D> '=E7=BB=9F=E5=80=9F=
=E7=BB=9F=E8=BF=98=E6=9F=93=E5=8F=91=E8=86=8F'
        'status' =3D> '1'
        'define_item' =3D> '[]'
        'subtype_id' =3D> '3'
        'group_ids' =3D> [
            0 =3D> '598'
        ]
    ]
    'temp_id' =3D> '4406'
    'auto_save' =3D> '1'
    'base_data' =3D> [
        'template_id' =3D> '4406'
        'keywords' =3D> ''
        'title' =3D> ''
        'weather_json' =3D>
'{\"create_weather\":\"\",\"create_temperature\":\"\",\"create_humidity\"=
:\"\"}'
        'define_item' =3D> '[]'
    ]
    'module_data' =3D>
'H4sIAAAAAAAAA2VRS6+j2Bn8L976SpiXgd5hwLyNAYOB7tYVhoN5v8GG1pVmNbtoFEWRRppF=
L7OLZj9S8mu6p3/G4NzboyhhgQ71FfVV1fm0SsoOtD3r9/7q3adV6Rdg9W719e+/fv3LP7/9=
48cvv/wLlKunVR+1VbEMkMd5qhcO/LQKQRe0Sd0v+O+/ff7yw+fl/e3fP3/9/LcvP/31248/=
Ldyu9/uhe2NHSQmekx48hN5/fEyHy0PsOQkXBF2Aa1sN9fK5/PF+hVPk6uPLsg8UD2yhYNhm=
u7D8oa+eO398dXHxO/Acvvl/cHO/f5N842dgulXtQ3T1sJ/0+SPjcrwBv49B+5x2Vbkgnz6s=
gnaBwPPb4MPq3YfVh9XTn/hDHrRLpBb83yweiiRM+ult8LLoh+B/Ii9piioc8u+G339a+o+q=
R/NBVdRVCcr+P1Fh6um/kNfGH47f7kcsT/5lSfG0Wvzmr+XAMI7DCwLu9fPbkhbk/vSq9xgu=
krnfvd5GUJVRcn0NHYPkGveL7+1m87JaPP7ZZusHy+L3H59WUZIDwe+Y2C+vC9S3A/jOWwlY=
J9LfnzXsnM5kahgspaAocnN4NSNvPa+IFz/kkx2DCD7vwHNvYHzZFK2HAYAGvXmfjEOWxnSW=
drGuKTotZsrV5HqBKgRgl6TcljdEE9LgtlfSLCsbfG3766Zum9ZLsGTr6+I8uMnhULucnhqc=
W8gmfQrTdldIlRwq9fZ23hpe716rvLIRCfGQktKxwGDHcKPngtFSnVsHYiVSLR7t/WRiBiuV=
0mIPK2OT0tGabjCK4vSZzkiV249prdx9YN+PLl5eh8Lr8n7YAfhQ9wpOujzOUXMhneRuZKTA=
bShscs7yqE4m8I00r5l+U+WwhsUaDd/dJEExdewoBUdHfDDm66wb6SgyB7ikgzC9I3mc2g5T=
n69w05wBsXOa3GXm+nDc5gW0QdekOYxeJWi0dPH4Ts+UOdOos97xDOkpgo6dGo+5qNPdhhre=
58fAuE8mc9aaQCsUkys5zW2sEDHbTE2Q3KQNr4qsRvbkfeHoFo+PspSOqrV1+vLOiAojUoog=
lZuuaZkKRoWpKC2av1CF1JZpOKoSdx5AeCvj4VRmcbXma70BJoO5G1K9HAg7s4425+WNw3rH=
RG7d+DzpudUW1hSLFnYSW8DquecZHEVJINS0DGttAZGAxpWknXq+rLq6DA4pz/JnhC4rNQSx=
WlRytaWcijoas+TDgbXeaaO98yneLjyYVbiczHjH1KHUOqemCQTsJDBeRrnHGjHSy9mNTEkP=
GfRQJhRt5z3enpL8Gmhwxe1u4U1Bali0IJtjZFq0Zc7BDU8p2SmSWqiy6vo+O4mSbCFBwPFp=
uG81lssJ7+yNyf1+zaNLRm/2G2N2+T2yzaDNFE9XIIYzB9SBi4JKnDqYJw8NyxD8gbyr6Ikg=
0foiCyhXGZSfETO5v9jHQzmY7RF3HYdUwD7R4QPnBD28mXyY6GBj38sMfPMIUxVlKseUiA0W=
cTZTmYmIUjdKhGbQsXBzOtiTdAOzKUXlEQ+ajlPlLUKPMFzmhLSheroXirODTtR+6cuPt9kF=
3wj2FM0Ej12RdHZQAgJJFd4POjSN5JY1cQlfx4PYO6nWYzdhUJ2kO5EOBHMlxJK+E+bkUMPD=
AJU3stkSkDwOm7yH9/cZDO1w3cGncBdI5z1ShhUa2GEKoAt7hggZa9hDhMx6w5L8xsY7Cjk6=
gaNZZ3dyCoTaataJAb7QJIE3EcNAatwtW1/CpgtIQy/bnFj3x7nt1tZRWbN7CvRjS5BEhB5x=
Fd86I7RGIM0ht6OqYPqIt6CNILW9JjBAbzdsB+IQ0kn2WpD3mZsqtZIjGFWPFGfgh4N4Eo6S=
uot2R/vERSZ3Ja+sqpwqH07S2K5b8pZlQR0TfVZrfjBByNkkiRgTk0N+GuHy6pEH8e4IHpMp=
k7U+KZcEtdOWFe81P8edeFxXVCmZbHMhrT1vobJAOwICXFrtR2F7bHcWGZ6lTqqTK2JdNt0R=
C4+2j92IHZmM0tlVR4x2DyW17bVqHZu3uqPFOedlpW0vKJStDYEYWJ2mVy8vH1/+AAV/8rUK=
CAAA'
    'encode_type' =3D> 'gzip'
]

$_COOKIE =3D [
    'eln_page_limit' =3D> '15'
    'ldap_check' =3D> '0'
    'integle_session' =3D> '0vd1scddjptlgvq6v4umqol4j7'
    'sims_u' =3D> '38828f261ee60584144cf546b2ff9ece'
    'lock_interval' =3D> '180'
    'center_language' =3D> 'CN'
    'page_type' =3D> '0'
    'last_active_time' =3D> '1750298803565'
]

$_SESSION =3D [
    '__flash' =3D> []
    'userinfo' =3D> [
        'user_id' =3D> '1135'
        'email' =3D> null
        'name' =3D> 'chenqi'
        'phone' =3D> null
        'ticket' =3D> '38828f261ee60584144cf546b2ff9ece'
        'reg_time' =3D> '1744077856'
        'Token' =3D> '7eb44480540d6e80df79fce77c791828'
        'register_from' =3D> ''
        'from_ldap' =3D> '0'
        'gender' =3D> '0'
        'nick_name' =3D> ''
        'contact_phone' =3D> ''
        'real_name' =3D> '=E9=99=88=E5=A5=87'
        'point' =3D> '0'
        'company_name' =3D> ''
        'job' =3D> ''
        'office_phone' =3D> ''
        'qq' =3D> ''
        'country' =3D> ''
        'province' =3D> ''
        'city' =3D> ''
        'detail_address' =3D> ''
        'post_code' =3D> ''
        'id_card' =3D> ''
        'big_img' =3D> ''
        'small_img' =3D> ''
        'unread_message' =3D> '2'
        'default_group' =3D> '0'
        'contact_email' =3D> ''
        'role_ids' =3D> '1,84'
        'department' =3D> []
        'id' =3D> '1135'
        'groups' =3D> [
            0 =3D> [
                'id' =3D> '1'
                'name' =3D> '=E5=85=AC=E5=8F=B8=E7=BE=A4'
                'role' =3D> '1'
            ]
            1 =3D> [
                'id' =3D> '598'
                'name' =3D> 'cq1'
                'role' =3D> '3'
            ]
        ]
        'current_company_id' =3D> '1'
        'app_access' =3D> 1
    ]
    'eln_lang' =3D> 'zh-CN'
]

$_SERVER =3D [
    'MIBDIRS' =3D> 'D:/xampp/php/extras/mibs'
    'MYSQL_HOME' =3D> '\\xampp\\mysql\\bin'
    'OPENSSL_CONF' =3D> 'D:/xampp/apache/bin/openssl.cnf'
    'PHP_PEAR_SYSCONF_DIR' =3D> '\\xampp\\php'
    'PHPRC' =3D> '\\xampp\\php'
    'TMP' =3D> '\\xampp\\tmp'
    'HTTP_HOST' =3D> 'dev.eln.integle.com'
    'HTTP_CONNECTION' =3D> 'keep-alive'
    'CONTENT_LENGTH' =3D> '2885'
    'HTTP_X_REQUESTED_WITH' =3D> 'XMLHttpRequest'
    'HTTP_USER_AGENT' =3D> 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)
AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' =3D> 'application/json, text/javascript, */*; q=3D0.01'
    'CONTENT_TYPE' =3D> 'application/x-www-form-urlencoded;
charset=3DUTF-8'
    'HTTP_ORIGIN' =3D> 'http://dev.eln.integle.com'
    'HTTP_REFERER' =3D> 'http://dev.eln.integle.com/'
    'HTTP_ACCEPT_ENCODING' =3D> 'gzip, deflate'
    'HTTP_ACCEPT_LANGUAGE' =3D> 'zh-CN,zh;q=3D0.9'
    'HTTP_COOKIE' =3D> 'eln_page_limit=3D15; ldap_check=3D0;
integle_session=3D0vd1scddjptlgvq6v4umqol4j7;
sims_u=3D38828f261ee60584144cf546b2ff9ece; lock_interval=3D180;
center_language=3DCN; page_type=3D0; last_active_time=3D1750298803565'
    'PATH' =3D> 'C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common
Files\\Oracle\\Java\\javapath;C:\\Program Files
(x86)\\PerkinElmerInformatics\\ChemOffice2017\\ChemScript\\Lib;C:\\WINDOW=
S\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System3=
2\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program
Files\\dotnet\\;C:\\Program Files
(x86)\\DSOC\\ExtractContent;C:\\Program Files
(x86)\\DSOC\\ExtractContent64\\OCR;D:\\Program
Files\\TortoiseSVN\\bin;D:\\Program
Files\\Java\\jdk-1.8\\bin;D:\\Program
Files\\php\\php-5.6.40-Win32-VC11-x64;D:\\composer;D:\\Program
Files\\Git\\cmd;D:\\Program
Files\\nodejs\\node_global\\node_modules;D:\\nvm;D:\\nvm4w\\nodejs;D:\\Pr=
ogram
Files\\nodejs\\node_global;D:\\Program
Files\\wget-1.21.4-win64;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Program
Files\\JetBrains\\IntelliJ IDEA 2024.1.4\\bin;;D:\\Program
Files\\JetBrains\\PhpStorm
2024.1.4\\bin;;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;=
D:\\Program
Files\\JetBrains\\WebStorm
2024.1.5\\bin;;D:\\Users\\chenc\\AppData\\Local\\Programs\\Microsoft
VS Code\\bin;D:\\Program Files\\cursor\\resources\\app\\bin'
    'SystemRoot' =3D> 'C:\\WINDOWS'
    'COMSPEC' =3D> 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' =3D>
'.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'WINDIR' =3D> 'C:\\WINDOWS'
    'SERVER_SIGNATURE' =3D> '<address>Apache/2.4.38 (Win64)
OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port
80</address>
'
    'SERVER_SOFTWARE' =3D> 'Apache/2.4.38 (Win64) OpenSSL/1.0.2q
PHP/5.6.40'
    'SERVER_NAME' =3D> 'dev.eln.integle.com'
    'SERVER_ADDR' =3D> '::1'
    'SERVER_PORT' =3D> '80'
    'REMOTE_ADDR' =3D> '::1'
    'DOCUMENT_ROOT' =3D> 'D:/integle2025/eln_5.3.11_dev/frontend/web'
    'REQUEST_SCHEME' =3D> 'http'
    'CONTEXT_PREFIX' =3D> ''
    'CONTEXT_DOCUMENT_ROOT' =3D>
'D:/integle2025/eln_5.3.11_dev/frontend/web'
    'SERVER_ADMIN' =3D> 'postmaster@localhost'
    'SCRIPT_FILENAME' =3D>
'D:/integle2025/eln_5.3.11_dev/frontend/web/index.php'
    'REMOTE_PORT' =3D> '64529'
    'GATEWAY_INTERFACE' =3D> 'CGI/1.1'
    'SERVER_PROTOCOL' =3D> 'HTTP/1.1'
    'REQUEST_METHOD' =3D> 'POST'
    'QUERY_STRING' =3D> 'r=3Dtemplate/update-temp'
    'REQUEST_URI' =3D> '/?r=3Dtemplate/update-temp'
    'SCRIPT_NAME' =3D> '/index.php'
    'PHP_SELF' =3D> '/index.php'
    'REQUEST_TIME_FLOAT' =3D> 1750298803.938
    'REQUEST_TIME' =3D> 1750298803
]