<script setup lang="ts">
import { reactive, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { useI18n } from 'vue-i18n';

const settings = reactive({
  publishMode: '' // 默认为空
})

const loading = ref(false)
const { t } = useI18n();

// 获取模板生效设置 ?r=template/get-template-effect-setting
const fetchTemplateEffectSetting = async () => {
  loading.value = true
  try {
    const url = '/?r=template/get-template-effect-setting'

    const response = await axios.get(url, {
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data;
    if (data && data.status === 1) {
      settings.publishMode = data.data.template_effect_mode === '1' ? 'immediate' : 'explicit'
    } else {
      ElMessage.error(data?.info || t('templateGeneralSettings.fetchFailed'))
    }
  }
  catch (error: any) {
    console.error(t('templateGeneralSettings.fetchFailed'), error);
    ElMessage.error(`${t('templateGeneralSettings.fetchFailed')}: ${error.message || t('common.unknownError')}`);
  } finally {
    loading.value = false
  }
}

//保存模板生效设置 ?r=template/save-template-effect-setting
const saveTemplateEffectSetting = async (template_effect_mode: number, newModeText: string) => {
  try {
    loading.value = true
    const url = '/?r=template/save-template-effect-setting'
    const response = await axios.post(url, {
      template_effect_mode: template_effect_mode
    }, {
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    const data = response.data;
    if (data && data.status === 1) {
      // 保存成功后再获取最新数据
      console.log('保存成功，正在刷新数据...')
      ElMessage({
      message: t('templateGeneralSettings.switchedToMode', { mode: newModeText }),
      type: 'success'
    })
      await fetchTemplateEffectSetting();
      return true
    } else {
      ElMessage.error(data?.info || t('templateGeneralSettings.fetchFailed'))
      loading.value = false
      return false
    }
  } catch (error: any) {
    console.error(t('templateGeneralSettings.saveFailed'), error);
    ElMessage.error(`${t('templateGeneralSettings.saveFailed')}: ${error.message || t('common.unknownError')}`);
    loading.value = false
    return false
  }
}

// 处理模式变更
const handleModeChange = (value: string) => {
  const oldValue = value === 'immediate' ? 'explicit' : 'immediate'
  const newModeText = value === 'immediate' ? t('templateGeneralSettings.immediateMode') : t('templateGeneralSettings.explicitMode')
  const newModeConfirmText = value === 'immediate' ?
    t('templateGeneralSettings.immediateModeSwitchConfirm') :
    t('templateGeneralSettings.explicitModeSwitchConfirm')

  ElMessageBox.confirm(
    newModeConfirmText,
    t('templateGeneralSettings.switchModeConfirmTitle', { mode: newModeText }),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  ).then(() => {
    // 保存模板生效设置
    saveTemplateEffectSetting(value === 'immediate' ? 1 : 2,  newModeText)
    console.log('模式已切换为:', value)
  }).catch(() => {
    // 用户取消操作，恢复原选择
    settings.publishMode = oldValue
    ElMessage({
      type: 'info',
      message: t('templateGeneralSettings.switchCancelled')
    })
  })
}

// 组件挂载时获取设置
onMounted(() => {
  fetchTemplateEffectSetting()
})
</script>
<template>
  <div class="template-general-settings">
    <div class="settings-form">
      <div class="setting-item">
        <div class="setting-label">{{ t('templateGeneralSettings.templateEffectMode') }}</div>
        <el-radio-group v-loading="loading" v-model="settings.publishMode" @change="handleModeChange">
          <el-radio :label="'immediate'">{{ t('templateGeneralSettings.immediateMode') }}</el-radio>
          <div class="radio-description" v-html="t('templateGeneralSettings.immediateModeDesc')">
          </div>
          <el-radio :label="'explicit'">{{ t('templateGeneralSettings.explicitMode') }}</el-radio>
          <div class="radio-description" v-html="t('templateGeneralSettings.explicitModeDesc')">
          </div>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>



<style scoped>
.template-general-settings {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: #303133;
}

.settings-form {
  max-width: 700px;
}

.setting-item {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.setting-label {
  width: 260px;
  margin-right: 40px;
  font-weight: bold;
  line-height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.sub-setting {
  margin-top: 10px;
  margin-left: 24px;
}

.sub-setting.disabled {
  opacity: 0.6;
}

.actions {
  margin-top: 30px;
}

/* 确保所有类型的消息都应用相同的位置 */
:deep(body .el-message--success),
:deep(body .el-message--warning),
:deep(body .el-message--info),
:deep(body .el-message--error) {
  top: 12vh !important;
}


.radio-description {
  margin-left: 24px;
  margin-top: 15px;
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}
</style>
