<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/24
 * Time: 14:16
 */

use frontend\models\TemplateModel;

/**
 * @var array $list
 */

$category = 'book';
?>
<div class="exp_data_box clear readonly">
<div style="display: flex;justify-content: space-between;">
    <div class="top-filter-box clear" style="border: 0;">

        <!--申请人筛选-->
        <div class="search-part iblock">
            <label class="label"><?php echo \Yii::t('book', 'applicant');?>：</label>
            <span class="iblock">
            <?php
            echo $this->renderFile(Yii::$app->getViewPath() . '/components/visible_user_select_tree.php', [
                'selectedUsersIds' => @getVar($filter['user_ids'], []),
                'inputName' => 'user_ids',
                'width' => '200px'
            ]);
            ?>
        </span>
        </div>

        <!--操作类别筛选-->
        <div class="search-part iblock">
            <label class="label"><?php echo \Yii::t('base', 'operation_type');?>：</label>
            <span class="iblock">
            <?php
            $operation_type = [
                ['type_code' => 6, 'type_name' => \Yii::t('base', 'create_or_update')],
                ['type_code' => 13, 'type_name' => \Yii::t('base', 'share')],
                ['type_code' => 16, 'type_name' => \Yii::t('base', 'set_is_company')],
                ['type_code' => 17, 'type_name' => \Yii::t('temp', 'publish')], // 发布模板
            ];
            echo $this->renderFile(Yii::$app->getViewPath() . '/components/multi_select.php', [
                'optionList' => $operation_type,
                'idKey' => 'type_code',
                'nameKey' => 'type_name',
                'nameAttr' => 'operation_type',
                'checkedIds' => @getVar($filter['operation_type'], []),
                'placeholder' => \Yii::t('base', 'select_operation_type'),
                'showSearch' => 0,
                'showSearchButton' => 0,
            ]);
            ?>
        </span>
        </div>


        <!--模板类型-->
        <div class="search-part iblock ">
            <?php
            echo $this->renderFile(Yii::$app->getViewPath() . '/components/template_subtype_select_tree.php', [
                'selectedMainTypes' => [],
                'selectedSubtypes' => [],
                'inputName' => '',
                'placeholder' => Yii::t('temp', 'pls_select_template_type'),
                'width' => '200px',
                'templateSubtypeList' => $templateSubtypeList,
                'label' => Yii::t('base', 'template_type'),
            ]);
            ?>
        </div>


        <div class="search-part iblock">
            <a class=" search_wo_ico filter-approval-list" data-type="<?php echo $type; ?>"
               title="<?= Yii::t('views/exp_list', 'search'); ?>"
               href="javascript:void(0)" style="margin-top: -2px;"></a>
        </div>
    </div>
    <div class="approval-btn-box" style="padding: 10px 0;">

    <a href="javascript:void(0)" class=" tool_btn approval_btn" data-type="approval" data-action="agree" data-title="<?php echo \Yii::t('views/exp_list', 'approval_dialog_title_agree');?>"><i class="ico agree"></i><?php echo \Yii::t('views/exp_list', 'approval_agree');?></a>
    <a href="javascript:void(0)" class=" tool_btn approval_btn" data-type="approval" data-action="refuse" data-title="<?php echo \Yii::t('views/exp_list', 'approval_dialog_title_refuse');?>"><i class="ico refuse"></i><?php echo \Yii::t('views/exp_list', 'approval_refuse');?></a>
    <span class="exp_href tool_btn approval-history " data-type="approval_history" type="<?php echo \Yii::$app->params['approval_type']['template'];?>" title="<?= \Yii::t('views/exp_list', 'approval_history');?>"><i class="ico history"></i></span>
    </div>
</div>
<table class="exp_list_table">
    <tr class="list_title">
        <!--全选-->
        <td style="width: 50px;">
            <div class="scroll_sabolute">
                <input type="checkbox" id="checkbox_all" />
                <label for="checkbox_all"></label>
            </div>
        </td>
        <!--模板名称-->
        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('temp', 'temp_name'); ?></div>
        </td>

        <!--操作类型-->
        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('base', 'operation_type'); ?></div>
        </td>

        <!--提交审核的版本号，发布管控模式才显示-->
        <?php if (isset($templateEffectMode) && $templateEffectMode == 2): ?>
        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('temp', 'version'); ?></div>
        </td>

        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('temp', 'effective_date'); ?></div>
        </td>
        <?php endif; ?>        

        <!--模板类型-->
        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('base', 'template_type'); ?></div>
        </td>


        <!--申请人-->
        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('book', 'applicant');?></div>
        </td>

        <!--申请时间-->
        <td style="min-width: 200px;">
            <div class="scroll_sabolute"><?php echo \Yii::t('book', 'application_time');?></div>
        </td>

        <!--action-->
        <td>
            <div class="scroll_sabolute"><?php echo \Yii::t('base', 'look');?></div>
        </td>

        <!--审批-->
        <td>
            <div class="scroll_sabolute" style="min-width: 120px"><?php echo \Yii::t('base', 'action');?></div>
        </td>
    </tr>

<?php if(!empty($list)) {
    foreach ($list as $item) {
        $extraData = $item['extra_data'];
        $tempCnt = !empty($item['temp_details']) ? count($item['temp_details']) : 0;
        $idx = 0;

        //$extraData中获取版本号和生效日期
        $submit_version = isset($extraData['version']) ? $extraData['version'] : '';
        $effective_date = isset($extraData['effective_date']) ? $extraData['effective_date'] : '';

        if(empty($item['temp_details']) || !is_array($item['temp_details']))
        continue; // 跳过空的模板详情，避免报错
        foreach ($item['temp_details'] as $detail) {
            $idx++;
?>
    <tr data-approvalId="<?php echo $item['approval_id'] ?>" data-id="<?php echo $item['id'] ?>" style="line-height: 50px;">
        <?php if($idx === 1) {?>
        <td class="choose" rowspan="<?php echo $tempCnt;?>">
            <label><input type="checkbox" class="checkbox" value="<?php echo $item['id'] ?>"/></label>
        </td>
        <?php }?>

        <!-- 模板名称 -->
        <td>
            <?php
            echo '<a href="javascript:void(0)" class="opts_btn" temp-id="' . $detail['id'] . '" temp-type="'. reset($item['temp_details'])['type'] . '">' . $detail['name'] . '</a>';
            ?>
        </td>

        <!--操作类型-->
        <?php if($idx === 1) {?>
        <td rowspan="<?php echo $tempCnt;?>">
            <?php
            switch ($item['type']) {
                case \Yii::$app->params['approval_type']['template']: //type=6
                    echo \Yii::t('base', 'create_or_update');
                    break;
                case \Yii::$app->params['approval_type']['template_share_batch']:
                case \Yii::$app->params['approval_type']['template_share']: //type=13
                    $shareGroupNamesArr = [];
                    if (!empty($extraData['group_id_arr'])) {
                        foreach ($extraData['group_id_arr'] as $groupId) {
                            $shareGroupNamesArr[] = isset($groupList[$groupId]['group_name'])?$groupList[$groupId]['group_name']:'';
                        }
                    }
                    $shareUserNamesArr = [];
                    if (!empty($extraData['user_id_arr'])) {
                        foreach ($extraData['user_id_arr'] as $uid) {
                            $shareUserNamesArr[] = \frontend\core\CommonServer::displayRealName($userList[$uid]);
                        }
                    }
                    //拼接表格分享的title字符串
                    $shareGroupNamesStr = !empty($shareGroupNamesArr) ? sprintf('%1$s:%2$s',\Yii::t('base', 'group'), join(',', $shareGroupNamesArr)) : '';
                    $shareUserNamesStr = !empty($shareUserNamesArr) ? sprintf('%1$s:%2$s',\Yii::t('base', 'user'), join(',', $shareUserNamesArr)) : '';
                    $separator = '';
                    if (!empty($shareGroupNamesStr) && !empty($shareUserNamesStr))
                        $separator = '; ';
                    $allShareStr = $shareGroupNamesStr . $separator .  $shareUserNamesStr;
                    $titleStr = sprintf('%1$s: %2$s', \Yii::t('temp', 'share_to'), $allShareStr);
                    echo sprintf('<a href="javascript:void(0)" title="%1$s" style="cursor: default">%2$s</a>', $titleStr,\Yii::t('base', 'share'));
                    break;
                case \Yii::$app->params['approval_type']['set_company_template']: //type=16
                    echo \Yii::t('base', 'set_is_company');
                    break;
                case \Yii::$app->params['approval_type']['publish_template']: //type=17, 发布模板审批
                    echo \Yii::t('temp', 'publish'); // 发布模板
                    break;                
            }
            ?>
        </td>

        <!--提交审核的版本号，发布管控模式才显示-->
        <?php if (isset($templateEffectMode) && $templateEffectMode == 2): ?>
        <td rowspan="<?php echo $tempCnt;?>">
            <?php echo $submit_version;?>
        </td>

        <!--生效日期-->
        <td rowspan="<?php echo $tempCnt;?>">
            <?php echo $effective_date;?>
        </td>
        <?php endif; ?>

        <!--模板类型-->
        <td rowspan="<?php echo $tempCnt;?>">
            <?php
            // 确保模板子类型列表是可用的数组
            $templateSubtypeList = isset($templateSubtypeList) && is_array($templateSubtypeList) 
                ? array_column($templateSubtypeList, null, 'id') 
                : [];

            // 获取子类型名称，如果不存在则显示"未分类"
            $subtypeName = !isset($templateSubtypeList[$detail['subtype_id']]) || 
                           $templateSubtypeList[$detail['subtype_id']]['id'] == 0 
                ? Yii::t('temp', 'uncategorized') 
                : $templateSubtypeList[$detail['subtype_id']]['name'];
                
            // 如果子类型为"未分类"，则不显示
            $subtypeName = $subtypeName == Yii::t('temp', 'uncategorized') 
                ? '' 
                : ' - ' . $subtypeName;
                
            // 根据模板类型和系统标志确定显示的模板类型名称
            if (1 == $detail['is_system'] || $detail['type'] == 3 || $detail['email'] == '<EMAIL>') {
                $templateType = Yii::t('temp', 'normal_temp');
            } else if (2 == $detail['type']) {
                $templateType = Yii::t('temp', 'intext_template') . $subtypeName;
            } else if (4 == $detail['type']) {
                $templateType = Yii::t('temp', 'intable_template') . $subtypeName;
            } else if (1 == $detail['type']) {
                $templateType = Yii::t('temp', 'all_temp') . $subtypeName;
            }
            
            // 限制显示长度，超过则显示省略号
            $maxLength = 20; // 最大显示字符数
            $displayType = mb_strlen($templateType) > $maxLength 
                ? mb_substr($templateType, 0, $maxLength) . '...' 
                : $templateType;
            
            echo '<span class="truncate" title="' . htmlspecialchars($templateType) . '">' . $displayType . '</span>';
            ?>
        </td>

        <!--申请人-->
        <td rowspan="<?php echo $tempCnt;?>">
            <?php echo isset($userList[$item['user_id']]) ? \frontend\core\CommonServer::displayUserName($userList[$item['user_id']]) : ''; ?>
        </td>

        <td rowspan="<?php echo $tempCnt;?>">
            <?php echo $item['create_time'];?>
        </td>
        <?php }?>

        <!--操作-->
        <td>
            <?php
            echo '<a href="javascript:void(0)" style="vertical-align: middle;" class="opts_btn table-ico look-ico" temp-id="' . $detail['id'] . '" data-type="show" title="' . Yii::t('base', 'look') . '" temp-type="'. reset($item['temp_details'])['type'] . '"></a>';
            if (in_array($detail['type'], [1, 3]))
                echo '<a href="javascript:void(0)" style="vertical-align: middle;" class="view-temp-history table-ico" temp-id="' . $detail['id'] . '" effect-mode= "'.$templateEffectMode.'" title="' . Yii::t('base', 'history') . '"></a>';
            ?>
            <div id="template-history-dialog" style="display: none; position: absolute;"></div> 
        </td>

        <?php if ($idx === 1) { ?>
            <td rowspan="<?= $tempCnt ?>">
                <!--审批操作-->
                <span class="approval_operate">
                    <a href="javascript:void(0)" class="tool_btn approval_btn" data-type="approvalSingle"
                       data-action="agree" data-title="<?= \Yii::t('base', 'approval_dialog_title_agree') ?>"
                       title="<?= \Yii::t('base', 'approval_dialog_title_agree') ?>"><i class="ico agree"></i></a>
                    <a href="javascript:void(0)" class="tool_btn approval_btn" data-type="approvalSingle"
                       data-action="refuse" data-title="<?= \Yii::t('base', 'approval_dialog_title_refuse') ?>"
                       title="<?= \Yii::t('base', 'approval_dialog_title_refuse') ?>"><i class="ico refuse"></i></a>
                    <a href="javascript:void(0)" class="tool_btn approval_btn" data-type="viewApproval"
                       data-action="view" data-title="<?= \Yii::t('base', 'approval_dialog_title_view') ?>"
                       title="<?= \Yii::t('base', 'approval_dialog_title_view') ?>"><i class="ico view"></i></a>
            </span>
            </td>
        <?php } ?>
    </tr>
<?php
        }
    }
}
?>
</table>

<?php if(!empty($total)) {?>
    <div class="page-box exp_list_page" data-type="loadMore" style="padding-right:60px;" data-num="<?php echo $total;?>" data-page="<?php echo $page;?>" data-limit="<?php echo $limit;?>">分页</div>
    <!-- 调用分页插件 -->
    <script type="text/javascript">
        var type = '<?php echo $type;?>';
        require(['approval'], function (module) {
            module.pageFn(type, module.genApprovalListPage);
        });
    </script>
<?php }?>
</div>
