define(function (require) {
    var tab = require('tab');

    var getHtml = {

        getExpContent: function (id) {
            window.toLoadIndarwCnt = 0; // 重置待加载InDraw数为0
            return $.ajaxFn({
                url: '/?r=experiment/view-exp',
                data: {
                    id: id
                },
                success: function (res) {
                    require(['file'],function(handler){
                        handler();
                    });
                    if (res.data.tipInfo) {
                        $.showAlert(res.data.tipInfo, 5);
                    }

                    if (res?.data?.material_menu_config) {
                        // 更新物料表菜单设置
                        require(['components/in_material/in_material_add'], function (InMaterialAdd) {
                            const materialMenu = res.data['material_menu_config'];
                            console.info('物料表菜单项',materialMenu);
                            InMaterialAdd.updateMaterialMenu(materialMenu);
                        });
                    }
                }
            }, undefined, true);
        },

        genExpPage: function (id) {
            id = typeof id === 'number' ? id.toString() : id;
            var tagId = tab.getTag('getExpContent', [id]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getExpContent(id).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [res.data.expId],
                            func: 'getExpContent',
                            name: res.data.tabName,
                            title: res.data.tabTitle,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        /* 实验列表相关 begin */
        // 我的实验列表 by zhhj at 2020/06/24
        getMyExpListContent: function (bookId) {
            return require('exp_list').getMyExpListContent(bookId);
        },

        // 记录表实验列表 by zhhj at 2020/06/24
        getBookExpListContent: function (bookId) {
            return require('exp_list').getBookExpListContent(bookId);
        },

        // 记录表合著实验列表 by zhhj at 2020/06/28
        getCoauthorExpListContent: function (bookId) {
            return require('exp_list').getCoauthorExpListContent(bookId);
        },

        // 分享给我的实验列表 by zhhj at 2020/06/28
        getSharedToMeExpListContent: function (groupId, userId, bookId) {
            return require('exp_list').getSharedToMeExpListContent(groupId, userId, bookId);
        },

        // 群成员实验列表 by zhhj at 2020/07/20
        getGroupMemberExpListContent: function (groupId, userId, bookId) {
            return require('exp_list').getGroupMemberExpListContent(groupId, userId, bookId);
        },

        // 下属群成员实验列表 // add by hkk 2022/7/7
        getSubordinateExpListContent: function (groupId, userId, bookId) {
            return require('exp_list').getSubordinateExpListContent(groupId, userId, bookId);
        },

        // 我分享的实验列表 by zhhj at 2020/06/29
        getSharedFromMeExpListContent: function (groupId, userId, bookId) {
            return require('exp_list').getSharedFromMeExpListContent(groupId, userId, bookId);
        },

        // 收藏夹实验列表 by zhhj at 2020/06/29
        getFavoritesExpListContent: function () {
            return require('exp_list').getFavoritesExpListContent();
        },

        // 最近浏览实验列表 by zhhj at 2020/06/29
        getRecentlyViewedExpListContent: function () {
            return require('exp_list').getRecentlyViewedExpListContent();
        },

        // 人名反应实验列表 by zhhj at 2020/06/29
        getNameReactionExpListContent: function () {
            return require('exp_list').getNameReactionExpListContent();
        },

        // 我参与的项目实验列表 by zhhj at 2020/06/29
        getProjectExpListContent: function (projectId) {
            return require('exp_list').getProjectExpListContent(projectId, 'getProjectExpListContent');
        },
        // 实验核查 - 按项目核查
        getCheckExpListByProjectContent: function () {
            return require('exp_list').getProjectExpListContent('', 'getCheckExpListByProjectContent');
        },

        // 核查的实验列表
        getCheckExpListContent: function (groupId, userId, bookId, projectId) {
            return require('exp_list').getGroupMemberExpListContent(
                groupId, userId, bookId, projectId,
                undefined, undefined, getPageLimit('getCheckExpListContent') || 15, undefined, undefined, 'getCheckExpListContent'
            );
        },

        // 搜索结果实验列表
        getSearchExpListContent: function () {
            return require('exp_list').getSearchExpListContent();
        },

        // 待审核列表
        getApprovalListContent: function (type) {
            return require('approval').getApprovalListContent(type);
        },

        // 我提交的审核列表
        getApprovalSubmittedListContent: function (type) {
            return require('approval').getApprovalSubmittedListContent(type);
        },

        // 历史审核列表
        getApprovalHistoryListContent: function (type) {
            return require('approval').getApprovalHistoryListContent(type);
        },
        /* 实验列表相关 end */

        // add by hkk 2020/6/22 获取痕迹内容
        getExpHisContent: function (hisId) {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=history/view-history',
                data: {
                    id: hisId
                }
            });
        },

        // add by hkk 2020/6/22 生成痕迹标签
        genExpHisPage: function (hisId) {
            var tagId = tab.getTag('getExpHisContent',  [hisId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getExpHisContent(hisId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [hisId],
                            func: 'getExpHisContent',
                            name: res.data.tagName,
                            title: res.data.tagTitle,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/23 获取实验模板内容
        getExpTempContent: function (tempId) {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=template/view-temp',
                data: {
                    id: tempId,
                    temp_type: '1', // 1 是全文 2是方法 此处获取的都是全文模板
                },
                success: function () {
                    require(['file'],function(handler){
                        handler();
                    });
                }
            });
        },

        // add by hkk 2020/6/23 生成实验模板标签
        genExpTempPage: function (tempId) {
            tempId = tempId + ''; // 转字符串
            var tagId = tab.getTag('getExpTempContent',  [tempId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getExpTempContent(tempId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [tempId],
                            func: 'getExpTempContent',
                            name: res.data.tagName,
                            title: res.data.tagName,
                        };
                        tab.openTag(obj);
                        require('add_module');
                    }
                });
            }
        },

        // add by hkk 2020/6/23 获取模板痕迹内容
        getExpTempHisContent: function (tempHisId) {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=template/view-history',
                data: {
                    his_id: tempHisId
                }
            });
        },

        // add by hkk 2020/6/23 生成模板痕迹标签
        genExpTempHisPage: function (tempHisId) {
            var tagId = tab.getTag('getExpTempHisContent',  [tempHisId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getExpTempHisContent(tempHisId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [tempHisId],
                            func: 'getExpTempHisContent',
                            name: res.data.tagName,
                            title: res.data.tagName,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        getTempHisContent: function (tempHisId) {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=template/view-temp-history',
                data: {
                    history_id: tempHisId
                }
            });
        },

        genTempHisPage: function (tempHisId) {
            var tagId = tab.getTag('getTempHisContent',  [tempHisId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getTempHisContent(tempHisId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [tempHisId],
                            func: 'getTempHisContent',
                            name: res.data.tagName,
                            title: res.data.tagName,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/23 获取方法模版（子模板）内容
        getSubTempContent: function (tempId) {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=template/view-sub-temp',
                data: {
                    id: tempId,
                }
            });
        },

        // add by hkk 2020/6/23 获取方法模版（子模板）内容
        getIntableTempContent: function (tempId) {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=template/view-intable-temp',
                data: {
                    id: tempId,
                }
            });
        },

        // add by hkk 2020/6/23 生成方法模版（子模板）标签
        genSubTempPage: function (tempId) {
            var tagId = tab.getTag('getSubTempContent', [tempId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getSubTempContent(tempId).then(function (res) {
                    if (res.status === 1) {

                        var obj = {
                            html: res.data.contentHtml,
                            params: [tempId],
                            func: 'getSubTempContent',
                            name: res.data.name,
                            title: res.data.name,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/23 生成方法模版（子模板）标签
        genIntableTempPage: function (tempId) {
            var tagId = tab.getTag('getSubTempContent', [tempId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getIntableTempContent(tempId).then(function (res) {
                    if (res.status === 1) {

                        var obj = {
                            html: res.data.contentHtml,
                            params: [tempId],
                            func: 'getIntableTempContent',
                            name: res.data.name,
                            title: res.data.name,
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/23 获取实验模板列表内容
        getExpTempListContent: function () {
            // add by lzs 2023/9/25 获取实验模板分页数量
            var data = {};
            var pagerSelect = $('.exp_conetnt.active .page_box:visible .pager-select');
            if (pagerSelect.length > 0) {
                data.limit = pagerSelect.val();
            }
            return $.ajaxFn({
                type: 'post',
                data: data,
                url: ELN_URL + '?r=template/template-list',
            });
        },

        // add by hkk 2020/6/23 获取实验模板列表内容
        getManageTempListContent: function (data) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=template/manage-template-list',
                data: data,
            });
        },

        // add by hkk 2020/6/23 获取实验模板列表内容
        getRecycleTempListContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=template/recycle-template-list',
            });
        },

        // add by hkk 2020/6/23 获取实验模板列表内容
        getTempListApprovalConfigContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=template/manage-template-approval',
            });
        },

                // 获取模板设置页面内容
        getTemplateSettingsContent: function (data) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=template/template-settings-content',
                data: data
            });
        },

            // 生成模板设置页面
        genTemplateSettingsPage: function (callBack) {
            var tagId = tab.getTag('getTemplateSettingsContent', []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getTemplateSettingsContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getTemplateSettingsContent',
                            callBackFunc: callBack,
                            name: mainLang('template_settings'),
                            title: mainLang('template_settings'),
                        };
                        tab.openTag(obj);
                        if (callBack) {
                            require('exp_module')[callBack.split('.')[1]].apply(null, []);
                        }
                    }
                });
            }
        },



        // add by hkk 2020/6/28 生成实验模板列表标签
        genExpTempListPage: function (callBack) {
            var tagId = tab.getTag('getExpTempListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getExpTempListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getExpTempListContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('temp_list'),
                            title: mainLang('temp_list'),
                        };
                        tab.openTag(obj);
                        require('exp_module')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by hkk 2020/6/28 生成实验模板列表标签
        genManageTempListPage: function (callBack) {
            var tagId = tab.getTag('getManageTempListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getManageTempListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getManageTempListContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('temp_manage'),
                            title: mainLang('temp_manage'),
                        };
                        tab.openTag(obj);
                        require('exp_module')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by hkk 2020/6/28 生成实验模板列表标签
        genRecycleTempListPage: function (callBack) {
            var tagId = tab.getTag('getRecycleTempListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getRecycleTempListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getRecycleTempListContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('recycle_template'),
                            title: mainLang('recycle_template'),
                        };
                        tab.openTag(obj);
                        require('exp_module')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by hkk 2020/6/28 生成实验模板列表标签
        genTemplateApprovalConfigPage: function (callBack) {
            var tagId = tab.getTag('getTempListApprovalConfigContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getTempListApprovalConfigContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getTempListApprovalConfigContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('applopval_config_template'),
                            title: mainLang('applopval_config_template'),
                        };
                        tab.openTag(obj);
                        require('exp_module')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by hkk 2020/6/29 获取我分享的模板列表内容
        getMyShareTempListContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=share-temple/share-temple-by-me',
                data:{
                    needUpdateAllPage: 1,
                }

            });
        },

        // add by hkk 2020/6/29 生成我分享的模板列表标签
        genMyShareTempListPage: function () {
            var tagId = tab.getTag('getMyShareTempListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getMyShareTempListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getMyShareTempListContent',
                            name: mainLang('my_share_temp'), // 我分享的模板
                            title: mainLang('my_share_temp'),
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/29 获取分享给我的模板列表内容
        getShareMeTempListContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=share-temple/share-temple-to-me',
                data:{
                    needUpdateAllPage: 1,
                }

            });
        },

        // add by hkk 2020/6/29 生成分享给我的模板列表标签
        genShareMeTempListPage: function () {
            var tagId = tab.getTag('getShareMeTempListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getShareMeTempListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getShareMeTempListContent',
                            name: mainLang('to_share_temp'),
                            title: mainLang('to_share_temp'),
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/29 获取群成员权限列表内容
        getGroupUserAuthorityContent: function () {
            var data = {};
            data.limit = getPageLimit('getGroupUserAuthorityContent') || 15;
            if (localStorage.getItem('eln_setting_group_authority_id') !== undefined) {
                data.group_id = localStorage.getItem('eln_setting_group_authority_id');
            }
            return $.ajaxFn({
                type: 'post',
                data: data,
                url: ELN_URL + '?r=group-setting/group-user-list',

            });
        },

        // add by hkk 2020/6/29 生成群成员权限列表标签
        genGroupUserAuthorityPage: function () {
            var tagId = tab.getTag('getGroupUserAuthorityContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getGroupUserAuthorityContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getGroupUserAuthorityContent',
                            name: jsLang['group_setting'],
                            title: jsLang['group_setting'],
                        };
                        tab.openTag(obj);
                        var selector = '.fs_select_group_name'
                        $(selector).fSelect({
                            placeholder: mainLang('select'),
                            numDisplayed: 3,
                            overflowText: '{n} selected',
                            noResultsText: mainLang('no_search_result'),
                            searchText: mainLang('search'),
                            showSearch: true
                        });

                        //fs-wrap 调整样式
                        $(selector).parents('.fs-wrap').css('width', '180px');
                        $(selector).parents('.fs-wrap').css('line-height', '1.2');
                        $(selector).parents('.fs-wrap').css('height', '28px');
                        $(selector).parents('.fs-wrap').css('margin-top', '2px');
                        $(selector).parents('.fs-wrap').css('vertical-align', 'top');
                    }
                });
            }
        },

        // add by hkk 2020/6/29 获取群主权限列表内容
        getGroupMasterAuthorityContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=company-setting/company-setting-auth',

            });
        },

        // add by hkk 2020/6/29 生成群主权限列表标签
        genGroupMasterAuthorityPage: function () {
            var tagId = tab.getTag('getGroupMasterAuthorityContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getGroupMasterAuthorityContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getGroupMasterAuthorityContent',
                            name: jsLang['company_setting'],
                            title: jsLang['company_setting'],
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/28 获取仪器库管理内容
        getInstrumentListContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/manage',
                data: {
                    needUpdateAllPage: 1,
                    type: 'instruments_manage',
                    limit: getPageLimit('getInstrumentListContent') || 15,
                }
            });
        },

        // add by zsm 2025/6/4 17:00 仪器库管理标签切换
        instrumentManagerTab: function (type) {
            var tagId = $('.exp_title .tag.on').attr('data-id');
            if(type === 1 ){ // 我的仪器库-查看界面
                this.getInstrumentListContent().then(function (res) {
                    require('tab').switchTag(tagId,res.data.contentHtml);  // 不换tag换内容
                });

            }else{ // TODO
                this.getInstrumentBookingRecordContent().then(function (res) {

                    require('tab').switchTag(tagId,res.data.contentHtml);  // 不换tag换内容
                    mountVueApp('/vue-ui/dist/instrumentsBookManage.js', '#instrument-book-record', {
                        bookList: res.data.data,
                        closeBookInstruments: function() {
                            // 使用unmountDynamicApp销毁应用
                            unmountVueApp('#instrument-book-record');
                            $('#instrument-book-record').hide();
                        }
                    });
                    $('#instrument-book-record').show();
                });

            }
        },

        // add by hkk 2020/6/28 生成仪器库管理页面
        genInstrumentListPage: function () {
            var tagId = tab.getTag('getInstrumentListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getInstrumentListContent',
                            name: jsLang['instruments_manage'], // todo
                            title: jsLang['instruments_manage'], // todo
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by zsm 2025/6/4 获取仪器预约内容
        getInstrumentBookingRecordContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument-booking/get-instrument-booking-info',
                data: {}
            });
        },

        // add by hkk 2020/6/28 获取我的仪器库内容
        getMyInstrumentListContent: function (name = null) {
            var data = {
                needUpdateAllPage: 1,
                type: 'my_instruments'
            }
            data.limit = getPageLimit('getMyInstrumentListContent') || 15
            // 支持外部链接可以自动进行名称筛选
            if (name) {
                data.instrument_name = name;
            }

            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/manage',
                data: data
            });
        },

        // add by hkk 2020/6/28 生成我的仪器库-管理页面
        genMyInstrumentListPage: function (name = null) {
            var tagId = tab.getTag('getMyInstrumentListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getMyInstrumentListContent(name).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getMyInstrumentListContent',
                            name: jsLang['my_instruments'],
                            title: jsLang['my_instruments'],
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },


        // add by hkk 2020/6/28 获取仪器操作痕迹内容列表
        getInstrumentTraceContent: function (insId) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-trace-page',
                data: {
                    needUpdateAllPage: "yes",
                    instrumentId:insId,
                }
            });
        },

        // add by hkk 2020/6/28 生成仪器操作痕迹内容页面
        genInstrumentTracePage: function (insId) {
            var tagId = tab.getTag('getInstrumentTraceContent',  [insId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentTraceContent(insId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [insId],
                            func: 'getInstrumentTraceContent',
                            name: mainLang('instrument_trace') + "(" + res.data.name + ")",
                            title: mainLang('instrument_trace') + "(" + res.data.name + ")",
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2021/4/23  获取仪器使用统计内容
        getInstrumentUseGraphContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-use-graph-page',
                data: {
                    needUpdateAllPage: "yes",
                }
            });
        },

        // add by hkk 2021/4/23 生成仪器使用统计图表页面
        genInstrumentUseGraphPage: function () {
            var tagId = tab.getTag('getInstrumentUseGraphContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentUseGraphContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getInstrumentUseGraphContent',
                            name: mainLang('instrument_use_graph'),
                            title: mainLang('instrument_use_graph'),
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2021/4/23  获取仪器预约统计内容
        getInstrumentBookGraphContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-book-graph-page',
                data: {
                    needUpdateAllPage: "yes",
                }
            });
        },

        // add by hkk 2021/4/23 生成仪器预约统计图表页面
        genInstrumentBookGraphPage: function () {
            var tagId = tab.getTag('getInstrumentBookGraphContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentBookGraphContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getInstrumentBookGraphContent',
                            name: mainLang('instrument_book_graph'),
                            title: mainLang('instrument_book_graph'),
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },


        // add by hkk 2023/10/9  获取我的序列内容
        getSequenceListContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=sequence/sequence-list',
                data: {
                    needUpdateAllPage: 1,
                    type: 'mine',
                    limit: getPageLimit('getSequenceListContent') || 15,
                }
            });
        },

        // add by hkk 2023/10/9 生成我的序列页面
        genSequenceListPage: function () {
            var tagId = tab.getTag('getSequenceListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getSequenceListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSequenceListContent',
                            name: jsLang['my_sequence'],
                            title: jsLang['my_sequence'],
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2023/10/9  获取InSequence编辑器
        getSequenceEditorContent: function (sequenceId,sequenceName,openType) {

            if (arguments[0] === 'reload') { // 重载走新的获取方式
                var argsArray = Array.prototype.slice.call(arguments);
                argsArray.shift();
                sequenceId = argsArray.join(','); // 多个逗号分隔
                openType = 'reload'
            }
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=sequence/sequence-editor',
                data: {
                    needUpdateAllPage: 1,
                    sequenceId:sequenceId,
                    sequenceName:sequenceName,
                    openType:openType, // add 或者 edit
                }
            });
        },

        // add by hkk 2023/10/17 打开InSequence编辑器
        genSequenceEditorPage :function(sequenceId,sequenceName,openType){
            var tagId = tab.getTag('getSequenceEditorContent',  []);

            if (tagId && $('#detail_sequence').length > 0 ) { // 存在Tag 直接切换到对应Tag即可

                tab.switchTag(tagId);

                // 更新隐藏域的参数
                $('#sequence-id-name').val(sequenceId);
                $('#sequence-id-name').attr('data-type',openType);
                $('#sequence-id-name').attr('data-name',sequenceName);

                // 标签页新增参数
                var tagObj = $(`.tag_bar .exp_title .tag[data-id=${tagId}]`);
                const currentIds  = JSON.parse(tagObj.attr('data-funcParams'));
                if(!currentIds.includes(sequenceId)){
                    currentIds.push(sequenceId);
                    tagObj.attr('data-funcParams',JSON.stringify(currentIds))
                }

                // 发消息给InSequence内部处理
                var sequenceFrame = $('#detail_sequence')[0].contentWindow;
                if (sequenceFrame) {
                    sequenceFrame.postMessage({
                        msg: 'openElnTag', data: {
                            id: sequenceId,
                            name: sequenceName,
                            type: openType,// add 或者edit
                        }
                    }, '*')
                }

            } else {

                if(tagId && $('#detail_sequence').length === 0){ // 刷新网页后中外部去诶换到sequence编辑器
                    tab.closeTag(tagId); // 先关闭再重新打开
                }

                this.getSequenceEditorContent(sequenceId,sequenceName,openType).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [sequenceId],
                            func: 'getSequenceEditorContent',
                            name: 'InSequence',
                            title: 'InSequence',
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2023/11/2  分享给我的序列
        getSequenceShareContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=sequence/share-to-me-list',
                data: {
                    needUpdateAllPage: 1,
                    type: 'mine',
                    limit: getPageLimit('getSequenceListContent') || 15,
                }
            });
        },

        // add by hkk 2023/10/9 生成我的序列页面
        genSequenceSharePage: function () {
            var tagId = tab.getTag('getSequenceShareContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getSequenceShareContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSequenceShareContent',
                            name: jsLang['share_to_me_sequence'],
                            title: jsLang['share_to_me_sequence'],
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // 获取InScada汇总内容
        getInscadaSummaryContent: function (dataType, inscadaType, numericalInstrumentType) {
            var filterUnclaimed = localStorage.getItem('filter_unclaimed_is_checked');
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-inscada-summary-page',
                data: {
                    needUpdateAllPage: "yes",
                    dataType,
                    inscada_type: inscadaType, // 区分我的仪器库和仪器库管理
                    numerical_instrument_type: numericalInstrumentType, // 数值类仪器细分类型
                    filterUnclaimed: filterUnclaimed, // 不看别人数据
                }
            });
        },

        // 生成InScada汇总页面
        genInscadaSummaryPage: function (inscada_type) {
            var tagId = tab.getTag('getInscadaSummaryContent',[2, inscada_type]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInscadaSummaryContent(2, inscada_type).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [2, inscada_type],
                            func: 'getInscadaSummaryContent',
                            name: mainLang('InScada'),
                            title: mainLang('InScada'),
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // 获取inscada汇总图表内容
        getInscadaGraphContent: function(dataType) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-inscada-graph-page',
                data: {
                    needUpdateAllPage: "yes",
                    dataType
                }
            });
        },

        // 生成inscada汇总图表页面
        genInscadaGraphPage: function(dataType) {
            var tagId = tab.getTag('getInscadaGraphContent');
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInscadaGraphContent(dataType).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getInscadaGraphContent',
                            name: mainLang('ins_data_graph'),
                            title: mainLang('ins_data_graph'),
                            dataType
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/7/17 获取单个仪器使用情况
        getInstrumentSingleUsageContent: function (insId) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-single-usage-page',
                data: {
                    needUpdateAllPage: "yes",
                    instrumentId:insId,
                }
            });
        },

        // add by hkk 2020/7/17 生成单个仪器使用情况页面
        genInstrumentSingleUsagePage: function (insId) {
            var tagId = tab.getTag('getInstrumentSingleUsageContent', [insId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentSingleUsageContent(insId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [insId],
                            func: 'getInstrumentSingleUsageContent',
                            name: mainLang('usage_situation') + "( " + res.data.insName + ")",
                            title: mainLang('usage_situation') + "( " + res.data.insName + ")",
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // InScada
        genInstrumentInScadaPage: function (insId, dataType,data_id) {
            let _that = this;//当前函数所属的getHtml对象
            //tab上的data-funcparams属性为一个数组，里面的值需要保持全为字符串，防止getTag中对比时不能找到已有的标签
            var tagId = tab.getTag('getInstrumentInScadaContent',  [String(insId), String(dataType)]); // 一台仪器的数据详情只需要一个标签页，不需要按数据id区分 jiangdm 2022/12/29

            //bug#31591，保持仪器库中同一个仪器的数据列表均只在一个tab中打开
            _that.getInstrumentInScadaContent(insId, dataType,data_id)
                .then(function (res) {
                    if (res.status === 1) {
                        if (tagId) {//这台仪器已经有打开的标签页，切换到该标签页并更新页面
                            tab.switchTag(tagId, res.data.contentHtml, [String(insId), String(dataType)]);
                        }
                        else {//这台仪器没有已经打开的标签页，新打开一个
                            var obj = {
                                html: res.data.contentHtml,
                                params: [String(insId), String(dataType)],
                                func: 'getInstrumentInScadaContent',
                                name: mainLang('InScada') + "( " + res.data.insName + ")",
                                title: mainLang('InScada') + "( " + res.data.insName + ")",
                            };
                            tab.openTag(obj);
                        }
                    }
                });

            /*注释代码已无用，以后再删 mod dx*/
            // if (tagId) { //存在Tag 直接切换到对应Tag即可
            //     tab.switchTag(tagId);
            // } else {
            //     this.getInstrumentInScadaContent(insId, dataType,data_id).then(function (res) {
            //         if (res.status === 1) {
            //             var obj = {
            //                 html: res.data.contentHtml,
            //                 params: [String(insId), String(dataType)],
            //                 func: 'getInstrumentInScadaContent',
            //                 name: mainLang('InScada') + "( " + res.data.insName + ")",
            //                 title: mainLang('InScada') + "( " + res.data.insName + ")",
            //             };
            //             tab.openTag(obj);
            //         }
            //     });
            // }
        },

        // InScada
        getInstrumentInScadaContent:  function (insId, dataType,data_id) {
            // 刷新页面时, 需要获取是否获取别人数据的参数
            const scadaFilterUnclaimed = Number(localStorage.getItem('filter_unclaimed_is_checked') ?? 1);
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-inscada-page',
                data: {
                    needUpdateAllPage: "yes",
                    instrumentId:insId,
                    dataType,
                    status: "1",
                    data_id,
                    filterUnclaimed: scadaFilterUnclaimed, // 是否不看别人数据的标记字段
                }
            });
        },

        // add by hkk 2020/7/17 获取仪器维修记录内容
        getInstrumentRepairRecordContent: function (insId) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-repair-record-page',
                data: {
                    needUpdateAllPage: "yes",
                    instrumentId:insId,
                },
                // success: function () {
                //     require(['instrument'], function(instrument) {
                //         instrument.instrumentSaveSortField('getInstrumentRepairRecordContent');
                //     });
                // }
            });
        },

        // add by hkk 2020/7/17 生成仪器维修记录页面
        genInstrumentRepairRecordPage: function (insId) {
            var tagId = tab.getTag('getInstrumentRepairRecordContent',  [insId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentRepairRecordContent(insId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [insId],
                            func: 'getInstrumentRepairRecordContent',
                            name: mainLang('repair_record') + "( " + res.data.insName + ")",
                            title: mainLang('repair_record') + "( " + res.data.insName + ")",
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/7/22 获取仪器运行记录内容
        getInstrumentOperateRecordContent: function (insId, experimentId = null) {
            // 构造请求数据
            var requestData = {
                needUpdateAllPage: "yes",
                instrumentId: insId
            };

            // 如果 experimentId 不为空，则添加到请求数据中
            if (experimentId) {
                requestData.expId = experimentId;
            }

            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-operate-record-page',
                data: requestData
                // success: function () {
                //     require(['instrument'], function(instrument) {
                //         instrument.instrumentSaveSortField('getInstrumentOperateRecordContent');
                //     });
                // }
            });
        },

        // add by hkk 2020/7/22 生成仪器运行记录页面
        genInstrumentOperateRecordPage: function (insId, expId = null) {
            var tagId = tab.getTag('getInstrumentOperateRecordContent',  [insId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentOperateRecordContent(insId, expId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [insId],
                            func: 'getInstrumentOperateRecordContent',
                            name: mainLang('operate_record') + "( " + res.data.insName + ")",
                            title: mainLang('operate_record') + "( " + res.data.insName + ")",
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/7/17 获取仪器维修记录内容
        getInstrumentCheckRecordContent: function (insId) {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=instrument/get-check-record-page',
                data: {
                    needUpdateAllPage: "yes",
                    instrumentId:insId,
                },
                // success: function () {
                //     require(['instrument'], function(instrument) {
                //         instrument.instrumentSaveSortField('getInstrumentCheckRecordContent');
                //     });
                // }
            });
        },

        // add by hkk 2020/7/17 生成仪器维修记录页面
        genInstrumentCheckRecordPage: function (insId) {
            var tagId = tab.getTag('getInstrumentCheckRecordContent',  [insId]);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getInstrumentCheckRecordContent(insId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [insId],
                            func: 'getInstrumentCheckRecordContent',
                            name: mainLang('check_record') + "( " + res.data.insName + ")",
                            title: mainLang('check_record') + "( " + res.data.insName + ")",
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },


        // add by hkk 2020/6/29 获取词库页面内容
        getWordDataListContent: function () {
            return $.ajaxFn({
                type: 'get',
                url: ELN_URL + '?r=chem/my-indraw-list',

            });
        },

        // add by hkk 2020/6/29 生成词库页面（初次时inDraw词库）
        genWordDataListPage: function () {
            var tagId = tab.getTag('getWordDataListContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getWordDataListContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getWordDataListContent',
                            name: mainLang('my_entry'), // 我的词条
                            title: mainLang('my_entry'), // 我的词条
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/29 获取我分享的词库页面内容
        getMyShareWordDataContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=share-data/my-share-list',
                data:{
                    order_type: 1,
                    share_type: 5, // 初次indraw 词库子标签
                }

            });
        },

        // add by hkk 2020/6/29 生成我分享的词库页面（初次时inDraw词库）
        genMyShareWordDataPage: function () {
            var tagId = tab.getTag('getMyShareWordDataContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getMyShareWordDataContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getMyShareWordDataContent',
                            name: mainLang('my_share_entry'), // 我分享的词条
                            title: mainLang('my_share_entry'), // 我分享的词条
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by hkk 2020/6/29 获取分享给我的词库页面内容
        getShareMeWordDataContent: function () {
            return $.ajaxFn({
                type: 'post',
                url: ELN_URL + '?r=share-data/to-share-list',
                data:{
                    order_type: 1,
                    share_type: 5, // 初次indraw 词库子标签
                }

            });
        },

        // add by hkk 2020/6/29 生成分享给我的词库页面（初次时inDraw词库）
        genShareMeWordDataPage: function () {
            var tagId = tab.getTag('getShareMeWordDataContent',  []);
            if (tagId) { //存在Tag 直接切换到对应Tag即可
                tab.switchTag(tagId);
            } else {
                this.getShareMeWordDataContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getShareMeWordDataContent',
                            name: mainLang('to_share_entry'), // 我分享的词条
                            title: mainLang('to_share_entry'), // 我分享的词条
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        //add by wy 2023/4/13 新增将页面条目存储到本地的函数
        // setLimitToLocal: function (itemName, limit) {
        //     var localLimitObj = localStorage.getItem('eln_page_limit') ? JSON.parse(localStorage.getItem('eln_page_limit')) : {};
        //     localLimitObj[itemName] = limit;
        //     localStorage.setItem('eln_page_limit', JSON.stringify(localLimitObj));
        // },

        //工单列表
        getWorkOrderContent: function (data) {
            if (typeof data == 'undefined' || data.length === 0) {  //如果是通过左侧工单入口或者刷新tab进入，就使用保存的页数
                if (localStorage.getItem('wo_search_history') != null) {    //如果有设置过工单筛选项且不是新建工单后的刷新
                    data = localStorage.getItem('wo_search_history');
                    data = JSON.parse(data);

                    data.needUpdateAllPage = 1;
                    data.limit = getPageLimit('getWorkOrderContent') || 15;
                    data.keyword = null;
                } else {
                    data = JSON.parse('{}');
                    data.limit = 15;
                }
            }


            return $.ajaxFn({
                url: '/?r=collaboration/list',
                data: data,
                success: function (res) {
                    setLimitToLocal('getWorkOrderContent', data.limit);
                }
            }, undefined, true);
        },

        genWorkOrderPage: function (callBack) {
            var tagId = tab.getTag('getWorkOrderContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getWorkOrderContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getWorkOrderContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('collaboration'),
                            title: mainLang('collaboration')
                        };
                        tab.openTag(obj);
                        require('collaboration')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by szq 2020/6/23 回收站
        getRecycleContent: function (type) {
            return require('reduction').getRecycleContent(type);
        },

        //add by wy 2023/3/10 物料表设置-中英文物料名称设置
        getShowIUPACContent: function () {
            return $.ajaxFn({
                url: '/?r=group-setting/company-setting-show-iupac'
            });
        },

        //add by wy 2023/3/10 物料表设置-导出物料表数据
        getExportMaterialsContent: function () {
            return $.ajaxFn({
                url: '/?r=group-setting/get-export-materials-data-page'
            })
        },

        // add by szq 2020/6/22 管理设置-物料表小数点设置
        getDecimalSettingContent: function () {
            return $.ajaxFn({
                url: '/?r=group-setting/company-setting-decimal'
            });
        },

        genDecimalSettingPage: function () {
            var tagId = tab.getTag('getDecimalSettingContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getDecimalSettingContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getDecimalSettingContent',
                            name: mainLang('setting_of_materials'),
                            title: mainLang('setting_of_materials')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by szq 2020/6/22 管理设置-企业词库
        getEnterpriseThesaurusContent: function () {
            return $.ajaxFn({
                url: '/?r=group-setting/company-dict'
            });
        },

        genEnterpriseThesaurusPage: function () {
            var tagId = tab.getTag('getEnterpriseThesaurusContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getEnterpriseThesaurusContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getEnterpriseThesaurusContent',
                            name: mainLang('company_dict'),
                            title: mainLang('company_dict')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by szq 2020/6/24 管理设置-记录本管理
        getBookNumberManageContent: function () {
            return require('book_manage').getBookNumberManageContent();
        },

        // add by szq 2020/6/22 管理设置-通用设置
        getGeneralSettingContent: function () {
            return $.ajaxFn({
                url: '/?r=company-setting/get-general-setting'
            });
        },

        genGeneralSettingPage: function () {
            var tagId = tab.getTag('getGeneralSettingContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getGeneralSettingContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getGeneralSettingContent',
                            name: mainLang('general_setting'),
                            title: mainLang('general_setting')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by szq 2020/6/22 帮助中心-使用视频
        getHelpVideoContent: function () {
            return $.ajaxFn({
                url: '/?r=site/video-help'
            });
        },

        genHelpVideoPage: function () {
            var tagId = tab.getTag('getHelpVideoContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getHelpVideoContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getHelpVideoContent',
                            name: mainLang('use_video'),
                            title: mainLang('use_video')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by szq 2020/6/22 帮助中心-常见问题
        getHelpQuestionContent: function () {
            return $.ajaxFn({
                url: '/?r=site/help'
            });
        },

        genHelpQuestionPage: function () {
            var tagId = tab.getTag('getHelpQuestionContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getHelpQuestionContent().then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getHelpQuestionContent',
                            name: mainLang('normal_question'),
                            title: mainLang('normal_question')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by lcy 2020/6/28 提工单
        getAddCollaborationContent: function (data) {
            var work_order_show_hide_obj = localStorage.getItem('work_order_show_hide_btn') ? JSON.parse(localStorage.getItem('work_order_show_hide_btn')) : {};
            var more_show_hide_status = work_order_show_hide_obj['more_show_hide_btn'] ? work_order_show_hide_obj['more_show_hide_btn'] : 0;
            var intable_show_hide_status = work_order_show_hide_obj['intable_show_hide_btn'] ? work_order_show_hide_obj['intable_show_hide_btn'] : 0;
            return $.ajaxFn({
                url: '/?r=collaboration/add-collaboration',
                data: data,
                success: function (res) {
                    if (res.status == 1) {
                        setTimeout(() => {
                            if (more_show_hide_status == 1) {
                                $('.exp_conetnt.active .more_show_hide').removeClass('hidden');
                            }
                            if (intable_show_hide_status == 1) {
                                $('.exp_conetnt.active .intable_show_hide').removeClass('hidden');
                            }
                        }, 300)
                    }
                }
            }, undefined, true);
        },

        genAddCollaborationContentPage: function (data) {
            var tagId = tab.getTag('getAddCollaborationContent', []);
            if (tagId) {
                tab.switchTag(tagId);
                $('.more_show_hide .datetimepicker').datetimepicker({
                    format: 'yyyy-mm-dd hh:ii',
                    autoclose: true,
                    minView: 0,// 0精确到分钟 2精确到小时
                    clearBtn: true,
                });
            } else {
                this.getAddCollaborationContent(data).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getAddCollaborationContent',
                            name: mainLang('add_collaboration'),
                            title: mainLang('add_collaboration')
                        };
                        tab.openTag(obj);
                        $('.more_show_hide .datetimepicker').datetimepicker({
                            format: 'yyyy-mm-dd hh:ii',
                            autoclose: true,
                            minView: 0,// 0精确到分钟 2精确到小时
                            clearBtn: true,
                        });
                    }
                });
            }
        },

        // 通过url链接来创建工单
        genAddCollaborationContentPageFromUrl: function (data) {
            this.getAddCollaborationContent(data).then(function (res) {
                if (res.status === 1) {
                    $.loading();    //请求页面时，显示loading遮罩
                    var tagId = tab.getTag('getAddCollaborationContent', []);
                    if (tagId) {
                        tab.switchTag(tagId, res.data.contentHtml);
                    } else {
                        tab.openTag({
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getAddCollaborationContent',
                            name: mainLang('add_collaboration'),
                            title: mainLang('add_collaboration')
                        });
                    }
                    //bug#27458 将隐藏input值恢复为0，后续更改项目可以触发任务更新 jiangdm 2022/7/22
                    $('.exp_conetnt.active .first_load').val('0');
                }

                //根据条件关闭遮罩
                $(document).ajaxComplete(function (event, xhr, setting) {   //等待请求可见用户的ajax请求完成
                    if (setting.url === ELN_URL + '?r=site/get-visible-users' || window.visibleUsers) { //如果请求已经完成
                        $.closeLoading();   //关闭loading遮罩
                    }
                });
                setTimeout(function () {
                    $.closeLoading();
                }, 10000);  //如果超过10s，关闭遮罩
            });
        },

        // add by lcy 2020/6/28 查看工单
        getViewCollaborationContent: function (collId) {
            var work_order_show_hide_obj = localStorage.getItem('work_order_show_hide_btn') ? JSON.parse(localStorage.getItem('work_order_show_hide_btn')) : {};
            var more_show_hide_status = work_order_show_hide_obj['more_show_hide_btn'] ? work_order_show_hide_obj['more_show_hide_btn'] : 0;
            var intable_show_hide_status = work_order_show_hide_obj['intable_show_hide_btn'] ? work_order_show_hide_obj['intable_show_hide_btn'] : 0;
            return $.ajaxFn({
                type: 'post',
                url: '/?r=collaboration/view-collaboration',
                data: {
                    id: collId,
                },
                success: function (res) {
                    setTimeout(() => {
                        if (more_show_hide_status == 1) {
                            $('.exp_conetnt.active .more_show_hide').removeClass('hidden');
                        }
                        if (intable_show_hide_status == 1) {
                            $('.exp_conetnt.active .intable_show_hide').removeClass('hidden');
                        }
                    }, 300)
                }
            },undefined,true);
        },

        genViewCollaborationContentPage: function (collId) {
            var tagId = tab.getTag('getViewCollaborationContent', [collId]);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getViewCollaborationContent(collId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [collId],
                            func: 'getViewCollaborationContent',
                            name: mainLang('view_collaboration'),
                            title: mainLang('view_collaboration')
                        };
                        tab.openTag(obj);
                    }
                });
            }
        },

        // add by lcy 2020/6/28 编辑工单
        getEditCollaborationContent: function (collId) {
            var work_order_show_hide_obj = localStorage.getItem('work_order_show_hide_btn') ? JSON.parse(localStorage.getItem('work_order_show_hide_btn')) : {};
            var more_show_hide_status = work_order_show_hide_obj['more_show_hide_btn'] ? work_order_show_hide_obj['more_show_hide_btn'] : 0;
            var intable_show_hide_status = work_order_show_hide_obj['intable_show_hide_btn'] ? work_order_show_hide_obj['intable_show_hide_btn'] : 0;
            return $.ajaxFn({
                type: 'post',
                url: '/?r=collaboration/edit-collaboration',
                data: {
                    id: collId,
                },
                success: function (res) {
                    if (res.status == 1) {
                        setTimeout(() => {
                            if (more_show_hide_status == 1) {
                                $('.exp_conetnt.active .more_show_hide').removeClass('hidden');
                            }
                            if (intable_show_hide_status == 1) {
                                $('.exp_conetnt.active .intable_show_hide').removeClass('hidden');
                            }
                        }, 300)
                    }
                }
            });
        },

        genEditCollaborationContentPage: function (collId) {
            var tagId = tab.getTag('getEditCollaborationContent', [collId]);
            if (tagId) {
                tab.switchTag(tagId);
                $('.more_show_hide .datetimepicker').datetimepicker({
                    format: 'yyyy-mm-dd hh:ii',
                    autoclose: true,
                    minView: 0,// 0精确到分钟 2精确到小时
                    clearBtn: true,
                });
            } else {
                this.getEditCollaborationContent(collId).then(function (res) {
                    if (res.status === 1) {
                        var edit_title = mainLang('edit_collaboration');
                        var wo_id = res.data.wo_id;
                        if (typeof wo_id != 'undefined' && wo_id) {
                            edit_title = mainLang('edit_collaboration') + 'WO' + wo_id;
                        }

                        var obj = {
                            html: res.data.contentHtml,
                            params: [collId],
                            func: 'getEditCollaborationContent',
                            name: edit_title,
                            title: edit_title
                        };
                        tab.openTag(obj);

                        $('.exp_conetnt.active .first_load').val('0');
                        //取消页面上的首次加载标志，使之可以根据项目选项选项变化修改任务变化

                        $('.more_show_hide .datetimepicker').datetimepicker({
                            format: 'yyyy-mm-dd hh:ii',
                            autoclose: true,
                            minView: 0,// 0精确到分钟 2精确到小时
                            clearBtn: true,
                        });
                    }
                });
            }
        },

        // add by lcy 2020/6/28 复制工单
        getCopyCollaborationContent: function (collId) {
            var work_order_show_hide_obj = localStorage.getItem('work_order_show_hide_btn') ? JSON.parse(localStorage.getItem('work_order_show_hide_btn')) : {};
            var more_show_hide_status = work_order_show_hide_obj['more_show_hide_btn'] ? work_order_show_hide_obj['more_show_hide_btn'] : 0;
            var intable_show_hide_status = work_order_show_hide_obj['intable_show_hide_btn'] ? work_order_show_hide_obj['intable_show_hide_btn'] : 0;
            console.log(collId);
            return $.ajaxFn({
                type: 'post',
                url: '/?r=collaboration/copy-collaboration',
                data: {
                    id: collId,
                },
                success: function (res) {
                    if (res.status == 1) {
                        setTimeout(() => {
                            if (more_show_hide_status == 1) {
                                $('.exp_conetnt.active .more_show_hide').removeClass('hidden');
                            }
                            if (intable_show_hide_status == 1) {
                                $('.exp_conetnt.active .intable_show_hide').removeClass('hidden');
                            }
                        }, 300)
                    }
                }
            });
        },

        genCopyCollaborationContentPage: function (collId) {
            var tagId = tab.getTag('getCopyCollaborationContent', [collId]);
            if (tagId) {
                tab.switchTag(tagId);
                $('.more_show_hide .datetimepicker').datetimepicker({
                    format: 'yyyy-mm-dd hh:ii',
                    autoclose: true,
                    minView: 0,// 0精确到分钟 2精确到小时
                    clearBtn: true,
                });
            } else {
                this.getCopyCollaborationContent(collId).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [collId],
                            func: 'getCopyCollaborationContent',
                            name: mainLang('add_collaboration'),
                            title: mainLang('add_collaboration')
                        };
                        tab.openTag(obj);

                        $('.exp_conetnt.active .first_load').val('0');  //取消页面上的首次加载标志，使之可以根据项目选项选项变化修改任务变化

                        $('.more_show_hide .datetimepicker').datetimepicker({
                            format: 'yyyy-mm-dd hh:ii',
                            autoclose: true,
                            minView: 0,// 0精确到分钟 2精确到小时
                            clearBtn: true,
                        });
                    }
                });
            }
        },
        //获取绩效统计列表页
        getUseStaticContent: function (data) {
            data = {...data};
            data.limit = getPageLimit('getUseStaticContent') || 15;
            return $.ajaxFn({
                url: '/?r=group-setting/use-static',
                type:'post',
                data: data
            });
        },

        genUseStaticPage: function (callBack) {
            var tagId = tab.getTag('getUseStaticContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getUseStaticContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getUseStaticContent',
                            callBackFunc:callBack, //
                            name: mainLang('use_static'),
                            title: mainLang('use_static')
                        };
                        tab.openTag(obj);
                        require('static')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by lcy 2020/6/29 活力图
        getUseStaticChatsTwoContent: function (data) {
            console.log(data);

            return $.ajaxFn({
                type: 'post',
                url: '/?r=group-setting/use-static-chats-two',
                data: {'group_id':data.group_id,'end_time':data.end_time,'department_id':data.department_id,'limit':data.limit,'role_id':data.role_id,'start_time':data.start_time,'user_id':data.user_id,
                }
            });
        },

        genUseStaticChatsTwoPage: function (data, id) {
            var tagId = tab.getTag('getUseStaticChatsTwoContent', [data]);
            if (tagId) {
                tab.closeTag(tagId);
            }
            var _this = this;
            this.getUseStaticChatsTwoContent(data).then(function (res) {
                if (res.status === 1) {
                    var obj = {
                        html: res.data.contentHtml,
                        params: [data],
                        func: 'getUseStaticChatsTwoContent',
                        name: mainLang('use_static_chats'),
                        title: mainLang('use_static_chats')
                    }
                    tab.openTag(obj);

                    if (id) {
                        _this.getUserActivityGraph(id);
                    }
                }
            });
        },

        // add by lcy 2020/6/29 活力图
        getUseStaticChatsTwoHistoyContent: function (id) {
            console.log('dasd',id);
            return $.ajaxFn({
                type: 'post',
                url: '/?r=group-setting/use-static-chats-two',
                data:{
                    temp_id:id,
                },
            });
        },

        genUseStaticChatsTwoHistoyPage: function (id) {
            this.genUseStaticChatsTwoPage([], id);
        },

        getUserActivityGraph: function (id) {
            var tagId = tab.getTag('getUseStaticChatsTwoContent');
            this.getUseStaticChatsTwoHistoyContent(id).then(function (res) {
                if (res.status === 1) {

                    var content = $('.exp_conetnt[data-id=' + tagId + ']');
                    content.html(res.data.contentHtml);

                    if (res.data.echats_data != '') {
                        //初始化图片
                        var myChart = echarts.init($('.exp_conetnt.active #show_pic')[0]);
                        var str = res.data.echats_data;
                        console.log(str);
                        //var app.title = '多 Y 轴示例';
                        var colors = str.color;
                        var data_legend = str.legend;
                        var data_x_ray = str.x_ray;
                        var data_yaxis = str.yaxis;
                        for (i = 0; i < data_yaxis.length; i++) {
                            data_yaxis[i] = JSON.parse(data_yaxis[i]);
                        }
                        console.log(data_yaxis);
                        var data_series = str.series;
                        for (i = 0; i < data_series.length; i++) {
                            data_series[i] = JSON.parse(data_series[i]);
                        }
                        console.log(data_series);

                        var option = {
                            color: colors,

                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'cross'
                                }
                            },
                            grid: {
                                right: '30%'
                            },
                            toolbox: {
                                feature: {
                                    saveAsImage: {show: true},
                                    dataView: {show: false, readOnly: false},
                                    restore: {show: true},

                                }
                            },
                            legend: {
                                data: data_legend
                            },
                            xAxis: [
                                {
                                    type: 'category',
                                    axisTick: {
                                        alignWithLabel: true
                                    },
                                    data: data_x_ray
                                }
                            ],
                            yAxis: data_yaxis,
                            series: data_series,
                        };


                        // 使用刚指定的配置项和数据显示图表。
                        myChart.setOption(option);

                    }
                    //初始化图片完成
                    $('.tool_data_box').html('');

                }
            });
        },

        //获取绩效统计历史
        getUseStaticHistoryContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/get-use-static-history',
                data: data
            });
        },

        genUseStaticHistoryPage: function (callBack) {
            var tagId = tab.getTag('getUseStaticHistoryContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getUseStaticHistoryContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getUseStaticHistoryContent',
                            callBackFunc:callBack, //
                            name: mainLang('history_data'),
                            title: mainLang('history_data'),
                        };
                        tab.openTag(obj);
                        console.log(callBack.split('.')[1]);
                        require('static')[callBack.split('.')[1]].apply(null, []);

                    }
                });
            }
        },

        //
        getSysLogContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/sys-log',
                data: data
            });
        },

        //
        getSysExpShareContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/sys-exp-share-log',
                data: data
            });
        },

        //
        getSysExpOpenContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/sys-exp-open-log',
                data: data
            });
        },

        //
        getSysFileExportContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/sys-file-export-log',
                data: data
            });
        },

        //
        getSysAuthChangeContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/sys-auth-change-log',
                data: data
            });
        },

        //
        getSysChangeNotebookGroupContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/sys-change-notebook-group-log',
                data: data
            });
        },

        genSysLogPage: function (callBack) {
            var tagId = tab.getTag('getSysLogContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getSysLogContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSysLogContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('login_log'),
                            title: mainLang('login_log')
                        };
                        tab.openTag(obj);
                        require('log')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        genSysExpShareLogPage: function (callBack) {
            var tagId = tab.getTag('getSysExpShareContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getSysExpShareContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSysExpShareContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('sys_exp_share_log'),
                            title: mainLang('sys_exp_share_log')
                        };
                        tab.openTag(obj);
                        require('log')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        genSysExpOpenLogPage: function (callBack) {
            var tagId = tab.getTag('getSysExpOpenContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getSysExpOpenContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSysExpOpenContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('sys_exp_open_log'),
                            title: mainLang('sys_exp_open_log')
                        };
                        tab.openTag(obj);
                        require('log')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        genSysFileExportLogPage: function (callBack) {
            var tagId = tab.getTag('getSysFileExportContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getSysFileExportContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSysFileExportContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('sys_file_export_log'),
                            title: mainLang('sys_file_export_log')
                        };
                        tab.openTag(obj);
                        require('log')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        genSysAuthChangeLogPage: function (callBack) {
            var tagId = tab.getTag('getSysAuthChangeContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getSysAuthChangeContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSysAuthChangeContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('sys_auth_change_log'),
                            title: mainLang('sys_auth_change_log')
                        };
                        tab.openTag(obj);
                        require('log')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        genSysChangeNotebookGroupgPage: function (callBack) {
            var tagId = tab.getTag('getSysChangeNotebookGroupContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getSysChangeNotebookGroupContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getSysChangeNotebookGroupContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('change_group_for_notebook_log'),
                            title: mainLang('change_group_for_notebook_log')
                        };
                        tab.openTag(obj);
                        require('log')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        // add by lcy 2020/6/30 结构化数据
        getStructDataContent: function (expNum) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=group-setting/struct-data',
                data: {
                    exp_num: expNum,
                }
            });
        },

        // add by lcy 2020/6/30 结构化数据
        getMyDataViewContent: function (type) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/my-data-view-empty',
                data: {
                    type: type,
                }
            });
        },

        genStructDataContentPage: function (expNum) {
            var tagId = tab.getTag('getStructDataContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getStructDataContent(expNum).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getStructDataContent',
                            name: mainLang('struct_data'),
                            title: mainLang('struct_data')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        genMyDataViewContentPage: function (type) {
            var tagId = tab.getTag('getMyDataViewContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getMyDataViewContent(type).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getMyDataViewContent',
                            name: mainLang('my_struct_data'),
                            title: mainLang('my_struct_data')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },


        getDataViewSharedToMe: function (expNum) {
            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/share-to-me-data-view',
                data: {
                    exp_num: expNum,
                }
            });
        },
        genDataViewSharedToMePage :function (expNum) {
            var tagId = tab.getTag('getDataViewSharedToMe', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getDataViewSharedToMe(expNum).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getDataViewSharedToMe',
                            name: mainLang('data_views_share_to_me'),
                            title: mainLang('data_views_share_to_me')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },
        getMyReportContent: function (type) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/my-report',
                data: {
                    type: type,
                }
            });
        },
        genMyReportContentPage: function (type) {
            var tagId = tab.getTag('getMyReportContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getMyReportContent(type).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getMyReportContent',
                            name: mainLang('my_reports'),
                            title: mainLang('my_reports')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        getReportCycleContent: function (type) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/report-cycle',
                data: {
                    type: type,
                }
            });
        },
        genReportCycleContentPage: function (type) {
            var tagId = tab.getTag('getReportCycleContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getReportCycleContent(type).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getReportCycleContent',
                            name: mainLang('cycle_report'),
                            title: mainLang('cycle_report')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        getReportContent: function (report_id, name, report_template_id) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/get-report-data',
                data: {
                    report_id: report_id,
                    name: name,
                    report_template_id: report_template_id,
                }
            });
        },
        genReportContentPage: function (report_id, name, report_template_id) {
            var tagId = tab.getTag('getReportContent', [report_id, name, report_template_id]);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getReportContent(report_id, name, report_template_id).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [report_id, name, report_template_id],
                            func: 'getReportContent',
                            name: mainLang('report')+':'+name,
                            title: mainLang('report')+':'+name
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        getReportTempContent: function (report_template_id, type) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/get-report-temp-data',
                data: {
                    report_template_id: report_template_id,
                    type: type,
                }
            });
        },
        genReportTempContentPage: function (report_template_id, type) {
            var tagId = tab.getTag('getReportTempContent', [report_template_id, type]);
            let name = '';
            const typeLangMap = {
                view: 'show',
                edit: 'edit',
                new: 'new',
                copy: 'copy',
            }
            if (type === 'new') {
                name = mainLang('create_new_report_template')
            } else {
                name = mainLang(typeLangMap[type]) + ': ' + mainLang('report_template')
            }
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getReportTempContent(report_template_id, type).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [report_template_id, type],
                            func: 'getReportTempContent',
                            name: name + res.data.report_temp_name,
                            title: name + res.data.report_temp_name
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        getMyReportTemplate: function(expNum) {
            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/my-report-template',
                data: {
                    exp_num: expNum,
                }
            });
        },
        genMyReportTemplatePage: function (expNum) {
            var tagId = tab.getTag('getMyReportTemplate', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getMyReportTemplate(expNum).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getMyReportTemplate',
                            name: mainLang('my_report_templates'),
                            title: mainLang('my_report_templates')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        getDataView: function(id,type,name,create_by) {
            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/get-data-view-page',
                data: {
                    id:id,
                    type:type,
                    name:name,
                    create_by_id:create_by,
                }
            });
        },
        //数据视图和子视图的id  type=1为数据视图 type=2为字数图
        genDataViewPage: function (id,type,name,create_by) {
            var tagId = tab.getTag('getDataView', [id, type, name,create_by]);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getDataView(id,type,name,create_by).then(function (res) {
                    if (res.status === 1) {
                        let nameTab = ''
                        if (type == 1) {
                            nameTab = mainLang('data_view')
                        } else {
                            nameTab = mainLang('sub_data_view')
                        }
                        var obj = {
                            html: res.data.contentHtml,
                            params: [id, type,name,create_by],
                            func: 'getDataView',
                            name: nameTab + ':' + name,
                            title: nameTab + ':' + name,
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },


        getReportTemplateCycleContent: function (type) {

            return $.ajaxFn({
                type: 'post',
                url: '/?r=struct-data/report-template-cycle',
            });
        },
        genReportTemplateCycleContentPage: function (type) {
            var tagId = tab.getTag('getReportTemplateCycleContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getReportTemplateCycleContent(type).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getReportTemplateCycleContent',
                            name: mainLang('cycle_report_template'),
                            title: mainLang('cycle_report_template')
                        }
                        tab.openTag(obj);
                    }
                });
            }
        },

        //结构化数据历史
        getStructDataHistoryContent: function (data) {
            return $.ajaxFn({
                url: '/?r=group-setting/structdata-history',
                data: data
            });
        },

        genStructDataHistoryPage: function (callBack) {
            var tagId = tab.getTag('getStructDataHistoryContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getStructDataHistoryContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getStructDataHistoryContent',
                            callBackFunc:callBack, //
                            name: mainLang('history_data'),
                            title: mainLang('history_data'),
                        };
                        tab.openTag(obj);
                        console.log(callBack.split('.')[1]);
                        require('struct_data')[callBack.split('.')[1]].apply(null, []);

                    }
                });
            }
        },
        //企业级权限
        genCompanyAuthPage: function (callBack) {
            var tagId = tab.getTag('getCompanyAuthContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getCompanyAuthContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getCompanyAuthContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('auth_manage'),
                            title: mainLang('auth_manage')
                        };
                        tab.openTag(obj);
                        require('company_setting')[callBack.split('.')[1]].apply(null, []);
                        // wh 企业级权限只有普通用户时第一行被隐藏造成排版错误  36069
                        // $(".exp_conetnt.active .eln_setting_table_temp").find(".tr").each(function (i) {
                        //     if($(this).attr('data-role-id') == '5'){
                        //         $(this).hide();
                        //     }
                        // });
                    }
                });
            }
        },
        //企业级角色
        genRoleManagePage: function (callBack) {
            var tagId = tab.getTag('getRoleManageContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getRoleManageContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getRoleManageContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('role_manage'),
                            title: mainLang('role_manage')
                        };
                        tab.openTag(obj);
                        require('company_setting')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },
        //实验核查
        genCheckUserManagePage: function (callBack) {
            var tagId = tab.getTag('getCheckUserManageContent', []);
            if (tagId) {
                tab.switchTag(tagId);
            } else {
                this.getCheckUserManageContent([]).then(function (res) {
                    if (res.status === 1) {
                        var obj = {
                            html: res.data.contentHtml,
                            params: [],
                            func: 'getCheckUserManageContent',
                            callBackFunc:callBack, // add by hkk 2020/6/23 传入的回调函数
                            name: mainLang('check_user_manage'),
                            title: mainLang('check_user_manage')
                        };
                        tab.openTag(obj);
                        require('company_setting')[callBack.split('.')[1]].apply(null, []);
                    }
                });
            }
        },

        //权限设置
        getCompanyAuthContent: function (data) {
            data = data ? data : {};
            data.limit = getPageLimit('getCompanyAuthContent') || 15;
            return $.ajaxFn({
                url: '/?r=company-setting/company-setting-auth',
                type:'post',
                data: data
            });
        },

        //角色管理
        getRoleManageContent: function (data) {
            return $.ajaxFn({
                url: '/?r=company-setting/role-manage',
                data: data
            });
        },

        //实验核查
        getCheckUserManageContent: function (data) {
            return $.ajaxFn({
                url: '/?r=company-setting/check-user-manage',
                data: data
            });
        },

        // add by lcy 2020/6/30 结构化数据
        getUserAuthContent: function (groupId,userId) {
            return $.ajaxFn({
                type: 'post',
                url: '/?r=company-setting/view-user-auth',
                data: {
                    user_id: userId,
                    group_id: groupId,
                }
            });
        },

        // add by lcy 2020/6/30 结构化数据
        getAuthCompareContent: function (groupId,userId) {
            return $.ajaxFn({
                type: 'post',
                url: '/?r=company-setting/view-auth-compare',
                data: {
                    group_id: groupId,
                }
            });
        },

        //add by zxc 20230330 我的->权限
        myAuth: function () {
            return $.ajaxFn({
                url: '/?r=group-setting/my-auth-box'
            });
        },

        getRelationship: function (id) {
            return $.ajaxFn({
                url: '/?r=experiment/get-relation-info',
                type: 'GET',
                data: {
                    id: id
                },
                success: function (res) {
                }
            }, undefined, true);
        },

        getRelationshipPage: function (id) {
            var tagId = tab.getTag('getRelationship');
            if (tagId) {
                tab.closeTag(tagId);
            }
            this.getRelationship(id).then(function (res) {
                if (res.status === 1) {
                    var obj = {
                        html: res.data.contentHtml,
                        params: [id],
                        func: 'getRelationship',
                        name: mainLang('relation_diagram'),
                        title: mainLang('relation_diagram'),
                    };
                    tab.openTag(obj);
                }
            });
        },
    }

    return getHtml
})
