a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:59:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:53:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:57:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"173";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:33:"application/json, text/plain, */*";s:12:"content-type";s:16:"application/json";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:167:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; last_active_time=**********002";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:39:"instrument-booking/get-instruments-data";s:6:"action";s:76:"frontend\controllers\InstrumentBookingController::actionGetInstrumentsData()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:173:"{"lang":"cn","bookTime":["2025-06-17","2025-06-17"],"pageSize":15,"curPage":1,"onlyBooked":false,"headers":{"Accept":"application/json","X-Requested-With":"XMLHttpRequest"}}";s:17:"Decoded to Params";a:6:{s:4:"lang";s:2:"cn";s:8:"bookTime";a:2:{i:0;s:10:"2025-06-17";i:1;s:10:"2025-06-17";}s:8:"pageSize";i:15;s:7:"curPage";i:1;s:10:"onlyBooked";b:0;s:7:"headers";a:2:{s:6:"Accept";s:16:"application/json";s:16:"X-Requested-With";s:14:"XMLHttpRequest";}}}s:6:"SERVER";a:44:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"173";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:33:"application/json, text/plain, */*";s:12:"CONTENT_TYPE";s:16:"application/json";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:167:"eln_page_limit=15; ldap_check=0; integle_session=0vd1scddjptlgvq6v4umqol4j7; sims_u=38828f261ee60584144cf546b2ff9ece; lock_interval=180; last_active_time=**********002";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:52:"D:/integle2025/eln_5.3.11_dev/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"56643";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:41:"r=instrument-booking/get-instruments-data";s:11:"REQUEST_URI";s:43:"/?r=instrument-booking/get-instruments-data";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.122;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:39:"instrument-booking/get-instruments-data";}s:4:"POST";a:0:{}s:6:"COOKIE";a:6:{s:14:"eln_page_limit";s:2:"15";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"0vd1scddjptlgvq6v4umqol4j7";s:6:"sims_u";s:32:"38828f261ee60584144cf546b2ff9ece";s:13:"lock_interval";s:3:"180";s:16:"last_active_time";s:13:"**********002";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1135";s:5:"email";N;s:4:"name";s:6:"chenqi";s:5:"phone";N;s:6:"ticket";s:32:"38828f261ee60584144cf546b2ff9ece";s:8:"reg_time";s:10:"1744077856";s:5:"Token";s:32:"7eb44480540d6e80df79fce77c791828";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:6:"陈奇";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"2";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,84";s:10:"department";a:0:{}s:2:"id";s:4:"1135";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"3";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:49:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.152458;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.1626749;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.299305;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.300143;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.301271;i:4;a:0:{}}i:5;a:5:{i:0;s:58:"Route requested: 'instrument-booking/get-instruments-data'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.3012891;i:4;a:0:{}}i:6;a:5:{i:0;s:64:"请求数据为:{"r":"instrument-booking\/get-instruments-data"}";i:1;i:4;i:2;s:7:"Request";i:3;d:**********.408185;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:132;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}}}i:7;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.558429;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.5811651;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.5817561;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.5820479;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.582427;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.5824821;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.5829239;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.5842631;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:161;s:8:"function";s:7:"getLogo";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.596483;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:161;s:8:"function";s:7:"getLogo";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.596935;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:161;s:8:"function";s:7:"getLogo";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.5972929;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2043;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:161;s:8:"function";s:7:"getLogo";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:18;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.60043;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:767;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:767;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:19;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7522011;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2096;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2096;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:783;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:20;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.7656679;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:23;a:5:{i:0;s:56:"SELECT * FROM `user_company_role` WHERE `user_id`='1135'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7836361;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:26;a:5:{i:0;s:89:"SELECT * FROM `user_group_role` WHERE (`user_id`='1135') AND (`group_id` IN ('1', '598'))";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7857111;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:821;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:29;a:5:{i:0;s:59:"SELECT * FROM `company_auth_setting` WHERE `user_id`='1135'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.79002;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:32;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.7947569;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:35;a:5:{i:0;s:178:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2, '')) AND (`r`.`is_company_role`=1) AND (`r`.`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.813853;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:38;a:5:{i:0;s:42:"SHOW FULL COLUMNS FROM `role_auth_setting`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8156569;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:41;a:5:{i:0;s:621:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'role_auth_setting' AND kcu.table_name = 'role_auth_setting'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.819279;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:44;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.820194;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:940;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:940;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:45;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.8205171;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2452;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2452;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:46;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.94961;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2096;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:2096;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2475;s:8:"function";s:25:"getUserAllInfoByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:54:"SELECT * FROM `user_group_role` WHERE `user_id`='1135'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.950098;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2496;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:50;a:5:{i:0;s:144:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2)) AND (`r`.`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9536769;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2517;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:53;a:5:{i:0;s:57:"SELECT * FROM `group_auth_setting` WHERE `user_id`='1135'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9558699;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2522;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:56;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9564481;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2548;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2548;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:57;a:5:{i:0;s:28:"Executing Redis Command: set";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.956821;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:254;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:254;s:8:"function";s:3:"set";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.957073;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:59;a:5:{i:0;s:53:"Route to run: instrument-booking/get-instruments-data";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.9581749;i:4;a:0:{}}i:60;a:5:{i:0;s:92:"Running action: frontend\controllers\InstrumentBookingController::actionGetInstrumentsData()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.9600899;i:4;a:0:{}}i:61;a:5:{i:0;s:519:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time`, MAX(IB.create_time) as latest_booking_time FROM `instruments` `Ins` INNER JOIN `instruments_book` `IB` ON Ins.id = IB.instrument_id WHERE (`Ins`.`status`='1') AND (`IB`.`create_by`='1135') GROUP BY `Ins`.`id` ORDER BY `latest_booking_time` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9678521;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.970911;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:67;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974762;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:70;a:5:{i:0;s:140:"SELECT COUNT(*) FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650'))";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9755399;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7275;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:73;a:5:{i:0;s:440:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time` FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650')) ORDER BY `Ins`.`create_time` DESC LIMIT 9";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9773991;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7308;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:76;a:5:{i:0;s:598:"SELECT `IB`.`id` AS `booking_id`, `IB`.`instrument_id`, `IB`.`start_time`, `IB`.`end_time`, `IB`.`status` AS `booking_status`, `IB`.`create_by`, `IB`.`create_time` AS `booking_create_time`, `IB`.`related_experiment`, `IB`.`remark` AS `booking_remark`, `IB`.`reminder` FROM `instruments_book` `IB` WHERE (((`IB`.`instrument_id` IN ('2647', '2648', '2656', '2666', '2654', '2650', '2665', '2664', '2661', '2658', '2655', '2653', '2652', '2649', '2645')) AND (`IB`.`status`=1)) AND (`IB`.`start_time` <= '2025-06-17 23:59:59')) AND (`IB`.`end_time` >= '2025-06-17 00:00:00') ORDER BY `IB`.`start_time`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9798651;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7373;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:79;a:5:{i:0;s:44:"Picture[common\components] Class Initialized";i:1;i:8;i:2;s:11:"application";i:3;d:1750154111.1382191;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Picture.php";s:4:"line";i:58;s:8:"function";s:5:"trace";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7432;s:8:"function";s:11:"__construct";s:5:"class";s:25:"common\components\Picture";s:4:"type";s:2:"->";}i:2;a:3:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7452;s:8:"function";s:9:"array_map";}}}i:80;a:5:{i:0;s:44:"Picture[common\components] Class Initialized";i:1;i:8;i:2;s:11:"application";i:3;d:1750154111.138469;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Picture.php";s:4:"line";i:58;s:8:"function";s:5:"trace";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7432;s:8:"function";s:11:"__construct";s:5:"class";s:25:"common\components\Picture";s:4:"type";s:2:"->";}i:2;a:3:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7452;s:8:"function";s:9:"array_map";}}}i:81;a:5:{i:0;s:44:"Picture[common\components] Class Initialized";i:1;i:8;i:2;s:11:"application";i:3;d:1750154111.138581;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Picture.php";s:4:"line";i:58;s:8:"function";s:5:"trace";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7432;s:8:"function";s:11:"__construct";s:5:"class";s:25:"common\components\Picture";s:4:"type";s:2:"->";}i:2;a:3:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7452;s:8:"function";s:9:"array_map";}}}i:82;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1750154111.139735;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:15579520;s:4:"time";d:1.0679001808166504;s:8:"messages";a:34:{i:21;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.7657051;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:22;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.783546;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:24;a:5:{i:0;s:56:"SELECT * FROM `user_company_role` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.78368;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:25;a:5:{i:0;s:56:"SELECT * FROM `user_company_role` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7844889;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:27;a:5:{i:0;s:89:"SELECT * FROM `user_group_role` WHERE (`user_id`='1135') AND (`group_id` IN ('1', '598'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.785744;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:821;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:28;a:5:{i:0;s:89:"SELECT * FROM `user_group_role` WHERE (`user_id`='1135') AND (`group_id` IN ('1', '598'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.789165;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:821;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:30;a:5:{i:0;s:59:"SELECT * FROM `company_auth_setting` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.79005;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:31;a:5:{i:0;s:59:"SELECT * FROM `company_auth_setting` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7909131;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:33;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.7947941;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:34;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.8137679;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:36;a:5:{i:0;s:178:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2, '')) AND (`r`.`is_company_role`=1) AND (`r`.`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8138881;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:37;a:5:{i:0;s:178:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2, '')) AND (`r`.`is_company_role`=1) AND (`r`.`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8148711;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:39;a:5:{i:0;s:42:"SHOW FULL COLUMNS FROM `role_auth_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8157041;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:40;a:5:{i:0;s:42:"SHOW FULL COLUMNS FROM `role_auth_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8180821;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:42;a:5:{i:0;s:621:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'role_auth_setting' AND kcu.table_name = 'role_auth_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8193121;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:43;a:5:{i:0;s:621:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'role_auth_setting' AND kcu.table_name = 'role_auth_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.820086;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:48;a:5:{i:0;s:54:"SELECT * FROM `user_group_role` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9501281;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2496;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:49;a:5:{i:0;s:54:"SELECT * FROM `user_group_role` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9535279;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2496;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:51;a:5:{i:0;s:144:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2)) AND (`r`.`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9537041;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2517;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:52;a:5:{i:0;s:144:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2)) AND (`r`.`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.954803;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2517;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:54;a:5:{i:0;s:57:"SELECT * FROM `group_auth_setting` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9559009;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2522;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:55;a:5:{i:0;s:57:"SELECT * FROM `group_auth_setting` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956378;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2522;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:62;a:5:{i:0;s:519:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time`, MAX(IB.create_time) as latest_booking_time FROM `instruments` `Ins` INNER JOIN `instruments_book` `IB` ON Ins.id = IB.instrument_id WHERE (`Ins`.`status`='1') AND (`IB`.`create_by`='1135') GROUP BY `Ins`.`id` ORDER BY `latest_booking_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9678819;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:63;a:5:{i:0;s:519:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time`, MAX(IB.create_time) as latest_booking_time FROM `instruments` `Ins` INNER JOIN `instruments_book` `IB` ON Ins.id = IB.instrument_id WHERE (`Ins`.`status`='1') AND (`IB`.`create_by`='1135') GROUP BY `Ins`.`id` ORDER BY `latest_booking_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9708469;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9709461;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9742451;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9747851;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9753821;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:140:"SELECT COUNT(*) FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.975565;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7275;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:140:"SELECT COUNT(*) FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.977246;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7275;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:440:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time` FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650')) ORDER BY `Ins`.`create_time` DESC LIMIT 9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9774201;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7308;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:440:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time` FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650')) ORDER BY `Ins`.`create_time` DESC LIMIT 9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9796519;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7308;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:598:"SELECT `IB`.`id` AS `booking_id`, `IB`.`instrument_id`, `IB`.`start_time`, `IB`.`end_time`, `IB`.`status` AS `booking_status`, `IB`.`create_by`, `IB`.`create_time` AS `booking_create_time`, `IB`.`related_experiment`, `IB`.`remark` AS `booking_remark`, `IB`.`reminder` FROM `instruments_book` `IB` WHERE (((`IB`.`instrument_id` IN ('2647', '2648', '2656', '2666', '2654', '2650', '2665', '2664', '2661', '2658', '2655', '2653', '2652', '2649', '2645')) AND (`IB`.`status`=1)) AND (`IB`.`start_time` <= '2025-06-17 23:59:59')) AND (`IB`.`end_time` >= '2025-06-17 00:00:00') ORDER BY `IB`.`start_time`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9798889;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7373;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:598:"SELECT `IB`.`id` AS `booking_id`, `IB`.`instrument_id`, `IB`.`start_time`, `IB`.`end_time`, `IB`.`status` AS `booking_status`, `IB`.`create_by`, `IB`.`create_time` AS `booking_create_time`, `IB`.`related_experiment`, `IB`.`remark` AS `booking_remark`, `IB`.`reminder` FROM `instruments_book` `IB` WHERE (((`IB`.`instrument_id` IN ('2647', '2648', '2656', '2666', '2654', '2650', '2665', '2664', '2661', '2658', '2655', '2653', '2652', '2649', '2645')) AND (`IB`.`status`=1)) AND (`IB`.`start_time` <= '2025-06-17 23:59:59')) AND (`IB`.`end_time` >= '2025-06-17 00:00:00') ORDER BY `IB`.`start_time`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9809761;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7373;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:30:{i:24;a:5:{i:0;s:56:"SELECT * FROM `user_company_role` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.78368;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:25;a:5:{i:0;s:56:"SELECT * FROM `user_company_role` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7844889;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:811;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:27;a:5:{i:0;s:89:"SELECT * FROM `user_group_role` WHERE (`user_id`='1135') AND (`group_id` IN ('1', '598'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.785744;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:821;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:28;a:5:{i:0;s:89:"SELECT * FROM `user_group_role` WHERE (`user_id`='1135') AND (`group_id` IN ('1', '598'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.789165;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:821;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:30;a:5:{i:0;s:59:"SELECT * FROM `company_auth_setting` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.79005;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:31;a:5:{i:0;s:59:"SELECT * FROM `company_auth_setting` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7909131;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:833;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:36;a:5:{i:0;s:178:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2, '')) AND (`r`.`is_company_role`=1) AND (`r`.`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8138881;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:37;a:5:{i:0;s:178:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2, '')) AND (`r`.`is_company_role`=1) AND (`r`.`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8148711;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:39;a:5:{i:0;s:42:"SHOW FULL COLUMNS FROM `role_auth_setting`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8157041;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:40;a:5:{i:0;s:42:"SHOW FULL COLUMNS FROM `role_auth_setting`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8180821;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:42;a:5:{i:0;s:621:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'role_auth_setting' AND kcu.table_name = 'role_auth_setting'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8193121;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:43;a:5:{i:0;s:621:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'role_auth_setting' AND kcu.table_name = 'role_auth_setting'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.820086;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:840;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:181;s:8:"function";s:22:"getCompanyAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:48;a:5:{i:0;s:54:"SELECT * FROM `user_group_role` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9501281;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2496;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:49;a:5:{i:0;s:54:"SELECT * FROM `user_group_role` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9535279;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2496;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:51;a:5:{i:0;s:144:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2)) AND (`r`.`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9537041;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2517;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:52;a:5:{i:0;s:144:"SELECT `t`.* FROM `role_auth_setting` `t` LEFT JOIN `company_role` `r` ON t.role_id=r.id WHERE (`t`.`role_id` IN (5, 1, 2)) AND (`r`.`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.954803;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2517;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:54;a:5:{i:0;s:57:"SELECT * FROM `group_auth_setting` WHERE `user_id`='1135'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9559009;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2522;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:55;a:5:{i:0;s:57:"SELECT * FROM `group_auth_setting` WHERE `user_id`='1135'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956378;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\services\CompanyAuthServer.php";s:4:"line";i:2522;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:183;s:8:"function";s:20:"getGroupAuthByUserId";s:5:"class";s:35:"frontend\services\CompanyAuthServer";s:4:"type";s:2:"::";}}}i:62;a:5:{i:0;s:519:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time`, MAX(IB.create_time) as latest_booking_time FROM `instruments` `Ins` INNER JOIN `instruments_book` `IB` ON Ins.id = IB.instrument_id WHERE (`Ins`.`status`='1') AND (`IB`.`create_by`='1135') GROUP BY `Ins`.`id` ORDER BY `latest_booking_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9678819;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:63;a:5:{i:0;s:519:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time`, MAX(IB.create_time) as latest_booking_time FROM `instruments` `Ins` INNER JOIN `instruments_book` `IB` ON Ins.id = IB.instrument_id WHERE (`Ins`.`status`='1') AND (`IB`.`create_by`='1135') GROUP BY `Ins`.`id` ORDER BY `latest_booking_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9708469;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9709461;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9742451;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9747851;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9753821;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7245;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:140:"SELECT COUNT(*) FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.975565;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7275;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:140:"SELECT COUNT(*) FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.977246;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7275;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:440:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time` FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650')) ORDER BY `Ins`.`create_time` DESC LIMIT 9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9774201;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7308;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:440:"SELECT `Ins`.`id`, `Ins`.`name`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`responsible_person`, `Ins`.`position`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`max_booking_duration`, `Ins`.`min_advance`, `Ins`.`model`, `Ins`.`pictures`, `Ins`.`create_time` FROM `instruments` `Ins` WHERE (`Ins`.`status`='1') AND (`Ins`.`id` NOT IN ('2647', '2648', '2656', '2666', '2654', '2650')) ORDER BY `Ins`.`create_time` DESC LIMIT 9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9796519;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7308;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:598:"SELECT `IB`.`id` AS `booking_id`, `IB`.`instrument_id`, `IB`.`start_time`, `IB`.`end_time`, `IB`.`status` AS `booking_status`, `IB`.`create_by`, `IB`.`create_time` AS `booking_create_time`, `IB`.`related_experiment`, `IB`.`remark` AS `booking_remark`, `IB`.`reminder` FROM `instruments_book` `IB` WHERE (((`IB`.`instrument_id` IN ('2647', '2648', '2656', '2666', '2654', '2650', '2665', '2664', '2661', '2658', '2655', '2653', '2652', '2649', '2645')) AND (`IB`.`status`=1)) AND (`IB`.`start_time` <= '2025-06-17 23:59:59')) AND (`IB`.`end_time` >= '2025-06-17 00:00:00') ORDER BY `IB`.`start_time`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9798889;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7373;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:598:"SELECT `IB`.`id` AS `booking_id`, `IB`.`instrument_id`, `IB`.`start_time`, `IB`.`end_time`, `IB`.`status` AS `booking_status`, `IB`.`create_by`, `IB`.`create_time` AS `booking_create_time`, `IB`.`related_experiment`, `IB`.`remark` AS `booking_remark`, `IB`.`reminder` FROM `instruments_book` `IB` WHERE (((`IB`.`instrument_id` IN ('2647', '2648', '2656', '2666', '2654', '2650', '2665', '2664', '2661', '2658', '2655', '2653', '2652', '2649', '2645')) AND (`IB`.`status`=1)) AND (`IB`.`start_time` <= '2025-06-17 23:59:59')) AND (`IB`.`end_time` >= '2025-06-17 00:00:00') ORDER BY `IB`.`start_time`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9809761;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:7373;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentBookingController.php";s:4:"line";i:42;s:8:"function";s:27:"queryInstrumentsWithBooking";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"68513b7e48ec1";s:3:"url";s:69:"http://dev.eln.integle.com/?r=instrument-booking/get-instruments-data";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1750154111;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:9:"mailCount";i:0;}}